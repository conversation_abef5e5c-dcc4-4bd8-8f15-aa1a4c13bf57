'use client';

import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  CircularProgress,
} from '@mui/material';

interface SignOutConfirmationModalProps {
  open: boolean;
  handleClose: () => void;
  handleConfirm: () => void;
  isSigningOut: boolean;
}

const SignOutConfirmationModal: React.FC<SignOutConfirmationModalProps> = ({
  open,
  handleClose,
  handleConfirm,
  isSigningOut,
}) => {
  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="xs"
      fullWidth
      BackdropProps={{
        sx: {
          backdropFilter: 'blur(10px)',
        },
      }}
      PaperProps={{
        sx: {
          borderRadius: '16px',
          backgroundColor: 'background.paper',
        },
      }}
    >
      <DialogTitle>Confirm Sign Out</DialogTitle>
      <DialogContent dividers>
        <Typography>Are you sure you want to sign out?</Typography>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose} color="inherit" variant="outlined" disabled={isSigningOut} sx={{ borderRadius: '16px' }}>
          Cancel
        </Button>
        <Button
          onClick={handleConfirm}
          color="error"
          variant="contained"
          disabled={isSigningOut}
          startIcon={isSigningOut ? <CircularProgress size={20} color="inherit" /> : null}
          sx={{ borderRadius: '16px' }}
        >
          {isSigningOut ? 'Signing Out...' : 'Sign Out'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default SignOutConfirmationModal;