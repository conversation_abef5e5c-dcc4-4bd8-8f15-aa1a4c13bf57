<!DOCTYPE html>
<html lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>HKBU GenAI Platform</title>
<script src="https://cdn.tailwindcss.com"></script>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&amp;display=swap" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet"/>
<style>
        body {
            font-family: 'Inter', sans-serif;
        }
    </style>
</head>
<body class="bg-gray-100">
<div class="flex h-screen">
<aside class="bg-slate-100 w-72 p-6 flex flex-col justify-between">
<div>
<div class="flex items-center mb-8">
<span class="material-icons text-blue-600 text-3xl mr-2">hub</span>
<h1 class="text-xl font-semibold text-gray-800">HKBU GenAI Platform</h1>
<span class="material-icons text-gray-500 ml-2">chevron_left</span>
</div>
<div class="mb-6">
<button class="flex items-center text-gray-600 hover:text-blue-600 w-full py-2 px-3 rounded-md">
<span class="material-icons mr-3">history</span>
<span>History</span>
</button>
</div>
<div>
<h2 class="text-xs text-gray-500 font-medium mb-3 px-3">Today</h2>
<nav class="space-y-1">
<a class="flex items-start text-sm text-gray-700 hover:bg-slate-200 py-2 px-3 rounded-md" href="#">
<span class="material-icons text-green-500 mr-3 mt-0.5">chat_bubble_outline</span>
<span>Admission Fee</span>
</a>
<a class="flex items-start text-sm text-gray-700 hover:bg-slate-200 py-2 px-3 rounded-md" href="#">
<span class="material-icons text-green-500 mr-3 mt-0.5">chat_bubble_outline</span>
<span>Tell me the application fee...</span>
</a>
<a class="flex items-start text-sm text-gray-700 hover:bg-slate-200 py-2 px-3 rounded-md" href="#">
<span class="material-icons text-green-500 mr-3 mt-0.5">chat_bubble_outline</span>
<span>Are there any creative arts...</span>
</a>
<a class="flex items-start text-sm text-gray-700 hover:bg-slate-200 py-2 px-3 rounded-md" href="#">
<span class="material-icons text-green-500 mr-3 mt-0.5">chat_bubble_outline</span>
<span>How to apply for double d...</span>
</a>
</nav>
</div>
<div class="mt-6">
<h2 class="text-xs text-gray-500 font-medium mb-3 px-3">7 Days</h2>
<nav class="space-y-1">
<a class="flex items-start text-sm text-gray-700 hover:bg-slate-200 py-2 px-3 rounded-md" href="#">
<span class="material-icons text-green-500 mr-3 mt-0.5">chat_bubble_outline</span>
<span>Are there any part-time gra...</span>
</a>
<a class="flex items-start text-sm text-gray-700 hover:bg-slate-200 py-2 px-3 rounded-md" href="#">
<span class="material-icons text-green-500 mr-3 mt-0.5">chat_bubble_outline</span>
<span>Can I stay in Hong Kong aft...</span>
</a>
</nav>
</div>
<div class="mt-6">
<h2 class="text-xs text-gray-500 font-medium mb-3 px-3">60 Days</h2>
<nav class="space-y-1">
<a class="flex items-start text-sm text-gray-700 hover:bg-slate-200 py-2 px-3 rounded-md" href="#">
<span class="material-icons text-blue-500 mr-3 mt-0.5">chat_bubble_outline</span>
<span>Can I do part time work whil...</span>
</a>
<a class="flex items-start text-sm text-gray-700 hover:bg-slate-200 py-2 px-3 rounded-md" href="#">
<span class="material-icons text-blue-500 mr-3 mt-0.5">chat_bubble_outline</span>
<span>Tell me the application fee o...</span>
</a>
<a class="flex items-start text-sm text-gray-700 hover:bg-slate-200 py-2 px-3 rounded-md" href="#">
<span class="material-icons text-green-500 mr-3 mt-0.5">chat_bubble_outline</span>
<span>When will classes starts? Ap...</span>
</a>
<a class="flex items-start text-sm text-gray-700 hover:bg-slate-200 py-2 px-3 rounded-md" href="#">
<span class="material-icons text-blue-500 mr-3 mt-0.5">chat_bubble_outline</span>
<span>If I am a Hong Kong citizen...</span>
</a>
</nav>
</div>
</div>
<div>
<button class="flex items-center text-gray-600 hover:text-blue-600 w-full py-2 px-3 rounded-md">
<span class="material-icons mr-3">settings</span>
<span>Settings</span>
</button>
</div>
</aside>
<main class="flex-1 p-8 overflow-y-auto">
<div class="max-w-4xl mx-auto">
<h2 class="text-3xl font-semibold text-gray-800 mb-8 text-center">What do you want to know?</h2>
<div class="flex justify-center items-center space-x-4 mb-8">
<button class="flex items-center bg-green-100 text-green-700 px-4 py-2 rounded-full shadow-sm hover:bg-green-200">
<span class="material-icons text-green-700 mr-2">smart_toy</span>
                        GPT-4 O
                    </button>
<button class="flex items-center bg-gray-200 text-gray-700 px-4 py-2 rounded-full shadow-sm hover:bg-gray-300">
<span class="material-icons text-gray-700 mr-2">search_check</span>
                        DeepSeek - R1
                    </button>
<button class="p-2 rounded-full hover:bg-gray-200">
<span class="material-icons text-gray-600">search</span>
</button>
</div>
<div class="relative mb-12">
<textarea class="w-full p-4 pr-20 border border-green-500 rounded-xl focus:ring-2 focus:ring-green-300 focus:border-green-500 resize-none shadow-lg" placeholder="Type your query" rows="3"></textarea>
<div class="absolute right-4 bottom-4 flex items-center space-x-3">
<button class="p-2 rounded-full hover:bg-gray-100">
<span class="material-icons text-gray-500">settings_voice</span>
</button>
<button class="p-3 bg-green-500 text-white rounded-full hover:bg-green-600 shadow">
<span class="material-icons">arrow_upward</span>
</button>
</div>
<span class="material-icons absolute left-4 bottom-4 text-gray-400">mic_none</span>
</div>
<section class="mb-12">
<div class="flex justify-between items-center mb-4">
<h3 class="text-2xl font-semibold text-gray-800">General Model</h3>
<a class="text-sm text-blue-600 hover:underline" href="#">See all</a>
</div>
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 relative">
<div class="bg-blue-600 p-6 rounded-xl shadow-md text-white flex flex-col">
<div class="flex items-center mb-2">
<span class="material-icons text-3xl mr-2">hub</span>
<h4 class="text-xl font-semibold">GPT-4o</h4>
</div>
<p class="text-sm text-blue-100 flex-grow">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc vulputate libero et velit interdum, ac aliquet odio mattis.</p>
</div>
<div class="bg-slate-200 p-6 rounded-xl shadow-md text-gray-700 flex flex-col">
<div class="flex items-center mb-2">
<span class="material-icons text-blue-500 text-3xl mr-2">auto_awesome</span>
<h4 class="text-xl font-semibold text-gray-800">DeepSeek-V3</h4>
</div>
<p class="text-sm text-gray-600 flex-grow">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc vulputate libero et velit interdum, ac aliquet odio mattis. Class aptent taciti socio...</p>
</div>
<div class="bg-slate-200 p-6 rounded-xl shadow-md text-gray-700 flex flex-col">
<div class="flex items-center mb-2">
<span class="material-icons text-blue-500 text-3xl mr-2">auto_awesome</span>
<h4 class="text-xl font-semibold text-gray-800">Gemini-1.5-Pro</h4>
</div>
<p class="text-sm text-gray-600 flex-grow">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc vulputate libero et velit interdum, ac aliquet odio mattis. Class ...</p>
</div>
<div class="bg-slate-200 p-6 rounded-xl shadow-md text-gray-700 flex flex-col">
<div class="flex items-center mb-2">
<span class="material-icons text-blue-500 text-3xl mr-2">auto_awesome</span>
<h4 class="text-xl font-semibold text-gray-800">Gemini-2.0-Flash</h4>
</div>
<p class="text-sm text-gray-600 flex-grow">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc vulputate libero et velit interdum, ac aliquet odio mattis. Class ...</p>
</div>
<button class="absolute right-[-20px] top-1/2 transform -translate-y-1/2 bg-white p-2 rounded-full shadow-md hover:bg-gray-100">
<span class="material-icons text-gray-600">chevron_right</span>
</button>
</div>
</section>
<section class="mb-12">
<div class="flex justify-between items-center mb-4">
<h3 class="text-2xl font-semibold text-gray-800">Thinking Model</h3>
<a class="text-sm text-blue-600 hover:underline" href="#">See all</a>
</div>
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 relative">
<div class="bg-slate-200 p-6 rounded-xl shadow-md text-gray-700 flex flex-col">
<div class="flex items-center mb-2">
<span class="material-icons text-purple-500 text-3xl mr-2">psychology</span>
<h4 class="text-xl font-semibold text-gray-800">Gemini-2.5-Pro</h4>
</div>
<p class="text-sm text-gray-600 flex-grow">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc vulputate libero et velit interdum, ac aliquet odio mattis. Class aptent...</p>
</div>
<div class="bg-slate-200 p-6 rounded-xl shadow-md text-gray-700 flex flex-col">
<div class="flex items-center mb-2">
<span class="material-icons text-teal-500 text-3xl mr-2">bubble_chart</span>
<h4 class="text-xl font-semibold text-gray-800">o1</h4>
</div>
<p class="text-sm text-gray-600 flex-grow">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc vulputate libero et velit interdum, ac aliquet odio mattis wh...</p>
</div>
<div class="bg-slate-200 p-6 rounded-xl shadow-md text-gray-700 flex flex-col">
<div class="flex items-center mb-2">
<span class="material-icons text-blue-500 text-3xl mr-2">auto_awesome</span>
<h4 class="text-xl font-semibold text-gray-800">DeepSeek-R1</h4>
</div>
<p class="text-sm text-gray-600 flex-grow">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc vulputate libero et velit interdum, ac aliquet odio mattis. Class... What...</p>
</div>
<div class="bg-slate-200 p-6 rounded-xl shadow-md text-gray-700 flex flex-col">
<div class="flex items-center mb-2">
<span class="material-icons text-green-500 text-3xl mr-2">scatter_plot</span>
<h4 class="text-xl font-semibold text-gray-800">o3-mini</h4>
</div>
<p class="text-sm text-gray-600 flex-grow">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc vulputate libero et velit interdum, ac aliquet odio mattis wh...</p>
</div>
<button class="absolute right-[-20px] top-1/2 transform -translate-y-1/2 bg-white p-2 rounded-full shadow-md hover:bg-gray-100">
<span class="material-icons text-gray-600">chevron_right</span>
</button>
</div>
</section>
<section>
<div class="flex justify-between items-center mb-4">
<h3 class="text-2xl font-semibold text-gray-800">Image Generation</h3>
</div>
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
<div class="bg-slate-200 p-6 rounded-xl shadow-md text-gray-700 flex flex-col">
<div class="flex items-center mb-2">
<img alt="Adobe Firefly logo" class="w-8 h-8 mr-2" src="https://lh3.googleusercontent.com/aida-public/AB6AXuA7wUPLXHDIoQ1eNa0NwXDBcdW8u0uRjxBDbOpzicNqU1XJJtX88-l8vPFgXLhb-xkCV1bXUzIf9P-V52OHKy9v1XKdQ44vN6e72fr8Ngn4zRUfL0kuR2XQv7AADSf_gnNjcuz3rpBThuIEUeTgZD83r1lijjkzgL8F1VOn86wA7GWJ0otL1Mt2P3QUiPgl5Z2y2S703IZj2YZnsR0-dvU2wly7o8lL74J71-GY2-gr_R2buwfxdkj3Q7Z3h7WEgstAaNY_G7NZaSlw"/> 
<h4 class="text-xl font-semibold text-gray-800">Adobe Firefly</h4>
</div>
<p class="text-sm text-gray-600 flex-grow">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc vulputate libero et velit interdum, ac aliquet odio mattis. Class aptent taciti sociosqu ad litora torquent per conubia nostra, per inceptos himenaeos.</p>
</div>
<div class="bg-slate-200 p-6 rounded-xl shadow-md text-gray-700 flex flex-col">
<div class="flex items-center mb-2">
<img alt="Adobe Express logo" class="w-8 h-8 mr-2" src="https://lh3.googleusercontent.com/aida-public/AB6AXuAjUE3H9IIa4AF1ysvLPQ9-qYFr7WgT_C8Sz2LjJtmtA_emQRqijuiEalhHzBDMbs2cQmLOlCBA2QJGj_WLBEVk60l0g625CO8cuxda8gdBmGuq8atbaMTK77ME0LfHsZcaN0z7dOeTjEu-R1pusgGQv0KNzhEOQpKz6oHSyh5SyZv14cHcjl3s0npua1zL_zZ9nQKZ_YXUCGnvUv_BAW0rhvZsTgqTPJLm6N4XrVFi8TI4HC3ZPDCDPxU1KBSnmz6UlkZ_F_IZmgwj"/> 
<h4 class="text-xl font-semibold text-gray-800">Adobe Express</h4>
</div>
<p class="text-sm text-gray-600 flex-grow">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc vulputate libero et velit interdum, ac aliquet odio mattis. Class aptent taciti sociosqu ad litora torquent per conubia nostra, per inceptos himenaeos.</p>
</div>
</div>
</section>
</div>
</main>
</div>

</body></html>