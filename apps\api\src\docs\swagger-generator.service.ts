import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';

export interface ProviderConfig {
  name: string;
  displayName: string;
  title: string;
  description: string;
  modelFilter: (model: any) => boolean;
  examples: {
    standard: any;
    [key: string]: any;
  };
  specialModels?: string[];
  chipLabels: {
    provider: string;
    version: string;
  };
  referenceUrl?: string;
}

@Injectable()
export class SwaggerGeneratorService {
  private readonly logger = new Logger(SwaggerGeneratorService.name);

  constructor(private readonly prisma: PrismaService) {}

  async generateSwaggerSpec(
    providerConfig: ProviderConfig,
    employeeType: string,
    username: string,
    host?: string,
  ): Promise<any> {
    try {
      // Execute stored procedure to get model list
      // Use null for api_version to get all models regardless of version
      const apiVersion = null;

      const modelList = await this.prisma.$queryRaw`
        EXEC sp_model_GetModelList_v6 
          @employee_type = ${employeeType},
          @username = ${username},
          @api = ${1},
          @api_version = ${apiVersion}
      `;

      // Log raw results for debugging
      this.logger.log(
        `[DEBUG] Raw model list for ${providerConfig.name}: ${(modelList as any[]).length} models found`,
      );
      (modelList as any[]).forEach((model, index) => {
        this.logger.log(
          `[DEBUG] Model ${index + 1}: model_name="${model.model_name}", api_status="${model.api_status}", rec_status="${model.rec_status}", model_type="${model.model_type}"`,
        );
      });

      // Filter models based on provider configuration
      const filteredModels = (modelList as any[]).filter((model) => {
        const hasModelName = !!model.model_name;
        const isApiActive = model.api_status === 'A';
        const isRecActive = model.rec_status === 'A';
        const passesProviderFilter = providerConfig.modelFilter(model);

        // Log only models that pass the filter
        if (
          hasModelName &&
          isApiActive &&
          isRecActive &&
          passesProviderFilter
        ) {
          this.logger.log(
            `[DEBUG] Model PASSED filter: model_name="${model.model_name}"`,
          );
        }

        return (
          hasModelName && isApiActive && isRecActive && passesProviderFilter
        );
      });

      const chatCompletionModels = filteredModels.filter(
        (m) => m.model_type !== 'embedding',
      );
      const embeddingModels = filteredModels.filter(
        (m) => m.model_type === 'embedding',
      );

      // Generate Markdown table for models and API versions
      const modelTableMarkdown =
        filteredModels.length > 0
          ? `| Model Name | API Version |
|------------|-------------|
${filteredModels.map((m) => `| ${m.model_name} | ${m.api_version || 'N/A'} |`).join('\n')}`
          : 'No active models available.';

      const basePath = process.env.NEXT_PUBLIC_BASE_PATH || '';

      // Determine default protocol and endpoint based on environment
      const isLocal = host
        ? host.includes('localhost') || host.includes('127.0.0.1')
        : false;
      const defaultProtocol = isLocal ? 'http' : 'https';
      const defaultEndpoint = isLocal ? 'localhost:3000' : 'genai.hkbu.edu.hk';

      // Build description with reference URL if provided
      let description = `${providerConfig.description}

**Available Models and API Versions:**

${modelTableMarkdown}`;

      if (providerConfig.referenceUrl) {
        description += `

You can refer to [${providerConfig.displayName} API documentation](${providerConfig.referenceUrl}) for further information.`;
      }

      description += `

**To test the REST API, you can use:**
- [Postman for Windows](${basePath}/general/hkbu-chatgpt-api?fullscreen=true)
- [Command line – Curl](${basePath}/general/hkbu-chatgpt-api?fullscreen=true#curl)
- [Python Example](${basePath}/general/hkbu-chatgpt-api?fullscreen=true#python)`;

      const spec = {
        openapi: '3.0.0',
        info: {
          title: providerConfig.title,
          description,
          version: apiVersion,
        },
        servers: [
          {
            url: '{protocol}://{endpoint}/api/v0/rest',
            description: 'HKBU GenAI Platform API',
            variables: {
              protocol: {
                default: defaultProtocol,
                enum: ['http', 'https'],
                description: 'Protocol (http for local, https for production)',
              },
              endpoint: {
                default: defaultEndpoint,
                description:
                  'Server endpoint (localhost:3000 for local development)',
              },
            },
          },
        ],
        security: [
          {
            'api-key': [],
          },
        ],
        components: {
          securitySchemes: {
            'api-key': {
              type: 'apiKey',
              in: 'header',
              name: 'api-key',
              description: 'API Key authentication',
            },
          },
          schemas: {
            CreateChatCompletionDto: {
              type: 'object',
              required: ['messages'],
              properties: {
                messages: {
                  type: 'array',
                  items: {
                    type: 'object',
                    required: ['role', 'content'],
                    properties: {
                      role: {
                        type: 'string',
                        enum: ['system', 'user', 'assistant'],
                      },
                      content: {
                        oneOf: [
                          { type: 'string' },
                          {
                            type: 'array',
                            items: {
                              type: 'object',
                              properties: {
                                type: {
                                  type: 'string',
                                  enum: ['text', 'image_url'],
                                },
                                text: { type: 'string' },
                                image_url: {
                                  type: 'object',
                                  properties: {
                                    url: { type: 'string' },
                                    detail: {
                                      type: 'string',
                                      enum: ['low', 'high', 'auto'],
                                    },
                                  },
                                },
                              },
                            },
                          },
                        ],
                      },
                    },
                  },
                  example: [
                    {
                      role: 'user',
                      content:
                        'Hello! Can you help me write a Python function to calculate the factorial of a number?',
                    },
                  ],
                },
                temperature: {
                  type: 'number',
                  minimum: 0,
                  maximum: 2,
                  default: 0.7,
                  description: providerConfig.specialModels
                    ? `Controls randomness. Not supported by ${providerConfig.specialModels.join(', ')} models.`
                    : 'Controls randomness in the response.',
                  example: 0.7,
                },
                max_tokens: {
                  type: 'integer',
                  minimum: 1,
                  example: 150,
                  description: 'Maximum number of tokens to generate.',
                },
                top_p: {
                  type: 'number',
                  minimum: 0,
                  maximum: 1,
                  example: 1,
                  description: providerConfig.specialModels
                    ? `Nucleus sampling parameter. Not supported by ${providerConfig.specialModels.join(', ')} models.`
                    : 'Nucleus sampling parameter.',
                },
                stream: {
                  type: 'boolean',
                  default: false,
                  description: 'Enable streaming responses.',
                },
              },
            },
            CreateEmbeddingsDto: {
              type: 'object',
              required: ['input'],
              properties: {
                input: {
                  oneOf: [
                    { type: 'string' },
                    { type: 'array', items: { type: 'string' } },
                  ],
                },
              },
            },
          },
        },
        paths: {},
      };

      // Add single parameterized path for chat completions
      if (chatCompletionModels.length > 0) {
        // Extract unique model names and API versions
        const modelNames = [
          ...new Set(chatCompletionModels.map((m) => m.model_name)),
        ];
        const apiVersions = [
          ...new Set(filteredModels.map((m) => m.api_version).filter((v) => v)),
        ];

        // Create examples based on provider configuration
        const examples: any = {};

        // Process all examples dynamically
        Object.entries(providerConfig.examples).forEach(([key, example]) => {
          if (example && typeof example === 'object' && example.value) {
            const exampleKey =
              key === 'standard'
                ? 'standard-models'
                : key === 'special'
                  ? 'special-models'
                  : `${key}-example`;

            examples[exampleKey] = {
              summary:
                example.summary ||
                `Example for ${providerConfig.displayName} models`,
              value: example.value,
            };
          }
        });

        spec.paths['/deployments/{modelDeploymentName}/chat/completions'] = {
          post: {
            summary: 'Creates a completion for the chat message',
            operationId: 'createChatCompletion',
            tags: ['Chat Completions'],
            parameters: [
              {
                name: 'modelDeploymentName',
                in: 'path',
                required: true,
                description: `The deployment name of the model. Available models:\n\n${modelTableMarkdown}`,
                schema: {
                  type: 'string',
                  enum: modelNames,
                },
              },
              // Include api-version parameter with appropriate requirements
              ...(apiVersions.length > 0
                ? [
                    {
                      name: 'api-version',
                      in: 'query',
                      required: true,
                      description: 'The API version to use for this operation',
                      schema: {
                        type: 'string',
                        enum: apiVersions,
                        default: apiVersions[0],
                      },
                    },
                  ]
                : [
                    {
                      name: 'api-version',
                      in: 'query',
                      required: false,
                      description: 'The API version to use for this operation (optional for some models)',
                      schema: {
                        type: 'string',
                        default: '2024-02-01',
                      },
                    },
                  ]),
            ],
            requestBody: {
              required: true,
              content: {
                'application/json': {
                  schema: {
                    $ref: '#/components/schemas/CreateChatCompletionDto',
                  },
                  examples,
                },
              },
            },
            responses: {
              '200': {
                description: 'Successful response',
                content: {
                  'application/json': {
                    schema: {
                      type: 'object',
                      properties: {
                        id: { type: 'string' },
                        object: { type: 'string', default: 'chat.completion' },
                        created: { type: 'integer' },
                        model: { type: 'string' },
                        choices: {
                          type: 'array',
                          items: {
                            type: 'object',
                            properties: {
                              index: { type: 'integer' },
                              message: {
                                type: 'object',
                                properties: {
                                  role: { type: 'string' },
                                  content: { type: 'string' },
                                },
                              },
                              finish_reason: { type: 'string' },
                            },
                          },
                        },
                        usage: {
                          type: 'object',
                          properties: {
                            prompt_tokens: { type: 'integer' },
                            completion_tokens: { type: 'integer' },
                            total_tokens: { type: 'integer' },
                          },
                        },
                      },
                    },
                  },
                },
              },
              '400': { description: 'Bad request' },
              '401': { description: 'Unauthorized' },
              '429': { description: 'Rate limit exceeded' },
              '500': { description: 'Internal server error' },
            },
            security: [{ 'api-key': [] }],
          },
        };
      }

      // Add single parameterized path for embeddings
      if (embeddingModels.length > 0) {
        // Extract unique embedding model names
        const embeddingModelNames = [
          ...new Set(embeddingModels.map((m) => m.model_name)),
        ];
        const embeddingApiVersions = [
          ...new Set(
            embeddingModels.map((m) => m.api_version).filter((v) => v),
          ),
        ];

        // Generate embedding models table
        const embeddingTableMarkdown =
          embeddingModels.length > 0
            ? `| Model Name | API Version |
|------------|-------------|
${embeddingModels.map((m) => `| ${m.model_name} | ${m.api_version || 'N/A'} |`).join('\n')}`
            : 'No active embedding models available.';

        spec.paths['/deployments/{modelDeploymentName}/embeddings'] = {
          post: {
            summary: 'Creates an embedding vector representing the input text',
            operationId: 'createEmbedding',
            tags: ['Embeddings'],
            parameters: [
              {
                name: 'modelDeploymentName',
                in: 'path',
                required: true,
                description: `The deployment name of the embedding model. Available models:\n\n${embeddingTableMarkdown}`,
                schema: {
                  type: 'string',
                  enum: embeddingModelNames,
                },
              },
              // Include api-version parameter with appropriate requirements
              ...(embeddingApiVersions.length > 0
                ? [
                    {
                      name: 'api-version',
                      in: 'query',
                      required: true,
                      description: 'The API version to use for this operation',
                      schema: {
                        type: 'string',
                        enum: embeddingApiVersions,
                        default: embeddingApiVersions[0],
                      },
                    },
                  ]
                : [
                    {
                      name: 'api-version',
                      in: 'query',
                      required: false,
                      description: 'The API version to use for this operation (optional for some models)',
                      schema: {
                        type: 'string',
                        default: '2024-02-01',
                      },
                    },
                  ]),
            ],
            requestBody: {
              required: true,
              content: {
                'application/json': {
                  schema: { $ref: '#/components/schemas/CreateEmbeddingsDto' },
                },
              },
            },
            responses: {
              '200': {
                description: 'Successful response',
                content: {
                  'application/json': {
                    schema: {
                      type: 'object',
                      properties: {
                        object: { type: 'string', default: 'list' },
                        data: {
                          type: 'array',
                          items: {
                            type: 'object',
                            properties: {
                              object: { type: 'string', default: 'embedding' },
                              embedding: {
                                type: 'array',
                                items: { type: 'number' },
                              },
                              index: { type: 'integer' },
                            },
                          },
                        },
                        model: { type: 'string' },
                        usage: {
                          type: 'object',
                          properties: {
                            prompt_tokens: { type: 'integer' },
                            total_tokens: { type: 'integer' },
                          },
                        },
                      },
                    },
                  },
                },
              },
              '400': { description: 'Bad request' },
              '401': { description: 'Unauthorized' },
              '429': { description: 'Rate limit exceeded' },
              '500': { description: 'Internal server error' },
            },
            security: [{ 'api-key': [] }],
          },
        };
      }

      return spec;
    } catch (error) {
      this.logger.error(
        `Error generating swagger spec for ${providerConfig.name}:`,
        error,
      );
      throw error;
    }
  }
}
