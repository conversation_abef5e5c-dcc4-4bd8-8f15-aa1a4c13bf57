import {
  Injectable,
  CanActivate,
  ExecutionContext,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { InjectRedis } from '@nestjs-modules/ioredis';
import { Redis } from 'ioredis';
import { AuthenticatedUser } from '../../auth/user.interface';

@Injectable()
export class GeneralRateLimitGuard implements CanActivate {
  private readonly logger = new Logger(GeneralRateLimitGuard.name);
  private readonly RATE_LIMIT = 60; // requests per minute
  private readonly TTL_SECONDS = 60; // 1 minute

  constructor(@InjectRedis() private readonly redis: Redis) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();

    // Skip endpoints that have their own specialised guards
    const url: string = request.originalUrl || request.url || '';
    if (this.shouldSkip(url)) {
      return true;
    }

    const user: AuthenticatedUser | undefined = request.user;
    const identifier = user?.userId || this.getClientIp(request);
    if (!identifier) {
      return true; // cannot identify
    }

    const key = `rate_limit:general:${identifier}`;

    try {
      const currentCountStr = await this.redis.get(key);
      const count = currentCountStr ? parseInt(currentCountStr, 10) : 0;
      const ttl = await this.redis.ttl(key);

      if (count >= this.RATE_LIMIT) {
        const resetAt = new Date(
          Date.now() + (ttl > 0 ? ttl * 1000 : this.TTL_SECONDS * 1000),
        );
        this.throwRateLimitExceeded(identifier, ttl, resetAt);
      }

      const newCount = await this.redis.incr(key);
      if (newCount === 1) {
        await this.redis.expire(key, this.TTL_SECONDS);
      }

      // Add headers
      const response = context.switchToHttp().getResponse();
      const updatedTtl = await this.redis.ttl(key);
      this.setHeader(response, 'X-RateLimit-Limit', this.RATE_LIMIT);
      this.setHeader(
        response,
        'X-RateLimit-Remaining',
        Math.max(0, this.RATE_LIMIT - newCount),
      );
      this.setHeader(
        response,
        'X-RateLimit-Reset',
        new Date(
          Date.now() +
            (updatedTtl > 0 ? updatedTtl * 1000 : this.TTL_SECONDS * 1000),
        ).toISOString(),
      );

      return true;
    } catch (err) {
      if (err instanceof HttpException) throw err;
      const message = err instanceof Error ? err.message : 'Unknown error';
      this.logger.error(`Redis error in general rate limiting: ${message}`);
      return true; // fail-open
    }
  }

  private shouldSkip(url: string): boolean {
    return (
      url.includes('/prompt-rewrite') ||
      url.includes('/speech') ||
      url.includes('/chat/completions') ||
      url.includes('/rest/deployments')
    );
  }

  private getClientIp(request: any): string | null {
    return (
      request.ip ||
      request.headers?.['x-forwarded-for']?.split(',').shift() ||
      request.connection?.remoteAddress ||
      null
    );
  }

  private setHeader(response: any, name: string, value: string | number): void {
    if (typeof response.header === 'function') {
      response.header(name, value.toString());
    } else if (typeof response.setHeader === 'function') {
      response.setHeader(name, value.toString());
    }
  }

  private throwRateLimitExceeded(
    identifier: string,
    ttl: number,
    resetAt: Date,
  ): never {
    this.logger.warn(
      `General API rate limit exceeded for identifier ${identifier}.`,
    );
    throw new HttpException(
      {
        statusCode: HttpStatus.TOO_MANY_REQUESTS,
        message: 'General API rate limit exceeded',
        error: 'Too Many Requests',
        retryAfter: ttl > 0 ? ttl : this.TTL_SECONDS,
        limit: this.RATE_LIMIT,
        remaining: 0,
        reset: resetAt.toISOString(),
      },
      HttpStatus.TOO_MANY_REQUESTS,
    );
  }
}
