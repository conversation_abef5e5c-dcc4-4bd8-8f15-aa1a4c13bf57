-- Stored Procedure to rename a conversation
CREATE PROCEDURE sp_cvst_RenameConversation
   @conversation_uuid UNIQUEIDENTIFIER,
   @new_title NVARCHAR(MAX),
   @encryption_key_name NVA<PERSON><PERSON><PERSON>(255),
   @decryption_cert_name NVA<PERSON><PERSON>R(255)
AS
BEGIN
   DECLARE @sql NVARCHAR(MAX);

   SET @sql = N'OPEN SYMMETRIC KEY ' + QUOTENAME(@encryption_key_name) + N' DECRYPTION BY CERTIFICATE ' + QUOTENAME(@decryption_cert_name) + N';';
   EXEC sp_executesql @sql;

   UPDATE dbo.conversation
   SET conversation_title = EncryptByKey(Key_GUID(@encryption_key_name), @new_title)
   WHERE conversation_uuid = @conversation_uuid;

   SET @sql = N'CLOSE SYMMETRIC KEY ' + QUOTENAME(@encryption_key_name) + N';';
   EXEC sp_executesql @sql;
END;
GO