import requests
import json
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:3003/api/v0"
API_KEY = "80b60025-07b4-4097-ba1d-e14695359dd0"

headers = {
    'Content-Type': 'application/json',
    'api-key': API_KEY
}

def make_single_request(model_name, request_id):
    """Make a single request and return result"""
    url = f"{BASE_URL}/rest/deployments/{model_name}/chat/completions"
    params = {"api-version": "2024-02-01"}
    
    data = {
        "messages": [{"role": "user", "content": "Hi"}],
        "max_tokens": 5,
        "temperature": 0.1
    }
    
    try:
        start_time = time.time()
        response = requests.post(url, headers=headers, json=data, params=params, timeout=10)
        end_time = time.time()
        
        rate_headers = {}
        for header, value in response.headers.items():
            if 'ratelimit' in header.lower():
                rate_headers[header] = value
        
        return {
            'id': request_id,
            'status': response.status_code,
            'duration': end_time - start_time,
            'rate_headers': rate_headers,
            'timestamp': datetime.now().isoformat(),
            'error_data': response.json() if response.status_code == 429 else None
        }
        
    except Exception as e:
        return {
            'id': request_id,
            'status': 'ERROR',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }

def test_concurrent_rate_limit(model_name="gpt-4.1-mini", max_workers=20, total_requests=70):
    """Make many concurrent requests to trigger rate limit"""
    print(f"Testing rate limit with {max_workers} concurrent workers")
    print(f"Making {total_requests} total requests to {model_name}")
    print(f"Rate limit should be 60 requests per minute")
    print("-" * 60)
    
    results = []
    successful = 0
    rate_limited = 0
    errors = 0
    
    start_time = time.time()
    
    # Use ThreadPoolExecutor for concurrent requests
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all requests at once
        futures = {executor.submit(make_single_request, model_name, i+1): i+1 
                  for i in range(total_requests)}
        
        # Process results as they complete
        for future in as_completed(futures):
            try:
                result = future.result()
                results.append(result)
                
                request_id = result['id']
                status = result['status']
                
                if status == 200:
                    successful += 1
                    remaining = result['rate_headers'].get('x-ratelimit-remaining', 'N/A')
                    if request_id <= 10 or successful % 10 == 0:  # Show first 10, then every 10th
                        print(f"Request {request_id:2d}: SUCCESS (remaining: {remaining})")
                elif status == 429:
                    rate_limited += 1
                    if rate_limited == 1:
                        print(f"Request {request_id:2d}: RATE LIMITED (429) - First hit!")
                        if result['error_data']:
                            print(f"  Error: {result['error_data'].get('message', 'No message')}")
                    elif rate_limited <= 5:
                        print(f"Request {request_id:2d}: RATE LIMITED (429)")
                else:
                    errors += 1
                    print(f"Request {request_id:2d}: {status}")
                    
            except Exception as e:
                errors += 1
                print(f"Request processing error: {e}")
    
    end_time = time.time()
    total_duration = end_time - start_time
    
    print("-" * 60)
    print(f"Test completed in {total_duration:.2f} seconds")
    print(f"Requests per second: {total_requests / total_duration:.2f}")
    print(f"Results:")
    print(f"  Successful (200): {successful}")
    print(f"  Rate limited (429): {rate_limited}")
    print(f"  Errors: {errors}")
    print(f"  Total: {len(results)}")
    
    # Analyze the first rate limit hit
    first_rate_limit = next((r for r in results if r['status'] == 429), None)
    if first_rate_limit:
        print(f"\nFirst rate limit details:")
        print(f"  Request ID: {first_rate_limit['id']}")
        print(f"  Timestamp: {first_rate_limit['timestamp']}")
        if first_rate_limit['error_data']:
            error_data = first_rate_limit['error_data']
            print(f"  Message: {error_data.get('message', 'N/A')}")
            print(f"  Retry After: {error_data.get('retryAfter', 'N/A')} seconds")
            print(f"  Limit: {error_data.get('limit', 'N/A')}")
            print(f"  Remaining: {error_data.get('remaining', 'N/A')}")
        
        return True  # Rate limiting worked
    else:
        print(f"\nNo rate limiting occurred!")
        print(f"This means either:")
        print(f"  1. The requests weren't fast enough")
        print(f"  2. The rate limit is higher than 60/minute") 
        print(f"  3. There's an issue with the rate limiting system")
        return False  # Rate limiting didn't work

def check_current_rate_limit_status(model_name="gpt-4.1-mini"):
    """Check current rate limit status for the model"""
    print(f"Checking current rate limit status for {model_name}...")
    
    # Make a single request to see current status
    result = make_single_request(model_name, 1)
    
    if result['status'] == 200:
        headers = result['rate_headers']
        print(f"Current status:")
        print(f"  Limit: {headers.get('x-ratelimit-limit', 'N/A')}")
        print(f"  Remaining: {headers.get('x-ratelimit-remaining', 'N/A')}")
        print(f"  Reset: {headers.get('x-ratelimit-reset', 'N/A')}")
        
        remaining = int(headers.get('x-ratelimit-remaining', 60))
        return remaining
    else:
        print(f"Failed to get status: {result}")
        return 60

def main():
    print("=" * 80)
    print("AGGRESSIVE RATE LIMITING TEST")
    print("=" * 80)
    
    model_name = "gpt-4.1-mini"
    
    # Check current status
    print("1. Checking current rate limit status...")
    remaining = check_current_rate_limit_status(model_name)
    
    print(f"\n2. Starting aggressive concurrent test...")
    print(f"We need to make more than {remaining} requests very quickly")
    
    # Calculate how many requests to make
    requests_needed = max(70, remaining + 10)  # At least 10 more than remaining
    
    # Test with high concurrency
    rate_limit_triggered = test_concurrent_rate_limit(
        model_name=model_name,
        max_workers=30,  # High concurrency
        total_requests=requests_needed
    )
    
    print(f"\n3. Final result:")
    if rate_limit_triggered:
        print("SUCCESS: Rate limiting is working correctly!")
        print("The system properly blocks requests with 429 when limit is exceeded.")
    else:
        print("ISSUE: Rate limiting may not be working as expected.")
        print("Consider checking Redis connection or rate limit configuration.")

if __name__ == "__main__":
    main()