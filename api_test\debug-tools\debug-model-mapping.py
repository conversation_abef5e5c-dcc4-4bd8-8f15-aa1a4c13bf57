import requests
import json

# Configuration
BASE_URL = "http://localhost:3003/api/v0"
API_KEY = "80b60025-07b4-4097-ba1d-e14695359dd0"

headers = {
    'Content-Type': 'application/json',
    'api-key': API_KEY
}

def get_all_usage():
    """Get all token usage data"""
    url = f"{BASE_URL}/rest/rate-limit/token-usage"
    response = requests.get(url, headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        return data.get('usage', [])
    return []

def test_specific_model(deployment_name, expected_model_name):
    """Test a specific model and track where it gets recorded"""
    print(f"\nTesting: {deployment_name}")
    print(f"Expected to record under: {expected_model_name}")
    
    # Get initial usage for all models
    initial_usage = get_all_usage()
    initial_dict = {u['modelName']: u['totalTokensUsed'] for u in initial_usage}
    
    print(f"Initial state for {expected_model_name}: {initial_dict.get(expected_model_name, 'Not found')} tokens")
    
    # Make API call
    url = f"{BASE_URL}/rest/deployments/{deployment_name}/chat/completions"
    data = {
        "messages": [
            {"role": "system", "content": "You are helpful."},
            {"role": "user", "content": f"Say 'hello from {deployment_name}' in 3 words."}
        ],
        "max_tokens": 10
    }
    
    response = requests.post(url, headers=headers, json=data, params={"api-version": "2024-02-01"})
    
    if response.status_code == 200:
        result = response.json()
        content = result['choices'][0]['message']['content']
        usage = result.get('usage', {})
        api_tokens = usage.get('total_tokens', 0)
        print(f"API call successful: {content}")
        print(f"API reported tokens: {api_tokens}")
        
        # Wait for update
        import time
        time.sleep(5)
        
        # Check all models for changes
        updated_usage = get_all_usage()
        updated_dict = {u['modelName']: u['totalTokensUsed'] for u in updated_usage}
        
        print(f"Changes detected:")
        changes_found = False
        for model_name, updated_tokens in updated_dict.items():
            initial_tokens = initial_dict.get(model_name, 0)
            if updated_tokens > initial_tokens:
                difference = updated_tokens - initial_tokens
                print(f"  {model_name}: +{difference} tokens (was {initial_tokens}, now {updated_tokens})")
                changes_found = True
        
        if not changes_found:
            print("  No changes detected in any model!")
        
        return api_tokens
    else:
        print(f"API call failed: {response.status_code} - {response.text}")
        return 0

def main():
    print("MODEL MAPPING DEBUG TEST")
    print("=" * 50)
    
    # Test cases
    test_cases = [
        ("qwen-plus", "qwen-plus"),
        ("qwen-max-2025-01-25", "qwen-max"),
        ("llama-4-maverick-17b-128e-instruct-maas", "llama-4-maverick")
    ]
    
    for deployment_name, expected_model in test_cases:
        test_specific_model(deployment_name, expected_model)
    
    print("\n" + "=" * 50)
    print("SUMMARY")
    print("If tokens are being recorded under different model names than expected,")
    print("this indicates the model mapping or canonical name resolution needs adjustment.")

if __name__ == "__main__":
    main()