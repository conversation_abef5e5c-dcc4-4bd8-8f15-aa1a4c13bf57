// This file primarily defines common interfaces for LLM services.
// The actual placeholder service class has been removed as specific
// implementations (Azure, Vertex, Anthropic) are now used.

import { Readable } from 'stream';

// Interface for common LLM streaming call options
export interface LlmStreamOptions {
  messages: any[]; // Processed messages for the LLM
  temperature?: number;
  maxTokens?: number; // Max completion tokens (if applicable)
  modelConfig: any; // The full model record from model_list table
  user: { userId: string; type: string; email: string; dept_unit_code: string }; // Include necessary user details
  conversationId: number; // DB ID
  dialogId: string; // UUID for request/response pair
  userMessageId: number; // DB ID of the user's message
  attachments?: any; // Optional attachments
  useGoogle?: boolean; // New: Flag to indicate if Google Search should be used
  searchSources?: any[]; // New: Search sources to be saved with the AI response
  requestedModelName?: string; // Original model name requested by user for consistent display/storage
}

// Potentially define a common interface for LLM services if needed for polymorphism,
// though direct injection and type-checking might be sufficient.
// export interface ILlmService {
//   createChatCompletionStream(options: LlmStreamOptions): Promise<Readable>;
// }
