#!/usr/bin/env python3
"""
Test script for rate limiting functionality
Tests that the API correctly enforces rate limits and returns 429 status codes
when the limit is exceeded.
"""

import requests
import json
import os
import time
import asyncio
from concurrent.futures import Thread<PERSON>oolExecutor
from dotenv import load_dotenv
from typing import Dict, List, Any, Optional

# Load environment variables
load_dotenv()

class RateLimitTester:
    def __init__(self):
        self.base_url = os.getenv('BASE_URL', 'http://localhost:3003/api/v0')
        self.api_key = os.getenv('API_KEY')
        
        if not self.api_key:
            raise ValueError("API_KEY not found in .env file")
        
        self.headers = {
            'Content-Type': 'application/json',
            'api-key': self.api_key
        }
        
        # Test model and endpoints
        self.test_model = 'gpt-4.1'  # Using a common model for testing
        self.chat_url = f"{self.base_url}/rest/deployments/{self.test_model}/chat/completions"
        self.rate_limit_url = f"{self.base_url}/rest/rate-limit/usage"
        
        # Test payload for chat completion
        self.test_payload = {
            "messages": [
                {
                    "role": "user",
                    "content": "Hi"
                }
            ],
            "max_tokens": 10,
            "temperature": 0.1,
            "stream": False
        }
        
        # Rate limit configuration (from the source code)
        self.rate_limit_per_model = 60  # 60 requests per minute
        self.ttl_seconds = 60  # 1 minute
    
    def make_chat_request(self) -> requests.Response:
        """Make a single chat completion request"""
        params = {"api-version": "2024-02-01"}
        return requests.post(
            self.chat_url,
            headers=self.headers,
            json=self.test_payload,
            params=params
        )
    
    def get_rate_limit_status(self) -> Dict[str, Any]:
        """Get current rate limit status"""
        response = requests.get(self.rate_limit_url, headers=self.headers)
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"Failed to get rate limit status: {response.status_code}")
    
    def test_rate_limit_exceeded(self) -> Dict[str, Any]:
        """Test that rate limiting triggers 429 status when exceeded"""
        print(f"\n🔥 Testing rate limit exceeded (making {self.rate_limit_per_model + 1} requests)")
        print(f"Target endpoint: {self.chat_url}")
        
        successful_requests = 0
        rate_limited_responses = []
        first_rate_limit_response = None
        
        # Make requests rapidly to trigger rate limit
        print("Making requests as fast as possible...")
        
        # Use ThreadPoolExecutor for concurrent requests
        with ThreadPoolExecutor(max_workers=10) as executor:
            # Submit all requests concurrently
            futures = []
            for i in range(self.rate_limit_per_model + 5):
                future = executor.submit(self.make_chat_request)
                futures.append((i + 1, future))
            
            # Process results as they complete
            for request_num, future in futures:
                try:
                    response = future.result(timeout=30)
                    
                    if response.status_code == 200:
                        successful_requests += 1
                        print(f"Request {request_num}: ✅ Success (200)")
                    elif response.status_code == 429:
                        if first_rate_limit_response is None:
                            first_rate_limit_response = response
                            print(f"Request {request_num}: 🚫 Rate limited (429) - First rate limit hit!")
                        else:
                            print(f"Request {request_num}: 🚫 Rate limited (429)")
                        
                        rate_limited_responses.append({
                            'request_number': request_num,
                            'status_code': response.status_code,
                            'headers': dict(response.headers),
                            'response_data': response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text
                        })
                    else:
                        print(f"Request {request_num}: ❓ Unexpected status {response.status_code}")
                    
                except Exception as e:
                    print(f"Request {request_num}: ❌ Error: {e}")
        
        # Analyze results
        print(f"\n📊 Test Results:")
        print(f"   Successful requests: {successful_requests}")
        print(f"   Rate limited responses: {len(rate_limited_responses)}")
        
        # Validate that rate limiting occurred
        if len(rate_limited_responses) == 0:
            return {
                'success': False,
                'error': 'No rate limiting occurred - expected 429 responses',
                'successful_requests': successful_requests,
                'rate_limited_responses': len(rate_limited_responses)
            }
        
        # Validate the first rate limit response
        if first_rate_limit_response:
            self.validate_rate_limit_response(first_rate_limit_response)
        
        return {
            'success': True,
            'successful_requests': successful_requests,
            'rate_limited_responses': len(rate_limited_responses),
            'first_rate_limit_response': rate_limited_responses[0] if rate_limited_responses else None,
            'rate_limit_triggered_at_request': rate_limited_responses[0]['request_number'] if rate_limited_responses else None
        }
    
    def validate_rate_limit_response(self, response: requests.Response) -> None:
        """Validate that a 429 response has the correct format and headers"""
        print(f"\n🔍 Validating rate limit response...")
        
        # Check status code
        assert response.status_code == 429, f"Expected 429, got {response.status_code}"
        print("✅ Status code 429 confirmed")
        
        # Check response format
        try:
            data = response.json()
            
            # Validate required fields in response
            required_fields = ['statusCode', 'message', 'error']
            for field in required_fields:
                assert field in data, f"Missing required field: {field}"
            
            # Validate specific values
            assert data['statusCode'] == 429, f"Expected statusCode 429, got {data['statusCode']}"
            assert data['error'] == 'Too Many Requests', f"Expected error 'Too Many Requests', got '{data['error']}'"
            assert 'Rate limit exceeded' in data['message'], f"Expected rate limit message, got '{data['message']}'"
            
            print("✅ Response format validated")
            print(f"   Message: {data['message']}")
            
            # Check for optional rate limit info
            if 'limit' in data:
                print(f"   Limit: {data['limit']}")
            if 'remaining' in data:
                print(f"   Remaining: {data['remaining']}")
            if 'reset' in data:
                print(f"   Reset at: {data['reset']}")
                
        except json.JSONDecodeError:
            raise AssertionError("Response is not valid JSON")
        
        # Check rate limit headers
        headers = response.headers
        rate_limit_headers = [
            'X-RateLimit-Limit',
            'X-RateLimit-Remaining', 
            'X-RateLimit-Reset'
        ]
        
        print(f"\n🔍 Checking rate limit headers...")
        for header in rate_limit_headers:
            if header in headers:
                print(f"✅ {header}: {headers[header]}")
            else:
                print(f"⚠️  Missing header: {header}")
    
    def test_rate_limit_headers(self) -> Dict[str, Any]:
        """Test that rate limit headers are included in successful responses"""
        print(f"\n📊 Testing rate limit headers in successful responses")
        
        response = self.make_chat_request()
        
        if response.status_code != 200:
            return {
                'success': False,
                'error': f"Expected 200 response, got {response.status_code}",
                'status_code': response.status_code
            }
        
        headers = response.headers
        expected_headers = ['X-RateLimit-Limit', 'X-RateLimit-Remaining', 'X-RateLimit-Reset']
        found_headers = {}
        missing_headers = []
        
        for header in expected_headers:
            if header in headers:
                found_headers[header] = headers[header]
                print(f"✅ {header}: {headers[header]}")
            else:
                missing_headers.append(header)
                print(f"❌ Missing: {header}")
        
        return {
            'success': len(missing_headers) == 0,
            'found_headers': found_headers,
            'missing_headers': missing_headers,
            'all_headers': dict(headers)
        }
    
    def test_rate_limit_reset(self) -> Dict[str, Any]:
        """Test that rate limits reset after the TTL period"""
        print(f"\n⏰ Testing rate limit reset after {self.ttl_seconds} seconds")
        
        # First, trigger rate limit
        print("Step 1: Triggering rate limit...")
        for i in range(self.rate_limit_per_model + 1):
            response = self.make_chat_request()
            if response.status_code == 429:
                print(f"✅ Rate limit triggered at request {i+1}")
                break
        else:
            return {
                'success': False,
                'error': 'Could not trigger rate limit in initial phase'
            }
        
        # Verify we're still rate limited
        print("Step 2: Verifying rate limit is active...")
        response = self.make_chat_request()
        if response.status_code != 429:
            return {
                'success': False,
                'error': f'Expected 429, got {response.status_code} - rate limit not active'
            }
        print("✅ Rate limit confirmed active")
        
        # Wait for reset (TTL + buffer)
        wait_time = self.ttl_seconds + 5  # Add 5 second buffer
        print(f"Step 3: Waiting {wait_time} seconds for rate limit to reset...")
        
        for remaining in range(wait_time, 0, -5):
            print(f"   Waiting... {remaining} seconds remaining")
            time.sleep(5)
        
        # Test that rate limit has reset
        print("Step 4: Testing if rate limit has reset...")
        response = self.make_chat_request()
        
        if response.status_code == 200:
            print("✅ Rate limit successfully reset - request succeeded")
            return {
                'success': True,
                'reset_confirmed': True,
                'wait_time': wait_time
            }
        else:
            return {
                'success': False,
                'error': f'Rate limit did not reset - got status {response.status_code}',
                'wait_time': wait_time
            }
    
    def test_multi_model_isolation(self) -> Dict[str, Any]:
        """Test that rate limits are isolated per model"""
        print(f"\n🔀 Testing multi-model rate limit isolation")
        
        # Test with two different models
        model1 = 'gpt-4.1'
        model2 = 'gpt-35-turbo'  # Using a different common model
        
        model1_url = f"{self.base_url}/rest/deployments/{model1}/chat/completions"
        model2_url = f"{self.base_url}/rest/deployments/{model2}/chat/completions"
        
        params = {"api-version": "2024-02-01"}
        
        print(f"Testing model isolation between {model1} and {model2}")
        
        # Test that both models work initially
        print("Step 1: Testing both models work...")
        response1 = requests.post(model1_url, headers=self.headers, json=self.test_payload, params=params)
        response2 = requests.post(model2_url, headers=self.headers, json=self.test_payload, params=params)
        
        if response1.status_code != 200:
            return {
                'success': False,
                'error': f'Model {model1} not working: {response1.status_code}'
            }
        
        if response2.status_code != 200:
            return {
                'success': False,
                'error': f'Model {model2} not working: {response2.status_code}'
            }
        
        print(f"✅ Both models working - {model1}: {response1.status_code}, {model2}: {response2.status_code}")
        
        # Exhaust rate limit for model1 only
        print(f"Step 2: Exhausting rate limit for {model1}...")
        rate_limited = False
        
        for i in range(self.rate_limit_per_model + 2):
            response = requests.post(model1_url, headers=self.headers, json=self.test_payload, params=params)
            if response.status_code == 429:
                print(f"✅ {model1} rate limited at request {i+1}")
                rate_limited = True
                break
        
        if not rate_limited:
            return {
                'success': False,
                'error': f'Could not trigger rate limit for {model1}'
            }
        
        # Verify model1 is rate limited but model2 still works
        print(f"Step 3: Verifying {model1} is rate limited but {model2} still works...")
        
        response1_limited = requests.post(model1_url, headers=self.headers, json=self.test_payload, params=params)
        response2_working = requests.post(model2_url, headers=self.headers, json=self.test_payload, params=params)
        
        if response1_limited.status_code != 429:
            return {
                'success': False,
                'error': f'Expected {model1} to be rate limited (429), got {response1_limited.status_code}'
            }
        
        if response2_working.status_code != 200:
            return {
                'success': False,
                'error': f'Expected {model2} to still work (200), got {response2_working.status_code}'
            }
        
        print(f"✅ Isolation confirmed - {model1}: 429 (rate limited), {model2}: 200 (working)")
        
        return {
            'success': True,
            'model1': model1,
            'model2': model2,
            'model1_rate_limited': True,
            'model2_working': True
        }
    
    def test_rate_limit_status_endpoint(self) -> Dict[str, Any]:
        """Test the rate limit status endpoint"""
        print(f"\n📈 Testing rate limit status endpoint")
        print(f"Endpoint: {self.rate_limit_url}")
        
        try:
            # Get initial status
            status_data = self.get_rate_limit_status()
            
            print("✅ Rate limit status endpoint working")
            print(f"   Found {len(status_data.get('usage', []))} model entries")
            
            # Look for our test model
            test_model_status = None
            for model_usage in status_data.get('usage', []):
                if model_usage.get('model') == self.test_model:
                    test_model_status = model_usage
                    break
            
            if test_model_status:
                print(f"✅ Found status for {self.test_model}:")
                print(f"   Limit: {test_model_status.get('limit')}")
                print(f"   Used: {test_model_status.get('used')}")
                print(f"   Remaining: {test_model_status.get('remaining')}")
                print(f"   Reset at: {test_model_status.get('resetAt')}")
                
                return {
                    'success': True,
                    'total_models': len(status_data.get('usage', [])),
                    'test_model_found': True,
                    'test_model_status': test_model_status
                }
            else:
                print(f"⚠️  Status for {self.test_model} not found")
                return {
                    'success': True,
                    'total_models': len(status_data.get('usage', [])),
                    'test_model_found': False,
                    'available_models': [m.get('model') for m in status_data.get('usage', [])]
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def test_user_info_and_redis_keys(self) -> Dict[str, Any]:
        """Test to debug user info and Redis key format"""
        print(f"\n🔍 Debugging user authentication and Redis keys")
        
        # First, make a simple request to understand the auth flow
        print("\n1. Making authenticated request to get user info...")
        
        # Try to get user info from a simple endpoint
        try:
            # Make a request that should trigger rate limiting
            response = self.make_chat_request()
            
            # Check response headers for any user info
            print("\n2. Response headers that might contain user info:")
            for header, value in response.headers.items():
                if 'user' in header.lower() or 'auth' in header.lower():
                    print(f"   {header}: {value}")
            
            # Get rate limit status to see what user ID is being used
            print("\n3. Checking rate limit status to understand user ID format...")
            status_data = self.get_rate_limit_status()
            
            # Make multiple requests to ensure we have some usage
            print("\n4. Making 5 requests to ensure usage is tracked...")
            for i in range(5):
                self.make_chat_request()
            
            # Get status again
            print("\n5. Checking rate limit status after requests...")
            status_data_after = self.get_rate_limit_status()
            
            # Find any model with usage > 0
            models_with_usage = []
            for model_usage in status_data_after.get('usage', []):
                if model_usage.get('used', 0) > 0:
                    models_with_usage.append(model_usage)
                    print(f"\n✅ Found usage for model: {model_usage.get('model')}")
                    print(f"   Used: {model_usage.get('used')}")
                    print(f"   Remaining: {model_usage.get('remaining')}")
            
            # Debug: Check if our test model has any usage
            test_model_usage = None
            for model_usage in status_data_after.get('usage', []):
                if model_usage.get('model') == self.test_model:
                    test_model_usage = model_usage
                    break
            
            if test_model_usage:
                print(f"\n6. Test model ({self.test_model}) usage:")
                print(f"   Used: {test_model_usage.get('used')}")
                print(f"   Expected Redis key format: rate_limit:{self.test_model}:<user_id>")
            else:
                print(f"\n⚠️  No usage found for test model: {self.test_model}")
            
            return {
                'success': True,
                'models_with_usage': len(models_with_usage),
                'test_model_usage': test_model_usage,
                'total_models_checked': len(status_data_after.get('usage', []))
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }


def main():
    """Main test function"""
    print("=" * 80)
    print("🚦 RATE LIMITING TESTS")
    print("=" * 80)
    
    try:
        tester = RateLimitTester()
        all_results = {}
        
        print(f"Testing against: {tester.base_url}")
        print(f"Target model: {tester.test_model}")
        print(f"Rate limit: {tester.rate_limit_per_model} requests per minute")
        
        # Test 0: Debug user info and Redis keys
        print("\n" + "="*50)
        print("TEST 0: Debug User Info and Redis Keys")
        print("="*50)
        try:
            result = tester.test_user_info_and_redis_keys()
            all_results['debug_test'] = result
            if result['success']:
                print("✅ Debug test completed")
                print(f"   Models with usage: {result['models_with_usage']}")
                print(f"   Total models checked: {result['total_models_checked']}")
            else:
                print("❌ Debug test failed")
                print(f"   Error: {result['error']}")
        except Exception as e:
            print(f"❌ ERROR in debug test: {e}")
            all_results['debug_test'] = {'success': False, 'error': str(e)}
        
        # Test 1: Rate limit headers in normal responses
        print("\n" + "="*50)
        print("TEST 1: Rate Limit Headers")
        print("="*50)
        try:
            result = tester.test_rate_limit_headers()
            all_results['headers_test'] = result
            if result['success']:
                print("✅ PASSED: Rate limit headers test")
            else:
                print("❌ FAILED: Rate limit headers test")
                print(f"   Missing headers: {result['missing_headers']}")
        except Exception as e:
            print(f"❌ ERROR in headers test: {e}")
            all_results['headers_test'] = {'success': False, 'error': str(e)}
        
        # Test 2: Rate limit status endpoint
        print("\n" + "="*50)
        print("TEST 2: Rate Limit Status Endpoint")
        print("="*50)
        try:
            result = tester.test_rate_limit_status_endpoint()
            all_results['status_endpoint_test'] = result
            if result['success']:
                print("✅ PASSED: Rate limit status endpoint test")
            else:
                print("❌ FAILED: Rate limit status endpoint test")
        except Exception as e:
            print(f"❌ ERROR in status endpoint test: {e}")
            all_results['status_endpoint_test'] = {'success': False, 'error': str(e)}
        
        # Test 3: Rate limit exceeded (main test)
        print("\n" + "="*50)
        print("TEST 3: Rate Limit Exceeded (MAIN TEST)")
        print("="*50)
        try:
            result = tester.test_rate_limit_exceeded()
            all_results['rate_limit_exceeded_test'] = result
            if result['success']:
                print("✅ PASSED: Rate limit exceeded test")
                print(f"   Rate limit triggered at request: {result['rate_limit_triggered_at_request']}")
                print(f"   Total successful: {result['successful_requests']}")
                print(f"   Total rate limited: {result['rate_limited_responses']}")
            else:
                print("❌ FAILED: Rate limit exceeded test")
                print(f"   Error: {result['error']}")
        except Exception as e:
            print(f"❌ ERROR in rate limit exceeded test: {e}")
            all_results['rate_limit_exceeded_test'] = {'success': False, 'error': str(e)}
        
        # Test 4: Multi-model isolation
        print("\n" + "="*50)
        print("TEST 4: Multi-Model Isolation")
        print("="*50)
        try:
            result = tester.test_multi_model_isolation()
            all_results['multi_model_test'] = result
            if result['success']:
                print("✅ PASSED: Multi-model isolation test")
            else:
                print("❌ FAILED: Multi-model isolation test")
                print(f"   Error: {result['error']}")
        except Exception as e:
            print(f"❌ ERROR in multi-model test: {e}")
            all_results['multi_model_test'] = {'success': False, 'error': str(e)}
        
        # Test 5: Rate limit reset (optional, takes time)
        if input("\n🕐 Run rate limit reset test? This takes ~65 seconds (y/N): ").lower().startswith('y'):
            print("\n" + "="*50)
            print("TEST 5: Rate Limit Reset")
            print("="*50)
            try:
                result = tester.test_rate_limit_reset()
                all_results['rate_limit_reset_test'] = result
                if result['success']:
                    print("✅ PASSED: Rate limit reset test")
                else:
                    print("❌ FAILED: Rate limit reset test")
                    print(f"   Error: {result['error']}")
            except Exception as e:
                print(f"❌ ERROR in rate limit reset test: {e}")
                all_results['rate_limit_reset_test'] = {'success': False, 'error': str(e)}
        else:
            print("⏭️  Skipping rate limit reset test")
        
        # Summary
        print("\n" + "="*80)
        print("📋 TEST SUMMARY")
        print("="*80)
        
        passed_tests = sum(1 for result in all_results.values() if result.get('success', False))
        total_tests = len(all_results)
        
        for test_name, result in all_results.items():
            status = "✅ PASS" if result.get('success', False) else "❌ FAIL"
            print(f"{status} {test_name}")
            if not result.get('success', False) and 'error' in result:
                print(f"      Error: {result['error']}")
        
        print(f"\nOverall: {passed_tests}/{total_tests} tests passed")
        
        if passed_tests == total_tests:
            print("🎉 ALL TESTS PASSED! Rate limiting is working correctly.")
            return 0
        else:
            print("⚠️  Some tests failed. Please review the results above.")
            return 1
            
    except Exception as e:
        print(f"❌ Test setup failed: {e}")
        return 1


if __name__ == "__main__":
    exit(main())