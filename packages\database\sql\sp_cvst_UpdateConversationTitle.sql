-- Stored Procedure to update the encrypted conversation title
-- =============================================
-- Author:      <PERSON>
-- Create date: 2025-04-16
-- Description: Encrypts the provided title and updates the conversation_title field for a given conversation_id.
-- =============================================
CREATE PROCEDURE [dbo].[sp_cvst_UpdateConversationTitle] -- Use ALTER if it might exist, CREATE if definitely new
    @conversation_id INT,
    @title NVARCHAR(255),
    @encryption_key_name NVARCHAR(128)
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @encrypted_title VARBINARY(MAX);
    DECLARE @sql NVARCHAR(MAX);

    -- Check if the key exists and is open
    IF NOT EXISTS (SELECT 1 FROM sys.openkeys WHERE key_name = @encryption_key_name)
    BEGIN
        -- Construct the dynamic SQL statement to open the key
        -- Use QUOTENAME for safety, although key names usually don't need it
        SET @sql = N'OPEN SYMMETRIC KEY ' + QUOTENAME(@encryption_key_name) + N' DECRYPTION BY CERTIFICATE DB_DECRYPTION_CERT;'; -- Assuming cert name is fixed

        BEGIN TRY
            -- Execute the dynamic SQL
            EXEC sp_executesql @sql;
        END TRY
        BEGIN CATCH
             RAISERROR('Failed to execute OPEN SYMMETRIC KEY statement for key [%s]. Title encryption failed.', 16, 1, @encryption_key_name);
             RETURN;
        END CATCH

        -- Check again if opening succeeded
        IF NOT EXISTS (SELECT 1 FROM sys.openkeys WHERE key_name = @encryption_key_name)
        BEGIN
            RAISERROR('Could not open symmetric key [%s] after attempting. Title encryption failed.', 16, 1, @encryption_key_name);
            RETURN; -- Exit if key cannot be opened
        END
    END

    -- Encrypt the title using the symmetric key
    -- Ensure the key is open before encrypting
    BEGIN TRY
        SET @encrypted_title = ENCRYPTBYKEY(KEY_GUID(@encryption_key_name), @title);
    END TRY
    BEGIN CATCH
        RAISERROR('Failed to encrypt title using key [%s].', 16, 1, @encryption_key_name);
        -- Consider closing the key if opened dynamically, though error handling can be complex
        RETURN;
    END CATCH

    -- Check if encryption produced a result
    IF @encrypted_title IS NULL
    BEGIN
        RAISERROR('Encryption resulted in NULL value for key [%s]. Title update aborted.', 16, 1, @encryption_key_name);
        RETURN;
    END

    -- Update the conversation table
    UPDATE [dbo].[conversation]
    SET
        [conversation_title] = @encrypted_title,
        [update_dt] = GETDATE(), -- Optionally update the update timestamp
        [update_by] = 'SYSTEM' -- Or pass the actual user/process if available
    WHERE
        [conversation_id] = @conversation_id;

    -- Optional: Check @@ROWCOUNT to see if the update was successful
    IF @@ROWCOUNT = 0
    BEGIN
        -- Log or handle the case where the conversation_id was not found
        PRINT 'Warning: Conversation ID ' + CAST(@conversation_id AS VARCHAR) + ' not found for title update.';
    END

    -- NOTE: Key Closing Management:
    -- Closing the key here if opened dynamically can be complex due to transaction/error states.
    -- It's often better to manage key opening/closing at a higher level (e.g., application connection scope)
    -- or ensure the key is opened once when needed and closed appropriately elsewhere.
    -- Leaving out explicit CLOSE here for simplicity, assuming broader management.

END
GO