import {
  Injectable,
  Logger,
  NotFoundException,
  OnModuleInit,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../prisma/prisma.service';
import { ModelMappingService } from '../common/services/model-mapping.service';
import { getModelNameVariants } from '../utils/model-name-normalization'; // Keep variants for database lookup only

// Interface for the LLM configuration returned by routing logic
export interface LlmConfig {
  provider:
    | 'azure'
    | 'google'
    | 'anthropic'
    | 'qwen'
    | 'gcp-vertexai-gemini'
    | 'gcp-vertexai-llama'
    | 'deepseek-aisvc'
    | 'unknown'; // Added specific vertex types
  apiKey?: string;
  // Provider-specific fields
  instanceName?: string; // e.g., Azure instance, potentially region for others
  deploymentName?: string; // e.g., Azure deployment ID
  modelName?: string; // Specific model for the provider (from DB)
  projectId?: string; // GCP Project ID
  region?: string; // GCP Region
  endpointUrl?: string; // Azure AI Service Endpoint URL
  apiVersion?: string; // Azure AI Service API Version / DB api_version
  modelType?: string; // From DB model_type
  supportsSystemMessages?: boolean; // Whether the model supports system messages
  supportsTemperature?: boolean; // Whether the model supports temperature parameter
}

@Injectable()
export class LlmConfigService implements OnModuleInit {
  private readonly logger = new Logger(LlmConfigService.name);
  private modelMap: Map<string, any> = new Map(); // Use 'any' type as workaround

  constructor(
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService, // Inject PrismaService
    private readonly modelMappingService: ModelMappingService, // Inject ModelMappingService
  ) {}

  async onModuleInit() {
    await this.loadModelData();
  }

  private async loadModelData() {
    try {
      this.logger.log('Loading model list data from database...');
      const models = await this.prisma.model_list.findMany({
        where: {
          OR: [
            // Regular models: require both rec_status and availability_status
            {
              rec_status: 'A',
              availability_status: 'A',
              model_type: { not: 'embedding' },
            },
            // Embedding models: only require api_status='A' (less strict)
            {
              api_status: 'A',
              model_type: 'embedding',
            },
          ],
        },
      });
      this.modelMap.clear();
      models.forEach((model) => {
        if (model.model_name) {
          // Use a unique key combining model_name and id to avoid overwrites
          const uniqueKey = `${model.model_name}_${model.id}`;
          this.logger.debug(`Adding model to map: Key='${uniqueKey}'`); // Log adding key
          this.modelMap.set(uniqueKey, model);
        } else {
          this.logger.warn(
            `Skipping model with null model_name: ID=${model.id}`,
          );
        }
      });
      this.logger.log(
        `Loaded ${this.modelMap.size} active models into config map. Keys: [${Array.from(this.modelMap.keys()).join(', ')}]`,
      ); // Log all loaded keys
    } catch (error) {
      this.logger.error('Failed to load model list data from database', error);
      // Decide how to handle this - throw error, use fallback, etc.
      // For now, log error and continue with potentially empty map
    }
  }

  /**
   * Determines if a model supports system messages based on model name and database configuration.
   * @param modelName - The model name to check
   * @param dbModelConfig - The database model configuration
   * @returns boolean indicating system message support
   */
  private supportsSystemMessages(
    modelName: string,
    dbModelConfig: any,
  ): boolean {
    // Check database flag first if available
    if (dbModelConfig?.supports_system_messages !== undefined) {
      return dbModelConfig.supports_system_messages;
    }

    // Fallback to model name pattern matching for known models that don't support system messages
    const modelNameLower = modelName.toLowerCase();

    // OpenAI o1 and o3-mini models don't support system messages
    const unsupportedPatterns = [
      /^o1-mini$/, // o1-mini
      /^o1$/, // o1
      /^o3-mini$/, // o3-mini
    ];

    const isUnsupported = unsupportedPatterns.some((pattern) =>
      pattern.test(modelNameLower),
    );

    if (isUnsupported) {
      this.logger.debug(
        `Model ${modelName} detected as not supporting system messages based on name pattern`,
      );
      return false;
    }

    // Default to true for all other models
    return true;
  }

  /**
   * Determines if a model supports temperature parameter based on model name and database configuration.
   * @param modelName - The model name to check
   * @param dbModelConfig - The database model configuration
   * @returns boolean indicating temperature parameter support
   */
  private supportsTemperature(
    modelName: string,
    dbModelConfig: any,
  ): boolean {
    // Check database flag first if available
    if (dbModelConfig?.supports_temperature !== undefined) {
      return dbModelConfig.supports_temperature;
    }

    // Fallback to model name pattern matching for known models that don't support temperature
    const modelNameLower = modelName.toLowerCase();

    // OpenAI o1 and o3 models don't support temperature parameter
    const unsupportedPatterns = [
      /^o1($|-)/, // o1, o1-mini, o1-preview, etc.
      /^o3($|-)/, // o3, o3-mini, etc.
    ];

    const isUnsupported = unsupportedPatterns.some((pattern) =>
      pattern.test(modelNameLower),
    );

    if (isUnsupported) {
      this.logger.debug(
        `Model ${modelName} detected as not supporting temperature parameter based on name pattern`,
      );
      return false;
    }

    // Default: assume model supports temperature
    return true;
  }

  /**
   * Determines the appropriate LLM provider, model, and credentials based on the requested model name.
   * Uses data loaded from the model_list table.
   * @param dept_unit_code - User's department code (used for Azure pool selection).
   * @param model_name - Requested model name (e.g., 'gpt-4', 'gemini-pro', 'claude-3-opus').
   * @returns LlmConfig object containing provider details and credentials.
   * @throws NotFoundException if configuration is missing or invalid.
   */
  async getLlmConfig(
    dept_unit_code: string,
    model_name: string,
  ): Promise<LlmConfig> {
    const getSecret = (key: string): string | undefined =>
      this.configService.get<string>(key);

    // Try to find model config from the loaded map, including variants
    this.logger.debug(
      `Attempting to get config for model_name: '${model_name}'`,
    ); // Log lookup attempt

    let dbModelConfig = this.modelMap.get(model_name);

    // If not found, try searching by name with ID suffix
    if (!dbModelConfig) {
      for (const [key, value] of this.modelMap.entries()) {
        if (key.startsWith(`${model_name}_`)) {
          dbModelConfig = value;
          this.logger.debug(
            `Found model config using key pattern: '${key}' for model: '${model_name}'`,
          );
          break;
        }
      }
    }

    // If still not found, try normalized and variant names
    if (!dbModelConfig) {
      // Get variants from both ModelMappingService (database-driven) and static utility
      const dynamicVariants =
        this.modelMappingService.getModelNameVariants(model_name);
      const staticVariants = getModelNameVariants(model_name);

      // Combine and deduplicate variants
      const allVariants = Array.from(
        new Set([...dynamicVariants, ...staticVariants]),
      );

      this.logger.debug(
        `Model '${model_name}' not found directly. Trying variants: [${allVariants.join(', ')}]`,
      );
      const variants = allVariants;

      for (const variant of variants) {
        // Try direct lookup first
        dbModelConfig = this.modelMap.get(variant);
        if (dbModelConfig) {
          this.logger.debug(`Found model config using variant: '${variant}'`);
          break;
        }

        // If not found, search for keys that start with the variant name followed by underscore
        // This handles the case where keys have ID suffixes like "gpt-4.1_111"
        for (const [key, value] of this.modelMap.entries()) {
          if (key.startsWith(`${variant}_`)) {
            dbModelConfig = value;
            this.logger.debug(
              `Found model config using key pattern: '${key}' for variant: '${variant}'`,
            );
            break;
          }
        }

        if (dbModelConfig) {
          break;
        }
      }
    }

    this.logger.debug(
      `Result from modelMap lookup for '${model_name}': ${dbModelConfig ? 'Found' : 'NotFound'}`,
    ); // Log lookup result

    if (!dbModelConfig) {
      // Log existing keys on failure for comparison
      this.logger.error(
        `Configuration for model '${model_name}' not found in database cache. Map keys: [${Array.from(this.modelMap.keys()).join(', ')}]`,
      );
      // Option: Attempt to reload cache?
      // await this.loadModelData();
      // dbModelConfig = this.modelMap.get(model_name);
      // if (!dbModelConfig) {
      throw new NotFoundException(
        `Configuration for model '${model_name}' is not available.`,
      );
      // }
    }

    let provider: LlmConfig['provider'] = 'unknown';
    let llmConfigResult: Partial<LlmConfig> = {};
    const modelType = dbModelConfig.model_type?.toLowerCase(); // Use model_type from DB

    // Determine provider based on model_type or name pattern
    if (
      modelType === 'azure_openai' ||
      model_name.includes('gpt') ||
      model_name === 'o1' ||
      model_name === 'o1-mini' ||
      model_name === 'o3-mini'
    ) {
      provider = 'azure';
    } else if (modelType === 'google_gemini' || model_name.includes('gemini')) {
      provider = 'gcp-vertexai-gemini'; // Specific provider for Gemini
    } else if (
      modelType === 'anthropic_claude' ||
      model_name.includes('claude')
    ) {
      provider = 'anthropic';
    } else if (modelType === 'deepseek' || model_name.includes('deepseek')) {
      provider = 'deepseek-aisvc';
    } else if (modelType === 'alibaba_qwen' || model_name.includes('qwen')) {
      provider = 'qwen';
    } else if (
      modelType === 'gcp_vertexai_llama' ||
      model_name.includes('llama')
    ) {
      // Use a specific type or name check for Llama
      provider = 'gcp-vertexai-llama'; // Specific provider for Llama
    } else if (modelType === 'embedding') {
      // Embedding models use Azure OpenAI for text-embedding models
      if (
        model_name.includes('text-embedding') ||
        model_name.includes('embedding')
      ) {
        provider = 'azure';
      }
    }
    // Add more provider mappings based on model_type as needed
    // Fallback check if type wasn't specific enough
    else if (modelType === 'gcp_vertexai') {
      if (model_name.includes('llama')) {
        provider = 'gcp-vertexai-llama';
      } else {
        // Assume Gemini if type is generic 'gcp_vertexai' and name doesn't indicate Llama
        provider = 'gcp-vertexai-gemini';
        this.logger.warn(
          `Generic model_type 'gcp_vertexai' found for ${model_name}, assuming Gemini. Consider using specific types like 'gcp_vertexai_llama' or 'google_gemini'.`,
        );
      }
    }

    // --- Provider-Specific Logic ---

    if (provider === 'azure') {
      // --- Azure Pool Logic (Retained, but could be simplified if DB holds more info) ---
      const isSceCie = dept_unit_code === 'SCE' || dept_unit_code === 'CIE';
      let api_env_pool: Partial<LlmConfig>[] = [];
      // Simplified example - ideally DB would guide this better
      if (isSceCie) {
        // SCE/CIE specific pool logic based on model_name (or dbModelConfig properties)
        if (
          model_name.includes('dalle') ||
          model_name.includes('gpt-4-turbo') ||
          model_name.includes('gpt-4-o') ||
          model_name === 'gpt-4-o-mini' ||
          model_name.includes('o1')
        ) {
          api_env_pool = [
            {
              instanceName: getSecret(
                'GENERAL_SCECIE_AZURE_OPENAI_API_INSTANCE_NAME_SWC',
              ),
              apiKey: getSecret('GENERAL_SCECIE_AZURE_OPENAI_API_KEY_SWC'),
            },
          ];
          if (model_name.includes('o1')) {
            api_env_pool.push({
              instanceName: getSecret(
                'GENERAL_SCECIE_AZURE_OPENAI_API_INSTANCE_NAME_USSOUTH',
              ),
              apiKey: getSecret('GENERAL_SCECIE_AZURE_OPENAI_API_KEY_USSOUTH'),
            });
          }
        } else if (model_name.includes('o3-mini')) {
          api_env_pool = [
            {
              instanceName: getSecret(
                'GENERAL_SCECIE_AZURE_OPENAI_API_INSTANCE_NAME_SWN',
              ),
              apiKey: getSecret('GENERAL_SCECIE_AZURE_OPENAI_API_KEY_SWN'),
            },
          ];
        } else {
          // Default GPT 3.5 for SCE/CIE
          api_env_pool = [
            {
              instanceName: getSecret(
                'GENERAL_SCECIE_AZURE_OPENAI_API_INSTANCE_NAME_USSOUTH',
              ),
              apiKey: getSecret('GENERAL_SCECIE_AZURE_OPENAI_API_KEY_USSOUTH'),
            },
          ];
        }
      } else {
        // General user pool logic based on model_name (or dbModelConfig properties)
        if (model_name.includes('dalle')) {
          api_env_pool = [
            {
              instanceName: getSecret(
                'GENERAL_AZURE_OPENAI_API_INSTANCE_NAME_AU',
              ),
              apiKey: getSecret('GENERAL_AZURE_OPENAI_API_KEY_AU'),
            },
            {
              instanceName: getSecret(
                'GENERAL_AZURE_OPENAI_API_INSTANCE_NAME_USEAST',
              ),
              apiKey: getSecret('GENERAL_AZURE_OPENAI_API_KEY_USEAST'),
            },
          ];
        } else if (model_name.includes('gpt-4-vision')) {
          api_env_pool = [
            {
              instanceName: getSecret(
                'GENERAL_SCECIE_AZURE_OPENAI_API_INSTANCE_NAME_SWC',
              ),
              apiKey: getSecret('GENERAL_SCECIE_AZURE_OPENAI_API_KEY_SWC'),
            },
          ];
        } else if (model_name.includes('text-embedding')) {
          api_env_pool = [
            {
              instanceName: getSecret(
                'GENERAL_AZURE_OPENAI_API_INSTANCE_NAME_CA',
              ),
              apiKey: getSecret('GENERAL_AZURE_OPENAI_API_KEY_CA'),
            },
          ];
        } else if (model_name.includes('gpt-4-turbo')) {
          api_env_pool = [
            {
              instanceName: getSecret(
                'GENERAL_AZURE_OPENAI_API_INSTANCE_NAME_AU',
              ),
              apiKey: getSecret('GENERAL_AZURE_OPENAI_API_KEY_AU'),
            },
            {
              instanceName: getSecret(
                'GENERAL_AZURE_OPENAI_API_INSTANCE_NAME_CA',
              ),
              apiKey: getSecret('GENERAL_AZURE_OPENAI_API_KEY_CA'),
            },
            {
              instanceName: getSecret(
                'GENERAL_AZURE_OPENAI_API_INSTANCE_NAME_UK',
              ),
              apiKey: getSecret('GENERAL_AZURE_OPENAI_API_KEY_UK'),
            },
          ];
        } else if (model_name === 'gpt-4-o') {
          api_env_pool = [
            {
              instanceName: getSecret(
                'GENERAL_AZURE_OPENAI_API_INSTANCE_NAME_USNORTH',
              ),
              apiKey: getSecret('GENERAL_AZURE_OPENAI_API_KEY_USNORTH'),
            },
            {
              instanceName: getSecret(
                'GENERAL_AZURE_OPENAI_API_INSTANCE_NAME_USEAST2',
              ),
              apiKey: getSecret('GENERAL_AZURE_OPENAI_API_KEY_USEAST2'),
            },
            {
              instanceName: getSecret(
                'GENERAL_AZURE_OPENAI_API_INSTANCE_NAME_USWEST',
              ),
              apiKey: getSecret('GENERAL_AZURE_OPENAI_API_KEY_USWEST'),
            },
            {
              instanceName: getSecret(
                'GENERAL_AZURE_OPENAI_API_INSTANCE_NAME_USWEST3',
              ),
              apiKey: getSecret('GENERAL_AZURE_OPENAI_API_KEY_USWEST3'),
            },
          ];
        } else if (model_name === 'gpt-4-o-mini') {
          api_env_pool = [
            {
              instanceName: getSecret(
                'GENERAL_AZURE_OPENAI_API_INSTANCE_NAME_USEAST',
              ),
              apiKey: getSecret('GENERAL_AZURE_OPENAI_API_KEY_USEAST'),
            },
          ];
        } else if (model_name.includes('o1')) {
          api_env_pool = [
            {
              instanceName: getSecret(
                'GENERAL_AZURE_OPENAI_API_INSTANCE_NAME_USEAST',
              ),
              apiKey: getSecret('GENERAL_AZURE_OPENAI_API_KEY_USEAST'),
            },
            {
              instanceName: getSecret(
                'GENERAL_AZURE_OPENAI_API_INSTANCE_NAME_USWEST',
              ),
              apiKey: getSecret('GENERAL_AZURE_OPENAI_API_KEY_USWEST'),
            },
            {
              instanceName: getSecret(
                'GENERAL_AZURE_OPENAI_API_INSTANCE_NAME_USEAST2',
              ),
              apiKey: getSecret('GENERAL_AZURE_OPENAI_API_KEY_USEAST2'),
            },
            {
              instanceName: getSecret(
                'GENERAL_AZURE_OPENAI_API_INSTANCE_NAME_USTEST',
              ),
              apiKey: getSecret('GENERAL_AZURE_OPENAI_API_KEY_USTEST'),
            },
          ];
        } else if (model_name.includes('o3-mini')) {
          api_env_pool = [
            {
              instanceName: getSecret(
                'GENERAL_AZURE_OPENAI_API_INSTANCE_NAME_USWEST',
              ),
              apiKey: getSecret('GENERAL_AZURE_OPENAI_API_KEY_USWEST'),
            },
            {
              instanceName: getSecret(
                'GENERAL_AZURE_OPENAI_API_INSTANCE_NAME_USEAST2',
              ),
              apiKey: getSecret('GENERAL_AZURE_OPENAI_API_KEY_USEAST2'),
            },
          ];
        } else if (model_name.includes('gpt-4.1')) {
          api_env_pool = [
            {
              instanceName: getSecret(
                'GENERAL_AZURE_OPENAI_API_INSTANCE_NAME_USEAST2',
              ),
              apiKey: getSecret('GENERAL_AZURE_OPENAI_API_KEY_USEAST2'),
            },
          ];
        } else {
          // Default GPT 3.5
          api_env_pool = [
            {
              instanceName: getSecret(
                'GENERAL_AZURE_OPENAI_API_INSTANCE_NAME_CA',
              ),
              apiKey: getSecret('GENERAL_AZURE_OPENAI_API_KEY_CA'),
            },
            {
              instanceName: getSecret(
                'GENERAL_AZURE_OPENAI_API_INSTANCE_NAME_USNORTH',
              ),
              apiKey: getSecret('GENERAL_AZURE_OPENAI_API_KEY_USNORTH'),
            },
          ];
        }
      }
      api_env_pool = api_env_pool.filter(
        (env) => env.instanceName && env.apiKey,
      );
      this.logger.debug(
        `Azure OpenAI pool for ${dept_unit_code} and model ${model_name}: ${JSON.stringify(api_env_pool)}`,
      ); // Log pool contents
      if (api_env_pool.length > 0) {
        const selected_env =
          api_env_pool[Math.floor(Math.random() * api_env_pool.length)];
        llmConfigResult = { ...selected_env };
      } else {
        this.logger.warn(
          `Azure OpenAI pool was empty for dept ${dept_unit_code} and model ${model_name}. Check config variables.`,
        );
      }
      // --- End Pool Logic ---

      llmConfigResult.deploymentName =
        dbModelConfig.deployment_name || model_name; // Use deployment_name from DB
      llmConfigResult.apiVersion =
        dbModelConfig.api_version ||
        getSecret('AZURE_OPENAI_API_VERSION') ||
        '2024-02-01'; // Use api_version from DB

      // Construct Azure OpenAI endpoint URL from instanceName
      if (!llmConfigResult.endpointUrl && llmConfigResult.instanceName) {
        llmConfigResult.endpointUrl = `https://${llmConfigResult.instanceName}.openai.azure.com`;
      }

      if (
        !llmConfigResult.instanceName ||
        !llmConfigResult.deploymentName ||
        !llmConfigResult.apiKey
      ) {
        throw new NotFoundException(
          `Incomplete Azure configuration resolved for model '${model_name}'. Missing instance, deployment, or key.`,
        );
      }
    } else if (provider === 'anthropic') {
      const apiKey = getSecret('ANTHROPIC_API_KEY');
      if (!apiKey)
        throw new NotFoundException(`API key missing for provider ${provider}`);
      llmConfigResult = { apiKey: apiKey };
    } else if (provider === 'deepseek-aisvc') {
      const isSceCieUser = dept_unit_code === 'SCE' || dept_unit_code === 'CIE';

      // Common endpoint and apiVersion for all DeepSeek instances via Azure AI Service
      // Model-specific endpoint/apiVersion can override this if AZURE_AI_SERVICE_DEEPSEEK_ENDPOINT/VERSION are set
      const commonEndpoint =
        getSecret('AZURE_AI_SERVICE_DEEPSEEK_ENDPOINT') ||
        getSecret('AZURE_AI_SERVICE_ENDPOINT');
      const commonApiVersion =
        getSecret('AZURE_AI_SERVICE_DEEPSEEK_API_VERSION') ||
        dbModelConfig.api_version ||
        getSecret('AZURE_AI_SERVICE_API_VERSION') ||
        '2024-05-01-preview';

      if (!commonEndpoint) {
        throw new NotFoundException(
          `Common Azure AI Service endpoint for DeepSeek models is not configured (AZURE_AI_SERVICE_DEEPSEEK_ENDPOINT or AZURE_AI_SERVICE_ENDPOINT).`,
        );
      }

      let selectedInstanceName: string | undefined;
      let selectedApiKey: string | undefined;

      if (isSceCieUser) {
        selectedInstanceName = getSecret(
          'GENERAL_SCECIE_DEEPSEEK_API_INSTANCE_NAME_USSOUTH',
        );
        selectedApiKey =
          getSecret('AZURE_AI_SERVICE_DEEPSEEK_KEY') ||
          getSecret('GENERAL_SCECIE_DEEPSEEK_API_KEY_USSOUTH') ||
          getSecret('AZURE_AI_SERVICE_KEY');
        if (!selectedInstanceName || !selectedApiKey) {
          this.logger.error(
            `SCE/CIE DeepSeek configuration missing: INSTANCE_NAME_USSOUTH or KEY_USSOUTH for model '${model_name}'`,
          );
          throw new NotFoundException(
            `Configuration for SCE/CIE DeepSeek model '${model_name}' in USSOUTH is incomplete.`,
          );
        }
        this.logger.debug(
          `SCE/CIE user routed to DeepSeek instance: ${selectedInstanceName}`,
        );
      } else {
        this.logger.debug(
          `DeepSeek routing for General user: ${dept_unit_code}`,
        );
        const general_deepseek_pool: Array<{
          instanceName: string;
          apiKey: string;
        }> = [];
        const generalRegions = ['EASTUS', 'USNORTH', 'USWEST'];

        // DeepSeek-specific API key first, then common fallback
        const deepseekApiKey = getSecret('AZURE_AI_SERVICE_DEEPSEEK_KEY');
        const commonApiKey = getSecret('AZURE_AI_SERVICE_KEY');

        for (const region of generalRegions) {
          const instanceName = getSecret(
            `GENERAL_DEEPSEEK_API_INSTANCE_NAME_${region}`,
          );
          const apiKey =
            deepseekApiKey ||
            getSecret(`GENERAL_DEEPSEEK_API_KEY_${region}`) ||
            commonApiKey;

          if (instanceName && apiKey) {
            general_deepseek_pool.push({ instanceName, apiKey });
          } else {
            this.logger.warn(
              `General DeepSeek config for region ${region} is incomplete. InstanceName: ${!!instanceName}, ApiKey: ${!!apiKey}, DeepSeek API Key: ${!!deepseekApiKey}, Common API Key: ${!!commonApiKey}`,
            );
          }
        }

        if (general_deepseek_pool.length > 0) {
          const selected =
            general_deepseek_pool[
              Math.floor(Math.random() * general_deepseek_pool.length)
            ];
          this.logger.debug(
            `General DeepSeek pool for model '${model_name}': ${JSON.stringify(general_deepseek_pool)}`,
          );
          this.logger.debug(`selected : ${JSON.stringify(selected)}`);
          selectedInstanceName = selected.instanceName;
          selectedApiKey = selected.apiKey;
          this.logger.debug(
            `General user randomly routed to DeepSeek instance: ${selectedInstanceName}`,
          );
        } else {
          this.logger.error(
            `No general DeepSeek instances available in pool for model '${model_name}'. Check GENERAL_DEEPSEEK_API_INSTANCE_NAME/KEY variables.`,
          );
          throw new NotFoundException(
            `No general regional configurations found for DeepSeek model '${model_name}'.`,
          );
        }
      }

      if (!selectedInstanceName) {
        // This case should ideally be caught by the specific checks above
        throw new NotFoundException(
          `Failed to determine a valid DeepSeek instance configuration for model '${model_name}'.`,
        );
      }

      llmConfigResult = {
        instanceName: selectedInstanceName,
        apiKey: selectedApiKey,
        endpointUrl: commonEndpoint,
        apiVersion: commonApiVersion,
      };

      // Log with API key masked for security
      const logResult = {
        ...llmConfigResult,
        apiKey: llmConfigResult.apiKey
          ? `${llmConfigResult.apiKey.substring(0, 5)}...${llmConfigResult.apiKey.slice(-5)}`
          : undefined,
      };
      this.logger.debug(
        `Final llmConfigResult for DeepSeek model '${model_name}': ${JSON.stringify(logResult)}`,
      );
    } else if (provider === 'qwen') {
      // ChatAlibabaTongyi constructor looks for `alibabaApiKey` field or `ALIBABA_API_KEY` env var.
      const apiKey =
        getSecret('ALIBABA_MODELSTUDIO_API_KEY') ||
        getSecret('DASHSCOPE_API_KEY') ||
        getSecret('ALIBABA_API_KEY');
      const endpointUrl = getSecret('ALIBABA_MODELSTUDIO_ENDPOINT'); // Fetch the base endpoint URL
      if (!apiKey)
        throw new NotFoundException(
          `API key for Qwen (ALIBABA_MODELSTUDIO_API_KEY, DASHSCOPE_API_KEY, or ALIBABA_API_KEY) missing for provider ${provider}`,
        );
      if (!endpointUrl)
        throw new NotFoundException(
          `Endpoint URL (ALIBABA_MODELSTUDIO_ENDPOINT) missing for Qwen provider.`,
        );
      llmConfigResult = {
        apiKey: apiKey,
        endpointUrl: endpointUrl,
        instanceName: 'dashscope-intl',
      }; // Include endpointUrl
    } else if (
      provider === 'gcp-vertexai-gemini' ||
      provider === 'gcp-vertexai-llama'
    ) {
      // Handle both Vertex AI types
      const region = getSecret('GOOGLE_VERTEXAI_REGION');
      const projectId = getSecret('GOOGLE_VERTEXAI_PROJECT_ID');
      if (!region || !projectId) {
        throw new NotFoundException(
          `GCP Vertex AI region or project ID not configured.`,
        );
      }
      llmConfigResult = { region: region, projectId: projectId }; // API key handled by ADC/env var
    }

    // --- Final Construction ---
    if (provider === 'unknown') {
      this.logger.error(
        `Could not determine provider for model ${model_name} (type: ${modelType})`,
      );
      throw new NotFoundException(
        `Configuration for model '${model_name}' is not available.`,
      );
    }

    const finalConfig: LlmConfig = {
      provider: provider,
      modelName: dbModelConfig.model_name ?? model_name, // Use model_name from DB, fallback to requested name
      deploymentName: dbModelConfig.deployment_name ?? undefined, // Use deployment_name from DB
      apiVersion: dbModelConfig.api_version ?? llmConfigResult.apiVersion, // Prioritize DB api_version
      modelType: dbModelConfig.model_type ?? undefined, // Include model_type from DB
      supportsSystemMessages: this.supportsSystemMessages(
        dbModelConfig.model_name ?? model_name,
        dbModelConfig,
      ), // Check system message support using DB model_name
      supportsTemperature: this.supportsTemperature(
        dbModelConfig.model_name ?? model_name,
        dbModelConfig,
      ), // Check temperature parameter support using DB model_name
      ...llmConfigResult, // Merge provider-specific results (apiKey, instanceName, etc.)
    };

    // Clean up undefined keys
    Object.keys(finalConfig).forEach(
      (key) =>
        (finalConfig as any)[key] === undefined &&
        delete (finalConfig as any)[key],
    );

    if (finalConfig.provider === 'qwen') {
      this.logger.debug(
        `[QWEN KEY CHECK] API Key being used for Qwen: ${finalConfig.apiKey ? finalConfig.apiKey.substring(0, 5) + '...' + finalConfig.apiKey.substring(finalConfig.apiKey.length - 5) : 'NOT FOUND'}`,
      );
    }
    this.logger.debug(
      `Resolved LLM config for ${model_name}/${dept_unit_code}: ${JSON.stringify(finalConfig)}`,
    );
    this.logger.debug(
      `Model ${model_name} supports system messages: ${finalConfig.supportsSystemMessages}`,
    );
    this.logger.debug(
      `Model ${model_name} supports temperature parameter: ${finalConfig.supportsTemperature}`,  
    );
    return finalConfig;
  }
  public async getAvailableModelsAndVersions(): Promise<
    Array<{
      modelName: string;
      apiVersions: string[];
      provider: string;
      modelType?: string;
    }>
  > {
    if (this.modelMap.size === 0) {
      this.logger.log(
        'Model map is empty, attempting to load model data for getAvailableModelsAndVersions...',
      );
      await this.loadModelData();
    }

    const modelsWithVersions: Array<{
      modelName: string;
      apiVersions: string[];
      provider: string;
      modelType?: string;
    }> = [];
    const providerGroup: Map<
      string,
      { apiVersions: Set<string>; modelType?: string }
    > = new Map();

    this.logger.debug(
      `Processing ${this.modelMap.size} models from map for API availability.`,
    );
    for (const [_key, dbModelConfig] of this.modelMap) {
      // Ensure dbModelConfig and necessary fields are not null/undefined
      if (
        dbModelConfig &&
        dbModelConfig.model_name &&
        dbModelConfig.api_status === 'A'
      ) {
        // Use a default API version if none is specified
        const apiVersion = dbModelConfig.api_version || 'default';
        let provider: LlmConfig['provider'] = 'unknown';
        const modelType = dbModelConfig.model_type?.toLowerCase();
        const model_name_lower = dbModelConfig.model_name.toLowerCase();

        // Provider determination logic (consistent with getLlmConfig or simplified as needed)
        if (
          modelType === 'azure_openai' ||
          model_name_lower.includes('gpt') ||
          model_name_lower.startsWith('o1') ||
          model_name_lower.startsWith('o3')
        ) {
          provider = 'azure';
        } else if (
          modelType === 'google_gemini' ||
          model_name_lower.includes('gemini')
        ) {
          provider = 'gcp-vertexai-gemini';
        } else if (
          modelType === 'anthropic_claude' ||
          model_name_lower.includes('claude')
        ) {
          provider = 'anthropic';
        } else if (
          modelType === 'deepseek' ||
          model_name_lower.includes('deepseek')
        ) {
          provider = 'deepseek-aisvc';
        } else if (
          modelType === 'alibaba_qwen' ||
          model_name_lower.includes('qwen')
        ) {
          provider = 'qwen';
        } else if (
          modelType === 'gcp_vertexai_llama' ||
          model_name_lower.includes('llama')
        ) {
          provider = 'gcp-vertexai-llama';
        } else if (modelType === 'gcp_vertexai') {
          // Fallback for generic gcp_vertexai
          if (model_name_lower.includes('llama'))
            provider = 'gcp-vertexai-llama';
          else provider = 'gcp-vertexai-gemini'; // Assume Gemini if not Llama
        }

        if (!providerGroup.has(provider)) {
          providerGroup.set(provider, {
            apiVersions: new Set(),
            modelType: dbModelConfig.model_type,
          });
        }
        providerGroup.get(provider)!.apiVersions.add(apiVersion);
        if (
          dbModelConfig.model_type &&
          !providerGroup.get(provider)!.modelType
        ) {
          providerGroup.get(provider)!.modelType = dbModelConfig.model_type;
        }
        // Log if we're using a default API version
        if (!dbModelConfig.api_version) {
          this.logger.debug(
            `Model ${dbModelConfig.model_name} has no API version specified, using 'default'`,
          );
        }
        // Debug log for each model processed
        this.logger.debug(
          `Processed model: ${dbModelConfig.model_name} -> provider: ${provider}, apiVersion: ${apiVersion}`,
        );
      } else {
        if (dbModelConfig && dbModelConfig.model_name) {
          this.logger.debug(
            `Skipping model ${dbModelConfig.model_name} due to missing fields or api_status not 'A'. API Status: ${dbModelConfig.api_status}`,
          );
        }
      }
    }

    // Debug log the final provider groups
    providerGroup.forEach((data, provider) => {
      this.logger.debug(
        `Final provider group: ${provider} -> API versions: [${Array.from(data.apiVersions).sort().join(', ')}]`,
      );
      modelsWithVersions.push({
        modelName: provider, // Use provider as modelName for consistency with frontend
        apiVersions: Array.from(data.apiVersions).sort(),
        provider: provider,
        modelType: data.modelType,
      });
    });

    this.logger.log(
      `Returning ${modelsWithVersions.length} API-available providers with their versions.`,
    );
    return modelsWithVersions.sort((a, b) =>
      a.provider.localeCompare(b.provider),
    );
  }
}
