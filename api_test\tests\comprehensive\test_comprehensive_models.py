#!/usr/bin/env python3
"""
Comprehensive test script for the HKBU GenAI Platform Chat Completion API.
Includes real-world scenarios like concurrent conversations, multi-round conversations,
stress testing, and advanced validation.

This script extends the basic test_all_models.py with advanced testing capabilities:
- Multi-round conversations (context retention testing)
- Concurrent conversations (load testing)
- Stress testing (rate limits, large payloads)
- Edge case testing (error handling)
- Advanced reporting and metrics
"""

import json
import os
import sys
import time
import argparse
import asyncio
import aiohttp
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple, Union
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import statistics
import uuid
from dotenv import load_dotenv


@dataclass
class TestResult:
    """Enhanced test result with detailed metrics."""
    model: str
    test_type: str
    success: bool
    start_time: float
    end_time: float
    response_time: float
    status_code: int
    prompt_tokens: int = 0
    completion_tokens: int = 0
    total_tokens: int = 0
    response: Optional[str] = None
    error: Optional[str] = None
    conversation_id: Optional[str] = None
    round_number: int = 1
    context_retained: Optional[bool] = None
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

    @property
    def tokens_per_second(self) -> float:
        """Calculate tokens per second rate."""
        if self.response_time > 0 and self.total_tokens > 0:
            return self.total_tokens / self.response_time
        return 0.0


@dataclass
class ConversationState:
    """Track state of a multi-round conversation."""
    conversation_id: str
    model: str
    messages: List[Dict[str, Any]]
    results: List[TestResult]
    start_time: float
    current_round: int = 1
    context_keywords: List[str] = None

    def __post_init__(self):
        if self.context_keywords is None:
            self.context_keywords = []


class ModelTester:
    """Enhanced model testing class with advanced capabilities."""
    
    def __init__(self, config: Dict[str, str], models_config: Dict[str, Any]):
        self.config = config
        self.models_config = models_config
        self.models = models_config.get("models", [])
        self.default_params = models_config.get("default_parameters", {})
        
        # Performance tracking
        self.performance_stats = defaultdict(list)
        self.conversation_states: Dict[str, ConversationState] = {}
        
        # Rate limiting - async compatible (more conservative)
        self.request_times = deque()
        self.rate_limit_window = 60  # seconds
        self.max_requests_per_minute = 30  # Further reduced from 50 to 30
        self.rate_limit_semaphore = asyncio.Semaphore(2)  # Max 2 concurrent requests (reduced from 5)
        self.last_request_time = 0
        self.min_request_interval = 3.0  # Minimum 3 seconds between requests (increased from 1)
        self.rate_limit_backoff = 1.0  # Exponential backoff multiplier
        
        # Results storage
        self.all_results: List[TestResult] = []

    async def wait_for_rate_limit(self, apply_backoff: bool = False):
        """Async rate limiting with semaphore, time-based limits, and exponential backoff."""
        async with self.rate_limit_semaphore:
            now = time.time()
            
            # Remove old requests outside the window
            while self.request_times and self.request_times[0] < now - self.rate_limit_window:
                self.request_times.popleft()
            
            # Wait if we're at the rate limit
            rate_limit_wait_count = 0
            while len(self.request_times) >= self.max_requests_per_minute:
                rate_limit_wait_count += 1
                wait_time = min(rate_limit_wait_count * 2, 10)  # Exponential backoff, max 10s
                await asyncio.sleep(wait_time)
                now = time.time()
                while self.request_times and self.request_times[0] < now - self.rate_limit_window:
                    self.request_times.popleft()
            
            # Apply exponential backoff if requested (after 429 errors)
            if apply_backoff:
                backoff_delay = self.min_request_interval * self.rate_limit_backoff
                await asyncio.sleep(backoff_delay)
                self.rate_limit_backoff = min(self.rate_limit_backoff * 1.5, 4.0)  # Max 4x backoff
            else:
                # Reset backoff on successful requests
                self.rate_limit_backoff = max(self.rate_limit_backoff * 0.9, 1.0)  # Gradually reduce
            
            # Ensure minimum interval between requests
            time_since_last = now - self.last_request_time
            effective_interval = self.min_request_interval * self.rate_limit_backoff
            if time_since_last < effective_interval:
                wait_time = effective_interval - time_since_last
                await asyncio.sleep(wait_time)
                now = time.time()
            
            self.request_times.append(now)
            self.last_request_time = now

    async def make_api_request(self, model_info: Dict[str, Any], messages: List[Dict[str, Any]], 
                              conversation_id: str = None, round_number: int = 1) -> TestResult:
        """Make an async API request with detailed tracking."""
        
        model_name = model_info["name"]
        timeout = model_info.get("timeout", 30)
        
        # Construct URL
        url = f"{self.config['base_url']}/rest/deployments/{model_name}/chat/completions"
        
        # Headers
        headers = {
            "Content-Type": "application/json",
            "api-key": self.config["api_key"]
        }
        
        # Query parameters
        api_version = model_info.get("api_version", self.default_params.get("api_version", "2024-02-01"))
        params = {"api-version": api_version}
        
        # Payload
        supports_temperature = model_info.get("supports_temperature", True)
        payload = {
            "messages": messages,
            "stream": False
        }
        
        if supports_temperature:
            payload["temperature"] = self.default_params.get("temperature", 0.7)

        start_time = time.time()
        conversation_id = conversation_id or str(uuid.uuid4())
        
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=timeout)) as session:
                async with session.post(url, params=params, headers=headers, json=payload) as response:
                    end_time = time.time()
                    response_time = end_time - start_time
                    
                    if response.status == 200:
                        response_data = await response.json()
                        
                        # Extract response content
                        assistant_message = None
                        if "choices" in response_data and len(response_data["choices"]) > 0:
                            assistant_message = response_data["choices"][0]["message"]["content"]
                        
                        # Extract usage info
                        usage = response_data.get("usage", {})
                        
                        return TestResult(
                            model=model_name,
                            test_type="api_call",
                            success=True,
                            start_time=start_time,
                            end_time=end_time,
                            response_time=response_time,
                            status_code=response.status,
                            prompt_tokens=usage.get('prompt_tokens', 0),
                            completion_tokens=usage.get('completion_tokens', 0),
                            total_tokens=usage.get('total_tokens', 0),
                            response=assistant_message,
                            conversation_id=conversation_id,
                            round_number=round_number,
                            metadata={
                                "has_conversation_uuid": "conversation_uuid" in response_data,
                                "response_data_keys": list(response_data.keys())
                            }
                        )
                    else:
                        error_text = await response.text()
                        result = TestResult(
                            model=model_name,
                            test_type="api_call",
                            success=False,
                            start_time=start_time,
                            end_time=end_time,
                            response_time=response_time,
                            status_code=response.status,
                            error=f"HTTP {response.status}: {error_text[:200]}",
                            conversation_id=conversation_id,
                            round_number=round_number
                        )
                        
                        # Apply backoff for rate limiting errors
                        if response.status == 429:
                            await self.wait_for_rate_limit(apply_backoff=True)
                        
                        return result
                        
        except asyncio.TimeoutError:
            return TestResult(
                model=model_name,
                test_type="api_call",
                success=False,
                start_time=start_time,
                end_time=time.time(),
                response_time=timeout,
                status_code=0,
                error=f"Timeout after {timeout}s",
                conversation_id=conversation_id,
                round_number=round_number
            )
        except Exception as e:
            return TestResult(
                model=model_name,
                test_type="api_call",
                success=False,
                start_time=start_time,
                end_time=time.time(),
                response_time=time.time() - start_time,
                status_code=0,
                error=str(e),
                conversation_id=conversation_id,
                round_number=round_number
            )

    def validate_context_retention(self, conversation_state: ConversationState, 
                                  current_response: str) -> bool:
        """Validate if the model retained context from previous messages."""
        if not conversation_state.context_keywords:
            return True  # No keywords to check
        
        if not current_response:
            return False
        
        # Enhanced context validation - check both keywords and conversation history
        response_lower = current_response.lower()
        
        # Check for context keywords
        keywords_found = sum(1 for keyword in conversation_state.context_keywords 
                           if keyword.lower() in response_lower)
        keyword_retention = keywords_found >= len(conversation_state.context_keywords) * 0.3  # Lowered threshold
        
        # Check if response references previous conversation topics
        previous_messages = [msg["content"].lower() for msg in conversation_state.messages 
                           if msg["role"] == "user"]
        
        # Look for references to previous topics
        topic_references = 0
        for prev_msg in previous_messages:
            # Extract key terms from previous messages (simple approach)
            prev_words = set(word for word in prev_msg.split() 
                           if len(word) > 3 and word.isalpha())
            response_words = set(word for word in response_lower.split() 
                               if len(word) > 3 and word.isalpha())
            
            # Check for word overlap
            if prev_words.intersection(response_words):
                topic_references += 1
                break  # Found at least one reference
        
        # Context is retained if either keywords or topic references are found
        has_topic_reference = topic_references > 0
        
        return keyword_retention or has_topic_reference

    async def test_single_model_basic(self, model_info: Dict[str, Any], 
                                    prompt: str) -> TestResult:
        """Test a single model with a basic prompt."""
        
        # Build messages based on model support for system messages
        supports_system_messages = model_info.get("supports_system_messages", True)
        messages = []
        if supports_system_messages:
            messages.append({
                "role": "system",
                "content": "You are a helpful assistant."
            })
        messages.append({
            "role": "user",
            "content": prompt
        })
        
        await self.wait_for_rate_limit()
        result = await self.make_api_request(model_info, messages)
        result.test_type = "basic_single"
        
        self.all_results.append(result)
        return result

    async def test_multi_round_conversation(self, model_info: Dict[str, Any], 
                                          conversation_template: List[Dict[str, Any]]) -> List[TestResult]:
        """Test multi-round conversation with context retention validation."""
        
        conversation_id = str(uuid.uuid4())
        model_name = model_info["name"]
        supports_system_messages = model_info.get("supports_system_messages", True)
        
        # Initialize conversation state
        conversation_state = ConversationState(
            conversation_id=conversation_id,
            model=model_name,
            messages=[],
            results=[],
            start_time=time.time(),
            context_keywords=conversation_template[0].get("context_keywords", [])
        )
        
        self.conversation_states[conversation_id] = conversation_state
        
        # Add system message if supported
        if supports_system_messages:
            conversation_state.messages.append({
                "role": "system",
                "content": "You are a helpful assistant. Remember details from our conversation."
            })
        
        results = []
        
        for round_num, template in enumerate(conversation_template, 1):
            # Add user message
            conversation_state.messages.append({
                "role": "user",
                "content": template["content"]
            })
            
            # Make API call with rate limiting
            await self.wait_for_rate_limit()
            result = await self.make_api_request(
                model_info, 
                conversation_state.messages.copy(), 
                conversation_id, 
                round_num
            )
            result.test_type = "multi_round"
            
            # Add adaptive delay between conversation rounds
            # Longer delay for later rounds to reduce rate limiting
            if round_num > 1:
                adaptive_delay = min(4.0 + (round_num - 2) * 1.0, 8.0)  # 4-8 seconds (increased)
                await asyncio.sleep(adaptive_delay)
            
            # Validate context retention (skip for first round)
            if round_num > 1 and result.success:
                result.context_retained = self.validate_context_retention(
                    conversation_state, result.response
                )
            
            # Add assistant response to conversation
            if result.success and result.response:
                conversation_state.messages.append({
                    "role": "assistant", 
                    "content": result.response
                })
            
            conversation_state.results.append(result)
            results.append(result)
            self.all_results.append(result)
            
            # Update conversation state
            conversation_state.current_round = round_num
            
            # Stop if this round failed
            if not result.success:
                break
        
        return results

    async def test_concurrent_conversations(self, model_info: Dict[str, Any], 
                                          prompts: List[str], 
                                          num_concurrent: int = 5) -> List[TestResult]:
        """Test multiple concurrent conversations with the same model."""
        
        model_name = model_info["name"]
        supports_system_messages = model_info.get("supports_system_messages", True)
        
        async def single_concurrent_test(prompt: str, test_id: int) -> TestResult:
            messages = []
            if supports_system_messages:
                messages.append({
                    "role": "system",
                    "content": f"You are assistant #{test_id}. Include your ID in responses."
                })
            messages.append({
                "role": "user",
                "content": f"[Test {test_id}] {prompt}"
            })
            
            await self.wait_for_rate_limit()
            result = await self.make_api_request(
                model_info, 
                messages, 
                f"concurrent_{test_id}_{uuid.uuid4()}"
            )
            result.test_type = "concurrent"
            result.metadata["concurrent_test_id"] = test_id
            return result
        
        # Create concurrent tasks
        tasks = []
        for i in range(num_concurrent):
            prompt = prompts[i % len(prompts)]  # Cycle through prompts
            tasks.append(single_concurrent_test(prompt, i + 1))
        
        # Note: Rate limiting is now handled per-request in make_api_request via wait_for_rate_limit
        
        # Execute concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                error_result = TestResult(
                    model=model_name,
                    test_type="concurrent",
                    success=False,
                    start_time=time.time(),
                    end_time=time.time(),
                    response_time=0,
                    status_code=0,
                    error=str(result),
                    conversation_id=f"concurrent_failed_{i}",
                    metadata={"concurrent_test_id": i + 1}
                )
                processed_results.append(error_result)
            else:
                processed_results.append(result)
            
        self.all_results.extend(processed_results)
        return processed_results

    async def test_stress_scenario(self, model_info: Dict[str, Any], 
                                 stress_config: Dict[str, Any]) -> List[TestResult]:
        """Run stress tests with high frequency requests."""
        
        num_requests = stress_config.get("num_requests", 20)
        request_interval = stress_config.get("interval", 0.5)  # seconds
        prompt = stress_config.get("prompt", "Stress test prompt")
        
        model_name = model_info["name"]
        supports_system_messages = model_info.get("supports_system_messages", True)
        
        messages = []
        if supports_system_messages:
            messages.append({
                "role": "system",
                "content": "You are being stress tested. Keep responses brief."
            })
        messages.append({
            "role": "user",
            "content": prompt
        })
        
        results = []
        start_time = time.time()
        
        for i in range(num_requests):
            # Small delay between requests
            if i > 0:
                await asyncio.sleep(request_interval)
            
            result = await self.make_api_request(
                model_info, 
                messages, 
                f"stress_{i}_{uuid.uuid4()}"
            )
            result.test_type = "stress"
            result.metadata["stress_request_number"] = i + 1
            result.metadata["time_since_start"] = time.time() - start_time
            
            results.append(result)
            
            # Log progress
            if (i + 1) % 5 == 0:
                success_rate = sum(1 for r in results if r.success) / len(results) * 100
                print(f"    Stress test progress: {i+1}/{num_requests} requests, {success_rate:.1f}% success")
        
        self.all_results.extend(results)
        return results

    def generate_performance_report(self, results: List[TestResult]) -> Dict[str, Any]:
        """Generate detailed performance report."""
        
        if not results:
            return {"error": "No results to analyze"}
        
        # Group results by model and test type
        by_model = defaultdict(list)
        by_test_type = defaultdict(list)
        
        for result in results:
            by_model[result.model].append(result)
            by_test_type[result.test_type].append(result)
        
        report = {
            "summary": {
                "total_tests": len(results),
                "successful_tests": sum(1 for r in results if r.success),
                "failed_tests": sum(1 for r in results if not r.success),
                "success_rate": sum(1 for r in results if r.success) / len(results) * 100,
                "total_tokens": sum(r.total_tokens for r in results),
                "avg_response_time": statistics.mean([r.response_time for r in results if r.success]),
                "test_duration": max([r.end_time for r in results]) - min([r.start_time for r in results])
            },
            "by_model": {},
            "by_test_type": {},
            "performance_metrics": {},
            "conversation_analysis": {}
        }
        
        # Per-model analysis
        for model, model_results in by_model.items():
            successful = [r for r in model_results if r.success]
            report["by_model"][model] = {
                "total_tests": len(model_results),
                "successful": len(successful),
                "success_rate": len(successful) / len(model_results) * 100 if model_results else 0,
                "avg_response_time": statistics.mean([r.response_time for r in successful]) if successful else 0,
                "total_tokens": sum(r.total_tokens for r in successful),
                "tokens_per_second": statistics.mean([r.tokens_per_second for r in successful if r.tokens_per_second > 0]) if successful else 0,
                "errors": [r.error for r in model_results if not r.success]
            }
        
        # Per-test-type analysis
        for test_type, type_results in by_test_type.items():
            successful = [r for r in type_results if r.success]
            report["by_test_type"][test_type] = {
                "total_tests": len(type_results),
                "successful": len(successful),
                "success_rate": len(successful) / len(type_results) * 100 if type_results else 0,
                "avg_response_time": statistics.mean([r.response_time for r in successful]) if successful else 0,
                "models_tested": len(set(r.model for r in type_results))
            }
        
        # Conversation analysis for multi-round tests
        multi_round_results = [r for r in results if r.test_type == "multi_round"]
        if multi_round_results:
            conversations = defaultdict(list)
            for result in multi_round_results:
                conversations[result.conversation_id].append(result)
            
            context_retention_rates = []
            for conv_id, conv_results in conversations.items():
                context_results = [r for r in conv_results if r.context_retained is not None]
                if context_results:
                    retention_rate = sum(1 for r in context_results if r.context_retained) / len(context_results)
                    context_retention_rates.append(retention_rate)
            
            report["conversation_analysis"] = {
                "total_conversations": len(conversations),
                "avg_context_retention": statistics.mean(context_retention_rates) * 100 if context_retention_rates else 0,
                "avg_conversation_length": statistics.mean([len(conv) for conv in conversations.values()]),
                "completed_conversations": sum(1 for conv in conversations.values() if all(r.success for r in conv))
            }
        
        return report

    def print_comprehensive_report(self, results: List[TestResult]):
        """Print a detailed comprehensive report."""
        
        report = self.generate_performance_report(results)
        
        print("\n" + "=" * 100)
        print("📊 COMPREHENSIVE TEST REPORT")
        print("=" * 100)
        
        # Summary
        summary = report["summary"]
        print(f"\n📈 OVERALL SUMMARY:")
        print(f"  Total Tests: {summary['total_tests']}")
        print(f"  Success Rate: {summary['successful_tests']}/{summary['total_tests']} ({summary['success_rate']:.1f}%)")
        print(f"  Average Response Time: {summary['avg_response_time']:.2f}s")
        print(f"  Total Tokens Used: {summary['total_tokens']:,}")
        print(f"  Test Duration: {summary['test_duration']:.1f}s")
        
        # Per-model results
        if report["by_model"]:
            print(f"\n🤖 RESULTS BY MODEL:")
            print("-" * 100)
            print(f"{'Model':<25} {'Tests':<8} {'Success':<8} {'Avg Time':<12} {'Tokens/s':<12} {'Tokens':<10}")
            print("-" * 100)
            
            for model, stats in sorted(report["by_model"].items()):
                print(f"{model:<25} {stats['total_tests']:<8} "
                      f"{stats['success_rate']:>6.1f}% {stats['avg_response_time']:>10.2f}s "
                      f"{stats['tokens_per_second']:>10.1f} {stats['total_tokens']:>9,}")
        
        # Per-test-type results
        if report["by_test_type"]:
            print(f"\n🧪 RESULTS BY TEST TYPE:")
            print("-" * 80)
            print(f"{'Test Type':<20} {'Tests':<8} {'Success':<8} {'Models':<8} {'Avg Time':<12}")
            print("-" * 80)
            
            for test_type, stats in sorted(report["by_test_type"].items()):
                print(f"{test_type:<20} {stats['total_tests']:<8} "
                      f"{stats['success_rate']:>6.1f}% {stats['models_tested']:<8} "
                      f"{stats['avg_response_time']:>10.2f}s")
        
        # Conversation analysis
        if "conversation_analysis" in report and report["conversation_analysis"]:
            conv_analysis = report["conversation_analysis"]
            print(f"\n💬 CONVERSATION ANALYSIS:")
            print(f"  Total Conversations: {conv_analysis['total_conversations']}")
            print(f"  Completed Conversations: {conv_analysis['completed_conversations']}")
            print(f"  Average Context Retention: {conv_analysis['avg_context_retention']:.1f}%")
            print(f"  Average Conversation Length: {conv_analysis['avg_conversation_length']:.1f} rounds")
        
        # Failed tests
        failed_results = [r for r in results if not r.success]
        if failed_results:
            print(f"\n❌ FAILED TESTS ({len(failed_results)}):")
            print("-" * 100)
            error_counts = defaultdict(int)
            for result in failed_results:
                error_type = result.error.split(':')[0] if result.error else "Unknown"
                error_counts[error_type] += 1
            
            for error_type, count in sorted(error_counts.items(), key=lambda x: x[1], reverse=True):
                print(f"  {error_type}: {count} occurrences")
        
        print("\n" + "=" * 100)


class TestScenarioRunner:
    """Orchestrates different test scenarios."""
    
    def __init__(self, config: Dict[str, str], models_config: Dict[str, Any]):
        self.tester = ModelTester(config, models_config)
        self.models = models_config.get("models", [])

    async def run_basic_tests(self, models_to_test: List[Dict[str, Any]], 
                            prompt: str) -> List[TestResult]:
        """Run basic single-prompt tests."""
        print(f"\n🔥 Running basic tests on {len(models_to_test)} models...")
        
        results = []
        for i, model_info in enumerate(models_to_test, 1):
            print(f"  [{i}/{len(models_to_test)}] Testing {model_info['name']}...")
            
            try:
                result = await self.tester.test_single_model_basic(model_info, prompt)
                
                if result.success:
                    print(f"    ✅ Success - {result.response_time:.2f}s - {result.total_tokens} tokens")
                else:
                    print(f"    ❌ Failed - {result.error}")
                
                results.append(result)
                
            except Exception as e:
                print(f"    ❌ Exception during test - {str(e)[:100]}")
                # Create a failed result for tracking
                failed_result = TestResult(
                    model=model_info["name"],
                    test_type="basic_single",
                    success=False,
                    start_time=time.time(),
                    end_time=time.time(),
                    response_time=0,
                    status_code=0,
                    error=f"Exception: {str(e)}"
                )
                results.append(failed_result)
            
            # Add delay between models in basic tests too
            if i < len(models_to_test):  # Not the last model
                await asyncio.sleep(2.0)  # Shorter delay for basic tests
        
        return results

    async def run_multi_round_tests(self, models_to_test: List[Dict[str, Any]]) -> List[TestResult]:
        """Run multi-round conversation tests."""
        print(f"\n💬 Running multi-round conversation tests...")
        
        # Define conversation templates
        conversation_templates = [
            [
                {"content": "I'm planning a trip to Paris. Can you suggest 3 main attractions?", 
                 "context_keywords": ["Paris", "attractions", "trip"]},
                {"content": "Which of these attractions would be best for photography?", 
                 "context_keywords": ["photography", "attractions"]},
                {"content": "What's the best time of day to visit for photos?", 
                 "context_keywords": ["time", "photos", "visit"]}
            ],
            [
                {"content": "I'm learning Python programming. Can you explain what a function is?", 
                 "context_keywords": ["Python", "function", "programming"]},
                {"content": "Can you show me an example of the function concept you just explained?", 
                 "context_keywords": ["example", "function"]},
                {"content": "How would I call that function in my code?", 
                 "context_keywords": ["call", "function", "code"]}
            ]
        ]
        
        all_results = []
        for i, model_info in enumerate(models_to_test, 1):
            print(f"  [{i}/{len(models_to_test)}] Testing {model_info['name']} conversations...")
            
            model_failed_completely = True
            model_results = []
            
            for j, template in enumerate(conversation_templates, 1):
                print(f"    Conversation {j}: {len(template)} rounds...")
                
                try:
                    results = await self.tester.test_multi_round_conversation(model_info, template)
                    
                    success_count = sum(1 for r in results if r.success)
                    context_retained = sum(1 for r in results if r.context_retained) if len(results) > 1 else 0
                    
                    if success_count > 0:
                        model_failed_completely = False
                    
                    print(f"      ✅ {success_count}/{len(results)} rounds successful")
                    if len(results) > 1:
                        print(f"      🧠 {context_retained}/{len(results)-1} context retention checks passed")
                    
                    model_results.extend(results)
                    
                    # Add delay between conversations to reduce rate limiting
                    if j < len(conversation_templates):  # Not the last conversation
                        await asyncio.sleep(5.0)  # 5 second delay between conversations (increased)
                        
                except Exception as e:
                    print(f"      ❌ Error in conversation {j}: {str(e)[:100]}")
                    # Continue with next conversation even if this one fails
                    continue
            
            all_results.extend(model_results)
            
            # Report if model failed completely
            if model_failed_completely:
                print(f"    ⚠️ Model {model_info['name']} failed all conversation attempts")
            else:
                model_success_rate = sum(1 for r in model_results if r.success) / len(model_results) * 100 if model_results else 0
                print(f"    📊 Overall model success rate: {model_success_rate:.1f}%")
            
            # Add longer delay between models to reduce rate limiting
            if i < len(models_to_test):  # Not the last model
                print(f"    ⏳ Waiting 8 seconds before next model...")
                await asyncio.sleep(8.0)  # 8 second delay between models
        
        return all_results

    async def run_concurrent_tests(self, models_to_test: List[Dict[str, Any]], 
                                 num_concurrent: int = 5) -> List[TestResult]:
        """Run concurrent conversation tests."""
        print(f"\n🚀 Running concurrent conversation tests ({num_concurrent} parallel)...")
        
        test_prompts = [
            "What is the capital of France?",
            "Explain quantum physics in simple terms.",
            "Write a haiku about technology.",
            "What are the benefits of renewable energy?",
            "Describe the process of photosynthesis."
        ]
        
        all_results = []
        for i, model_info in enumerate(models_to_test, 1):
            print(f"  [{i}/{len(models_to_test)}] Testing {model_info['name']} concurrency...")
            
            results = await self.tester.test_concurrent_conversations(
                model_info, test_prompts, num_concurrent
            )
            
            success_count = sum(1 for r in results if r.success)
            avg_response_time = statistics.mean([r.response_time for r in results if r.success]) if success_count > 0 else 0
            
            print(f"    ✅ {success_count}/{len(results)} concurrent requests successful")
            print(f"    ⏱️ Average response time: {avg_response_time:.2f}s")
            
            all_results.extend(results)
        
        return all_results

    async def run_stress_tests(self, models_to_test: List[Dict[str, Any]]) -> List[TestResult]:
        """Run stress tests."""
        print(f"\n💪 Running stress tests...")
        
        stress_configs = [
            {
                "name": "High Frequency",
                "num_requests": 15,
                "interval": 0.3,
                "prompt": "Quick test: what is 2+2?"
            },
            {
                "name": "Sustained Load", 
                "num_requests": 10,
                "interval": 1.0,
                "prompt": "Explain artificial intelligence in one paragraph."
            }
        ]
        
        all_results = []
        for i, model_info in enumerate(models_to_test, 1):
            print(f"  [{i}/{len(models_to_test)}] Stress testing {model_info['name']}...")
            
            for config in stress_configs:
                print(f"    Running {config['name']} test...")
                results = await self.tester.test_stress_scenario(model_info, config)
                
                success_count = sum(1 for r in results if r.success)
                success_rate = success_count / len(results) * 100
                
                print(f"      ✅ {success_count}/{len(results)} requests successful ({success_rate:.1f}%)")
                
                all_results.extend(results)
        
        return all_results

    async def run_all_tests(self, models_to_test: List[Dict[str, Any]], 
                          test_types: List[str], 
                          basic_prompt: str = "What is 2+2? Please provide only the numeric answer.",
                          concurrent_count: int = 5) -> List[TestResult]:
        """Run all specified test types."""
        
        all_results = []
        
        if "basic" in test_types:
            results = await self.run_basic_tests(models_to_test, basic_prompt)
            all_results.extend(results)
        
        if "multi_round" in test_types:
            results = await self.run_multi_round_tests(models_to_test)
            all_results.extend(results)
        
        if "concurrent" in test_types:
            results = await self.run_concurrent_tests(models_to_test, concurrent_count)
            all_results.extend(results)
        
        if "stress" in test_types:
            results = await self.run_stress_tests(models_to_test)
            all_results.extend(results)
        
        return all_results


# Configuration and utility functions
def load_config() -> Dict[str, str]:
    """Load configuration from .env file."""
    load_dotenv()
    
    api_key = os.getenv("API_KEY")
    base_url = os.getenv("BASE_URL")
    
    if not api_key:
        raise ValueError("API_KEY not found in .env file")
    if not base_url:
        raise ValueError("BASE_URL not found in .env file")
    
    return {
        "api_key": api_key,
        "base_url": base_url
    }


def load_models_config() -> Dict[str, Any]:
    """Load models configuration from models.json."""
    config_path = os.path.join(os.path.dirname(__file__), "../../config/models.json")
    if not os.path.exists(config_path):
        raise ValueError("models.json not found. Please create it first.")
    
    with open(config_path, 'r') as f:
        return json.load(f)


def save_detailed_results(results: List[TestResult], filename: Optional[str] = None):
    """Save detailed test results to JSON file."""
    if not filename:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"comprehensive_test_results_{timestamp}.json"
    
    # Ensure we have an absolute path
    if not os.path.isabs(filename):
        # Save in the same directory as this script
        script_dir = os.path.dirname(os.path.abspath(__file__))
        filename = os.path.join(script_dir, filename)
    
    print(f"🔄 Saving results to: {filename}")
    
    try:
        # Convert results to dict format
        results_data = [asdict(result) for result in results]
        
        # Generate summary statistics
        successful = [r for r in results if r.success]
        by_model = defaultdict(list)
        by_test_type = defaultdict(list)
        
        for result in results:
            by_model[result.model].append(result)
            by_test_type[result.test_type].append(result)
        
        summary = {
            "test_metadata": {
                "test_date": datetime.now().isoformat(),
                "total_tests": len(results),
                "successful_tests": len(successful),
                "failed_tests": len(results) - len(successful),
                "success_rate": len(successful) / len(results) * 100 if results else 0,
                "unique_models": len(set(r.model for r in results)),
                "test_types": list(set(r.test_type for r in results)),
                "total_duration": max([r.end_time for r in results]) - min([r.start_time for r in results]) if results else 0
            },
            "performance_summary": {
                "avg_response_time": statistics.mean([r.response_time for r in successful]) if successful else 0,
                "total_tokens": sum(r.total_tokens for r in results),
                "avg_tokens_per_second": statistics.mean([r.tokens_per_second for r in successful if r.tokens_per_second > 0]) if successful else 0
            },
            "model_breakdown": {
                model: {
                    "total_tests": len(model_results),
                    "success_rate": sum(1 for r in model_results if r.success) / len(model_results) * 100,
                    "avg_response_time": statistics.mean([r.response_time for r in model_results if r.success]) if [r for r in model_results if r.success] else 0
                }
                for model, model_results in by_model.items()
            },
            "test_type_breakdown": {
                test_type: {
                    "total_tests": len(type_results),
                    "success_rate": sum(1 for r in type_results if r.success) / len(type_results) * 100,
                    "models_tested": len(set(r.model for r in type_results))
                }
                for test_type, type_results in by_test_type.items()
            }
        }
        
        output_data = {
            "summary": summary,
            "detailed_results": results_data
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=2, default=str, ensure_ascii=False)
        
        # Verify file was created and get size
        if os.path.exists(filename):
            file_size = os.path.getsize(filename)
            print(f"💾 Detailed results saved to: {filename} ({file_size:,} bytes)")
        else:
            print(f"❌ Failed to create file: {filename}")
            
    except Exception as e:
        print(f"❌ Error saving results to {filename}: {e}")
        # Try to save to a fallback location
        fallback_filename = f"test_results_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        try:
            with open(fallback_filename, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, indent=2, default=str, ensure_ascii=False)
            print(f"💾 Results saved to fallback file: {fallback_filename}")
        except Exception as fallback_error:
            print(f"❌ Failed to save to fallback file: {fallback_error}")
            print("📄 Results summary:")
            print(f"   Total tests: {len(results)}")
            print(f"   Success rate: {len([r for r in results if r.success])}/{len(results)}")


async def main():
    """Main async function to run comprehensive tests."""
    parser = argparse.ArgumentParser(
        description="Comprehensive testing for HKBU GenAI Platform API",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Test Types:
  basic      - Single prompt tests for each model
  multi_round - Multi-round conversations with context retention testing
  concurrent - Multiple simultaneous conversations per model  
  stress     - High-frequency requests and sustained load testing

Examples:
  python test_comprehensive_models.py --types basic,concurrent
  python test_comprehensive_models.py --models qwen-plus,gemini-2.5-flash --types multi_round
  python test_comprehensive_models.py --types stress --concurrent 10
        """
    )
    
    parser.add_argument(
        "--types",
        help="Comma-separated test types to run",
        default="basic,multi_round,concurrent",
        choices=["basic", "multi_round", "concurrent", "stress"]
    )
    parser.add_argument(
        "--models",
        help="Comma-separated list of specific models to test"
    )
    parser.add_argument(
        "--prompt",
        help="Custom prompt for basic tests",
        default="What is 2+2? Please provide only the numeric answer."
    )
    parser.add_argument(
        "--concurrent",
        type=int,
        default=5,
        help="Number of concurrent conversations for concurrent tests"
    )
    parser.add_argument(
        "--save",
        help="Save detailed results to JSON file",
        nargs='?',
        const=True,
        default=False
    )
    parser.add_argument(
        "--no-report",
        action="store_true",
        help="Skip printing the comprehensive report"
    )
    
    args = parser.parse_args()
    
    try:
        # Load configurations
        config = load_config()
        models_config = load_models_config()
        
        # Determine test types
        test_types = [t.strip() for t in args.types.split(",")]
        
        # Get models to test
        all_models = models_config.get("models", [])
        if args.models:
            requested_models = [m.strip() for m in args.models.split(",")]
            models_to_test = [m for m in all_models if m["name"] in requested_models]
            
            if len(models_to_test) != len(requested_models):
                found_models = [m["name"] for m in models_to_test]
                not_found = [m for m in requested_models if m not in found_models]
                print(f"⚠️  Models not found: {', '.join(not_found)}")
        else:
            models_to_test = all_models
        
        if not models_to_test:
            print("❌ No models to test!")
            sys.exit(1)
        
        # Print test configuration
        print("🚀 HKBU GenAI Platform - Comprehensive Model Testing")
        print("=" * 100)
        print(f"📍 API Endpoint: {config['base_url']}")
        print(f"🔑 API Key: {config['api_key'][:8]}...")
        print(f"🎯 Models to Test: {len(models_to_test)} ({', '.join([m['name'] for m in models_to_test])})")
        print(f"🧪 Test Types: {', '.join(test_types)}")
        print(f"📝 Basic Prompt: {args.prompt}")
        if "concurrent" in test_types:
            print(f"⚡ Concurrent Tests: {args.concurrent} parallel conversations")
        
        # Initialize test runner
        runner = TestScenarioRunner(config, models_config)
        
        # Run tests
        start_time = time.time()
        print(f"\n🏁 Starting comprehensive tests at {datetime.now().strftime('%H:%M:%S')}...")
        
        all_results = await runner.run_all_tests(
            models_to_test, 
            test_types, 
            args.prompt, 
            args.concurrent
        )
        
        total_time = time.time() - start_time
        print(f"\n🏁 All tests completed in {total_time:.1f}s")
        
        # Generate and print report
        if not args.no_report:
            runner.tester.print_comprehensive_report(all_results)
        
        # Save results if requested
        if args.save:
            filename = args.save if isinstance(args.save, str) else None
            save_detailed_results(all_results, filename)
        
        # Exit with appropriate code
        failed_count = len([r for r in all_results if not r.success])
        success_rate = (len(all_results) - failed_count) / len(all_results) * 100 if all_results else 0
        
        print(f"\n🎯 Final Result: {success_rate:.1f}% success rate ({len(all_results) - failed_count}/{len(all_results)} tests passed)")
        
        sys.exit(0 if failed_count == 0 else 1)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())