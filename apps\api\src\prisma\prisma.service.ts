import { Injectable, OnModuleInit, INestApplication } from '@nestjs/common';
// Adjust the import path based on the actual location relative to the package root
import { PrismaClient } from '@hkbu-genai-platform/database/generated/client';

@Injectable()
export class PrismaService extends PrismaClient implements OnModuleInit {
  constructor() {
    super({
      // Optionally add Prisma client options here, e.g., logging
      // log: ['query', 'info', 'warn', 'error'],
    });
  }

  async onModuleInit() {
    // Optional: Connect to the database when the module initializes
    await this.$connect();
  }

  async enableShutdownHooks(app: INestApplication) {
    // Ensure Prisma disconnects gracefully on application shutdown
    process.on('beforeExit', async () => {
      await app.close();
    });
  }
}
