# Azure OpenAI LangChain Token Usage Test

This test script verifies whether <PERSON><PERSON>hain's ChatOpenAI class properly extracts token usage information from Azure OpenAI streaming responses.

## Background

Based on previous Python testing, Azure OpenAI **DOES** provide token usage information in both streaming and non-streaming modes. However, the current <PERSON><PERSON>hai<PERSON> integration in the codebase may not be extracting this information correctly, leading to zero token counts being recorded.

## Test Components

### 1. Non-Streaming Test
- Uses LangChain's ChatOpenAI with `streaming: false`
- Verifies baseline token extraction capability
- Should show token usage metadata if Lang<PERSON>hain supports it

### 2. Streaming Test (LangChain)
- Uses LangChain's ChatOpenAI with streaming enabled
- Configured with `streamUsage: true` and `stream_options: { include_usage: true }`
- Logs detailed chunk structure to identify where token usage appears
- Tests the same configuration used in `processLangchainStream`

### 3. Direct API Test
- Bypasses <PERSON><PERSON>hain entirely
- Makes direct HTTP requests to Azure OpenAI API
- Confirms that Azure OpenAI provides token usage (baseline truth)
- Uses the same configuration as Python test that proved token availability

## Configuration

The test uses the `hkbu-chatgpt-us-east2` Azure OpenAI instance with:
- **Model**: gpt-4.1-mini (deployment: `chatgpt-4.1`)
- **API Version**: `2024-12-01-preview` (supports token usage)
- **Endpoint**: `https://hkbu-chatgpt-us-east2.openai.azure.com`

## Running the Test

### Option 1: Using the batch script (Windows)
```cmd
run-token-test.bat
```

### Option 2: Manual installation and run
```cmd
npm install
node test-azure-langchain-tokens.js
```

## Expected Results

### If LangChain Integration Works
- All three tests should show token usage
- Streaming test should extract tokens from chunk metadata
- This would indicate the current implementation should work

### If LangChain Integration Has Issues (Expected)
- Non-streaming might work (simpler case)
- Streaming test shows no token usage in LangChain chunks
- Direct API test shows token usage (proving Azure provides it)
- This confirms the LangChain integration issue

## Key Findings to Look For

1. **Chunk Structure**: What paths contain token usage in LangChain chunks?
2. **Token Locations**: Does usage appear in `usage_metadata`, `response_metadata.usage`, or other paths?
3. **Timing**: Does token usage appear in final chunks only or throughout?
4. **Comparison**: How does LangChain chunk structure differ from direct API response?

## Next Steps Based on Results

### If LangChain Works
- Review `processLangchainStream` implementation for bugs
- Check if token extraction logic needs updates

### If LangChain Doesn't Work (Expected)
- Implement direct Azure OpenAI API approach (similar to DeepSeek service)
- Or patch LangChain response processing to extract tokens correctly
- Update memory bank with specific findings

## Files Created
- `test-azure-langchain-tokens.js` - Main test script
- `test-package.json` - Dependencies specification
- `run-token-test.bat` - Easy execution script
- `TOKEN-TEST-README.md` - This documentation