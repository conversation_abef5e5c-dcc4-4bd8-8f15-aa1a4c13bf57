# Comprehensive API Testing Framework

This directory contains an enhanced testing framework for the HKBU GenAI Platform API, designed to simulate real-world usage patterns and stress test the system under various conditions.

## Overview

The comprehensive testing framework extends the basic `test_all_models.py` with advanced testing capabilities:

- **Multi-round conversations** - Test context retention across conversation turns
- **Concurrent conversations** - Test multiple simultaneous conversations per model  
- **Stress testing** - High-frequency requests and sustained load testing
- **Edge case testing** - Error handling and boundary condition tests
- **Advanced reporting** - Detailed metrics and performance analysis

## Files Structure

```
api_test/
├── test_all_models.py                    # Basic testing (original)
├── test_comprehensive_models.py          # Enhanced testing framework
├── test_scenarios.json                   # Test scenario configurations
├── models.json                          # Model configurations
├── .env                                 # Environment variables
├── test_templates/                      # Template configurations
│   ├── multi_round_conversations.json   # Conversation templates
│   ├── concurrent_test_prompts.json     # Concurrent test prompts
│   └── stress_test_configs.json         # Stress test configurations
├── reports/                             # Generated test reports (auto-created)
└── README_comprehensive_testing.md      # This file
```

## Quick Start

### Prerequisites

Install required dependencies:
```bash
pip install aiohttp asyncio python-dotenv requests
```

### Basic Usage

1. **Run basic tests** (equivalent to original test_all_models.py):
```bash
python test_comprehensive_models.py --types basic
```

2. **Run multi-round conversation tests**:
```bash
python test_comprehensive_models.py --types multi_round
```

3. **Run concurrent conversation tests**:
```bash
python test_comprehensive_models.py --types concurrent --concurrent 8
```

4. **Run stress tests**:
```bash
python test_comprehensive_models.py --types stress
```

5. **Run all test types**:
```bash
python test_comprehensive_models.py --types basic,multi_round,concurrent,stress
```

### Test Specific Models

Test only specific models:
```bash
python test_comprehensive_models.py --models qwen-plus,gemini-2.5-flash --types multi_round
```

### Save Results

Save detailed results to JSON file:
```bash
python test_comprehensive_models.py --types concurrent --save
python test_comprehensive_models.py --types stress --save custom_results.json
```

## Test Types Explained

### 1. Basic Tests (`--types basic`)

Single-prompt tests similar to the original testing script:
- Tests each model with a simple prompt
- Validates basic API functionality
- Quick health check for all models

**Example:**
```bash
python test_comprehensive_models.py --types basic --prompt "What is artificial intelligence?"
```

### 2. Multi-Round Conversations (`--types multi_round`)

Tests conversation context retention:
- Sends 3-5 messages in sequence
- Validates that models remember previous context
- Tests conversation flow and coherence

**Built-in conversation scenarios:**
- Travel Planning (Paris trip recommendations)
- Programming Tutorial (Python functions)
- Recipe Planning (Pasta cooking)
- Science Explanation (Gravity concepts)

**Context validation:**
- Tracks keywords from previous messages
- Validates context retention rate
- Reports conversation completion rate

### 3. Concurrent Conversations (`--types concurrent`)

Tests multiple simultaneous conversations:
- Launches 5-10 parallel conversations per model
- Uses different prompts for each conversation
- Validates conversation isolation (no cross-talk)
- Tests server load handling

**Example:**
```bash
python test_comprehensive_models.py --types concurrent --concurrent 10
```

### 4. Stress Testing (`--types stress`)

High-frequency and sustained load testing:
- **High Frequency**: 15 requests with 0.3s intervals
- **Sustained Load**: 10 requests with 1.0s intervals
- **Burst Tests**: Short bursts of rapid requests
- **Long Response Tests**: Requests requiring detailed responses

**Metrics tracked:**
- Success rate under load
- Response time consistency
- Rate limiting behavior
- Error patterns

## Configuration Files

### models.json

Defines available models and their capabilities:
```json
{
  "name": "model-name",
  "deployment_name": "deployment-name", 
  "supports_system_messages": true,
  "supports_temperature": true,
  "timeout": 30,
  "api_version": "2024-02-01"
}
```

### test_scenarios.json

Comprehensive test scenario definitions:
- Basic test prompts
- Multi-round conversation templates  
- Concurrent test configurations
- Stress test scenarios
- Edge case definitions
- Validation rules

### Template Files

**multi_round_conversations.json:**
- Pre-defined conversation flows
- Context keywords for validation
- Expected response elements

**concurrent_test_prompts.json:**
- Prompt sets for concurrent testing
- Isolation testing scenarios
- Load testing patterns

**stress_test_configs.json:**
- Stress testing scenarios
- Load patterns (linear, exponential, sine wave)
- Failure simulation scenarios

## Advanced Features

### Context Retention Validation

For multi-round conversations:
- Tracks context keywords across conversation turns
- Validates that responses reference previous context
- Configurable validation thresholds
- Reports context retention rates per model

### Conversation Isolation Testing

For concurrent conversations:
- Ensures no cross-conversation contamination
- Tests with different user personas
- Validates response independence
- Detects conversation bleeding

### Performance Benchmarking

Comprehensive performance metrics:
- Response time distribution
- Tokens per second calculation
- Throughput measurements
- Error rate analysis
- Performance consistency tracking

### Detailed Reporting

Multi-level reporting:
- **Summary**: Overall test results and success rates
- **By Model**: Per-model performance breakdown
- **By Test Type**: Performance by test scenario
- **Conversation Analysis**: Context retention and completion rates
- **Performance Metrics**: Response times, throughput, efficiency
- **Error Analysis**: Error categorization and patterns

## Sample Output

```
🚀 HKBU GenAI Platform - Comprehensive Model Testing
================================================================================
📍 API Endpoint: http://localhost:3003/api/v0
🔑 API Key: 4b217a6f...
🎯 Models to Test: 3 (qwen-plus, gemini-2.5-flash, deepseek-v3)
🧪 Test Types: basic, multi_round, concurrent
📝 Basic Prompt: What is 2+2? Please provide only the numeric answer.

🔥 Running basic tests on 3 models...
  [1/3] Testing qwen-plus...
    ✅ Success - 1.23s - 15 tokens
  [2/3] Testing gemini-2.5-flash...
    ✅ Success - 0.87s - 12 tokens
  [3/3] Testing deepseek-v3...
    ✅ Success - 2.14s - 18 tokens

💬 Running multi-round conversation tests...
  [1/3] Testing qwen-plus conversations...
    Conversation 1: 3 rounds...
      ✅ 3/3 rounds successful
      🧠 2/2 context retention checks passed
    Conversation 2: 3 rounds...
      ✅ 3/3 rounds successful  
      🧠 2/2 context retention checks passed

🚀 Running concurrent conversation tests (5 parallel)...
  [1/3] Testing qwen-plus concurrency...
    ✅ 5/5 concurrent requests successful
    ⏱️ Average response time: 1.45s

================================================================================
📊 COMPREHENSIVE TEST REPORT
================================================================================

📈 OVERALL SUMMARY:
  Total Tests: 24
  Success Rate: 24/24 (100.0%)
  Average Response Time: 1.34s
  Total Tokens Used: 432
  Test Duration: 45.2s

🤖 RESULTS BY MODEL:
--------------------------------------------------------------------------------
Model                     Tests   Success  Avg Time    Tokens/s   Tokens
--------------------------------------------------------------------------------
qwen-plus                 8       100.0%      1.23s      12.1        156
gemini-2.5-flash          8       100.0%      0.87s      18.4        142  
deepseek-v3               8       100.0%      2.14s       8.7        134

🧪 RESULTS BY TEST TYPE:
--------------------------------------------------------------------------------
Test Type            Tests   Success  Models   Avg Time
--------------------------------------------------------------------------------
basic                3       100.0%   3        1.41s
multi_round          12      100.0%   3        1.28s
concurrent           9       100.0%   3        1.35s

💬 CONVERSATION ANALYSIS:
  Total Conversations: 6
  Completed Conversations: 6
  Average Context Retention: 95.5%
  Average Conversation Length: 3.0 rounds
```

## Best Practices

### 1. Rate Limiting

The framework includes built-in rate limiting:
- Respects API rate limits (60 requests/minute by default)
- Automatically throttles requests
- Configurable limits per scenario

### 2. Error Handling

Comprehensive error handling:
- Graceful timeout handling
- Network error recovery
- API error categorization
- Detailed error reporting

### 3. Resource Management

Efficient resource usage:
- Async/await for concurrent operations
- Connection pooling for HTTP requests
- Memory-efficient result storage
- Cleanup of temporary resources

### 4. Scalability

Framework scales with your needs:
- Configurable concurrency levels
- Modular test scenarios
- Extensible reporting
- Template-based configuration

## Troubleshooting

### Common Issues

1. **Rate Limiting Errors**
   - Reduce concurrent count: `--concurrent 3`
   - Increase intervals in stress test configs
   - Check API key permissions

2. **Timeout Errors**
   - Increase model timeout values in models.json
   - Check network connectivity
   - Verify API endpoint availability

3. **Memory Issues**
   - Reduce number of concurrent tests
   - Enable result streaming for large test runs
   - Monitor system resources

4. **Context Retention Failures**
   - Check model's context window limits
   - Verify conversation template keywords
   - Adjust context validation thresholds

### Debug Mode

Enable verbose logging:
```bash
python test_comprehensive_models.py --types multi_round --debug
```

## Extending the Framework

### Adding New Test Types

1. Create test method in `ModelTester` class
2. Add test type to `TestScenarioRunner`
3. Update command line arguments
4. Add configuration in test_scenarios.json

### Custom Conversation Templates

Add to `test_templates/multi_round_conversations.json`:
```json
{
  "custom_scenario": {
    "name": "Custom Test",
    "rounds": [
      {
        "content": "Your prompt here",
        "context_keywords": ["keyword1", "keyword2"],
        "expected_response_elements": ["element1", "element2"]
      }
    ]
  }
}
```

### Custom Metrics

Extend the `TestResult` dataclass:
1. Add new fields to TestResult
2. Update metric collection in test methods  
3. Extend reporting functions
4. Add to performance analysis

## Integration with CI/CD

Example GitHub Actions workflow:

```yaml
name: API Comprehensive Testing
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Setup Python
      uses: actions/setup-python@v2
      with:
        python-version: '3.9'
    - name: Install dependencies
      run: pip install aiohttp asyncio python-dotenv requests
    - name: Run comprehensive tests
      run: |
        cd api_test
        python test_comprehensive_models.py --types basic,concurrent --save ci_results.json
      env:
        API_KEY: ${{ secrets.API_KEY }}
        BASE_URL: ${{ secrets.BASE_URL }}
```

## License

This testing framework is part of the HKBU GenAI Platform project.