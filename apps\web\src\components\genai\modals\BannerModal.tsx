import { Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import moment from 'moment';

type BannerModalProps = {
  show: boolean;
  onClickClose?: () => void;
  onClose?: () => void;
};

const BannerModal = (props: BannerModalProps) => {
  const { show, onClickClose, onClose = () => {} } = props;

  return (
    <Transition.Root show={show}>
      <Dialog
        as="div"
        className="relative z-30"
        onClose={onClose}
        __demoMode={true}
      >
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-2xl bg-white text-left shadow-xl transition-all sm:my-8 w-full sm:w-full sm:max-w-[640px]">
                <ChatGPT4TurboBanner onClickClose={onClickClose} />
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
};

export default BannerModal;

const ChatGPT4TurboBanner = (props: { onClickClose?: () => void }) => {
  const CrossSvg = () => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      height="24"
      viewBox="0 -960 960 960"
      width="24"
      fill="#FFF"
    >
      <path d="m256-200-56-56 224-224-224-224 56-56 224 224 224-224 56 56-224 224 224 224-56 56-224-224-224 224Z" />
    </svg>
  );

  // Removed unused SVG component definitions: GeminiSVG, CladueSVG, LLamaSVG, OpenAISVG, MoreModel
  const title = 'Exciting update for you !';

  return (
    <div className=" bg-white p-3">
      <div className="w-full flex justify-end mb-2 sm:mb-6">
        <div
          className=" bg-emerald-700 p-2.5 rounded-full cursor-pointer"
          onClick={props.onClickClose}
        >
          <CrossSvg />
        </div>
      </div>
      <div className=" px-5 mb-2 sm:mb-6">
        <div className="text-xl font-bold sm:text-2xl mb-1">{title}</div>
        <p className="text-sm text-gray-600">
          We are excited to announce that several cutting-edge LLM models have
          been added to the platform.
        </p>
      </div>
      <hr className="border-t border-gray-300 my-4" />
      {/* DeepSeek Released */}
      <div className="flex flex-row justify-center items-center sm:p-4 gap-5 sm:mb-6 mx-5">
        <div className="flex flex-1 items-center flex-col gap-2 sm:gap-5">
          {/* <DeepSeekLogo /> Removed */}

          <p className="ml-2 font-bold">DeepSeek</p>
        </div>
        <div className="flex flex-2 items-center flex-col gap-2 px-5 text-sm">
          {/* Some Description about the DeepSeek */}
          <p>
            We have introduced
            <span className="font-bold"> DeepSeek-R1</span> and
            <span className="font-bold"> DeepSeek-V3 </span>, both are
            exceptional in Chinese Language Proficiency and strong in reasoning.
          </p>
        </div>
      </div>
      {/* Separator */}
      <hr className="border-t border-gray-300 my-4" />
      {/* Qwen */}
      <div className="flex flex-row justify-center items-center sm:p-4 gap-5 sm:mb-6 mx-5">
        <div className="flex flex-1 items-center flex-col gap-2 sm:gap-5">
          {/* <QwenLogo /> Removed */}

          <p className="ml-2 font-bold">Qwen</p>
        </div>
        <div className="flex flex-2 items-center flex-col gap-2 px-5  text-sm">
          {/* Some Description about the Qwen */}
          <p>
            The latest <span className="font-bold"> Qwen </span> models
            including
            <span className="font-bold"> Qwen2.5-Max </span>
            and
            <span className="font-bold"> Qwen-Plus </span>
            are now available. These models also excel in Chinese language
            understanding.
          </p>
        </div>
      </div>
      {/* Separator */}
      <hr className="border-t border-gray-300 my-4" />
      {/* O1 O1 Mini
      <div className="flex flex-row justify-center items-center sm:p-4 gap-5 sm:mb-6 mx-5">
        <div className="flex flex-1 items-center flex-col gap-2 sm:gap-5">
          <OpenAISVG />

          <p className="ml-2 font-bold">o1/o1-mini</p>
        </div>
        <div className="flex flex-2 items-center flex-col gap-2 px-5"> */}
      {/* Some Description about the o1 o1-mini */}
      {/* <p>
            The art of the state model <span className="font-bold"> o1 </span>
            and <span className="font-bold"> o1-mini </span> are now available.
            On preview version, these models are
            <span className="font-bold"> only available on API access.</span>
            The is designed to "think" their responses through and provide more
            accurate responses.
          </p>
        </div>
      </div> */}
      {/* Separator */}
      {/* <hr className="border-t border-gray-300 my-4" /> */}
      <div className="self-end justify-end text-end">
        {moment('2025-03-31', 'YYYY-M-D').format('D MMMM YYYY')}
      </div>
    </div>
  );
};
