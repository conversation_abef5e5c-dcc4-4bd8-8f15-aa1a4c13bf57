#!/usr/bin/env node

/**
 * Test Script: Azure OpenAI LangChain Token Usage Verification
 * 
 * This script tests whether <PERSON><PERSON>hain's ChatOpenAI class properly extracts
 * token usage information from Azure OpenAI streaming responses.
 * 
 * Based on the memory bank findings, Azure OpenAI DOES provide token usage
 * in both streaming and non-streaming modes, but <PERSON><PERSON><PERSON><PERSON> might not be
 * extracting it correctly.
 */

import dotenv from 'dotenv';
import { ChatOpenAI } from '@langchain/openai';
import { HumanMessage } from '@langchain/core/messages';

// Load environment variables from parent directory
dotenv.config({ path: '../apps/api/.env' });

// Test configuration
const TEST_CONFIG = {
  // Using the hkbu-chatgpt-us-east2 instance as per user request
  azureOpenAIEndpoint: 'https://hkbu-chatgpt-us-east2.openai.azure.com',
  azureOpenAIApiKey: process.env.GENERAL_AZURE_OPENAI_API_KEY_USEAST2,
  azureOpenAIApiVersion: '2024-12-01-preview', // Version that supports token usage
  deploymentName: 'chatgpt-4.1', // gpt-4.1-mini deployment
  testMessage: 'Hello! Please respond with a short greeting. This is a test message to verify token usage extraction.',
  temperature: 0.7,
};

console.log('🧪 Azure OpenAI LangChain Token Usage Test');
console.log('==========================================');
console.log(`Endpoint: ${TEST_CONFIG.azureOpenAIEndpoint}`);
console.log(`Deployment: ${TEST_CONFIG.deploymentName}`);
console.log(`API Version: ${TEST_CONFIG.azureOpenAIApiVersion}`);
console.log('');

// Validate configuration
if (!TEST_CONFIG.azureOpenAIApiKey) {
  console.error('❌ GENERAL_AZURE_OPENAI_API_KEY_USEAST2 not found in environment variables');
  process.exit(1);
}

/**
 * Test 1: Non-Streaming Request
 * This should provide token usage as baseline comparison
 */
async function testNonStreaming() {
  console.log('📝 Test 1: Non-Streaming Request');
  console.log('--------------------------------');
  
  try {
    const chatModel = new ChatOpenAI({
      temperature: TEST_CONFIG.temperature,
      modelName: TEST_CONFIG.deploymentName,
      streaming: false, // Explicitly non-streaming
      configuration: {
        apiKey: TEST_CONFIG.azureOpenAIApiKey,
        baseURL: `${TEST_CONFIG.azureOpenAIEndpoint}/openai/deployments/${TEST_CONFIG.deploymentName}`,
        defaultQuery: { 'api-version': TEST_CONFIG.azureOpenAIApiVersion },
      },
    });

    const message = new HumanMessage(TEST_CONFIG.testMessage);
    console.log(`Input: "${TEST_CONFIG.testMessage}"`);
    
    const startTime = Date.now();
    const response = await chatModel.invoke([message]);
    const endTime = Date.now();
    
    console.log(`\n✅ Response received in ${endTime - startTime}ms`);
    console.log(`Content: "${response.content}"`);
    console.log('\n🔍 Token Usage Analysis:');
    console.log('Response object keys:', Object.keys(response));
    console.log('Usage metadata:', response.usage_metadata);
    console.log('Response metadata:', response.response_metadata);
    
    // Check various possible token locations
    const possibleTokenPaths = [
      response.usage_metadata,
      response.response_metadata?.tokenUsage,
      response.response_metadata?.usage_metadata,
      response.response_metadata?.usage,
      response.response_metadata?.llmOutput?.usage,
    ];
    
    possibleTokenPaths.forEach((path, index) => {
      if (path) {
        console.log(`Token path ${index + 1}:`, path);
      }
    });
    
    return response;
  } catch (error) {
    console.error('❌ Non-streaming test failed:', error);
    throw error;
  }
}

/**
 * Test 2: Streaming Request with Enhanced Token Detection
 * This tests LangChain's streaming token usage extraction
 */
async function testStreaming() {
  console.log('\n📡 Test 2: Streaming Request with Token Usage');
  console.log('---------------------------------------------');
  
  try {
    // Configure ChatOpenAI with streaming enabled and token usage requested
    const chatModel = new ChatOpenAI({
      temperature: TEST_CONFIG.temperature,
      modelName: TEST_CONFIG.deploymentName,
      streaming: true,
      streamUsage: true, // Request token usage in streaming mode
      configuration: {
        apiKey: TEST_CONFIG.azureOpenAIApiKey,
        baseURL: `${TEST_CONFIG.azureOpenAIEndpoint}/openai/deployments/${TEST_CONFIG.deploymentName}`,
        defaultQuery: { 'api-version': TEST_CONFIG.azureOpenAIApiVersion },
      },
      modelKwargs: {
        stream_options: { include_usage: true }, // Azure OpenAI specific parameter
      },
    });

    const message = new HumanMessage(TEST_CONFIG.testMessage);
    console.log(`Input: "${TEST_CONFIG.testMessage}"`);
    
    let accumulatedContent = '';
    let chunkCount = 0;
    let tokensFound = false;
    let finalUsageMetadata = null;
    
    const startTime = Date.now();
    const stream = await chatModel.stream([message]);
    
    console.log('\n🔄 Processing stream chunks...');
    
    for await (const chunk of stream) {
      chunkCount++;
      
      // Log every chunk's structure for debugging
      console.log(`\n--- Chunk ${chunkCount} ---`);
      console.log('Chunk keys:', Object.keys(chunk));
      console.log('Content:', chunk.content || '[no content]');
      
      // Detailed token usage detection (same logic as processLangchainStream)
      const usageMetadata = 
        chunk?.usage_metadata ?? 
        chunk?.response_metadata?.tokenUsage ??
        chunk?.response_metadata?.usage_metadata ??
        chunk?.response_metadata?.usage ?? 
        chunk?.usage;
      
      if (usageMetadata) {
        console.log('🎯 Token usage found in chunk!');
        console.log('Usage metadata:', JSON.stringify(usageMetadata, null, 2));
        tokensFound = true;
        finalUsageMetadata = usageMetadata;
      }
      
      // Log response metadata for detailed analysis
      if (chunk.response_metadata) {
        console.log('Response metadata keys:', Object.keys(chunk.response_metadata));
        console.log('Response metadata:', JSON.stringify(chunk.response_metadata, null, 2));
      }
      
      // Accumulate content
      if (chunk.content) {
        accumulatedContent += chunk.content;
      }
      
      // Log finish reason if present
      const finishReason = 
        chunk?.response_metadata?.finishReason ??
        chunk?.response_metadata?.finish_reason ??
        chunk?.choices?.[0]?.finish_reason;
      
      if (finishReason) {
        console.log('Finish reason:', finishReason);
      }
    }
    
    const endTime = Date.now();
    
    console.log('\n✅ Streaming completed');
    console.log(`Duration: ${endTime - startTime}ms`);
    console.log(`Total chunks: ${chunkCount}`);
    console.log(`Final content: "${accumulatedContent}"`);
    console.log(`Tokens found: ${tokensFound ? '✅ YES' : '❌ NO'}`);
    
    if (finalUsageMetadata) {
      console.log('\n🏆 Final Token Usage:');
      console.log(JSON.stringify(finalUsageMetadata, null, 2));
    } else {
      console.log('\n⚠️  No token usage metadata found in any chunk');
    }
    
    return { accumulatedContent, tokensFound, finalUsageMetadata };
  } catch (error) {
    console.error('❌ Streaming test failed:', error);
    throw error;
  }
}

/**
 * Test 3: Direct Azure OpenAI API Call
 * This bypasses LangChain to verify Azure OpenAI provides token usage
 */
async function testDirectAPI() {
  console.log('\n🔗 Test 3: Direct Azure OpenAI API Call');
  console.log('--------------------------------------');
  
  try {
    const endpoint = `${TEST_CONFIG.azureOpenAIEndpoint}/openai/deployments/${TEST_CONFIG.deploymentName}/chat/completions?api-version=${TEST_CONFIG.azureOpenAIApiVersion}`;
    
    const payload = {
      messages: [
        { role: 'user', content: TEST_CONFIG.testMessage }
      ],
      temperature: TEST_CONFIG.temperature,
      stream: true,
      stream_options: { include_usage: true }, // Request token usage
    };
    
    console.log('Endpoint:', endpoint);
    console.log('Payload:', JSON.stringify(payload, null, 2));
    
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'api-key': TEST_CONFIG.azureOpenAIApiKey,
      },
      body: JSON.stringify(payload),
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API request failed: ${response.status} ${response.statusText}\n${errorText}`);
    }
    
    console.log('\n🔄 Processing direct API stream...');
    
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let accumulatedContent = '';
    let chunkCount = 0;
    let usageFound = false;
    let finalUsage = null;
    
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      
      const chunk = decoder.decode(value, { stream: true });
      const lines = chunk.split('\n').filter(line => line.trim());
      
      for (const line of lines) {
        if (!line.startsWith('data:')) continue;
        
        const data = line.slice(5).trim();
        if (data === '[DONE]') continue;
        
        try {
          const parsed = JSON.parse(data);
          chunkCount++;
          
          console.log(`\n--- Direct API Chunk ${chunkCount} ---`);
          console.log('Chunk keys:', Object.keys(parsed));
          
          // Check for content
          const content = parsed.choices?.[0]?.delta?.content;
          if (content) {
            accumulatedContent += content;
            console.log('Content:', content);
          }
          
          // Check for usage (appears in final chunk before [DONE])
          if (parsed.usage) {
            console.log('🎯 Token usage found in direct API!');
            console.log('Usage:', JSON.stringify(parsed.usage, null, 2));
            usageFound = true;
            finalUsage = parsed.usage;
          }
          
        } catch (parseError) {
          console.log('Parse error for line:', data);
        }
      }
    }
    
    console.log('\n✅ Direct API streaming completed');
    console.log(`Total chunks: ${chunkCount}`);
    console.log(`Final content: "${accumulatedContent}"`);
    console.log(`Usage found: ${usageFound ? '✅ YES' : '❌ NO'}`);
    
    if (finalUsage) {
      console.log('\n🏆 Direct API Token Usage:');
      console.log(JSON.stringify(finalUsage, null, 2));
    }
    
    return { accumulatedContent, usageFound, finalUsage };
  } catch (error) {
    console.error('❌ Direct API test failed:', error);
    throw error;
  }
}

/**
 * Main test execution
 */
async function runTests() {
  try {
    // Test 1: Non-streaming baseline
    const nonStreamingResult = await testNonStreaming();
    
    // Test 2: LangChain streaming
    const streamingResult = await testStreaming();
    
    // Test 3: Direct API comparison
    const directResult = await testDirectAPI();
    
    // Summary analysis
    console.log('\n📊 TEST RESULTS SUMMARY');
    console.log('=======================');
    
    console.log('1. Non-streaming (LangChain):');
    console.log(`   - Content received: ${!!nonStreamingResult.content}`);
    console.log(`   - Token metadata available: ${!!nonStreamingResult.usage_metadata || !!nonStreamingResult.response_metadata}`);
    
    console.log('2. Streaming (LangChain):');
    console.log(`   - Content received: ${!!streamingResult.accumulatedContent}`);
    console.log(`   - Tokens found: ${streamingResult.tokensFound}`);
    
    console.log('3. Direct API (Raw):');
    console.log(`   - Content received: ${!!directResult.accumulatedContent}`);
    console.log(`   - Usage found: ${directResult.usageFound}`);
    
    // Key finding
    if (directResult.usageFound && !streamingResult.tokensFound) {
      console.log('\n🔍 KEY FINDING:');
      console.log('   ⚠️  Azure OpenAI provides token usage, but LangChain is NOT extracting it');
      console.log('   📋 This confirms the LangChain integration issue identified in the memory bank');
    } else if (streamingResult.tokensFound) {
      console.log('\n🔍 KEY FINDING:');
      console.log('   ✅ LangChain IS successfully extracting token usage from Azure OpenAI');
      console.log('   📋 The processLangchainStream implementation should work correctly');
    }
    
  } catch (error) {
    console.error('\n💥 Test execution failed:', error);
    process.exit(1);
  }
}

// Execute tests
runTests().then(() => {
  console.log('\n🏁 All tests completed successfully');
}).catch((error) => {
  console.error('\n💥 Test suite failed:', error);
  process.exit(1);
});