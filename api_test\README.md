# HKBU GenAI Platform API Testing Suite

Comprehensive testing suite for the HKBU GenAI Platform Chat Completion REST API, supporting multiple LLM models.

## Prerequisites

1. **Python 3.7+** installed on your system
2. **API Server running** (default: `localhost:3003`)
3. **Valid API key** for authentication

## Quick Start

1. Navigate to the `api_test` directory:
   ```bash
   cd api_test
   ```

2. Create and activate a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Copy `.env.example` to `.env` and configure your settings:
   ```bash
   cp .env.example .env
   # Edit .env with your API key and base URL
   ```

## Available Test Scripts

### 1. Test a Single Model (`test_single_model.py`)

Test any specific model with custom prompts and parameters.

```bash
# Basic usage
python test_single_model.py gpt-4.1

# With custom prompt
python test_single_model.py gemini-2.5-pro -p "Explain quantum computing in simple terms"

# With custom timeout
python test_single_model.py o1 -t 60

# List all available models
python test_single_model.py --list-models
```

**Options:**
- `model`: Model name (required) - use the model_name from database, not deployment_name
- `-p, --prompt`: Custom prompt to send to the model
- `-t, --timeout`: Request timeout in seconds (default: 30)
- `--list-models`: List all available models from models.json

### 2. Test All Models (`test_all_models.py`)

Comprehensive testing of all configured models with detailed reporting.

```bash
# Test all models sequentially
python test_all_models.py

# Test all models in parallel (faster)
python test_all_models.py --parallel

# Test with custom prompt
python test_all_models.py -p "What is the capital of France?"

# Test specific models only
python test_all_models.py --models "gpt-4.1,gemini-2.5-pro,deepseek-v3"

# Save results to JSON file
python test_all_models.py --save
python test_all_models.py --save my_results.json

# Adjust parallel workers
python test_all_models.py --parallel --workers 10
```

**Options:**
- `-p, --prompt`: Custom prompt for all models
- `--parallel`: Run tests in parallel
- `--workers`: Number of parallel workers (default: 5)
- `--save`: Save results to JSON file
- `--models`: Comma-separated list of specific models to test

### 3. Test Conversation Isolation (`test_conversation_isolation.py`)

Verify that REST API calls don't create conversations in the WebUI.

```bash
python test_conversation_isolation.py
```

This script:
- Makes 5 API calls with unique timestamped messages
- Verifies responses don't contain conversation_uuid
- Provides instructions to verify no conversations were created in WebUI

### 4. Test OpenAI Models (`test_openai_models.py`)

Dedicated test script for o1 and o3-mini models with special handling.

```bash
python test_openai_models.py
```

This script:
- Tests o1 and o3-mini models specifically
- Does NOT send system messages (these models don't support them)
- Includes multiple test scenarios:
  - Basic greeting
  - Math problems
  - Reasoning tasks
  - Code generation
  - Multi-turn conversations
- Provides detailed error reporting

### 5. Legacy Single Model Test (`test_gpt4_chat.py`)

Original test script for backward compatibility.

```bash
python test_gpt4_chat.py
```

## Configuration

### Environment Variables (.env)

Create a `.env` file with:
```env
API_KEY=your-api-key-here
BASE_URL=http://localhost:3003/api/v0
```

### Models Configuration (models.json)

The `models.json` file contains all available models:
```json
{
  "models": [
    {
      "name": "gpt-4.1",
      "deployment_name": "gpt-4.1",
      "description": "GPT-4.1 model",
      "timeout": 30
    },
    ...
  ],
  "default_parameters": {
    "temperature": 0.7,
    "stream": false,
    "api_version": "2024-02-01"
  }
}
```

## Available Models

Currently configured models:
- **OpenAI Models**: gpt-4.1, gpt-4.1-mini, o1, o3-mini
- **Google Models**: gemini-2.5-flash, gemini-2.5-pro
- **DeepSeek Models**: deepseek-r1, deepseek-v3
- **Alibaba Models**: qwen-plus, qwen-max
- **Meta Models**: llama-4-maverick

### Model-Specific Considerations

#### OpenAI o1 and o3-mini Models
- **Do NOT support system messages** - Only user/assistant messages are allowed
- May require specific Azure instance configuration
- Longer timeout recommended for o1 (60s)
- Use `test_openai_models.py` for comprehensive testing

#### Other Models
- Most models support system messages by default
- Check `models.json` for model-specific timeout and configuration
- API version is taken from database, not from test configuration

## Expected Output

### Successful Response
```
🚀 Testing HKBU GenAI Platform - Model: gpt-4.1
📍 URL: http://localhost:3003/api/v0/rest/deployments/gpt-4.1/chat/completions
🔑 API Key: 4b217a6f...
💬 Prompt: Hello, how are you? Please respond in one sentence.
------------------------------------------------------------
📊 Status Code: 200
⏱️  Response Time: 1.23s
✅ Request successful!
✅ Response structure is valid (OpenAI-compatible format)!

🤖 Assistant Response:
I'm doing well, thank you for asking!

📋 Response Details:
   ID: chatcmpl-abc123...
   Model: gpt-4.1
   Object Type: chat.completion
   Created: 1234567890
   Finish Reason: stop

📊 Token Usage:
   Prompt Tokens: 25
   Completion Tokens: 10
   Total Tokens: 35
```

### Batch Testing Summary
```
📊 TEST SUMMARY
================================================================================

⏱️  Total Execution Time: 45.32s
📈 Success Rate: 9/11 (81.8%)

✅ SUCCESSFUL MODELS (9):
--------------------------------------------------------------------------------
Model                     Response Time    Tokens     Status
--------------------------------------------------------------------------------
gpt-4.1-mini                     0.89s         35     ✓
gemini-2.5-flash                 1.23s         42     ✓
gpt-4.1                          1.45s         35     ✓
...

❌ FAILED MODELS (2):
--------------------------------------------------------------------------------
Model                     Error
--------------------------------------------------------------------------------
o3-mini                   HTTP 404: Model not found...
deepseek-r1               Timeout after 60s

📊 PERFORMANCE STATISTICS:
--------------------------------------------------------------------------------
  Average Response Time: 2.34s
  Fastest Response: 0.89s
  Slowest Response: 5.67s
  Total Tokens Used: 387
================================================================================
```

## Troubleshooting

### Common Issues

1. **Connection Error**
   - Verify API server is running
   - Check BASE_URL in .env matches your server

2. **401 Unauthorized**
   - Verify API_KEY is correct
   - Ensure API key has proper permissions

3. **404 Model Not Found**
   - Check model deployment name is correct
   - Verify model is available in your deployment

4. **Timeout Errors**
   - Increase timeout for slower models (e.g., deepseek-r1)
   - Check network connectivity
   - Consider using parallel testing with fewer workers

### Debug Mode

For detailed debugging, you can modify the scripts to print full responses:
```python
# In test scripts, change error handling to show full response
print(f"Full response: {response.text}")
```

## Architecture Notes

### REST API Isolation
The REST API endpoints are designed to be stateless and do not create conversations:
- Uses separate service method (`createRestApiCompletion`)
- Does not save messages to database
- Returns OpenAI-compatible format
- Tracks token usage with `is_api=1` flag

### Response Format
All responses follow OpenAI's chat completion format:
```json
{
  "id": "chatcmpl-...",
  "object": "chat.completion",
  "created": 1234567890,
  "model": "gpt-4.1",
  "choices": [...],
  "usage": {
    "prompt_tokens": 10,
    "completion_tokens": 20,
    "total_tokens": 30
  }
}
```

## Development

### Adding New Models

1. Update `models.json` with the new model configuration
2. Test the model individually: `python test_single_model.py new-model`
3. Run full test suite to ensure compatibility

### Creating Custom Test Scripts

Use the existing scripts as templates. Key components:
- Load configuration from `.env` and `models.json`
- Build proper request format with messages array
- Handle timeouts and errors gracefully
- Validate response structure

## Files

- `test_single_model.py` - Test individual models with options
- `test_all_models.py` - Comprehensive batch testing with reporting
- `test_conversation_isolation.py` - Verify API isolation from WebUI
- `test_openai_models.py` - Dedicated test for o1 and o3-mini models
- `test_gpt4_chat.py` - Legacy single model test
- `models.json` - Model configurations and parameters
- `requirements.txt` - Python dependencies
- `.env` - Environment configuration (create from .env.example)
- `README.md` - This documentation