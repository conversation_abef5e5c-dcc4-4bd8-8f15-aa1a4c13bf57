'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useSearchParams } from 'next/navigation';
import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import TextField from '@mui/material/TextField';
import Autocomplete from '@mui/material/Autocomplete';
import IconButton from '@mui/material/IconButton';
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import MicIcon from '@mui/icons-material/Mic';
import PublicIcon from '@mui/icons-material/Public';
import LibraryBooksIcon from '@mui/icons-material/LibraryBooks'; // New import for Prompt Gallery icon
import Button from '@mui/material/Button';
import Stack from '@mui/material/Stack';
import Tooltip from '@mui/material/Tooltip';
import Chip from '@mui/material/Chip';
import CloseIcon from '@mui/icons-material/Close';
import AutoFixHighIcon from '@mui/icons-material/AutoFixHigh';
import Snackbar from '@mui/material/Snackbar';
import Alert from '@mui/material/Alert';
import { Theme } from '@mui/material/styles';
import { event } from '@/components/genai/GoogleAnalytics';
import type { GptModel } from '@/lib/types/common';
import { selectAvailableModels } from '@/lib/store/modelSlice'; // Import model selector
import { getModelIcon } from '@/lib/utils/getModelIcon'; // Import model icon utility
import {
  selectSelectedNextModelName,
  setSelectedNextModelName,
  selectIsThinking,
  selectConversationId,
  selectConversationModel,
} from '@/lib/store/chatSlice'; // Import chat selectors/actions
import {
  selectSelectedPrompt,
  setSelectedPrompt,
} from '@/lib/store/promptSlice';
import SendIcon from '@/assets/icons/SendIcon';
import {
  modelSupportsTools,
  getFeatureUnavailableReason,
} from '@/lib/utils/modelCapabilities'; // Import model capability utilities
import { getSession } from 'next-auth/react'; // Import for JWT authentication

interface ChatInputAreaProps {
  isLoading: boolean;
  tempFileList: File[];
  setTempFileList: React.Dispatch<React.SetStateAction<File[]>>;
  handleReset: () => void;
  handleSubmit: (
    e?: React.FormEvent<HTMLFormElement> | React.MouseEvent<HTMLButtonElement>,
  ) => void;
  onTextChange: () => void;
  onKeyDown: React.KeyboardEventHandler<HTMLDivElement>;
  supportedFileList: string;
  visionModelList: string[];
  disableFileUpload: boolean;
  onClickStopButton: () => void;
  uploadFileSizeLimit: number;
  sizeExceedsMessage: string;
  setChatErrorMessage: (message: string | undefined) => void;
  messageInputRef: React.RefObject<
    HTMLTextAreaElement | HTMLInputElement | null
  >;
  useGoogleEnabled: boolean;
  setUseGoogleEnabled: (enabled: boolean) => void;
  hideModelSelector?: boolean; // Add new optional prop
  onSelectedMentionsChange?: (selectedMentions: Map<string, number[]>) => void; // Callback when selected mentions change
  clearSelectedMentionsRef?: React.MutableRefObject<(() => void) | undefined>; // Ref to function to clear selected mentions
  onMentionModelSelected?: () => void; // Callback when user explicitly selects a model via @mention
  onPromptSelect?: (prompt: any) => void;
  promptContent?: string;
  setPromptContent?: (content: string) => void;
  systemInstruction?: string | null; // New prop for system instruction
  setSystemInstruction?: (instruction: string) => void; // New prop for setting system instruction
  onPromptAssistantClick?: (currentPrompt: string) => void; // New prop for wizard icon click
}

const getSupportedMimeType = (): string => {
  const types = [
    'audio/wav',
    'audio/webm;codecs=opus',
    'audio/ogg;codecs=opus',
    'audio/mp4',
  ];
  for (const type of types) {
    if (
      typeof MediaRecorder !== 'undefined' &&
      MediaRecorder.isTypeSupported(type)
    ) {
      return type;
    }
  }
  return 'audio/webm'; // Fallback
};

// Helper function to format recording duration
const formatRecordingDuration = (seconds: number): string => {
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

const ChatInputArea: React.FC<ChatInputAreaProps> = ({
  isLoading,
  tempFileList,
  setTempFileList,
  // handleReset,
  handleSubmit,
  onTextChange,
  onKeyDown,
  supportedFileList,
  // visionModelList,
  disableFileUpload,
  // onClickStopButton,
  uploadFileSizeLimit,
  sizeExceedsMessage,
  setChatErrorMessage,
  messageInputRef,
  useGoogleEnabled,
  setUseGoogleEnabled,
  hideModelSelector = false, // Default to false
  onSelectedMentionsChange,
  clearSelectedMentionsRef,
  onMentionModelSelected,
  onPromptSelect,
  promptContent,
  setPromptContent,
  systemInstruction, // Destructure new prop
  setSystemInstruction, // Destructure new prop
  onPromptAssistantClick, // Destructure new prop
}) => {
  // Use a local state to manage the TextField's value, initialized by promptContent prop
  const [localPromptContent, setLocalPromptContent] = useState(promptContent || '');

  // Update local state when promptContent prop changes
  useEffect(() => {
    setLocalPromptContent(promptContent || '');
  }, [promptContent]);

  const LAST_USED_MODEL_LS_KEY = 'chatApp_lastUsedModelName';

  // --- Redux State ---
  const dispatch = useDispatch();
  const availableModels = useSelector(selectAvailableModels); // Get available models from Redux
  const selectedNextModelName = useSelector(selectSelectedNextModelName);
  const conversationModel = useSelector(selectConversationModel);
  const isThinking = useSelector(selectIsThinking);
  const conversationId = useSelector(selectConversationId);
  const selectedPrompt = useSelector(selectSelectedPrompt);
  const searchParams = useSearchParams(); // Get URL search parameters

  // Effect to load the last used model on initial mount
  useEffect(() => {
    if (window.location.pathname.includes('/chat/new')) {
      dispatch(setSelectedPrompt(null));
    }
  }, [dispatch]);

  useEffect(() => {
    if (selectedPrompt) {
      setLocalPromptContent(selectedPrompt);
      if (setPromptContent) {
        setPromptContent(selectedPrompt);
      }
      dispatch(setSelectedPrompt(null));
    }
  }, [selectedPrompt, dispatch, setPromptContent]);

  // Effect to load the last used model on initial mount
  useEffect(() => {
    if (availableModels && availableModels.length > 0) {
      // Ensure models are loaded
      // Skip localStorage restoration if we're in an existing conversation with a specific model already set
      // This prevents localStorage from overriding conversation-specific model selection
      if (conversationId && selectedNextModelName) {
        return;
      }

      // If we're in a conversation but no model is selected yet, wait for the conversation history to load
      // This prevents localStorage from overriding the model that should come from chat history
      if (conversationId && !selectedNextModelName) {
        return;
      }

      // Check if we have a model parameter in URL (for new chat pages)
      const modelFromUrl = searchParams.get('model');
      if (!conversationId && modelFromUrl) {
        // If we're on a new chat page with URL parameter, wait for the URL processing to complete
        // Don't apply localStorage logic if URL model parameter exists
        if (!selectedNextModelName) {
          // URL model hasn't been processed yet, wait for it
          console.log(
            `[ChatInputArea] Waiting for URL model parameter to be processed: ${modelFromUrl}`,
          );
          return;
        }
        // If selectedNextModelName is set and matches URL, we're good
        if (selectedNextModelName === modelFromUrl) {
          return;
        }
      }

      // If we're on a new chat page and a model is already selected (likely from URL parameter),
      // don't override it with localStorage or default logic
      if (!conversationId && selectedNextModelName) {
        return;
      }

      const lastModelName = localStorage.getItem(LAST_USED_MODEL_LS_KEY);
      if (lastModelName) {
        const modelExists = availableModels.some(
          (model) => model.model_name === lastModelName,
        );
        if (modelExists) {
          // Only dispatch if the current selection is null/undefined or different
          if (
            !selectedNextModelName ||
            selectedNextModelName !== lastModelName
          ) {
            dispatch(setSelectedNextModelName(lastModelName));
          }
        } else {
          localStorage.removeItem(LAST_USED_MODEL_LS_KEY);
        }
      } else if (
        !selectedNextModelName &&
        availableModels.length > 0 &&
        !conversationId
      ) {
        // If no last used model, no current selection, and not in an existing conversation, set a default
        // Only set default for new conversations, not existing ones
        dispatch(setSelectedNextModelName(availableModels[0].model_name));
      }
    }
  }, [
    dispatch,
    availableModels,
    selectedNextModelName,
    conversationId,
    searchParams,
  ]);

  // Fallback effect: Only trigger for truly new conversations where no model has been set
  // Don't fallback if we're in an existing conversation where the user previously selected a model
  useEffect(() => {
    if (
      availableModels &&
      availableModels.length > 0 &&
      !selectedNextModelName
    ) {
      // Only trigger fallback for new conversations (no conversationId)
      // For existing conversations, let the conversation maintain its model selection
      if (!conversationId) {
        const timeoutId = setTimeout(() => {
          console.log(
            '[ChatInputArea] Fallback: No model selected for new conversation, attempting fallback',
          );

          // Try localStorage first
          const lastModelName = localStorage.getItem(LAST_USED_MODEL_LS_KEY);
          if (
            lastModelName &&
            availableModels.some((model) => model.model_name === lastModelName)
          ) {
            console.log(
              `[ChatInputArea] Fallback: Using localStorage model: ${lastModelName}`,
            );
            dispatch(setSelectedNextModelName(lastModelName));
          } else {
            // Use first available model as final fallback
            console.log(
              `[ChatInputArea] Fallback: Using first available model: ${availableModels[0].model_name}`,
            );
            dispatch(setSelectedNextModelName(availableModels[0].model_name));
          }
        }, 1000); // Reduced timeout since we're only handling new conversations

        return () => clearTimeout(timeoutId);
      }
    }
  }, [availableModels, conversationId, selectedNextModelName, dispatch]);

  // Find the selected model object based on the name
  // Prioritize selectedNextModelName, fall back to conversationModel for display
  // Ensure we always have a controlled value (null instead of undefined) for MUI Autocomplete
  const displayModelName = selectedNextModelName || conversationModel;
  const selectedNextModelObject =
    availableModels.find((m) => m.model_name === displayModelName) || null; // Use null to keep Autocomplete consistently controlled

  // Handler for Autocomplete change (when an option is selected)
  const handleModelSelectionChange = (
    event: React.SyntheticEvent,
    newValue: GptModel | undefined,
  ) => {
    // Autocomplete's onChange provides undefined when cleared (if clearable) or no selection
    const newModelName = newValue ? newValue.model_name : undefined;
    dispatch(setSelectedNextModelName(newModelName));
    if (newModelName) {
      localStorage.setItem(LAST_USED_MODEL_LS_KEY, newModelName);
    } else {
      // If newValue is null (e.g. if Autocomplete was clearable, though it's not now)
      // localStorage.removeItem(LAST_USED_MODEL_LS_KEY); // Optional: clear if selection is removed
    }
  };

  // Note: Search is now universally supported via keyword extraction approach
  // No need to disable search based on tool support

  // --- @Mention State ---
  const [mentionAnchorEl, setMentionAnchorEl] = React.useState<null | HTMLElement>(
    null,
  );
  const [mentionQuery, setMentionQuery] = React.useState('');
  const [filteredMentionModels, setFilteredMentionModels] = React.useState<
    GptModel[]
  >([]);
  const mentionStartIndexRef = useRef<number>(-1);

  // Track selected model mentions - map from model name to its positions in the text
  const [selectedModelMentions, setSelectedModelMentions] = React.useState<
    Map<string, number[]>
  >(new Map());

  // Track selected index for keyboard navigation in mention dropdown
  const [selectedMentionIndex, setSelectedMentionIndex] = React.useState<number>(0);

  // Ref for the mention dropdown container
  const mentionDropdownRef = useRef<HTMLDivElement | null>(null);

  // Notify parent when selected mentions change
  useEffect(() => {
    if (onSelectedMentionsChange) {
      onSelectedMentionsChange(selectedModelMentions);
    }
  }, [selectedModelMentions, onSelectedMentionsChange]);

  // Function to clear selected mentions (can be used after message submission)
  const clearSelectedMentions = useCallback(() => {
    setSelectedModelMentions(new Map());
  }, []);

  // Expose clear function to parent component
  useEffect(() => {
    if (clearSelectedMentionsRef) {
      clearSelectedMentionsRef.current = clearSelectedMentions;
    }
  }, [clearSelectedMentions, clearSelectedMentionsRef]);

  // Scroll selected mention item into view when index changes
  useEffect(() => {
    if (mentionDropdownRef.current && mentionAnchorEl) {
      const selectedItem = mentionDropdownRef.current.querySelector(
        `[data-index="${selectedMentionIndex}"]`,
      );
      if (selectedItem) {
        selectedItem.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
        });
      }
    }
  }, [selectedMentionIndex, mentionAnchorEl]);

  // Recording States
  const [isBackendRecording, setIsBackendRecording] = React.useState(false);
  const [isProcessingAudio, setIsProcessingAudio] = React.useState(false);
  
  // Recording Duration Tracking States
  const [recordingStartTime, setRecordingStartTime] = React.useState<number | null>(null);
  const [recordingDuration, setRecordingDuration] = React.useState<number>(0);
  const recordingTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Refs
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const audioStreamRef = useRef<MediaStream | null>(null);
  const fileInputRef = useRef<HTMLInputElement | null>(null);

  // Transcript States
  const [backendTranscript, setBackendTranscript] = React.useState('');

  // Snackbar States
  const [snackbarOpen, setSnackbarOpen] = React.useState(false);
  const [snackbarMessage, setSnackbarMessage] = React.useState('');
  const [snackbarSeverity, setSnackbarSeverity] = React.useState<'success' | 'error'>(
    'error',
  );

  // Helper function to show snackbar notifications
  const showSnackbar = useCallback(
    (message: string, severity: 'success' | 'error' = 'error') => {
      setSnackbarMessage(message);
      setSnackbarSeverity(severity);
      setSnackbarOpen(true);
    },
    [],
  );

  // Function to send audio data to the backend (MediaRecorder)
  const sendAudioToBackend = useCallback(
    async (audioBlob: Blob) => {
      const formData = new FormData();
      formData.append('file', audioBlob, 'recording.wav');
      
      // Add recording duration to form data
      const durationInSeconds = recordingDuration;
      formData.append('recordingDuration', durationInSeconds.toString());
      
      console.log(`[STT] Sending audio with duration: ${durationInSeconds} seconds`);

      setIsProcessingAudio(true);
      setBackendTranscript('');
      setChatErrorMessage(undefined);
      console.log('[Backend] Sending audio...');

      try {
        // Get JWT token from session for authentication
        const session = await getSession();
        const token = session?.accessToken;
        
        // Use dynamic API base URL pattern like other API calls in the project
        const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;
        const apiUrl = `${apiBaseUrl || '/api/v0'}/general/speech/transcribe`;
        console.log(`Sending audio to: ${apiUrl}`);
        
        // Prepare headers with authentication
        const headers: HeadersInit = {};
        if (token) {
          headers['Authorization'] = `Bearer ${token}`;
          console.log('[DEBUG] STT request - Authorization header SET');
        } else {
          console.log('[DEBUG] STT request - No token found, Authorization header NOT SET');
        }
        
        const response = await fetch(apiUrl, {
          method: 'POST',
          headers,
          body: formData,
        });

        if (!response.ok) {
          const errorData = await response
            .json()
            .catch(() => ({ message: 'Failed to parse error response' }));
          throw new Error(
            errorData.message || `HTTP error! status: ${response.status}`,
          );
        }

        const result = await response.json();
        console.log('[Backend] Transcription result:', result);

        if (result.transcription && result.transcription.trim()) {
          // Replace newlines with spaces to maintain single-line display
          const cleanedTranscription = result.transcription.replace(/\n/g, ' ');
          setBackendTranscript(cleanedTranscription);
          if (messageInputRef.current) {
            const nativeInputValueSetter = Object.getOwnPropertyDescriptor(
              messageInputRef.current instanceof HTMLInputElement
                ? window.HTMLInputElement.prototype
                : window.HTMLTextAreaElement.prototype,
              'value',
            )?.set;
            nativeInputValueSetter?.call(
              messageInputRef.current,
              cleanedTranscription,
            );
            const inputEvent = new Event('input', { bubbles: true });
            messageInputRef.current.dispatchEvent(inputEvent);
            onTextChange();
          }
        } else {
          console.warn('[Backend] Received empty transcription.');
          setBackendTranscript('[No result from backend]');
          showSnackbar(
            'No speech detected. Please try again or speak louder.',
            'error',
          );
        }
      } catch (error: any) {
        console.error('[Backend] Error sending audio:', error);
        setChatErrorMessage(`Backend transcription failed: ${error.message}`);
        setBackendTranscript(`[Backend Error: ${error.message}]`);
      } finally {
        setIsProcessingAudio(false);
      }
    },
    [setChatErrorMessage, messageInputRef, onTextChange, showSnackbar, recordingDuration],
  );

  // --- Start/Stop Backend Recording (MediaRecorder) ---
  const startBackendRecording = useCallback(async () => {
    if (isBackendRecording || typeof navigator.mediaDevices === 'undefined')
      return;

    setChatErrorMessage(undefined);
    setBackendTranscript('');

    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      audioStreamRef.current = stream;
      const mimeType = getSupportedMimeType();
      const recorder = new MediaRecorder(stream, { mimeType });
      mediaRecorderRef.current = recorder;
      audioChunksRef.current = [];

      recorder.ondataavailable = (event) => {
        if (event.data.size > 0) audioChunksRef.current.push(event.data);
      };

      recorder.onstop = () => {
        const mimeType =
          mediaRecorderRef.current?.mimeType || getSupportedMimeType();
        const audioBlob = new Blob(audioChunksRef.current, { type: mimeType });
        sendAudioToBackend(audioBlob);
        audioStreamRef.current?.getTracks().forEach((track) => track.stop());
        audioStreamRef.current = null;
      };

      recorder.onerror = (event: any) => {
        console.error('[Backend] MediaRecorder error:', event.error);
        setChatErrorMessage(
          `Backend Recording error: ${event.error.message || 'Unknown error'}`,
        );
        setIsBackendRecording(false);
        setIsProcessingAudio(false);
        audioStreamRef.current?.getTracks().forEach((track) => track.stop());
        audioStreamRef.current = null;
      };

      recorder.start();
      setIsBackendRecording(true);
      
      // Start recording timer
      const startTime = Date.now();
      setRecordingStartTime(startTime);
      setRecordingDuration(0);
      
      // Update duration every 100ms for smooth UI updates
      recordingTimerRef.current = setInterval(() => {
        const elapsed = (Date.now() - startTime) / 1000; // Convert to seconds
        setRecordingDuration(elapsed);
        
        // Optional: Auto-stop after 5 minutes (300 seconds) to prevent excessive usage
        if (elapsed >= 300) {
          console.log('[STT] Auto-stopping recording after 5 minutes');
          stopBackendRecording();
        }
      }, 100);
    } catch (error: any) {
      console.error('[Backend] Error accessing microphone:', error);
      setChatErrorMessage(
        `Could not access microphone: ${error.message}. Please check permissions.`,
      );
      setIsBackendRecording(false);
    }
  }, [isBackendRecording, sendAudioToBackend, setChatErrorMessage]);

  const stopBackendRecording = useCallback(() => {
    if (
      !isBackendRecording ||
      !mediaRecorderRef.current ||
      mediaRecorderRef.current.state !== 'recording'
    )
      return;

    setTimeout(() => {
      if (
        mediaRecorderRef.current &&
        mediaRecorderRef.current.state === 'recording'
      ) {
        mediaRecorderRef.current.stop();
      }
    }, 300);
    setIsBackendRecording(false);
    
    // Stop and cleanup recording timer
    if (recordingTimerRef.current) {
      clearInterval(recordingTimerRef.current);
      recordingTimerRef.current = null;
    }
    
    console.log(`[STT] Recording stopped. Final duration: ${recordingDuration.toFixed(2)} seconds`);
  }, [isBackendRecording, recordingDuration]);

  // --- Toggle Functions for Buttons ---
  const toggleBackendRecording = useCallback(() => {
    if (isBackendRecording) {
      stopBackendRecording();
    } else {
      startBackendRecording();
    }
  }, [isBackendRecording, startBackendRecording, stopBackendRecording]);

  // --- Cleanup Effect ---
  useEffect(() => {
    return () => {
      if (
        mediaRecorderRef.current &&
        mediaRecorderRef.current.state === 'recording'
      ) {
        mediaRecorderRef.current.stop();
      }
      if (audioStreamRef.current) {
        audioStreamRef.current.getTracks().forEach((track) => track.stop());
        audioStreamRef.current = null;
      }
      // Cleanup recording timer
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current);
      }
    };
  }, []);

  // --- File Handling ---
  const handleFileSelection = (event: React.ChangeEvent<HTMLInputElement>) => {
    setChatErrorMessage(undefined);
    const files = event.target.files;
    if (!files) return;

    const newFiles: File[] = [];
    const errors: string[] = [];
    const allowedTypes = supportedFileList
      .split(',')
      .map((t) => t.trim().toLowerCase());

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const fileTypeLower = file.type.toLowerCase();
      const fileNameLower = file.name.toLowerCase();
      const fileExtension = fileNameLower.includes('.')
        ? `.${fileNameLower.split('.').pop()}`
        : '';

      const isMimeMatch = allowedTypes.includes(fileTypeLower);
      const isWildcardMatch = allowedTypes.includes(
        fileTypeLower.split('/')[0] + '/*',
      );
      const isExtensionMatch = fileExtension
        ? allowedTypes.includes(fileExtension)
        : false;

      if (!isMimeMatch && !isWildcardMatch && !isExtensionMatch) {
        errors.push(`File type not supported: ${file.name} (${file.type})`);
        continue;
      }

      if (file.size > uploadFileSizeLimit) {
        errors.push(
          `${sizeExceedsMessage}: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)}MB)`,
        );
        continue;
      }

      if (
        tempFileList.some((existingFile) => existingFile.name === file.name)
      ) {
        errors.push(`File already added: ${file.name}`);
        continue;
      }

      newFiles.push(file);
    }

    if (errors.length > 0) {
      setChatErrorMessage(errors.join('\n'));
    }

    setTempFileList((prev) => [...prev, ...newFiles]);

    if (event.target) {
      event.target.value = '';
    }
  };

  const handleRemoveFile = (fileNameToRemove: string) => {
    setTempFileList((prev) =>
      prev.filter((file) => file.name !== fileNameToRemove),
    );
  };

  // Internal handler for TextField change - Includes @Mention Logic
  const handleInternalChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      const inputElement = event.currentTarget;
      const value = inputElement.value;
      setLocalPromptContent(value); // Update local state
      if (setPromptContent) {
        setPromptContent(value); // Also call parent's setter if provided
      }
      const cursorPosition = inputElement.selectionStart ?? value.length;

    // --- @Mention Detection (only when model selector is visible) ---
    if (!hideModelSelector) {
      const textBeforeCursor = value.substring(0, cursorPosition);
      const mentionMatch = textBeforeCursor.match(/@(.*)$/);

        if (mentionMatch) {
          const query = mentionMatch[1].toLowerCase();
          setMentionQuery(query);
          mentionStartIndexRef.current = mentionMatch.index ?? -1;

          // *** Ensure availableModels is correctly referenced here ***
          const filtered = availableModels.filter(
            (
              model: GptModel, // Added type annotation
            ) =>
              model.model_name?.toLowerCase().includes(query) ||
              model.display_name?.toLowerCase().includes(query),
          );
          setFilteredMentionModels(filtered);
          setSelectedMentionIndex(0); // Reset selection when filtering

          if (filtered.length > 0) {
            setMentionAnchorEl(inputElement);
          } else {
            setMentionAnchorEl(null);
          }
        } else {
          setMentionAnchorEl(null);
          mentionStartIndexRef.current = -1;
        }
      } else {
        // When hideModelSelector is true, ensure mention dropdown is never shown
        setMentionAnchorEl(null);
        mentionStartIndexRef.current = -1;
      }
      // --- End @Mention Detection ---

      onTextChange(); // Call the parent's handler
    },
    [
      setPromptContent,
      hideModelSelector,
      availableModels,
      onTextChange,
      setMentionQuery,
      setFilteredMentionModels,
      setSelectedMentionIndex,
      setMentionAnchorEl,
    ],
  );

  // --- @Mention Selection Handler (Updated to keep @mention text as visual tag) ---
  const handleMentionSelect = useCallback((model: GptModel, event?: React.MouseEvent | React.KeyboardEvent) => {
    console.log(`[MENTION] Attempting to select model: ${model.display_name} (${model.model_name})`);
    
    // Prevent event bubbling and default behavior to avoid conflicts
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    
    if (!messageInputRef.current || mentionStartIndexRef.current === -1) {
      console.log('[MENTION] Aborting: No input ref or invalid mention start index');
      return;
    }

    const inputElement = messageInputRef.current;
    const currentValue = inputElement.value;
    // Ensure mentionStartIndexRef.current is valid before proceeding
    if (
      mentionStartIndexRef.current < 0 ||
      mentionStartIndexRef.current >= currentValue.length
    ) {
      console.error('[MENTION] Invalid mention start index');
      setMentionAnchorEl(null);
      mentionStartIndexRef.current = -1;
      return;
    }
    const mentionEndIndex =
      mentionStartIndexRef.current + 1 + mentionQuery.length;

    // Replace the incomplete @mention with the complete model name
    const mentionText = `@${model.display_name || model.model_name}`;
    const newValue =
      currentValue.substring(0, mentionStartIndexRef.current) + // Text before '@'
      mentionText + // Complete @mention text
      currentValue.substring(mentionEndIndex); // Text after the original mention query

    console.log(`[MENTION] Replacing "${currentValue.substring(mentionStartIndexRef.current, mentionEndIndex)}" with "${mentionText}"`);

    // Track this mention position for later removal during submission
    const newMentions = new Map(selectedModelMentions);
    const mentionStartPos = mentionStartIndexRef.current;
    newMentions.set(model.model_name, [mentionStartPos]);
    setSelectedModelMentions(newMentions);

    // Update input value programmatically
    const nativeInputValueSetter = Object.getOwnPropertyDescriptor(
      inputElement instanceof HTMLInputElement
        ? window.HTMLInputElement.prototype
        : window.HTMLTextAreaElement.prototype,
      'value',
    )?.set;
    nativeInputValueSetter?.call(inputElement, newValue);

    // Update local state to reflect the change
    setLocalPromptContent(newValue);
    if (setPromptContent) {
      setPromptContent(newValue);
    }

    // Dispatch state update to set the selected model
    dispatch(setSelectedNextModelName(model.model_name));
    console.log(`[MENTION] Dispatched setSelectedNextModelName: ${model.model_name}`);

    // Notify parent that user explicitly selected a model via @mention
    if (onMentionModelSelected) {
      onMentionModelSelected();
    }

    // Position cursor after the @mention text first
    const newCursorPosition = mentionStartPos + mentionText.length;
    
    // Close menu with a slight delay to ensure all state updates complete
    setTimeout(() => {
      setMentionAnchorEl(null);
      mentionStartIndexRef.current = -1;
      console.log('[MENTION] Closed dropdown and reset mention tracking');
      
      // Ensure focus and cursor position
      if (inputElement) {
        inputElement.focus();
        inputElement.setSelectionRange(newCursorPosition, newCursorPosition);
      }
    }, 50); // Small delay to ensure state updates complete

    const inputEvent = new Event('input', { bubbles: true });
    inputElement.dispatchEvent(inputEvent);
    onTextChange();
  }, [dispatch, selectedModelMentions, mentionQuery, onMentionModelSelected, onTextChange, setPromptContent]);
  // --- End @Mention Selection Handler ---

  // Custom key handler for mention navigation
  const handleKeyDown = (event: React.KeyboardEvent<HTMLDivElement>) => {
    if (mentionAnchorEl && filteredMentionModels.length > 0) {
      if (event.key === 'ArrowDown') {
        event.preventDefault();
        setSelectedMentionIndex((prev) =>
          prev < filteredMentionModels.length - 1 ? prev + 1 : prev,
        );
        return;
      } else if (event.key === 'ArrowUp') {
        event.preventDefault();
        setSelectedMentionIndex((prev) => (prev > 0 ? prev - 1 : prev));
        return;
      } else if (event.key === 'Enter') {
        event.preventDefault();
        if (filteredMentionModels[selectedMentionIndex]) {
          handleMentionSelect(filteredMentionModels[selectedMentionIndex], event);
        }
        return;
      } else if (event.key === 'Escape') {
        event.preventDefault();
        setMentionAnchorEl(null);
        mentionStartIndexRef.current = -1;
        // Ensure focus stays on input
        if (messageInputRef.current) {
          messageInputRef.current.focus();
        }
        return;
      }
    }

    // Call the original onKeyDown handler for other keys
    onKeyDown(event);
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
      {/* Display Selected Files */}
      {tempFileList && tempFileList.length > 0 && (
        <Stack
          direction="row"
          spacing={1}
          sx={{ mb: 1, flexWrap: 'wrap', px: 1 }}
        >
          {tempFileList.map((file) => (
            <Chip
              key={file.name}
              label={file.name}
              size="small"
              onDelete={() => handleRemoveFile(file.name)}
              deleteIcon={<CloseIcon fontSize="small" />}
              sx={{ maxWidth: '200px' }}
            />
          ))}
        </Stack>
      )}


      {/* Main Input Area Box - Now a Column Flex Container */}
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column', // Changed to column
          gap: hideModelSelector ? 0 : 1, // Gap between rows
          bgcolor: 'background.default',
          border: '4px solid',
          borderColor: 'primary.main',
          borderRadius: '25px',
          boxShadow: (theme) => theme.shadows[2],
          py: 1, // Adjusted padding
          px: 1,
        }}
      >
        {/* Row 1: TextField and Send Button */}
        <Box sx={{ display: 'flex', alignItems: 'flex-end', gap: 1 }}>
          {/* Input Field */}
          <TextField
            fullWidth
            multiline
            minRows={hideModelSelector ? 3 : 1}
            maxRows={5}
            variant="outlined"
            placeholder="Type your query (use @ to select model)"
            inputRef={messageInputRef as React.Ref<HTMLTextAreaElement>}
            value={localPromptContent} // Use local state for value
            onChange={handleInternalChange}
            onKeyDown={handleKeyDown}
            disabled={
              isLoading || isThinking || isProcessingAudio || isBackendRecording
            }
            sx={{
              '& .MuiOutlinedInput-root': {
                padding: 0,
                borderRadius: '14px',
                bgcolor: 'background.default',
                '& fieldset': { border: 'none' },
                '&:hover fieldset': { border: 'none' },
                '&.Mui-focused fieldset': { border: 'none' },
              },
              '& .MuiOutlinedInput-input': {
                padding: '12px',
                height: hideModelSelector ? '24px' : 'auto',
                fontSize: '1rem',
                overflowY: 'auto',
                '&::-webkit-scrollbar': {
                  width: '8px',
                },
                '&::-webkit-scrollbar-track': {
                  backgroundColor: 'transparent',
                },
                '&::-webkit-scrollbar-thumb': {
                  backgroundColor: (theme: Theme) => theme.palette.grey[400],
                  borderRadius: '4px',
                },
                '&::-webkit-scrollbar-thumb:hover': {
                  backgroundColor: (theme: Theme) => theme.palette.grey[500],
                },
              },
              '& .MuiInputBase-input::placeholder': {
                fontSize: '1rem',
                opacity: 0.8,
              },
              '& .MuiInputAdornment-root': {
                alignSelf: 'flex-end',
                paddingBottom: '12px',
              },
            }}
          />
        </Box>

        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            width: '100%',
            pt: 0.5 /* Add some padding top */,
          }}
        >
          {/* Left-aligned Buttons & Model Selector */}
          <Stack direction="row" spacing={1} alignItems="center">
            {!hideModelSelector && (
              <>
                {/* Enhanced Search Toggle Button with clearer state indication */}
                <Button
                  variant="contained"
                  startIcon={<PublicIcon fontSize="small" />}
                  onClick={() => {
                    const newSearchState = !useGoogleEnabled;
                    setUseGoogleEnabled(newSearchState);
                    event({
                      action: 'click',
                      category: 'chat_input',
                      label: 'search_toggle',
                      value: newSearchState ? 1 : 0,
                    });
                  }}
                  sx={{
                    bgcolor: useGoogleEnabled
                      ? 'primary.main'
                      : 'action.selected',
                    color: useGoogleEnabled
                      ? 'primary.contrastText'
                      : 'text.primary',
                    border: 'none',
                    borderRadius: '25px',
                    minWidth: 0,
                    width: { xs: 40, md: 140 }, // Fixed width to accommodate both text states on one line
                    px: 2,
                    fontWeight: 500,
                    fontSize: { xs: 0, md: '0.8rem' },
                    gap: 0,
                    textTransform: 'none',
                    boxShadow: 'none',
                    p: { xs: 0, md: '8px 16px' },
                    height: { xs: 40, md: 'auto' },
                    '& .MuiButton-startIcon': {
                      xs: { margin: 0 },
                      md: {
                        marginRight: '4px',
                        marginLeft: '-4px',
                      },
                    },
                    '&:hover': {
                      bgcolor: useGoogleEnabled
                        ? 'primary.dark'
                        : 'action.hover',
                      boxShadow: 'none',
                    },
                  }}
                  disabled={isLoading || isThinking}
                >
                  {useGoogleEnabled ? 'Search: ON' : 'Search: OFF'}
                </Button>
                {onPromptAssistantClick && !localPromptContent && (
                  <Button
                    variant="contained"
                    startIcon={<AutoFixHighIcon fontSize="small" />}
                    onClick={() => {
                      onPromptAssistantClick('');
                      event({
                        action: 'click',
                        category: 'chat_input',
                        label: 'help_me_write',
                        value: 1,
                      });
                    }}
                    sx={{
                      bgcolor: 'action.selected',
                      color: 'text.primary',
                      border: 'none',
                      borderRadius: '25px',
                      minWidth: 0,
                      px: 2,
                      fontWeight: 500,
                      fontSize: { xs: 0, md: '0.8rem' },
                      gap: 0,
                      textTransform: 'none',
                      boxShadow: 'none',
                      p: { xs: 0, md: '8px 16px' },
                      width: { xs: 40, md: 'auto' },
                      height: { xs: 40, md: 'auto' },
                      '& .MuiButton-startIcon': {
                        xs: { margin: 0 },
                        md: {
                          marginRight: '4px',
                          marginLeft: '-4px',
                        },
                      },
                      '&:hover': {
                        bgcolor: 'action.hover',
                        boxShadow: 'none',
                      },
                    }}
                    disabled={isLoading || isThinking}
                  >
                    Help me write
                  </Button>
                )}
              </>
            )}
          </Stack>

          {/* Right-aligned Web Speech Mic Button */}
          <Stack direction="row" spacing={1} alignItems="center">
            <Tooltip
              title={
                isProcessingAudio
                  ? 'Processing...'
                  : isBackendRecording
                    ? `Stop Mic (${formatRecordingDuration(recordingDuration)})`
                    : 'Use Mic'
              }
              arrow
            >
              <span
                style={{
                  display: 'flex',
                  width: 40,
                  height: 40,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <IconButton
                  size="medium"
                  onClick={() => {
                    toggleBackendRecording();
                    event({
                      action: 'click',
                      category: 'chat_input',
                      label: 'use_mic',
                      value: 1,
                    });
                  }}
                  disabled={isLoading || isThinking || isProcessingAudio}
                  aria-label={
                    isBackendRecording
                      ? 'Stop backend recording'
                      : 'Start backend recording'
                  }
                  sx={{
                    color: isBackendRecording ? 'error.main' : 'primary.main',
                  }}
                >
                  {isProcessingAudio ? (
                    <CircularProgress size={20} color="primary" />
                  ) : (
                    <MicIcon fontSize="medium" />
                  )}
                </IconButton>
              </span>
            </Tooltip>
            
            {/* Recording Duration Display */}
            {isBackendRecording && (
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  minWidth: '50px',
                  height: '24px',
                  px: 1,
                  backgroundColor: 'error.main',
                  color: 'error.contrastText',
                  borderRadius: 1,
                  fontSize: '0.75rem',
                  fontWeight: 'bold',
                  fontFamily: 'monospace',
                }}
              >
                {formatRecordingDuration(recordingDuration)}
              </Box>
            )}
            
            {!disableFileUpload && (
              <label htmlFor="file-input-for-chat-adornment">
                <Tooltip title="Attach Files (A)" arrow>
                  <span
                    style={{
                      display: 'flex',
                      width: 40,
                      height: 40,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <IconButton
                      component="span"
                      size="medium"
                      aria-label="attach file"
                      disabled={
                        disableFileUpload ||
                        isLoading ||
                        isThinking ||
                        isProcessingAudio ||
                        isBackendRecording
                      }
                      sx={{ color: 'primary.main' }}
                    >
                      <AttachFileIcon fontSize="medium" />
                    </IconButton>
                  </span>
                </Tooltip>
              </label>
            )}
            {onPromptAssistantClick && localPromptContent && (
              <Tooltip title="Rewrite Prompt" arrow>
                <IconButton
                  size="medium"
                  onClick={() => {
                    onPromptAssistantClick(localPromptContent);
                    event({
                      action: 'click',
                      category: 'chat_input',
                      label: 'rewrite_prompt',
                      value: 1,
                    });
                  }}
                  disabled={isLoading || isThinking}
                  aria-label="rewrite prompt"
                  sx={{ color: 'primary.main' }}
                >
                  <AutoFixHighIcon fontSize="medium" />
                </IconButton>
              </Tooltip>
            )}
            {/* Send Button */}
            <IconButton
              onClick={(e) => {
                handleSubmit(e);
                event({
                  action: 'click',
                  category: 'chat_input',
                  label: 'send_message',
                  value: 1,
                });
              }}
              disabled={
                isLoading ||
                isThinking ||
                isProcessingAudio ||
                isBackendRecording ||
                (localPromptContent.trim() === '' && tempFileList.length === 0)
              }
              aria-label="send message"
              size="medium"
              sx={{
                bgcolor: 'action.selected',
                color: 'primary.main',
                borderRadius: 25,
                width: 50,
                height: 50,
                mb: 0.5,
                '&:hover': {
                  bgcolor: 'primary.main',
                  color: 'primary.contrastText',
                },
                '&.Mui-disabled': { bgcolor: 'action.disabled' },
                transition: 'all 0.2s ease',
              }}
            >
              <SendIcon fill="currentColor" />
            </IconButton>
          </Stack>
        </Box>

        {/* Hidden File Input - ensure ID is unique */}
        <input
          id="file-input-for-chat-adornment"
          type="file"
          multiple
          hidden
          ref={fileInputRef}
          onChange={handleFileSelection}
          accept={supportedFileList}
        />
      </Box>

      {/* @Mention Suggestion Dropdown - custom implementation */}
      {!hideModelSelector &&
        mentionAnchorEl &&
        filteredMentionModels.length > 0 && (
          <Box
            ref={mentionDropdownRef}
            sx={{
              position: 'fixed',
              bgcolor: 'background.paper',
              boxShadow: 2,
              borderRadius: 1,
              maxHeight: 200,
              overflow: 'auto',
              zIndex: 1300,
              minWidth: 200,
              border: '1px solid',
              borderColor: 'divider',
            }}
            style={{
              // Position above the input area, near model chip
              bottom:
                window.innerHeight -
                mentionAnchorEl.getBoundingClientRect().top +
                5,
              left: mentionAnchorEl.getBoundingClientRect().left,
            }}
          >
            {filteredMentionModels.map((model, index) => (
              <Box
                key={model.model_name}
                data-index={index}
                onMouseDown={(event) => {
                  console.log(`[MENTION] MouseDown on ${model.display_name} (${model.model_name})`);
                  event.preventDefault(); // Prevent focus loss
                  handleMentionSelect(model, event);
                }}
                sx={{
                  px: 2,
                  py: 1,
                  cursor: 'pointer',
                  '&:hover': {
                    bgcolor: 'action.hover',
                  },
                  bgcolor:
                    selectedMentionIndex === index
                      ? 'action.selected'
                      : 'transparent',
                  fontSize: '0.875rem',
                }}
              >
                {model.display_name || model.model_name}
              </Box>
            ))}
          </Box>
        )}

      {/* Snackbar for showing notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={4000}
        onClose={() => setSnackbarOpen(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setSnackbarOpen(false)}
          severity={snackbarSeverity}
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default ChatInputArea;
