'use client';

import React from 'react';
import {
  FormControl,
  FormControlLabel,
  Radio,
  RadioGroup,
  Typography,
} from '@mui/material';
import { useAppDispatch, useAppSelector } from '@/lib/store/hooks';
import { selectTheme, setTheme, ThemeMode } from '@/lib/store/themeSlice';

const AppearanceSection = () => {
  const dispatch = useAppDispatch();
  const currentTheme = useAppSelector(selectTheme);

  const handleThemeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setTheme(event.target.value as ThemeMode));
  };

  return (
    <div>
      <Typography variant="h6" gutterBottom>
        Appearance
      </Typography>
      <FormControl component="fieldset">
        <RadioGroup
          aria-label="theme"
          name="theme"
          value={currentTheme}
          onChange={handleThemeChange}
        >
          <FormControlLabel value="light" control={<Radio />} label="Light" />
          <FormControlLabel value="dark" control={<Radio />} label="Dark" />
          <FormControlLabel
            value="system"
            control={<Radio />}
            label="System Default"
          />
        </RadioGroup>
      </FormControl>
    </div>
  );
};

export default AppearanceSection;
