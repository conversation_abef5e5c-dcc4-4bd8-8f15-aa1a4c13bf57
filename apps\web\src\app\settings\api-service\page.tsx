'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation'; // Import useRouter
import {
  Alert,
  Button,
  CircularProgress,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Box,
  IconButton,
  Snackbar,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  LinearProgress,
  Chip,
} from '@mui/material';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import RefreshIcon from '@mui/icons-material/Refresh';
import { event } from '@/components/genai/GoogleAnalytics';
// import { Layout } from '@/components/layout'; // Assuming a layout component exists
// import AccessDenied from '@/components/AccessDenied'; // Assuming an AccessDenied component

interface ModelVersionInfo {
  modelName: string;
  apiVersions: string[];
  provider: string;
  modelType?: string;
}

interface ApiKeyResponse {
  apiKey: string;
}

interface RateLimitStatus {
  model: string;
  limit: number;
  used: number;
  remaining: number;
  resetAt: string;
}

interface RateLimitUsageResponse {
  usage: RateLimitStatus[];
}

const ApiServiceSettingsPage = () => {
  const { data: session, status } = useSession();
  const router = useRouter(); // Initialize useRouter
  const [modelsData, setModelsData] = useState<ModelVersionInfo[]>([]);
  const [loadingModels, setLoadingModels] = useState(true);
  const [errorModels, setErrorModels] = useState<string | null>(null);

  const [apiKey, setApiKey] = useState<string | null>(null);
  const [generatingKey, setGeneratingKey] = useState(false);
  const [keyError, setKeyError] = useState<string | null>(null);
  const [showCopiedSnackbar, setShowCopiedSnackbar] = useState(false);
  
  const [rateLimitData, setRateLimitData] = useState<RateLimitStatus[]>([]);
  const [loadingRateLimit, setLoadingRateLimit] = useState(false);
  const [rateLimitError, setRateLimitError] = useState<string | null>(null);
  const [rateLimitExpanded, setRateLimitExpanded] = useState(false);

  // Placeholder for checking if user is authorized for API service
  // This should ideally come from the session or a dedicated user profile fetch
  const isUserApiAuthorized = session?.user?.rest === true; // Matching old project's session check

  const fetchModelsAndVersions = useCallback(async () => {
    setLoadingModels(true);
    setErrorModels(null);
    try {
      // Adjust API path if your NestJS API is namespaced, e.g., /api/llm/models-versions
      const response = await fetch('/api/rest/llm/models-versions');
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message ||
            `Failed to fetch models and versions: ${response.statusText}`,
        );
      }
      const data: ModelVersionInfo[] = await response.json();
      setModelsData(data);
    } catch (err) {
      setErrorModels(err instanceof Error ? err.message : String(err));
      setModelsData([]); // Clear data on error
    } finally {
      setLoadingModels(false);
    }
  }, []);

  const handleGenerateApiKey = async () => {
    setGeneratingKey(true);
    setKeyError(null);
    setApiKey(null);
    try {
      // Adjust API path if your NestJS API is namespaced, e.g., /api/auth/api-key
      const response = await fetch('/api/rest/auth/api-key', {
        method: 'POST',
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message ||
            `Failed to generate API key: ${response.statusText}`,
        );
      }
      const data: ApiKeyResponse = await response.json();
      setApiKey(data.apiKey);
      event({
        action: 'click',
        category: 'api_service',
        label: 'generate_api_key',
        value: 1,
      });
    } catch (err) {
      setKeyError(err instanceof Error ? err.message : String(err));
    } finally {
      setGeneratingKey(false);
    }
  };

  const handleCopyToClipboard = () => {
    if (apiKey) {
      navigator.clipboard.writeText(apiKey);
      setShowCopiedSnackbar(true);
      event({
        action: 'click',
        category: 'api_service',
        label: 'copy_api_key',
        value: 1,
      });
    }
  };
  
  const fetchRateLimitUsage = useCallback(async () => {
    const storedApiKey = localStorage.getItem('apiKey');
    if (!storedApiKey) {
      setRateLimitError('No API key found. Please generate an API key first.');
      return;
    }
    
    setLoadingRateLimit(true);
    setRateLimitError(null);
    try {
      const response = await fetch('/api/rest/rate-limit/usage', {
        headers: {
          'api-key': storedApiKey,
        },
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message || `Failed to fetch rate limit usage: ${response.statusText}`,
        );
      }
      const data: RateLimitUsageResponse = await response.json();
      setRateLimitData(data.usage);
    } catch (err) {
      setRateLimitError(err instanceof Error ? err.message : String(err));
      setRateLimitData([]);
    } finally {
      setLoadingRateLimit(false);
    }
  }, []);

  useEffect(() => {
    if (status === 'authenticated' && isUserApiAuthorized) {
      fetchModelsAndVersions();
    }
  }, [status, isUserApiAuthorized, fetchModelsAndVersions]);
  
  useEffect(() => {
    if (rateLimitExpanded && localStorage.getItem('apiKey')) {
      fetchRateLimitUsage();
      // Auto-refresh every 30 seconds when expanded
      const interval = setInterval(() => {
        fetchRateLimitUsage();
      }, 30000);
      return () => clearInterval(interval);
    }
  }, [rateLimitExpanded, fetchRateLimitUsage]);

  if (status === 'loading') {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100svh',
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  // if (!session || !isUserApiAuthorized) {
  //     // return <Layout><AccessDenied /></Layout>; // Replace with your actual Layout and AccessDenied components
  //     return <Typography variant="h6" color="error">Access Denied. You are not authorized to view this page.</Typography>;
  // }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        API Service Settings
      </Typography>
      <Paper sx={{ mb: 3, p: 2 }}>
        <Typography variant="h5" gutterBottom>
          API Key Management
        </Typography>
        {apiKey && (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              mb: 1,
              p: 1,
              border: '1px solid grey',
              borderRadius: 1,
              background: '#f5f5f5',
            }}
          >
            <Typography
              variant="body1"
              sx={{ flexGrow: 1, fontFamily: 'monospace', overflowX: 'auto' }}
            >
              {apiKey}
            </Typography>
            <IconButton onClick={handleCopyToClipboard} size="small">
              <ContentCopyIcon />
            </IconButton>
          </Box>
        )}
        <Button
          variant="contained"
          onClick={handleGenerateApiKey}
          disabled={generatingKey}
        >
          {generatingKey ? (
            <CircularProgress size={24} />
          ) : apiKey ? (
            'Regenerate API Key'
          ) : (
            'Generate API Key'
          )}
        </Button>
        {keyError && (
          <Alert severity="error" sx={{ mt: 2 }}>
            {keyError}
          </Alert>
        )}
        {!apiKey && !generatingKey && (
          <Typography variant="caption" display="block" sx={{ mt: 1 }}>
            Click to generate your unique API key. The key will only be
            displayed once.
          </Typography>
        )}
        {apiKey && (
          <Typography variant="caption" display="block" sx={{ mt: 1 }}>
            Your new API key is displayed above. Please save it securely.
            Regenerating will invalidate the old key.
          </Typography>
        )}
      </Paper>
      
      <Accordion 
        expanded={rateLimitExpanded}
        onChange={(_, expanded) => setRateLimitExpanded(expanded)}
        sx={{ mb: 3 }}
      >
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="h5">API Rate Limit Usage</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Box sx={{ mb: 2, display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              size="small"
              startIcon={<RefreshIcon />}
              onClick={fetchRateLimitUsage}
              disabled={loadingRateLimit}
            >
              Refresh
            </Button>
          </Box>
          {loadingRateLimit && <CircularProgress />}
          {rateLimitError && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {rateLimitError}
            </Alert>
          )}
          {!loadingRateLimit && !rateLimitError && rateLimitData.length === 0 && (
            <Typography>No rate limit data available.</Typography>
          )}
          {!loadingRateLimit && rateLimitData.length > 0 && (
            <TableContainer component={Paper}>
              <Table size="small" aria-label="rate limit usage table">
                <TableHead>
                  <TableRow>
                    <TableCell>Model</TableCell>
                    <TableCell align="center">Used</TableCell>
                    <TableCell align="center">Limit</TableCell>
                    <TableCell align="center">Usage</TableCell>
                    <TableCell align="center">Status</TableCell>
                    <TableCell>Reset Time</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {rateLimitData.map((item) => {
                    const usagePercentage = (item.used / item.limit) * 100;
                    const resetTime = new Date(item.resetAt).toLocaleTimeString();
                    return (
                      <TableRow key={item.model}>
                        <TableCell component="th" scope="row">
                          {item.model}
                        </TableCell>
                        <TableCell align="center">{item.used}</TableCell>
                        <TableCell align="center">{item.limit}</TableCell>
                        <TableCell align="center">
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Box sx={{ width: '100%', mr: 1 }}>
                              <LinearProgress
                                variant="determinate"
                                value={usagePercentage}
                                color={
                                  usagePercentage > 90
                                    ? 'error'
                                    : usagePercentage > 70
                                    ? 'warning'
                                    : 'primary'
                                }
                                sx={{ height: 8, borderRadius: 4 }}
                              />
                            </Box>
                            <Typography variant="body2" color="text.secondary" sx={{ minWidth: 45 }}>
                              {`${Math.round(usagePercentage)}%`}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell align="center">
                          <Chip
                            label={item.remaining > 0 ? 'Available' : 'Exhausted'}
                            color={item.remaining > 0 ? 'success' : 'error'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>{resetTime}</TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </AccordionDetails>
      </Accordion>
      
      <Typography variant="h5" gutterBottom>
        Available LLM Models & API Versions
      </Typography>
      {loadingModels && <CircularProgress />}
      {errorModels && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {errorModels}
        </Alert>
      )}
      {!loadingModels && !errorModels && modelsData.length === 0 && (
        <Typography>No models or API versions currently available.</Typography>
      )}
      {!loadingModels && modelsData.length > 0 && (
        <TableContainer component={Paper}>
          <Table
            sx={{ minWidth: 650 }}
            aria-label="llm models and versions table"
          >
            <TableHead>
              <TableRow>
                <TableCell>Model Name</TableCell>
                <TableCell>Provider</TableCell>
                <TableCell>Model Type</TableCell>
                <TableCell>
                  Available API Versions (Click to see Swagger)
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {modelsData.map((model) => (
                <TableRow key={model.modelName}>
                  <TableCell component="th" scope="row">
                    {model.modelName}
                  </TableCell>
                  <TableCell>{model.provider}</TableCell>
                  <TableCell>{model.modelType || 'N/A'}</TableCell>
                  <TableCell>
                    {model.apiVersions.map((version) => (
                      // In a real app, this would link to the Swagger UI view:
                      // e.g., <Link href={`/settings/api-service/docs/${model.modelName}/${version}`} key={version} passHref>
                      <Button
                        variant="text"
                        key={version}
                        sx={{ mr: 1, textTransform: 'none' }}
                        onClick={() => {
                          router.push(
                            `/settings/api-service/docs/${encodeURIComponent(version)}`,
                          );
                          event({
                            action: 'click',
                            category: 'api_service',
                            label: `view_docs_${version}`,
                            value: 1,
                          });
                        }}
                      >
                        {version}
                      </Button>
                    ))}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}
      <Snackbar
        open={showCopiedSnackbar}
        autoHideDuration={2000}
        onClose={() => setShowCopiedSnackbar(false)}
        message="API Key copied to clipboard!"
      />
    </Box>
  );
};

export default ApiServiceSettingsPage;
