> "./apps/api/.env"
#echo "DATABASE_URL=\"$DATABASE_URL\"" >> "./apps/api/.env"
echo "BUAM_NEXTAUTH_JWT_SECRET=\"$BUAM_NEXTAUTH_JWT_SECRET\"" >> "./apps/api/.env"
#echo "DB_ENCRYPTION_KEY_NAME=\"$DB_ENCRYPTION_KEY_NAME\"" >> "./apps/api/.env"
#echo "DB_DECRYPTION_CERT_NAME=\"$DB_DECRYPTION_CERT_NAME\"" >> "./apps/api/.env"
echo "AZURE_OPENAI_API_VERSION=\"$AZURE_OPENAI_API_VERSION\"" >> "./apps/api/.env"
echo "GENERAL_SCECIE_AZURE_OPENAI_API_INSTANCE_NAME_SWC=\"$GENERAL_SCECIE_AZURE_OPENAI_API_INSTANCE_NAME_SWC\"" >> "./apps/api/.env"
echo "GENERAL_SCECIE_AZURE_OPENAI_API_KEY_SWC=\"$GENERAL_SCECIE_AZURE_OPENAI_API_KEY_SWC\"" >> "./apps/api/.env"
echo "GENERAL_SCECIE_AZURE_OPENAI_API_INSTANCE_NAME_USSOUTH=\"$GENERAL_SCECIE_AZURE_OPENAI_API_INSTANCE_NAME_USSOUTH\"" >> "./apps/api/.env"
echo "GENERAL_SCECIE_AZURE_OPENAI_API_KEY_USSOUTH=\"$GENERAL_SCECIE_AZURE_OPENAI_API_KEY_USSOUTH\"" >> "./apps/api/.env"
echo "GENERAL_SCECIE_AZURE_OPENAI_API_INSTANCE_NAME_SWN=\"$GENERAL_SCECIE_AZURE_OPENAI_API_INSTANCE_NAME_SWN\"" >> "./apps/api/.env"
echo "GENERAL_SCECIE_AZURE_OPENAI_API_KEY_SWN=\"$GENERAL_SCECIE_AZURE_OPENAI_API_KEY_SWN\"" >> "./apps/api/.env"
echo "GENERAL_AZURE_OPENAI_API_INSTANCE_NAME_CA=\"$GENERAL_AZURE_OPENAI_API_INSTANCE_NAME_CA\"" >> "./apps/api/.env"
echo "GENERAL_AZURE_OPENAI_API_KEY_CA=\"$GENERAL_AZURE_OPENAI_API_KEY_CA\"" >> "./apps/api/.env"
echo "GENERAL_AZURE_OPENAI_API_INSTANCE_NAME_USNORTH=\"$GENERAL_AZURE_OPENAI_API_INSTANCE_NAME_USNORTH\"" >> "./apps/api/.env"
echo "GENERAL_AZURE_OPENAI_API_KEY_USNORTH=\"$GENERAL_AZURE_OPENAI_API_KEY_USNORTH\"" >> "./apps/api/.env"
echo "GENERAL_AZURE_OPENAI_API_INSTANCE_NAME_AU=\"$GENERAL_AZURE_OPENAI_API_INSTANCE_NAME_AU\"" >> "./apps/api/.env"
echo "GENERAL_AZURE_OPENAI_API_KEY_AU=\"$GENERAL_AZURE_OPENAI_API_KEY_AU\"" >> "./apps/api/.env"
echo "GENERAL_AZURE_OPENAI_API_INSTANCE_NAME_USEAST=\"$GENERAL_AZURE_OPENAI_API_INSTANCE_NAME_USEAST\"" >> "./apps/api/.env"
echo "GENERAL_AZURE_OPENAI_API_KEY_USEAST=\"$GENERAL_AZURE_OPENAI_API_KEY_USEAST\"" >> "./apps/api/.env"
echo "GENERAL_AZURE_OPENAI_API_INSTANCE_NAME_UK=\"$GENERAL_AZURE_OPENAI_API_INSTANCE_NAME_UK\"" >> "./apps/api/.env"
echo "GENERAL_AZURE_OPENAI_API_KEY_UK=\"$GENERAL_AZURE_OPENAI_API_KEY_UK\"" >> "./apps/api/.env"
echo "GENERAL_AZURE_OPENAI_API_INSTANCE_NAME_USEAST2=\"$GENERAL_AZURE_OPENAI_API_INSTANCE_NAME_USEAST2\"" >> "./apps/api/.env"
echo "GENERAL_AZURE_OPENAI_API_KEY_USEAST2=\"$GENERAL_AZURE_OPENAI_API_KEY_USEAST2\"" >> "./apps/api/.env"
echo "GENERAL_AZURE_OPENAI_API_INSTANCE_NAME_USWEST=\"$GENERAL_AZURE_OPENAI_API_INSTANCE_NAME_USWEST\"" >> "./apps/api/.env"
echo "GENERAL_AZURE_OPENAI_API_KEY_USWEST=\"$GENERAL_AZURE_OPENAI_API_KEY_USWEST\"" >> "./apps/api/.env"
echo "GENERAL_AZURE_OPENAI_API_INSTANCE_NAME_USWEST3=\"$GENERAL_AZURE_OPENAI_API_INSTANCE_NAME_USWEST3\"" >> "./apps/api/.env"
echo "GENERAL_AZURE_OPENAI_API_KEY_USWEST3=\"$GENERAL_AZURE_OPENAI_API_KEY_USWEST3\"" >> "./apps/api/.env"
echo "GENERAL_AZURE_OPENAI_API_INSTANCE_NAME_JP=\"$GENERAL_AZURE_OPENAI_API_INSTANCE_NAME_JP\"" >> "./apps/api/.env"
echo "GENERAL_AZURE_OPENAI_API_KEY_JP=\"$GENERAL_AZURE_OPENAI_API_KEY_JP\"" >> "./apps/api/.env"
echo "GENERAL_AZURE_OPENAI_API_INSTANCE_NAME_FR=\"$GENERAL_AZURE_OPENAI_API_INSTANCE_NAME_FR\"" >> "./apps/api/.env"
echo "GENERAL_AZURE_OPENAI_API_KEY_FR=\"$GENERAL_AZURE_OPENAI_API_KEY_FR\"" >> "./apps/api/.env"
echo "AZURE_OCR_KEY=\"$AZURE_OCR_KEY\"" >> "./apps/api/.env"
echo "AZURE_OCR_ENDPOINT=\"$AZURE_OCR_ENDPOINT\"" >> "./apps/api/.env"
echo "AZURE_OCR_SCECIE_KEY=\"$AZURE_OCR_SCECIE_KEY\"" >> "./apps/api/.env"
echo "AZURE_OCR_SCECIE_ENDPOINT=\"$AZURE_OCR_SCECIE_ENDPOINT\"" >> "./apps/api/.env"
echo "GOOGLE_API_KEY=\"$GOOGLE_API_KEY\"" >> "./apps/api/.env"
echo "GOOGLE_CSE_API_KEY=\"$GOOGLE_CSE_API_KEY\"" >> "./apps/api/.env"
echo "GOOGLE_CSE_ID=\"$GOOGLE_CSE_ID\"" >> "./apps/api/.env"
echo "GENERAL_SCECIE_DEEPSEEK_API_INSTANCE_NAME_USSOUTH=\"$GENERAL_SCECIE_DEEPSEEK_API_INSTANCE_NAME_USSOUTH\"" >> "./apps/api/.env"
echo "GENERAL_SCECIE_DEEPSEEK_API_KEY_USSOUTH=\"$GENERAL_SCECIE_DEEPSEEK_API_KEY_USSOUTH\"" >> "./apps/api/.env"
echo "GENERAL_DEEPSEEK_API_INSTANCE_NAME_EASTUS=\"$GENERAL_DEEPSEEK_API_INSTANCE_NAME_EASTUS\"" >> "./apps/api/.env"
echo "GENERAL_DEEPSEEK_API_KEY_EASTUS=\"$GENERAL_DEEPSEEK_API_KEY_EASTUS\"" >> "./apps/api/.env"
echo "GENERAL_DEEPSEEK_API_INSTANCE_NAME_USNORTH=\"$GENERAL_DEEPSEEK_API_INSTANCE_NAME_USNORTH\"" >> "./apps/api/.env"
echo "GENERAL_DEEPSEEK_API_KEY_USNORTH=\"$GENERAL_DEEPSEEK_API_KEY_USNORTH\"" >> "./apps/api/.env"
echo "GENERAL_DEEPSEEK_API_INSTANCE_NAME_USWEST=\"$GENERAL_DEEPSEEK_API_INSTANCE_NAME_USWEST\"" >> "./apps/api/.env"
echo "GENERAL_DEEPSEEK_API_KEY_USWEST=\"$GENERAL_DEEPSEEK_API_KEY_USWEST\"" >> "./apps/api/.env"
echo "DASHSCOPE_API_KEY=\"$DASHSCOPE_API_KEY\"" >> "./apps/api/.env"
echo "ALIBABA_MODELSTUDIO_API_KEY=\"$ALIBABA_MODELSTUDIO_API_KEY\"" >> "./apps/api/.env"
echo "ALIBABA_MODELSTUDIO_ENDPOINT=\"$ALIBABA_MODELSTUDIO_ENDPOINT\"" >> "./apps/api/.env"
echo "GOOGLE_VERTEXAI_REGION=\"$GOOGLE_VERTEXAI_REGION\"" >> "./apps/api/.env"
echo "GOOGLE_VERTEXAI_PROJECT_ID=\"$GOOGLE_VERTEXAI_PROJECT_ID\"" >> "./apps/api/.env"
echo "GOOGLE_VERTEX_AI_KEY=\"$GOOGLE_VERTEX_AI_KEY\"" >> "./apps/api/.env"
echo "GOOGLE_APPLICATION_CREDENTIALS=\"$GOOGLE_APPLICATION_CREDENTIALS\"" >> "./apps/api/.env"
echo "AZURE_AI_SERVICE_ENDPOINT=\"$AZURE_AI_SERVICE_ENDPOINT\"" >> "./apps/api/.env"
echo "AZURE_AI_SERVICE_KEY=\"$AZURE_AI_SERVICE_KEY\"" >> "./apps/api/.env"
echo "AZURE_AI_SERVICE_DEEPSEEK_ENDPOINT=\"$AZURE_AI_SERVICE_DEEPSEEK_ENDPOINT\"" >> "./apps/api/.env"
echo "AZURE_AI_SERVICE_DEEPSEEK_KEY=\"$AZURE_AI_SERVICE_DEEPSEEK_KEY\"" >> "./apps/api/.env"
echo "AZURE_AI_SERVICE_DEEPSEEK_API_VERSION=\"$AZURE_AI_SERVICE_DEEPSEEK_API_VERSION\"" >> "./apps/api/.env"
echo "FEEDBACK_EMAIL=\"$FEEDBACK_EMAIL\"" >> "./apps/api/.env"
echo "HTTPS_PROXY=\"$HTTPS_PROXY\"" >> "./apps/api/.env"
echo "NODE_ENV=\"$NODE_ENV\"" >> "./apps/api/.env"
echo "PORT=\"$PORT\"" >> "./apps/api/.env"
echo "CORS_ORIGIN=\"$CORS_ORIGIN\"" >> "./apps/api/.env"
echo "JWT_SECRET=\"$JWT_SECRET\"" >> "./apps/api/.env"
echo "DAILY_TOKEN_LIMIT=\"$DAILY_TOKEN_LIMIT\"" >> "./apps/api/.env"
echo "MONTHLY_TOKEN_LIMIT=\"$MONTHLY_TOKEN_LIMIT\"" >> "./apps/api/.env"
echo "ENV=\"$ENV\"" >> "./apps/api/.env"
echo "MAX_TOKEN_PROMPT=\"$MAX_TOKEN_PROMPT\"" >> "./apps/api/.env"
echo "RATE_LIMIT_EMAIL=\"$RATE_LIMIT_EMAIL\"" >> "./apps/api/.env"
echo "VISION_ENDPOINT=\"$VISION_ENDPOINT\"" >> "./apps/api/.env"
echo "STAFF_MONTHLY_TOKEN_LIMIT=\"$STAFF_MONTHLY_TOKEN_LIMIT\"" >> "./apps/api/.env"
echo "STUDENT_MONTHLY_TOKEN_LIMIT=\"$STUDENT_MONTHLY_TOKEN_LIMIT\"" >> "./apps/api/.env"
echo "AZURE_OPENAI_API_DEPLOYMENT_NAME=\"$AZURE_OPENAI_API_DEPLOYMENT_NAME\"" >> "./apps/api/.env"
echo "AZURE_OPENAI_API_MODEL_NAME=\"$AZURE_OPENAI_API_MODEL_NAME\"" >> "./apps/api/.env"
echo "REDIS_HOST=\"$REDIS_HOST\"" >> "./apps/api/.env"
echo "REDIS_PORT=\"$REDIS_PORT\"" >> "./apps/api/.env"
#echo "REDIS_PASSWORD=\"$REDIS_PASSWORD\"" >> "./apps/api/.env"
echo "REDIS_DB=\"$REDIS_DB\"" >> "./apps/api/.env"
echo "REDIS_HOST_1=\"$REDIS_HOST_1\"" >> "./apps/api/.env"
echo "REDIS_PORT_1=\"$REDIS_PORT_1\"" >> "./apps/api/.env"
#echo "REDIS_PASSWORD_1=\"$REDIS_PASSWORD_1\"" >> "./apps/api/.env"
echo "REDIS_DB_1=\"$REDIS_DB_1\"" >> "./apps/api/.env"
echo "REDIS_SHOW_FRIENDLY_ERROR_STACK_1=\"$REDIS_SHOW_FRIENDLY_ERROR_STACK_1\"" >> "./apps/api/.env"
echo "REDIS_IP_1=\"$REDIS_IP_1\"" >> "./apps/api/.env"
echo "REDIS_ROLE_1=\"$REDIS_ROLE_1\"" >> "./apps/api/.env"
echo "REDIS_HOST_2=\"$REDIS_HOST_2\"" >> "./apps/api/.env"
echo "REDIS_PORT_2=\"$REDIS_PORT_2\"" >> "./apps/api/.env"
#echo "REDIS_PASSWORD_2=\"$REDIS_PASSWORD_2\"" >> "./apps/api/.env"
echo "REDIS_DB_2=\"$REDIS_DB_2\"" >> "./apps/api/.env"
echo "REDIS_SHOW_FRIENDLY_ERROR_STACK_2=\"$REDIS_SHOW_FRIENDLY_ERROR_STACK_2\"" >> "./apps/api/.env"
echo "REDIS_IP_2=\"$REDIS_IP_2\"" >> "./apps/api/.env"
echo "REDIS_ROLE_2=\"$REDIS_ROLE_2\"" >> "./apps/api/.env"
echo "REDIS_HOST_3=\"$REDIS_HOST_3\"" >> "./apps/api/.env"
echo "REDIS_PORT_3=\"$REDIS_PORT_3\"" >> "./apps/api/.env"
#echo "REDIS_PASSWORD_3=\"$REDIS_PASSWORD_3\"" >> "./apps/api/.env"
echo "REDIS_DB_3=\"$REDIS_DB_3\"" >> "./apps/api/.env"
echo "REDIS_SHOW_FRIENDLY_ERROR_STACK_3=\"$REDIS_SHOW_FRIENDLY_ERROR_STACK_3\"" >> "./apps/api/.env"
echo "REDIS_IP_3=\"$REDIS_IP_3\"" >> "./apps/api/.env"
echo "REDIS_ROLE_3=\"$REDIS_ROLE_3\"" >> "./apps/api/.env"
echo "REDIS_HOST_4=\"$REDIS_HOST_4\"" >> "./apps/api/.env"
echo "REDIS_PORT_4=\"$REDIS_PORT_4\"" >> "./apps/api/.env"
#echo "REDIS_PASSWORD_4=\"$REDIS_PASSWORD_4\"" >> "./apps/api/.env"
echo "REDIS_DB_4=\"$REDIS_DB_4\"" >> "./apps/api/.env"
echo "REDIS_SHOW_FRIENDLY_ERROR_STACK_4=\"$REDIS_SHOW_FRIENDLY_ERROR_STACK_4\"" >> "./apps/api/.env"
echo "REDIS_IP_4=\"$REDIS_IP_4\"" >> "./apps/api/.env"
echo "REDIS_ROLE_4=\"$REDIS_ROLE_4\"" >> "./apps/api/.env"
echo "REDIS_HOST_5=\"$REDIS_HOST_5\"" >> "./apps/api/.env"
echo "REDIS_PORT_5=\"$REDIS_PORT_5\"" >> "./apps/api/.env"
#echo "REDIS_PASSWORD_5=\"$REDIS_PASSWORD_5\"" >> "./apps/api/.env"
echo "REDIS_DB_5=\"$REDIS_DB_5\"" >> "./apps/api/.env"
echo "REDIS_SHOW_FRIENDLY_ERROR_STACK_5=\"$REDIS_SHOW_FRIENDLY_ERROR_STACK_5\"" >> "./apps/api/.env"
echo "REDIS_IP_5=\"$REDIS_IP_5\"" >> "./apps/api/.env"
echo "REDIS_ROLE_5=\"$REDIS_ROLE_5\"" >> "./apps/api/.env"
echo "REDIS_HOST_6=\"$REDIS_HOST_6\"" >> "./apps/api/.env"
echo "REDIS_PORT_6=\"$REDIS_PORT_6\"" >> "./apps/api/.env"
#echo "REDIS_PASSWORD_6=\"$REDIS_PASSWORD_6\"" >> "./apps/api/.env"
echo "REDIS_DB_6=\"$REDIS_DB_6\"" >> "./apps/api/.env"
echo "REDIS_SHOW_FRIENDLY_ERROR_STACK_6=\"$REDIS_SHOW_FRIENDLY_ERROR_STACK_6\"" >> "./apps/api/.env"
echo "REDIS_IP_6=\"$REDIS_IP_6\"" >> "./apps/api/.env"
echo "REDIS_ROLE_6=\"$REDIS_ROLE_6\"" >> "./apps/api/.env"
echo "VISION_SCECIE_ENDPOINT=\"$VISION_SCECIE_ENDPOINT\"" >> "./apps/api/.env"
echo "SCERESTAPI_ENDPOINT=\"$SCERESTAPI_ENDPOINT\"" >> "./apps/api/.env"
echo "SCERESTAPI_KEY=\"$SCERESTAPI_KEY\"" >> "./apps/api/.env"
echo "TOTAL_STUDENT=\"$TOTAL_STUDENT\"" >> "./apps/api/.env"
echo "TOTAL_STAFF=\"$TOTAL_STAFF\"" >> "./apps/api/.env"
echo "USAGE_ALERT_EMAIL=\"$USAGE_ALERT_EMAIL\"" >> "./apps/api/.env"
echo "SLES_API_ENDPOINT=\"$SLES_API_ENDPOINT\"" >> "./apps/api/.env"
echo "VERTEX_AI_PROJECT_NAME=\"$VERTEX_AI_PROJECT_NAME\"" >> "./apps/api/.env"
echo "VERTEX_AI_PROJECT_LOCATION=\"$VERTEX_AI_PROJECT_LOCATION\"" >> "./apps/api/.env"
echo "BASE64_FILE_PATH=\"$BASE64_FILE_PATH\"" >> "./apps/api/.env"
echo "GENERAL_AZURE_AI_SERVICE_ENDPOINT=\"$GENERAL_AZURE_AI_SERVICE_ENDPOINT\"" >> "./apps/api/.env"
echo "GEMINI_GROUNDING_DAILY_USAGE_LIMIT=\"$GEMINI_GROUNDING_DAILY_USAGE_LIMIT\"" >> "./apps/api/.env"
echo "AZURE_TENANT_ID=\"$AZURE_TENANT_ID\"" >> "./apps/api/.env"
echo "AZURE_CLIENT_ID=\"$AZURE_CLIENT_ID\"" >> "./apps/api/.env"
echo "AZURE_CLIENT_SECRET=\"$AZURE_CLIENT_SECRET\"" >> "./apps/api/.env"
echo "KEY_VAULT_NAME=\"$KEY_VAULT_NAME\"" >> "./apps/api/.env"
echo "ALIBABA_MODELSTUDIO_ENDPOINT=\"$ALIBABA_MODELSTUDIO_ENDPOINT\"" >> "./apps/api/.env"
echo "AZURE_OPEN_AI_BASE=\"$AZURE_OPEN_AI_BASE\"" >> "./apps/api/.env"
echo "AZURE_OPEN_AI_CHAT_VERSION=\"$AZURE_OPEN_AI_CHAT_VERSION\"" >> "./apps/api/.env"
echo "AZURE_OPENAI_EMBEDDING_ENDPOINT=\"$AZURE_OPENAI_EMBEDDING_ENDPOINT\"" >> "./apps/api/.env"
echo "AZURE_OPENAI_ENDPOINT=\"$AZURE_OPENAI_ENDPOINT\"" >> "./apps/api/.env"
echo "AZURE_OPENAI_SPEECH_ENDPOINT=\"$AZURE_OPENAI_SPEECH_ENDPOINT\"" >> "./apps/api/.env"
echo "AZURE_OPENAI_SPEECH_API_KEY=\"$AZURE_OPENAI_SPEECH_API_KEY\"" >> "./apps/api/.env"
echo "AZURE_OPENAI_SPEECH_API_VERSION=\"$AZURE_OPENAI_SPEECH_API_VERSION\"" >> "./apps/api/.env"
echo "MAILER_HOST=\"$MAILER_HOST\"" >> "./apps/api/.env"
echo "MAILER_PORT=\"$MAILER_PORT\"" >> "./apps/api/.env"
echo "MAILER_USER=\"$MAILER_USER\"" >> "./apps/api/.env"
echo "MAILER_PASSWORD=\"$MAILER_PASSWORD\"" >> "./apps/api/.env"
echo "MAILER_FROM=\"$MAILER_FROM\"" >> "./apps/api/.env"
echo "BASE_PATH=\"$BASE_PATH\"" >> "./apps/api/.env"
echo "STT_STAFF_MONTHLY_TOKEN_LIMIT=\"$STT_STAFF_MONTHLY_TOKEN_LIMIT\"" >> "./apps/api/.env"
echo "STT_STUDENT_MONTHLY_TOKEN_LIMIT=\"$STT_STUDENT_MONTHLY_TOKEN_LIMIT\"" >> "./apps/api/.env"
echo "PROMPT_REWRITE_STAFF_MONTHLY_TOKEN_LIMIT=\"$PROMPT_REWRITE_STAFF_MONTHLY_TOKEN_LIMIT\"" >> "./apps/api/.env"
echo "PROMPT_REWRITE_STUDENT_MONTHLY_TOKEN_LIMIT=\"$PROMPT_REWRITE_STUDENT_MONTHLY_TOKEN_LIMIT\"" >> "./apps/api/.env"
echo "RUN_CRON_JOBS=\"$RUN_CRON_JOBS\"" >> "./apps/api/.env"
