import { Injectable, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { PrismaService } from '../prisma/prisma.service'; // Assuming user validation involves DB lookup

// Define the expected payload structure based on how you sign the token
interface JwtPayload {
  sub: string; // Typically the user ID (maps to userId)
  email: string;
  name?: string; // Optional: NextAuth often includes name if available in profile
  type: string; // e.g., 'STAFF', 'STUDENT' (from custom claims)
  dept_unit_code: string; // (from custom claims)
  // Standard JWT claims like iat, exp, jti will also be present but not explicitly typed here
  // unless needed for validation logic.
  // Custom claims like 'rest' or 'accessToken' from NextAuth.js token can be added if needed by API.
}

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private configService: ConfigService,
    private prisma: PrismaService, // Inject PrismaService if DB validation is needed
  ) {
    const secret = configService.get<string>('BUAM_NEXTAUTH_JWT_SECRET'); // Read the aligned secret name
    if (!secret) {
      throw new Error(
        'BUAM_NEXTAUTH_JWT_SECRET environment variable is not set',
      ); // Update error message
    }
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(), // Standard way to extract token
      ignoreExpiration: false, // Ensure token expiration is checked
      secretOrKey: secret, // Use the validated secret
    });
  }

  /**
   * This method is called after the token is successfully verified.
   * It receives the decoded payload.
   * You should perform any additional validation here (e.g., check if user exists/is active).
   * The return value is attached to request.user.
   */
  async validate(payload: JwtPayload) {
    // console.log('JWT Payload received:', payload); // Debugging line

    // Example: Validate if the user still exists in the database
    // const user = await this.prisma.user.findUnique({ where: { id: payload.sub } });
    // if (!user) {
    //   throw new UnauthorizedException('User not found');
    // }
    // if (!user.isActive) { // Example: Check if user is active
    //    throw new UnauthorizedException('User account is inactive');
    // }

    // Return the payload or a custom user object to be attached to request.user
    // For simplicity now, just return the essential parts of the payload
    // Ensure the returned object matches the AuthenticatedUser interface
    // and includes all necessary fields derived from the JWT payload.
    return {
      userId: payload.sub,
      email: payload.email,
      type: payload.type,
      dept_unit_code: payload.dept_unit_code,
      // name: payload.name, // Include if AuthenticatedUser interface has a name field
    };
    // Or return the full user object if fetched: return user;
  }
}
