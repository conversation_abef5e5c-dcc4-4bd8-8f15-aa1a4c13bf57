{"description": "Template definitions for multi-round conversation testing", "version": "1.0.0", "templates": {"customer_support": {"name": "Customer Support Simulation", "description": "Simulates a customer support conversation", "rounds": [{"role": "user", "content": "I'm having trouble logging into my account. Can you help?", "context_keywords": ["login", "account", "trouble", "help"], "expected_response_elements": ["help", "login", "troubleshoot", "steps"]}, {"role": "user", "content": "I tried resetting my password but didn't receive the email.", "context_keywords": ["password", "reset", "email", "receive"], "expected_response_elements": ["email", "spam", "wait", "check"]}, {"role": "user", "content": "I found it in my spam folder. What should I do next?", "context_keywords": ["spam", "found", "next", "steps"], "expected_response_elements": ["click", "link", "follow", "instructions"]}]}, "educational_tutoring": {"name": "Educational Tutoring Session", "description": "Progressive learning conversation", "rounds": [{"role": "user", "content": "Can you explain what photosynthesis is?", "context_keywords": ["photosynthesis", "explain", "process"], "expected_response_elements": ["plants", "sunlight", "carbon dioxide", "oxygen"]}, {"role": "user", "content": "What role does chlorophyll play in this process?", "context_keywords": ["chlorophyll", "role", "process", "photosynthesis"], "expected_response_elements": ["chlorophyll", "green", "absorb", "sunlight"]}, {"role": "user", "content": "Why are most plants green then?", "context_keywords": ["plants", "green", "color", "chlorophyll"], "expected_response_elements": ["chlorophyll", "green", "reflect", "absorb"]}]}, "creative_writing": {"name": "Creative Writing Collaboration", "description": "Collaborative story development", "rounds": [{"role": "user", "content": "Let's write a short story together. Start with: 'The old lighthouse keeper noticed something strange in the fog.'", "context_keywords": ["lighthouse", "keeper", "strange", "fog", "story"], "expected_response_elements": ["lighthouse", "fog", "mysterious", "story"]}, {"role": "user", "content": "What did the lighthouse keeper see in the fog?", "context_keywords": ["lighthouse", "keeper", "see", "fog"], "expected_response_elements": ["saw", "fog", "shape", "mysterious"]}, {"role": "user", "content": "How should our story continue from here?", "context_keywords": ["story", "continue", "lighthouse", "fog"], "expected_response_elements": ["continue", "story", "next", "happened"]}]}, "technical_troubleshooting": {"name": "Technical Troubleshooting", "description": "IT support style conversation", "rounds": [{"role": "user", "content": "My computer is running very slowly. What could be causing this?", "context_keywords": ["computer", "slow", "running", "causing"], "expected_response_elements": ["slow", "causes", "memory", "processes"]}, {"role": "user", "content": "I notice my hard drive is almost full. Could that be the issue?", "context_keywords": ["hard drive", "full", "issue", "slow"], "expected_response_elements": ["hard drive", "space", "performance", "clean"]}, {"role": "user", "content": "What's the best way to free up space safely?", "context_keywords": ["free", "space", "safely", "hard drive"], "expected_response_elements": ["delete", "files", "safe", "cleanup"]}]}, "recipe_development": {"name": "Recipe Development", "description": "Cooking conversation with ingredient modifications", "rounds": [{"role": "user", "content": "I want to make chocolate chip cookies. What ingredients do I need?", "context_keywords": ["chocolate chip cookies", "ingredients", "need"], "expected_response_elements": ["flour", "sugar", "butter", "chocolate chips"]}, {"role": "user", "content": "I don't have butter. What can I substitute?", "context_keywords": ["butter", "substitute", "replace", "cookies"], "expected_response_elements": ["substitute", "oil", "margarine", "applesauce"]}, {"role": "user", "content": "How will using oil instead of butter affect the texture?", "context_keywords": ["oil", "butter", "texture", "affect", "cookies"], "expected_response_elements": ["texture", "softer", "less crispy", "difference"]}]}, "product_research": {"name": "Product Research", "description": "Product comparison and recommendation", "rounds": [{"role": "user", "content": "I'm looking for a new smartphone under $500. What would you recommend?", "context_keywords": ["smartphone", "under $500", "recommend"], "expected_response_elements": ["smartphone", "budget", "recommend", "features"]}, {"role": "user", "content": "Battery life is very important to me. Which phones have the best battery?", "context_keywords": ["battery life", "important", "best battery", "phones"], "expected_response_elements": ["battery", "life", "hours", "long lasting"]}, {"role": "user", "content": "Between the phones you mentioned, which has the best camera?", "context_keywords": ["phones", "mentioned", "best camera", "between"], "expected_response_elements": ["camera", "quality", "photos", "better"]}]}}, "conversation_settings": {"default_timeout": 30, "context_validation": {"keyword_threshold": 0.4, "response_relevance_check": true}, "progression_rules": {"stop_on_failure": false, "max_rounds": 5, "require_context_in_all_rounds": true}}}