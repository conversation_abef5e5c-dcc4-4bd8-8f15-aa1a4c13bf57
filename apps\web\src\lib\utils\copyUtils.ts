/**
 * Utility functions for copying chat message content to clipboard
 * Supports Word-compatible formatting for tables and rich content
 */

/**
 * Converts markdown content to clipboard-friendly format
 * Handles tables, lists, code blocks, and other markdown elements
 * @param markdownContent - The markdown content to convert
 * @returns Formatted text suitable for clipboard
 */
export function convertMarkdownForClipboard(markdownContent: string): string {
  if (!markdownContent) return '';

  let content = markdownContent;

  // Remove citation links in markdown format [text](url)
  content = content.replace(/\[(\d+)\]\([^)]+\)/g, '[$1]');

  // Convert tables to tab-separated format for plain text fallback
  content = convertTablesToTSV(content);

  // Convert headings
  content = content.replace(/^#{1,6}\s+(.+)$/gm, (match, heading) => {
    return heading.trim() + '\n';
  });

  // Convert bold and italic formatting
  content = content.replace(/\*\*\*(.+?)\*\*\*/g, '$1'); // Bold italic
  content = content.replace(/\*\*(.+?)\*\*/g, '$1'); // Bold
  content = content.replace(/\*(.+?)\*/g, '$1'); // Italic
  content = content.replace(/__(.+?)__/g, '$1'); // Bold (underscore)
  content = content.replace(/_(.+?)_/g, '$1'); // Italic (underscore)

  // Convert unordered lists
  content = content.replace(/^\s*[-*+]\s+(.+)$/gm, '• $1');

  // Convert ordered lists
  content = content.replace(/^\s*(\d+)\.\s+(.+)$/gm, '$1. $2');

  // Convert code blocks - preserve indentation
  content = content.replace(/```[\w]*\n([\s\S]*?)```/g, (match, code) => {
    return code.trim();
  });

  // Convert inline code
  content = content.replace(/`([^`]+)`/g, '$1');

  // Convert blockquotes
  content = content.replace(/^\s*>\s+(.+)$/gm, '$1');

  // Convert links to text with URL
  content = content.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '$1 ($2)');

  // Clean up extra whitespace but preserve intentional line breaks
  content = content.replace(/\n{3,}/g, '\n\n');

  return content.trim();
}

/**
 * Converts markdown content to HTML format for rich clipboard copying
 * Preserves table structure, formatting, and other rich elements
 * @param markdownContent - The markdown content to convert
 * @returns HTML formatted content
 */
export function convertMarkdownToHTML(markdownContent: string): string {
  if (!markdownContent) return '';

  let content = markdownContent;

  // Remove citation links in markdown format [text](url)
  content = content.replace(/\[(\d+)\]\([^)]+\)/g, '[$1]');

  // Convert tables to HTML format
  content = convertTablesToHTML(content);

  // Convert headings
  content = content.replace(/^#{6}\s+(.+)$/gm, '<h6>$1</h6>');
  content = content.replace(/^#{5}\s+(.+)$/gm, '<h5>$1</h5>');
  content = content.replace(/^#{4}\s+(.+)$/gm, '<h4>$1</h4>');
  content = content.replace(/^#{3}\s+(.+)$/gm, '<h3>$1</h3>');
  content = content.replace(/^#{2}\s+(.+)$/gm, '<h2>$1</h2>');
  content = content.replace(/^#{1}\s+(.+)$/gm, '<h1>$1</h1>');

  // Convert bold and italic formatting
  content = content.replace(/\*\*\*(.+?)\*\*\*/g, '<strong><em>$1</em></strong>'); // Bold italic
  content = content.replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>'); // Bold
  content = content.replace(/\*(.+?)\*/g, '<em>$1</em>'); // Italic
  content = content.replace(/__(.+?)__/g, '<strong>$1</strong>'); // Bold (underscore)
  content = content.replace(/_(.+?)_/g, '<em>$1</em>'); // Italic (underscore)

  // Convert unordered lists
  content = content.replace(/^(\s*[-*+]\s+.+)$/gm, (match) => {
    const lines = match.split('\n').filter(line => line.trim());
    const listItems = lines.map(line => {
      const content = line.replace(/^\s*[-*+]\s+/, '');
      return `<li>${content}</li>`;
    }).join('');
    return `<ul>${listItems}</ul>`;
  });

  // Convert ordered lists
  content = content.replace(/^(\s*\d+\.\s+.+)$/gm, (match) => {
    const lines = match.split('\n').filter(line => line.trim());
    const listItems = lines.map(line => {
      const content = line.replace(/^\s*\d+\.\s+/, '');
      return `<li>${content}</li>`;
    }).join('');
    return `<ol>${listItems}</ol>`;
  });

  // Convert code blocks
  content = content.replace(/```[\w]*\n([\s\S]*?)```/g, (match, code) => {
    return `<pre><code>${escapeHtml(code.trim())}</code></pre>`;
  });

  // Convert inline code
  content = content.replace(/`([^`]+)`/g, '<code>$1</code>');

  // Convert blockquotes
  content = content.replace(/^\s*>\s+(.+)$/gm, '<blockquote>$1</blockquote>');

  // Convert links
  content = content.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>');

  // Convert line breaks to HTML
  content = content.replace(/\n\n/g, '</p><p>');
  content = content.replace(/\n/g, '<br>');

  // Wrap in paragraph tags if not already wrapped
  if (!content.startsWith('<')) {
    content = `<p>${content}</p>`;
  }

  return content;
}

/**
 * Escapes HTML special characters
 * @param text - Text to escape
 * @returns Escaped HTML text
 */
function escapeHtml(text: string): string {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

/**
 * Converts markdown tables to HTML format for Word compatibility
 * @param content - Content containing markdown tables
 * @returns Content with tables converted to HTML format
 */
function convertTablesToHTML(content: string): string {
  // Regex to match markdown tables
  const tableRegex = /^\|(.+)\|\s*\n\|[-:\s|]+\|\s*\n((?:\|.+\|\s*\n?)+)/gm;

  return content.replace(tableRegex, (match, headerRow, bodyRows) => {
    // Process header row
    const headers = headerRow
      .split('|')
      .map((cell: string) => cell.trim())
      .filter((cell: string) => cell.length > 0);

    // Process body rows
    const rows = bodyRows
      .trim()
      .split('\n')
      .map((row: string) => {
        return row
          .split('|')
          .map((cell: string) => cell.trim())
          .filter((cell: string) => cell.length > 0);
      })
      .filter((row: string[]) => row.length > 0);

    // Generate HTML table with styling
    const headerHtml = headers
      .map((header: string) => `<th style="border: 1px solid #ccc; padding: 8px; background-color: #f5f5f5; font-weight: bold;">${escapeHtml(header)}</th>`)
      .join('');

    const bodyHtml = rows
      .map((row: string[]) => {
        const cellsHtml = row
          .map((cell: string) => `<td style="border: 1px solid #ccc; padding: 8px;">${escapeHtml(cell)}</td>`)
          .join('');
        return `<tr>${cellsHtml}</tr>`;
      })
      .join('');

    return `<table style="border-collapse: collapse; width: 100%; margin: 10px 0;">
      <thead>
        <tr>${headerHtml}</tr>
      </thead>
      <tbody>
        ${bodyHtml}
      </tbody>
    </table>`;
  });
}

/**
 * Converts markdown tables to tab-separated values (TSV) format
 * This format serves as a fallback for plain text applications
 * @param content - Content containing markdown tables
 * @returns Content with tables converted to TSV format
 */
function convertTablesToTSV(content: string): string {
  // Regex to match markdown tables
  const tableRegex = /^\|(.+)\|\s*\n\|[-:\s|]+\|\s*\n((?:\|.+\|\s*\n?)+)/gm;

  return content.replace(tableRegex, (match, headerRow, bodyRows) => {
    // Process header row
    const headers = headerRow
      .split('|')
      .map((cell: string) => cell.trim())
      .filter((cell: string) => cell.length > 0);

    // Process body rows
    const rows = bodyRows
      .trim()
      .split('\n')
      .map((row: string) => {
        return row
          .split('|')
          .map((cell: string) => cell.trim())
          .filter((cell: string) => cell.length > 0);
      })
      .filter((row: string[]) => row.length > 0);

    // Convert to TSV format
    const tsvLines = [
      headers.join('\t'), // Header row
      ...rows.map((row: string[]) => row.join('\t')) // Body rows
    ];

    return tsvLines.join('\n') + '\n\n';
  });
}

/**
 * Extracts plain text content from a message, handling different content types
 * @param messageContent - The raw message content (may include markdown, HTML, etc.)
 * @returns Clean text content suitable for copying
 */
export function extractCopyableContent(messageContent: string): string {
  if (!messageContent) return '';

  let content = messageContent;

  // Remove thinking blocks if present
  content = content.replace(/<think>[\s\S]*?<\/think>/g, '');

  // Remove HTML tags but preserve content
  content = content.replace(/<[^>]+>/g, '');

  // Convert the processed markdown
  return convertMarkdownForClipboard(content);
}

/**
 * Copies content to clipboard with both HTML and text formats for maximum compatibility
 * @param content - The markdown content to copy
 * @returns Promise that resolves to success boolean
 */
export async function copyToClipboard(content: string): Promise<boolean> {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      // Modern clipboard API with multiple formats
      const htmlContent = convertMarkdownToHTML(content);
      const textContent = convertMarkdownForClipboard(content);
      
      const clipboardItems = [
        new ClipboardItem({
          'text/html': new Blob([htmlContent], { type: 'text/html' }),
          'text/plain': new Blob([textContent], { type: 'text/plain' })
        })
      ];
      
      await navigator.clipboard.write(clipboardItems);
      return true;
    } else {
      // Fallback to text-only for older browsers
      const textContent = convertMarkdownForClipboard(content);
      return await copyTextToClipboard(textContent);
    }
  } catch (err) {
    console.error('Multi-format clipboard copy failed, falling back to text:', err);
    // Fallback to text-only copy
    try {
      const textContent = convertMarkdownForClipboard(content);
      return await copyTextToClipboard(textContent);
    } catch (fallbackErr) {
      console.error('Text fallback copy also failed:', fallbackErr);
      return false;
    }
  }
}

/**
 * Safely copies plain text to clipboard with error handling (fallback function)
 * @param text - Text to copy
 * @returns Promise that resolves to success boolean
 */
async function copyTextToClipboard(text: string): Promise<boolean> {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
      return true;
    } else {
      // Fallback for older browsers or non-secure contexts
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'absolute';
      textArea.style.left = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      
      try {
        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);
        return successful;
      } catch (err) {
        document.body.removeChild(textArea);
        console.error('Fallback copy failed:', err);
        return false;
      }
    }
  } catch (err) {
    console.error('Copy to clipboard failed:', err);
    return false;
  }
}