import { humanFileSize } from '@/lib/utils/HumanFileSize'; // Use path alias
import { HtmlFile } from '@/lib/types/common'; // Use path alias

const FileItemComponent = (props: {
  file: HtmlFile;
  showClose?: boolean;
  onPressClose?: () => void;
}) => {
  const { file, showClose, onPressClose } = props;
  return (
    <div className="relative">
      <a
        className={`flex mt-2 p-2 items-center bg-neutral-900 no-underline border-neutral-800 border-2 rounded-xl overflow-hidden ${
          !file.url && 'pointer-events-none'
        }`}
        href={file.url}
        download={file.name}
      >
        <div className="flex flex-col overflow-hidden min-w-[100px]">
          <div className="text-base	text-slate-50 break-words overflow-hidden	whitespace-nowrap truncate h-6 max-w-[200px]">
            {file.name}
          </div>
          <div className="text-sm	text-slate-300">{`${(
            file.name.split('.').pop() || ''
          ) // Add fallback for undefined
            .toUpperCase()} · ${humanFileSize(file.size)}`}</div>
        </div>
      </a>
      {showClose && (
        <button
          className="absolute flex -top-0.5 -right-2 w-5 h-5 rounded-2xl bg-slate-300 items-center justify-center"
          onClick={onPressClose}
        >
          <svg
            fill="#333"
            xmlns="http://www.w3.org/2000/svg"
            height="16"
            viewBox="0 -960 960 960"
            width="16"
          >
            <path d="m249-207-42-42 231-231-231-231 42-42 231 231 231-231 42 42-231 231 231 231-42 42-231-231-231 231Z" />
          </svg>
        </button>
      )}
    </div>
  );
};

export default FileItemComponent;
