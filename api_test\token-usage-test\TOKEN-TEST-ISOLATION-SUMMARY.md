# Token Usage Test - Isolated Implementation

## Overview
All token usage test code has been moved to an isolated folder to prevent any interference with the main codebase.

## Test Location
📁 **Location**: `./token-usage-test/`

## Contents
- **`test-azure-langchain-tokens.js`** - Main test script
- **`package.json`** - Isolated dependencies (will not affect main project)
- **`README.md`** - Comprehensive test documentation
- **`run-token-test.bat`** - Windows execution script
- **`run-token-test.sh`** - Unix/Linux execution script
- **`.gitignore`** - Prevents committing test artifacts

## Safety Measures
✅ **Isolated Dependencies**: Test uses its own `package.json` and `node_modules`
✅ **No Main Code Impact**: Test only reads from `../apps/api/.env`, doesn't modify anything
✅ **Git Protection**: `.gitignore` prevents accidental commits of test artifacts
✅ **Separate Execution**: Test runs independently of main application

## Running the Test

### Windows
```cmd
cd token-usage-test
run-token-test.bat
```

### Unix/Linux/Mac
```bash
cd token-usage-test
./run-token-test.sh
```

### Manual
```bash
cd token-usage-test
npm install
node test-azure-langchain-tokens.js
```

## What the Test Does
1. **Non-streaming test**: LangChain baseline token extraction
2. **Streaming test**: LangChain streaming with detailed chunk analysis  
3. **Direct API test**: Raw Azure OpenAI API for comparison

## Expected Results
The test should confirm whether LangChain properly extracts token usage from Azure OpenAI streaming responses, helping identify the root cause of the token tracking issue.

## Clean Up
To remove the test:
```bash
rm -rf token-usage-test/
```