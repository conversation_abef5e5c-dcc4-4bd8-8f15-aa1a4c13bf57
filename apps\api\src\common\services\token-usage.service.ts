import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../prisma/prisma.service';
import { ModelMappingService } from './model-mapping.service';
import { AuthenticatedUser } from '../../auth/user.interface';
import { Prisma } from '@hkbu-genai-platform/database/generated/client';
import { getHongKongTime, getHongKongDateOnly } from '../utils/timezone.util';

interface TokenUsageRecord {
  username: string;
  modelName: string;
  tokenDate: Date;
  promptTokens: number;
  completionTokens: number;
  totalTokens: number;
  isApi: boolean;
  conversationUuid?: string; // Optional: for chat services with conversation context
}

interface MonthlyTokenUsage {
  username: string;
  modelName: string;
  totalTokensUsed: number;
  promptTokensUsed: number;
  completionTokensUsed: number;
  messageCount: number;
  conversationCount: number;
  lastUpdated: Date;
}

interface UserTokenLimit {
  monthlyLimit: number;
  source: 'model_default' | 'user_specific' | 'service_env';
  userType?: string;
}

@Injectable()
export class TokenUsageService {
  private readonly logger = new Logger(TokenUsageService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly modelMappingService: ModelMappingService,
    private readonly configService: ConfigService,
  ) {}



  /**
   * Get monthly token usage for a user and model
   */
  async getMonthlyTokenUsage(
    username: string,
    modelName: string,
  ): Promise<MonthlyTokenUsage | null> {
    try {
      // Get current month's date range in Hong Kong timezone
      const hkTime = getHongKongTime();
      const startOfMonth = new Date(Date.UTC(hkTime.getUTCFullYear(), hkTime.getUTCMonth(), 1));
      const endOfMonth = new Date(Date.UTC(hkTime.getUTCFullYear(), hkTime.getUTCMonth() + 1, 0, 23, 59, 59, 999));

      // Get all model name variants
      const modelVariants =
        this.modelMappingService.getModelNameVariants(modelName);

      // Query token usage for all model variants
      const tokenUsage = await this.prisma.acl_user_token_spent.findMany({
        where: {
          username,
          model_name: {
            in: modelVariants,
          },
          token_date: {
            gte: startOfMonth,
            lte: endOfMonth,
          },
        },
      });

      if (tokenUsage.length === 0) {
        return null;
      }

      // Aggregate usage across all variants
      const aggregated = tokenUsage.reduce(
        (acc, record) => {
          acc.totalTokensUsed += record.token_spent || 0;
          acc.promptTokensUsed += record.token_spent_user || 0;
          acc.completionTokensUsed += record.token_spent_assistant || 0;
          acc.messageCount += record.message_count || 0;
          acc.conversationCount += record.conversation_count || 0;
          if (record.update_dt && record.update_dt > acc.lastUpdated) {
            acc.lastUpdated = record.update_dt;
          }
          return acc;
        },
        {
          username,
          modelName,
          totalTokensUsed: 0,
          promptTokensUsed: 0,
          completionTokensUsed: 0,
          messageCount: 0,
          conversationCount: 0,
          lastUpdated: new Date(0),
        },
      );

      return aggregated;
    } catch (error) {
      this.logger.error(
        `Error getting monthly token usage for ${username}/${modelName}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Get token limit for a user and model
   */
  async getUserTokenLimit(
    user: AuthenticatedUser,
    modelName: string,
  ): Promise<UserTokenLimit | null> {
    try {
      const isStaff = user.type === 'STAFF';

      // Check environment variables for specific models first
      let envVarName: string | null = null;
      if (modelName === 'gemini-2.0-flash-lite-001') {
        envVarName = isStaff
          ? 'PROMPT_REWRITE_STAFF_MONTHLY_TOKEN_LIMIT'
          : 'PROMPT_REWRITE_STUDENT_MONTHLY_TOKEN_LIMIT';
      } else if (modelName === 'chatgpt-4o-mini-transcribe') {
        envVarName = isStaff
          ? 'STT_STAFF_MONTHLY_TOKEN_LIMIT'
          : 'STT_STUDENT_MONTHLY_TOKEN_LIMIT';
      }

      if (envVarName) {
        const envLimit = this.configService.get<string>(envVarName);
        if (envLimit) {
          const monthlyLimit = parseInt(envLimit, 10);
          if (!isNaN(monthlyLimit) && monthlyLimit > 0) {
            this.logger.debug(
              `Using environment-based token limit for ${modelName}: ${monthlyLimit}`,
            );
            return {
              monthlyLimit,
              source: 'service_env',
              userType: user.type,
            };
          }
        }
      }

      // Get all model name variants
      const modelVariants =
        this.modelMappingService.getModelNameVariants(modelName);

      // Check for user-specific limits
      const userSpecificLimit =
        await this.prisma.acl_user_token_limit.findFirst({
          where: {
            username: user.userId,
            model: {
              model_name: {
                in: modelVariants,
              },
            },
          },
          include: {
            model: true,
          },
        });

      if (userSpecificLimit?.monthly_token_limit) {
        return {
          monthlyLimit: userSpecificLimit.monthly_token_limit,
          source: 'user_specific',
          userType: user.type,
        };
      }

      // Fall back to model default limits
      const model = await this.prisma.model_list.findFirst({
        where: {
          model_name: {
            in: modelVariants,
          },
          OR: [
            // Regular models: require both rec_status and availability_status
            {
              rec_status: 'A',
              availability_status: 'A',
              model_type: { not: 'embedding' },
            },
            // Embedding models: only require api_status='A' (less strict)
            {
              api_status: 'A',
              model_type: 'embedding',
            },
          ],
        },
      });

      if (!model) {
        this.logger.warn(`Model ${modelName} not found`);
        return null;
      }

      // Determine limit based on user type from JWT
      const monthlyLimit = isStaff
        ? model.stf_monthly_token_limit
        : model.std_monthly_token_limit;

      if (!monthlyLimit) {
        return null;
      }

      return {
        monthlyLimit,
        source: 'model_default',
        userType: user.type,
      };
    } catch (error) {
      this.logger.error(
        `Error getting token limit for ${user.userId}/${modelName}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Update token usage after a completion
   */
  async updateTokenUsage(usage: TokenUsageRecord): Promise<void> {
    try {
      // Use Hong Kong timezone for daily grouping (matching SQL Server GETDATE() behavior)
      const today = getHongKongDateOnly();

      // Get canonical model name to ensure consistent storage
      const canonicalModelName = this.modelMappingService.getCanonicalModelName(
        usage.modelName,
      );

      if (canonicalModelName !== usage.modelName) {
        this.logger.debug(
          `Model name normalized: ${usage.modelName} -> ${canonicalModelName}`,
        );
      }

      // Check if record exists for today
      const existing = await this.prisma.acl_user_token_spent.findFirst({
        where: {
          username: usage.username,
          model_name: canonicalModelName,
          token_date: today,
          is_api: usage.isApi ? 1 : 0,
        },
      });

      if (existing) {
        // Calculate conversation count based on actual unique conversations for this day
        let conversationCount: number;
        
        if (usage.conversationUuid) {
          // For chat services: count unique conversations from conversation table
          // Use Hong Kong timezone date range to match how conversations are typically viewed
          const hkToday = getHongKongDateOnly();
          const hkEndOfDay = new Date(hkToday.getTime());
          hkEndOfDay.setUTCHours(23, 59, 59, 999);
          
          const uniqueConversations = await this.prisma.conversation.findMany({
            where: {
              ssoid: usage.username,
              model_name: canonicalModelName,
              create_dt: {
                gte: hkToday,
                lte: hkEndOfDay,
              },
            },
            select: {
              conversation_uuid: true,
            },
            distinct: ['conversation_uuid'],
          });
          
          conversationCount = uniqueConversations.length;
          this.logger.debug(
            `Counted ${conversationCount} unique conversations for ${usage.username}/${canonicalModelName} on ${today.toISOString().split('T')[0]}`,
          );
        } else {
          // For non-chat services: increment as before
          conversationCount = (existing.conversation_count || 0) + 1;
        }

        // Update existing record
        await this.prisma.acl_user_token_spent.update({
          where: { id: existing.id },
          data: {
            token_spent: (existing.token_spent || 0) + usage.totalTokens,
            token_spent_user:
              (existing.token_spent_user || 0) + usage.promptTokens,
            token_spent_assistant:
              (existing.token_spent_assistant || 0) + usage.completionTokens,
            last_token_spent: usage.totalTokens,
            message_count: (existing.message_count || 0) + 1,
            conversation_count: conversationCount,
            update_by: usage.username,
            update_dt: getHongKongTime(),
          },
        });
      } else {
        // Calculate conversation count for new record
        let conversationCount: number;
        
        if (usage.conversationUuid) {
          // For chat services: count unique conversations from conversation table
          // Use Hong Kong timezone date range to match how conversations are typically viewed
          const hkToday = getHongKongDateOnly();
          const hkEndOfDay = new Date(hkToday.getTime());
          hkEndOfDay.setUTCHours(23, 59, 59, 999);
          
          const uniqueConversations = await this.prisma.conversation.findMany({
            where: {
              ssoid: usage.username,
              model_name: canonicalModelName,
              create_dt: {
                gte: hkToday,
                lte: hkEndOfDay,
              },
            },
            select: {
              conversation_uuid: true,
            },
            distinct: ['conversation_uuid'],
          });
          
          conversationCount = uniqueConversations.length;
          this.logger.debug(
            `New record: Counted ${conversationCount} unique conversations for ${usage.username}/${canonicalModelName} on ${today.toISOString().split('T')[0]}`,
          );
        } else {
          // For non-chat services: start with 1
          conversationCount = 1;
        }

        // Create new record
        await this.prisma.acl_user_token_spent.create({
          data: {
            username: usage.username,
            model_name: canonicalModelName,
            token_date: today,
            is_api: usage.isApi ? 1 : 0,
            token_spent: usage.totalTokens,
            token_spent_user: usage.promptTokens,
            token_spent_assistant: usage.completionTokens,
            last_token_spent: usage.totalTokens,
            message_count: 1,
            conversation_count: conversationCount,
            create_by: usage.username,
            create_dt: getHongKongTime(),
          },
        });
      }

      this.logger.debug(
        `Updated token usage for ${usage.username}/${usage.modelName}: ${usage.totalTokens} tokens`,
      );
    } catch (error) {
      this.logger.error(`Error updating token usage:`, error);
      throw error;
    }
  }

  /**
   * Get all monthly token usage for a user across all models
   */
  async getAllMonthlyTokenUsage(
    username: string,
  ): Promise<MonthlyTokenUsage[]> {
    try {
      // Get current month's date range in Hong Kong timezone
      const hkTime = getHongKongTime();
      const startOfMonth = new Date(Date.UTC(hkTime.getUTCFullYear(), hkTime.getUTCMonth(), 1));
      const endOfMonth = new Date(Date.UTC(hkTime.getUTCFullYear(), hkTime.getUTCMonth() + 1, 0, 23, 59, 59, 999));

      const tokenUsage = await this.prisma.acl_user_token_spent.findMany({
        where: {
          username,
          token_date: {
            gte: startOfMonth,
            lte: endOfMonth,
          },
        },
      });

      // Group by canonical model name using ModelMappingService to prevent duplicates
      const grouped = tokenUsage.reduce(
        (acc, record) => {
          const rawModelName = record.model_name || 'unknown';
          // Get canonical model name to group variants together
          const canonicalModelName =
            this.modelMappingService.getCanonicalModelName(rawModelName);

          if (!acc[canonicalModelName]) {
            acc[canonicalModelName] = {
              username,
              modelName: canonicalModelName, // Use canonical name for display
              totalTokensUsed: 0,
              promptTokensUsed: 0,
              completionTokensUsed: 0,
              messageCount: 0,
              conversationCount: 0,
              lastUpdated: new Date(0),
            };
          }

          acc[canonicalModelName].totalTokensUsed += record.token_spent || 0;
          acc[canonicalModelName].promptTokensUsed +=
            record.token_spent_user || 0;
          acc[canonicalModelName].completionTokensUsed +=
            record.token_spent_assistant || 0;
          acc[canonicalModelName].messageCount += record.message_count || 0;
          acc[canonicalModelName].conversationCount +=
            record.conversation_count || 0;

          if (
            record.update_dt &&
            record.update_dt > acc[canonicalModelName].lastUpdated
          ) {
            acc[canonicalModelName].lastUpdated = record.update_dt;
          }

          return acc;
        },
        {} as Record<string, MonthlyTokenUsage>,
      );

      return Object.values(grouped);
    } catch (error) {
      this.logger.error(
        `Error getting all monthly token usage for ${username}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Check if user has exceeded monthly token limit
   * Note: This now only checks if the user has already reached/exceeded their limit
   * It does NOT pre-emptively block based on estimated tokens
   */
  async checkTokenLimit(
    user: AuthenticatedUser,
    modelName: string,
  ): Promise<{
    allowed: boolean;
    currentUsage: number;
    monthlyLimit: number | null;
    remaining: number;
    message?: string;
  }> {
    try {
      // Get current usage
      const usage = await this.getMonthlyTokenUsage(user.userId, modelName);
      const currentUsage = usage?.totalTokensUsed || 0;

      // Get limit
      const limitInfo = await this.getUserTokenLimit(user, modelName);

      if (!limitInfo) {
        // No limit configured - allow
        return {
          allowed: true,
          currentUsage,
          monthlyLimit: null,
          remaining: Number.MAX_SAFE_INTEGER,
        };
      }

      const remaining = limitInfo.monthlyLimit - currentUsage;

      // Only block if user has already reached or exceeded the limit
      if (currentUsage >= limitInfo.monthlyLimit) {
        return {
          allowed: false,
          currentUsage,
          monthlyLimit: limitInfo.monthlyLimit,
          remaining: Math.max(0, remaining),
          message:
            'You have reached the monthly token limit. Please try again next month.',
        };
      }

      // Allow the request even if they only have 1 token remaining
      return {
        allowed: true,
        currentUsage,
        monthlyLimit: limitInfo.monthlyLimit,
        remaining,
      };
    } catch (error) {
      this.logger.error(
        `Error checking token limit for ${user.userId}/${modelName}:`,
        error,
      );
      // Allow on error to avoid blocking users
      return {
        allowed: true,
        currentUsage: 0,
        monthlyLimit: null,
        remaining: Number.MAX_SAFE_INTEGER,
      };
    }
  }

  /**
   * Check if user has exceeded limit AFTER recording token usage
   * This is called after the LLM response to determine if they've now exceeded their limit
   */
  async checkTokenLimitPostUsage(
    user: AuthenticatedUser,
    modelName: string,
  ): Promise<{
    exceeded: boolean;
    currentUsage: number;
    monthlyLimit: number | null;
    message?: string;
  }> {
    try {
      // Get updated usage after the request
      const usage = await this.getMonthlyTokenUsage(user.userId, modelName);
      const currentUsage = usage?.totalTokensUsed || 0;

      // Get limit
      const limitInfo = await this.getUserTokenLimit(user, modelName);

      if (!limitInfo) {
        // No limit configured
        return {
          exceeded: false,
          currentUsage,
          monthlyLimit: null,
        };
      }

      // Check if they've now exceeded the limit
      if (currentUsage > limitInfo.monthlyLimit) {
        return {
          exceeded: true,
          currentUsage,
          monthlyLimit: limitInfo.monthlyLimit,
          message: `You have exceeded your monthly token limit (${currentUsage}/${limitInfo.monthlyLimit} tokens used). Future requests will be blocked until next month.`,
        };
      }

      return {
        exceeded: false,
        currentUsage,
        monthlyLimit: limitInfo.monthlyLimit,
      };
    } catch (error) {
      this.logger.error(
        `Error checking post-usage token limit for ${user.userId}/${modelName}:`,
        error,
      );
      // Don't block on error
      return {
        exceeded: false,
        currentUsage: 0,
        monthlyLimit: null,
      };
    }
  }
}
