'use client';
import * as React from 'react';
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import NextAppDirEmotionCacheProvider from './EmotionCache';
import { lightTheme, darkTheme } from './theme';
import { useMediaQuery } from '@mui/material';
import { useAppSelector, useAppDispatch } from '@/lib/store/hooks';
import { selectTheme, setTheme } from '@/lib/store/themeSlice';

export default function ThemeRegistry({
  children,
}: {
  children: React.ReactNode;
}) {
  const dispatch = useAppDispatch();
  const themeMode = useAppSelector(selectTheme);
  const prefersDarkMode = useMediaQuery('(prefers-color-scheme: dark)');

  // Sync localStorage with Redux on mount
  React.useEffect(() => {
    const storedTheme = localStorage.getItem('mui-theme-mode');
    if (storedTheme && (storedTheme === 'light' || storedTheme === 'dark' || storedTheme === 'system')) {
      // Only dispatch if different from current Redux state
      if (storedTheme !== themeMode) {
        dispatch(setTheme(storedTheme as 'light' | 'dark' | 'system'));
      }
    }
  }, [dispatch]); // Only run on mount

  const theme = React.useMemo(() => {
    if (themeMode === 'system') {
      return prefersDarkMode ? darkTheme : lightTheme;
    }
    return themeMode === 'dark' ? darkTheme : lightTheme;
  }, [themeMode, prefersDarkMode]);

  return (
    <NextAppDirEmotionCacheProvider options={{ key: 'mui' }}>
      <ThemeProvider theme={theme}>
        {/* CssBaseline kickstart an elegant, consistent, and simple baseline to build upon. */}
        <CssBaseline />
        {children}
      </ThemeProvider>
    </NextAppDirEmotionCacheProvider>
  );
}
