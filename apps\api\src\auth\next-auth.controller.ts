import { Controller, Post, Body, HttpCode, HttpStatus } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { getHongKongTime } from '../common/utils/timezone.util';

@Controller('next-auth')
export class NextAuthController {
  constructor(private readonly prisma: PrismaService) {}

  @Post('check-user')
  @HttpCode(HttpStatus.OK)
  async checkUser(
    @Body() body: { userId: string; employeeType: string; deptCode: string },
  ) {
    const { userId, employeeType, deptCode } = body;
    console.log('checkUser', { userId, employeeType, deptCode });
    if (employeeType === 'OTHER') {
      const otherUser = await this.prisma.acl_other_user.findUnique({
        where: { username: userId },
      });
      return { authorized: !!otherUser };
    }

    if (
      employeeType === 'STUDENT' &&
      (deptCode === 'SCE' || deptCode === 'CIE')
    ) {
      const aclUser = await this.prisma.acl_user.findUnique({
        where: { username: userId },
      });
      return { authorized: !!aclUser };
    }

    return { authorized: true }; // Default to authorized if no specific checks apply
  }

  @Post('upsert-user')
  @HttpCode(HttpStatus.OK)
  async upsertUser(
    @Body() body: { userId: string; deptCode: string; employeeType: string },
  ) {
    const { userId, deptCode, employeeType } = body;
    console.log('upsertUser', { userId, deptCode, employeeType });
    await this.prisma.acl_user_details.upsert({
      where: { username: userId },
      update: {
        update_by: userId,
        update_dt: getHongKongTime(),
      },
      create: {
        username: userId,
        dept_unit_code: deptCode,
        employee_type: employeeType,
        create_by: userId,
        create_dt: getHongKongTime(),
        update_by: userId,
        update_dt: getHongKongTime(),
      },
    });
    return { success: true };
  }
}
