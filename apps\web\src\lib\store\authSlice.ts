import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from './store'; // Assuming RootState is exported from store.ts

// Define the state structure for authentication
interface AuthState {
  isAuthenticated: boolean;
}

// Define the initial state
const initialState: AuthState = {
  isAuthenticated: false,
};

// Create the auth slice
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    // Action to set the authentication session data
    setAuthSession: (
      state,
      action: PayloadAction<{ isAuthenticated: boolean }>,
    ) => {
      state.isAuthenticated = action.payload.isAuthenticated;
    },
    // Action to clear the authentication session
    clearAuthSession: (state) => {
      state.isAuthenticated = false;
    },
  },
});

// Export the actions
export const { setAuthSession, clearAuthSession } = authSlice.actions;

// Export selectors
export const selectIsAuthenticated = (state: RootState) =>
  state.auth.isAuthenticated;

// Export the reducer
export default authSlice.reducer;
