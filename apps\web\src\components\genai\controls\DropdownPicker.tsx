import React, { ChangeEvent, useRef, useState } from 'react';

type DropdownPickerProps<T> = {
  title?: string;
  titleProps?: React.DetailedHTMLProps<
    React.HTMLAttributes<HTMLDivElement>,
    HTMLDivElement
  >;
  placeholder?: string;
  alertMessage?: string;
  option?: Option<T>[];
  onPick?: (_: T) => void;
  inputable?: boolean;
  isError?: boolean;
  errorMessage?: string;
} & React.DetailedHTMLProps<
  React.HTMLAttributes<HTMLDivElement>,
  HTMLDivElement
>;

export type Option<T> = {
  value?: T;
  displayName?: string;
};

const DropdownPicker = <T extends string | number | readonly string[]>(
  props: DropdownPickerProps<T>,
) => {
  const {
    title,
    titleProps = {},
    placeholder = 'Select...',
    option = [],
    onPick,
    inputable,
    className,
    isError,
    errorMessage,
    ...otherProps
  } = props;

  const { /* className: titleClassName, */ ...otherTitleProps } = titleProps; // Removed unused titleClassName

  const messageInputRef = useRef<HTMLTextAreaElement | null>(null);

  const [selectedOption, setSelectedOption] = useState<string>('');

  const handleSelect = (event: ChangeEvent<HTMLSelectElement>) => {
    const newValue = option.find(
      (item) => item.value === event.target.value,
    )?.value;
    if (newValue !== undefined) {
      // Ensure newValue is defined before calling onPick
      onPick?.(newValue);
    }
    setSelectedOption(event.target.value);

    if (messageInputRef.current) messageInputRef.current.value = '';
  };

  const onInputChange = () => {
    if (messageInputRef.current) {
      // Ensure ref is current before accessing value
      onPick?.(messageInputRef.current.value as T);
    }
  };

  return (
    <div>
      <div className="flex flex-col gap-2">
        {title && <p className={`min-w-[80px] ${otherTitleProps}`}>{title}</p>}
        <div className="flex flex-row items-center gap-2">
          <div className={`relative ${className}`} {...otherProps}>
            <select
              value={selectedOption}
              onChange={handleSelect}
              className="block appearance-none w-full bg-white border border-gray-300 hover:border-gray-400 px-4 py-2 pr-8 rounded shadow leading-tight focus:outline-none focus:shadow-outline"
            >
              <option value="">{placeholder}</option>
              {option.map(({ value, displayName }, index) => (
                <option key={index} value={value}>
                  {displayName}
                </option>
              ))}
              {inputable && (
                <option value={'other'}>{'Other (please specify)'}</option>
              )}
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
              <svg
                className="fill-current h-4 w-4"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 -960 960 960"
              >
                <path d="M480-360 280-560h400L480-360Z" />
              </svg>
            </div>
          </div>
          {selectedOption === 'other' && (
            <textarea
              autoFocus={true}
              ref={messageInputRef}
              className=" h-[38px] resize-none outline-none border-2 p-1.5 overflow-hidden"
              rows={1}
              onChange={onInputChange}
            />
          )}
        </div>
      </div>
      {isError &&
        (selectedOption === '' ||
          (selectedOption === 'other' &&
            messageInputRef.current?.value === '')) && (
          <div className="text-red-500">
            {(errorMessage ?? selectedOption === 'other')
              ? 'Please fill in the text box'
              : 'Please select an option'}
          </div>
        )}
    </div>
  );
};

export default DropdownPicker;
