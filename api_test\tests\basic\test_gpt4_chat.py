#!/usr/bin/env python3
"""
Simple Python script to test the HKBU GenAI Platform Chat Completion API.
This script replicates the exact same test that was successful in Postman.
"""

import json
import os
import sys
import requests
from dotenv import load_dotenv
from typing import Dict, Any


def load_config() -> Dict[str, str]:
    """Load configuration from .env file."""
    load_dotenv()
    
    api_key = os.getenv("API_KEY")
    base_url = os.getenv("BASE_URL")
    
    if not api_key:
        raise ValueError("API_KEY not found in .env file")
    if not base_url:
        raise ValueError("BASE_URL not found in .env file")
    
    return {
        "api_key": api_key,
        "base_url": base_url
    }


def test_chat_completion(config: Dict[str, str]) -> None:
    """Test the chat completion endpoint with gpt-4.1 model."""
    
    # Construct the exact URL from the Postman test
    url = f"{config['base_url']}/rest/deployments/gpt-4.1/chat/completions"
    
    # Query parameters
    params = {
        "api-version": "2024-02-01"
    }
    
    # Headers - exactly as in Postman test
    headers = {
        "Content-Type": "application/json",
        "api-key": config["api_key"]
    }
    
    # Request payload - exactly as in Postman test
    payload = {
        "messages": [
            {
                "role": "system",
                "content": "You are a helpful assistant."
            },
            {
                "role": "user",
                "content": "Hello, how are you?"
            }
        ],
        "temperature": 0.7,
    }
    
    print("🚀 Testing HKBU GenAI Platform Chat Completion API")
    print(f"📍 URL: {url}")
    print(f"🔑 API Key: {config['api_key'][:8]}...")
    print(f"📝 Model: gpt-4.1")
    print(f"💬 Message: {payload['messages'][1]['content']}")
    print("-" * 50)
    
    try:
        # Make the request
        response = requests.post(
            url,
            params=params,
            headers=headers,
            json=payload,
            timeout=30
        )
        
        # Check response status
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Request successful!")
            
            # Parse response
            response_data = response.json()
            
            # Validate response structure (OpenAI-compatible format)
            required_fields = ["id", "object", "created", "model", "choices", "usage"]
            missing_fields = [field for field in required_fields if field not in response_data]
            
            if missing_fields:
                print(f"⚠️  Missing fields in response: {missing_fields}")
            else:
                print("✅ Response structure is valid (OpenAI-compatible format)!")
            
            # Extract and display the assistant's response
            if "choices" in response_data and len(response_data["choices"]) > 0:
                assistant_message = response_data["choices"][0]["message"]["content"]
                print(f"🤖 Assistant Response: {assistant_message}")
            
            # Display response details
            print("\n📋 Response Details:")
            print(f"   ID: {response_data.get('id', 'N/A')}")
            print(f"   Model: {response_data.get('model', 'N/A')}")
            print(f"   Object Type: {response_data.get('object', 'N/A')}")
            print(f"   Created: {response_data.get('created', 'N/A')}")
            print(f"   Finish Reason: {response_data.get('choices', [{}])[0].get('finish_reason', 'N/A')}")
            
            # Display usage info
            usage = response_data.get("usage", {})
            print(f"   Usage - Prompt Tokens: {usage.get('prompt_tokens', 'N/A')}")
            print(f"   Usage - Completion Tokens: {usage.get('completion_tokens', 'N/A')}")
            print(f"   Usage - Total Tokens: {usage.get('total_tokens', 'N/A')}")
            
        else:
            print(f"❌ Request failed with status code: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Unable to connect to the API server.")
        print("   Make sure the server is running on localhost:3003")
        
    except requests.exceptions.Timeout:
        print("❌ Timeout Error: Request took too long to complete.")
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Request Error: {e}")
        
    except json.JSONDecodeError:
        print("❌ JSON Decode Error: Invalid response format.")
        print(f"Raw response: {response.text}")
        
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")


def main():
    """Main function to run the test."""
    try:
        config = load_config()
        test_chat_completion(config)
        
    except Exception as e:
        print(f"❌ Configuration Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()