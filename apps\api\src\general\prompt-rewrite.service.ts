import { Injectable, Logger } from '@nestjs/common';
import { VertexGeminiService } from '../llm/vertex-gemini.service';
import { TokenUsageService } from '../common/services/token-usage.service';
import { ModelMappingService } from '../common/services/model-mapping.service';
import { AuthenticatedUser } from '../auth/user.interface';
import {
  RewritePromptDto,
  GeneratePromptDto,
  PromptRewriteResponseDto,
} from './dto/prompt-rewrite.dto';

@Injectable()
export class PromptRewriteService {
  private readonly logger = new Logger(PromptRewriteService.name);

  constructor(
    private readonly vertexGeminiService: VertexGeminiService,
    private readonly tokenUsageService: TokenUsageService,
    private readonly modelMappingService: ModelMappingService,
  ) {}

  async rewrite(
    rewritePromptDto: RewritePromptDto,
    user: AuthenticatedUser,
  ): Promise<PromptRewriteResponseDto> {
    const { prompt } = rewritePromptDto;
    const modelName = 'gemini-2.0-flash-lite-001';

    // Get canonical model name for consistent tracking
    const canonicalModelName =
      this.modelMappingService.getCanonicalModelName(modelName);

    // Check token limits before processing
    const tokenLimitCheck = await this.tokenUsageService.checkTokenLimit(
      user,
      canonicalModelName,
    );
    if (!tokenLimitCheck.allowed) {
      throw new Error(
        tokenLimitCheck.message || 'Prompt rewrite token limit exceeded',
      );
    }

    const rewriteInstruction = `
      As an expert in prompt engineering, rewrite the following prompt to be more clear, concise, and effective for a large language model.
      Only return the rewritten prompt text, without any additional labels or explanations.

      Original Prompt:
      "${prompt}"
    `;

    this.logger.log(
      `Prompt rewrite request for user ${user.userId} using model ${modelName}`,
    );

    const result = await this.vertexGeminiService.generateText(
      modelName,
      rewriteInstruction,
    );

    // Extract actual token usage from Vertex Gemini API response
    const inputTokens = result.tokenUsage.promptTokens;
    const outputTokens = result.tokenUsage.completionTokens;
    const totalTokens = result.tokenUsage.totalTokens;

    this.logger.log(
      `Prompt rewrite completed: ${inputTokens} input + ${outputTokens} output = ${totalTokens} total tokens`,
    );

    // Record token usage using real model name
    await this.tokenUsageService.updateTokenUsage({
      username: user.userId,
      modelName: canonicalModelName,
      tokenDate: new Date(),
      promptTokens: inputTokens,
      completionTokens: outputTokens,
      totalTokens: totalTokens,
      isApi: false, // This is web UI, not REST API
    });

    // Check if user exceeded monthly limit after this usage
    const postUsageCheck =
      await this.tokenUsageService.checkTokenLimitPostUsage(
        user,
        canonicalModelName,
      );
    if (postUsageCheck.exceeded) {
      this.logger.warn(postUsageCheck.message);
    }

    return { prompt: result.text };
  }

  async generate(
    generatePromptDto: GeneratePromptDto,
    user: AuthenticatedUser,
  ): Promise<PromptRewriteResponseDto> {
    const { idea } = generatePromptDto;
    const modelName = 'gemini-2.0-flash-lite-001';

    // Get canonical model name for consistent tracking
    const canonicalModelName =
      this.modelMappingService.getCanonicalModelName(modelName);

    // Check token limits before processing
    const tokenLimitCheck = await this.tokenUsageService.checkTokenLimit(
      user,
      canonicalModelName,
    );
    if (!tokenLimitCheck.allowed) {
      throw new Error(
        tokenLimitCheck.message || 'Prompt generation token limit exceeded',
      );
    }

    const generateInstruction = `
      As an expert in prompt engineering, generate a detailed and effective prompt for a large language model based on the following idea.
      The generated prompt should be specific, well-structured, and provide enough context for the model to generate a comprehensive and accurate response.
      Only return the generated prompt text, without any additional labels or explanations.

      Idea:
      "${idea}"
    `;

    this.logger.log(
      `Prompt generation request for user ${user.userId} using model ${modelName}`,
    );

    const result = await this.vertexGeminiService.generateText(
      modelName,
      generateInstruction,
    );

    // Extract actual token usage from Vertex Gemini API response
    const inputTokens = result.tokenUsage.promptTokens;
    const outputTokens = result.tokenUsage.completionTokens;
    const totalTokens = result.tokenUsage.totalTokens;

    this.logger.log(
      `Prompt generation completed: ${inputTokens} input + ${outputTokens} output = ${totalTokens} total tokens`,
    );

    // Record token usage using real model name
    await this.tokenUsageService.updateTokenUsage({
      username: user.userId,
      modelName: canonicalModelName,
      tokenDate: new Date(),
      promptTokens: inputTokens,
      completionTokens: outputTokens,
      totalTokens: totalTokens,
      isApi: false, // This is web UI, not REST API
    });

    // Check if user exceeded monthly limit after this usage
    const postUsageCheck =
      await this.tokenUsageService.checkTokenLimitPostUsage(
        user,
        canonicalModelName,
      );
    if (postUsageCheck.exceeded) {
      this.logger.warn(postUsageCheck.message);
    }

    return { prompt: result.text };
  }
}
