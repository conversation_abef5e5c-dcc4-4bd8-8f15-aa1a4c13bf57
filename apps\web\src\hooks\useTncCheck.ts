import { useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useCheckTncStatusQuery } from '@/lib/store/apiSlice';
import { useAppDispatch } from '@/lib/store/hooks';
import { openTncModal, setTncModalShowAgree } from '@/lib/store/uiSlice';

/**
 * Custom hook to automatically check T&C status and show modal if needed
 * Triggers T&C modal if user hasn't accepted T&C within the last 30 days
 */
export const useTncCheck = () => {
  const { data: session, status } = useSession();
  const dispatch = useAppDispatch();
  
  // Only run the query when user is authenticated
  const {
    data: tncStatus,
    error,
    isLoading,
    refetch,
  } = useCheckTncStatusQuery(undefined, {
    skip: status !== 'authenticated',
    refetchOnMountOrArgChange: true,
  });

  useEffect(() => {
    // Only proceed if user is authenticated and we have T&C status data
    if (status === 'authenticated' && tncStatus && !isLoading) {
      if (!tncStatus.agreed) {
        console.log('T&C acceptance required - showing modal');
        // User needs to accept T&C (either first time or expired)
        dispatch(setTncModalShowAgree(true));
        dispatch(openTncModal());
      } else {
        console.log('T&C status valid - no action needed');
      }
    }
  }, [tncStatus, status, isLoading, dispatch]);

  useEffect(() => {
    // Log any T&C check errors
    if (error) {
      console.error('Error checking T&C status:', error);
    }
  }, [error]);

  return {
    tncStatus,
    isLoading,
    error,
    refetch,
  };
};