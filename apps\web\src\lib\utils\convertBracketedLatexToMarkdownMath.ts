/**
 * Converts bracketed LaTeX expressions [ ... ] in plain text to $...$ for KaTeX rendering.
 * - Only converts brackets that are not inside code blocks or already inside $...$ or $$...$$.
 * - Optionally, you can extend this to support block math with [[ ... ]] to $$...$$.
 */
export function convertBracketedLatexToMarkdownMath(text: string): string {
  // Simple state machine to avoid code blocks (```...```)
  const codeBlockRegex = /```[\s\S]*?```/g;
  let codeBlocks: string[] = [];
  let placeholder = '___CODEBLOCK___';
  let i = 0;

  // Mask code blocks
  const masked = text.replace(codeBlockRegex, (match) => {
    codeBlocks.push(match);
    return `${placeholder}${i++}`;
  });

  // Replace [ ... ] with $...$ only if content looks like LaTeX
  const replaced = masked.replace(/\[(.+?)\]/g, (match, p1) => {
    // Avoid replacing if inside a code block (already masked)
    // Avoid replacing if content contains newlines (could be block math)
    // Avoid replacing if content doesn't contain typical LaTeX characters
    if (p1.includes('\n') || !/[\\^{}_]/.test(p1)) {
      return match; // Return original match if it doesn't look like inline LaTeX
    }
    // Also check if it's already surrounded by $ to avoid double wrapping
    const index = masked.indexOf(match);
    const charBefore = index > 0 ? masked[index - 1] : '';
    const charAfter =
      index + match.length < masked.length ? masked[index + match.length] : '';
    if (charBefore === '$' || charAfter === '$') {
      return match; // Already looks like math mode
    }

    return `$${p1}$`; // Convert to inline math
  });

  // Restore code blocks
  let restored = replaced;
  codeBlocks.forEach((block, idx) => {
    restored = restored.replace(`${placeholder}${idx}`, block);
  });

  return restored;
}
