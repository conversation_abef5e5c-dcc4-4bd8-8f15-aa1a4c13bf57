import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class ApiKeyService {
  private readonly logger = new Logger(ApiKeyService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
  ) {}

  async generateApiKey(
    ssoid: string,
    ipAddress: string,
  ): Promise<{ apiKey: string }> {
    if (!ssoid) {
      this.logger.error('SSOID is required to generate an API key.');
      throw new HttpException(
        'User identifier (SSOID) is missing.',
        HttpStatus.BAD_REQUEST,
      );
    }

    const apiKey = uuidv4();
    this.logger.log(`Generated new API key for SSOID: ${ssoid}`);

    try {
      // Assuming sp_acl_GenUserApiKey is adapted or available for Prisma
      // For Prisma, direct SQL execution or a mapped model interaction would be used.
      // If sp_acl_GenUserApiKey is a raw SQL query:
      await this.prisma
        .$executeRaw`EXEC sp_acl_GenUserApiKey @ssoid=${ssoid}, @api_key=${apiKey}, @ip_address=${ipAddress}`;

      this.logger.log(`Successfully stored API key for SSOID: ${ssoid}`);
      return { apiKey };
    } catch (error) {
      let errorMessage =
        'An unknown database error occurred while generating API key.';
      let errorStack: string | undefined = undefined; // Corrected type
      if (error instanceof Error) {
        errorMessage = error.message;
        errorStack = error.stack;
      }
      this.logger.error(
        `Database error while generating API key for SSOID ${ssoid}: ${errorMessage}`,
        errorStack,
      );
      throw new HttpException(
        'Failed to generate or store API key due to a database error.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // Placeholder for API key validation logic if needed directly in this service later
  /*
    async validateApiKey(apiKey: string): Promise<any | null> { // Replace 'any' with UserPayload interface
      // Logic to fetch user details based on API key
      // e.g., using a stored procedure like sp_acl_ValidateApiKey @api_key=apiKey
      // const userRecord = await this.prisma.$queryRaw`EXEC sp_acl_ValidateApiKey @api_key=${apiKey}`;
      // if (userRecord && userRecord.length > 0) {
      //   return userRecord[0];
      // }
      return null;
    }
    */
}
