# STT (Speech-to-Text) Environment Configuration 

## Required Environment Variables

The following environment variables must be set with the new audio duration-based token limits:

### Token Limits (Duration-Based: 1 second = 1 token)

```bash
# Staff monthly limit: 4,320,000 seconds (1,200 hours)
STT_STAFF_MONTHLY_TOKEN_LIMIT=4320000

# Student monthly limit: 2,160,000 seconds (600 hours)  
STT_STUDENT_MONTHLY_TOKEN_LIMIT=2160000
```

## Deployment Notes

1. **Environment Configuration**: These variables are already included in `gitlab/script/prepare-api-env.sh`
2. **Values Must Be Updated**: The deployment team needs to set these specific values in the environment
3. **Model Mapping**: These limits apply to the `chatgpt-4o-mini-transcribe` model

## Token Calculation Method

- **Input Tokens**: `Math.ceil(recordingDurationInSeconds)` 
- **Output Tokens**: `0` (transcription is input-only)
- **Total Tokens**: Same as input tokens

## Frontend Integration Requirements

**CRITICAL**: The frontend must send the actual recording duration to prevent token overcharging.

### Required Form Data Fields
```javascript
const formData = new FormData();
formData.append('file', audioBlob, 'recording.wav');
formData.append('recordingDuration', recordingDurationInSeconds.toString());
```

### Frontend Implementation Checklist
- [ ] Implement recording timer that tracks actual duration
- [ ] Send `recordingDuration` field in multipart form data
- [ ] Display recording duration to user during recording
- [ ] Validate duration before sending (should be > 0.5s and < 300s)

## Fallback Behavior

If `recordingDuration` is not provided by frontend:
- **Conservative Fallback**: 5 seconds (to prevent overcharging)
- **Warning Logged**: System will warn about missing duration
- **Recommendation**: Frontend should always send actual duration

## Validation Rules

- **Minimum Duration**: 0.5 seconds (rounded up to 1 second for token calculation)
- **Maximum Duration**: 300 seconds (5 minutes) 
- **Warnings**: System logs warnings for durations outside normal ranges

## Migration from Previous Implementation

- **Before**: Used zero token fallback or inaccurate file-size estimation
- **After**: Uses actual recording duration for accurate token tracking
- **Impact**: More accurate billing and rate limiting based on actual audio length