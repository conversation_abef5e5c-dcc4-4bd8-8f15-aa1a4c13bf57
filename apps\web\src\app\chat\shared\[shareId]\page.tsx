'use client';

import React, { useEffect, useRef, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useAccessSharedConversationMutation } from '@/lib/store/apiSlice';
import { Box, CircularProgress, Typography, Alert } from '@mui/material';

export default function SharedConversationPage() {
  const params = useParams();
  const router = useRouter();
  const shareId = params.shareId as string;

  // Add refs and state to prevent duplicate calls
  const hasProcessedRef = useRef(false);
  const [hasProcessed, setHasProcessed] = useState(false);

  const [accessSharedConversation, { isLoading, error }] =
    useAccessSharedConversationMutation();

  useEffect(() => {
    // Guard against multiple executions
    if (hasProcessedRef.current || hasProcessed || !shareId) {
      return;
    }

    const handleAccess = async () => {
      // Mark as processed immediately
      hasProcessedRef.current = true;
      setHasProcessed(true);

      try {
        const result = await accessSharedConversation(shareId).unwrap();
        // Redirect to the new conversation
        router.push(result.redirectUrl);
      } catch (err) {
        console.error('Failed to access shared conversation:', err);
        // Show error for a moment before redirecting
        setTimeout(() => {
          router.push('/');
        }, 3000);
      }
    };

    handleAccess();
  }, [shareId, accessSharedConversation, hasProcessed, router]);

  if (isLoading) {
    return (
      <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        minHeight="100svh"
        gap={2}
      >
        <CircularProgress />
        <Typography variant="h6" color="text.secondary">
          Creating your copy of the conversation...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        minHeight="100svh"
        gap={2}
        px={3}
      >
        <Alert severity="error" sx={{ maxWidth: 500 }}>
          <Typography variant="h6" gutterBottom>
            Unable to access shared conversation
          </Typography>
          <Typography variant="body2">
            The link may be invalid or you may not have permission to access
            this conversation. You'll be redirected to the home page shortly.
          </Typography>
        </Alert>
      </Box>
    );
  }

  return null;
}
