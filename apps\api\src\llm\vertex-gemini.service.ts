import {
  Injectable,
  Logger,
  InternalServerErrorException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { TokenUsageService } from '../common/services/token-usage.service';
import { getHongKongTime, getHongKongDateOnly } from '../common/utils/timezone.util';
import { Readable, PassThrough } from 'stream';
import { v4 as uuidv4 } from 'uuid'; // Import uuid
import { GoogleCustomSearch } from '@langchain/community/tools/google_custom_search'; // New import
import { Tool } from '@langchain/core/tools'; // New import
import { AgentExecutor, createOpenAIToolsAgent } from 'langchain/agents'; // New import
import {
  ChatPromptTemplate,
  MessagesPlaceholder,
} from '@langchain/core/prompts'; // New import
import {
  HumanMessage,
  AIMessage,
  SystemMessage,
  BaseMessage,
  ToolMessage,
} from '@langchain/core/messages'; // New import
import { RunnableConfig } from '@langchain/core/runnables'; // New import
import {
  VertexAI,
  GenerativeModel,
  Content,
  Part,
  GenerateContentResult,
  GenerateContentResponse,
  StreamGenerateContentResult, // Add this type
} from '@google-cloud/vertexai';
import { LlmStreamOptions } from './llm.service';

// Define the structure expected by Vertex AI Gemini messages
interface GeminiMessage {
  role: 'user' | 'model'; // Gemini uses 'user' and 'model'
  parts: Part[];
}

// Define Source interface for search results (Copied from chat-completion.service.ts)
interface Source {
  title: string;
  link: string;
  snippet: string;
}

// Interface for returning both text and token usage from generateText
interface GenerateTextResult {
  text: string;
  tokenUsage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}

// Define the standard agent prompt with citation instructions (Copied from chat-completion.service.ts)
const AGENT_PROMPT = ChatPromptTemplate.fromMessages([
  // Removed system message from AGENT_PROMPT as it's passed separately as systemInstruction
  new MessagesPlaceholder('chat_history'),
  ['human', '{input}'],
  new MessagesPlaceholder('agent_scratchpad'),
]);

// Helper function for sleep
const sleep = (ms: number): Promise<void> =>
  new Promise((resolve) => setTimeout(resolve, ms));

@Injectable()
export class VertexGeminiService {
  private readonly logger = new Logger(VertexGeminiService.name);
  private googleSearchTool: GoogleCustomSearch | null = null; // New property

  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
    private tokenUsageService: TokenUsageService,
  ) {
    // Initialize Google Search Tool conditionally (Copied from chat-completion.service.ts)
    const googleApiKey = this.configService.get<string>('GOOGLE_CSE_API_KEY');
    const googleCseId = this.configService.get<string>('GOOGLE_CSE_ID');
    if (googleApiKey && googleCseId) {
      this.googleSearchTool = new GoogleCustomSearch({
        apiKey: googleApiKey,
        googleCSEId: googleCseId,
      });
      this.logger.log(
        'Google Custom Search tool initialized in VertexGeminiService.',
      );
    } else {
      this.logger.warn(
        'Google CSE API Key or ID not found. Google Search tool disabled in VertexGeminiService.',
      );
    }
  }

  /**
   * Transforms LlmMessage format to GeminiMessage format.
   * Handles role mapping and content structure, ensuring alternating turns.
   */
  private transformMessagesToGemini(
    messages: LlmStreamOptions['messages'],
    isToolEnabled: boolean = false,
  ): GeminiMessage[] {
    const geminiMessages: GeminiMessage[] = [];
    let lastRole: 'user' | 'model' | null = null;

    for (const msg of messages) {
      let currentRole: 'user' | 'model';
      const parts: Part[] = [];

      if (msg.role === 'system') {
        if (isToolEnabled) {
          // If tools are enabled, system messages are handled by systemInstruction, so skip them here.
          continue;
        } else {
          // If no tools, prepend system message content to the next user message.
          // This is a simplification; a more robust solution might involve a dedicated system instruction field
          // or ensuring the first message is always user-role with system context.
          // For now, we'll treat it as part of the user's input.
          this.logger.warn(
            `Treating system message as user message for Gemini (no tools): ${JSON.stringify(msg)}`,
          );
          currentRole = 'user';
          if (typeof msg.content === 'string') {
            parts.push({ text: msg.content });
          } else if (Array.isArray(msg.content)) {
            parts.push(
              ...msg.content
                .filter((part) => typeof part.text === 'string')
                .map((part) => ({ text: part.text })),
            );
          }
        }
      } else if (msg.role === 'assistant') {
        currentRole = 'model';
      } else if (msg.role === 'user') {
        currentRole = 'user';
      } else {
        this.logger.warn(
          `Unknown message role for Gemini: ${msg.role}. Treating as user message.`,
        );
        currentRole = 'user';
      }

      // Add content parts
      if (typeof msg.content === 'string') {
        parts.push({ text: msg.content });
      } else if (Array.isArray(msg.content)) {
        parts.push(
          ...msg.content
            .filter((part) => typeof part.text === 'string')
            .map((part) => ({ text: part.text })),
        );
        // TODO: Add image part handling if needed { inlineData: { mimeType: '...', data: '...' } }
      } else {
        this.logger.error(
          `Unsupported message content format for Gemini: ${JSON.stringify(msg.content)}`,
        );
        continue; // Skip message if format is wrong
      }

      // Filter out messages with empty parts or only empty text parts
      if (
        !parts ||
        parts.length === 0 ||
        parts.every((part) => !part.text || part.text.trim() === '')
      ) {
        this.logger.warn(
          `Skipping message with empty content for Gemini: ${JSON.stringify(msg)}`,
        );
        continue;
      }

      // Ensure alternating roles
      if (lastRole === currentRole) {
        // If roles are consecutive, merge with the previous message if possible, or adjust role.
        // For simplicity, if the last message was 'user' and current is 'user', append to last.
        // If last was 'model' and current is 'model', this indicates an issue in history or a multi-part model response.
        if (
          currentRole === 'user' &&
          geminiMessages.length > 0 &&
          geminiMessages[geminiMessages.length - 1].role === 'user'
        ) {
          this.logger.warn(`Merging consecutive user messages for Gemini.`);
          geminiMessages[geminiMessages.length - 1].parts.push(...parts);
          continue;
        } else if (
          currentRole === 'model' &&
          geminiMessages.length > 0 &&
          geminiMessages[geminiMessages.length - 1].role === 'model'
        ) {
          this.logger.warn(`Merging consecutive model messages for Gemini.`);
          geminiMessages[geminiMessages.length - 1].parts.push(...parts);
          continue;
        } else {
          // This case should ideally not happen with proper history, but as a fallback,
          // we might need to adjust the role or log a severe warning.
          this.logger.error(
            `Non-alternating roles detected that cannot be merged: last=${lastRole}, current=${currentRole}. This might cause API errors.`,
          );
        }
      }

      geminiMessages.push({ role: currentRole, parts });
      lastRole = currentRole;
    }
    return geminiMessages;
  }

  /**
   * Creates a Vertex AI Gemini chat completion stream.
   */
  async createChatCompletionStream(
    options: LlmStreamOptions,
  ): Promise<Readable> {
    const {
      modelConfig,
      messages,
      temperature,
      conversationId,
      dialogId,
      userMessageId,
      user,
      useGoogle,
      searchSources,
      requestedModelName,
    } = options; // Added useGoogle, searchSources, and requestedModelName
    const modelName = modelConfig.model_name; // e.g., gemini-1.5-pro-preview-0409

    // --- Determine if tools should be used ---
    const canBindTools = this.googleSearchTool && useGoogle;
    const tools: Tool[] =
      canBindTools && this.googleSearchTool ? [this.googleSearchTool] : [];
    // Gemini 2.5 models currently do NOT support function-calling / tools. Enabling tools causes
    // the Vertex AI endpoint to return 400 INVALID_ARGUMENT. Detect such models and disable tools
    // proactively to avoid run-time failures until official support is added.
    let shouldBindTools = tools.length > 0;
    if (shouldBindTools && /^gemini-2\.5/.test(modelName)) {
      this.logger.warn(
        `Tools disabled for model ${modelName} because this Gemini version does not yet support function calling.`,
      );
      shouldBindTools = false;
    }

    this.logger.debug(
      `Vertex Gemini tools available: ${tools.length}, canBindTools: ${canBindTools}, shouldBindTools: ${shouldBindTools}`,
    );

    this.logger.log(
      `Creating Vertex AI Gemini stream for model ${modelName} by user ${user.userId}`,
    );

    // --- Initialize Vertex AI Client ---
    const projectId = this.configService.get<string>(
      'GOOGLE_VERTEXAI_PROJECT_ID',
    ); // Corrected key
    const location = this.configService.get<string>('GOOGLE_VERTEXAI_REGION'); // Corrected key, e.g., us-central1
    // Optional: Configure credentials via environment variable GOOGLE_APPLICATION_CREDENTIALS
    // pointing to a service account key file.
    const serviceAccountKeyPath = this.configService.get<string>(
      'GOOGLE_APPLICATION_CREDENTIALS',
    );

    if (!projectId || !location) {
      this.logger.error('Google Project ID or Location not configured.');
      throw new InternalServerErrorException(
        'Configuration error for Vertex AI.',
      );
    }

    let vertexAIClient: VertexAI;
    try {
      vertexAIClient = new VertexAI({
        project: projectId,
        location: location,
        // The SDK typically uses ADC automatically if GOOGLE_APPLICATION_CREDENTIALS is set.
        // Explicitly passing credentials here might override ADC.
        googleAuthOptions: {
          credentials: serviceAccountKeyPath
            ? JSON.parse(
                require('fs').readFileSync(serviceAccountKeyPath, 'utf8'),
              )
            : undefined,
        },
      });
    } catch (error) {
      const message =
        error instanceof Error
          ? error.message
          : 'Unknown Vertex AI client init error';
      const stack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Failed to initialize Vertex AI client: ${message}`,
        stack,
      );
      throw new InternalServerErrorException(
        'Failed to initialize Vertex AI client.',
      );
    }

    // Instantiate models
    const generativeModel = vertexAIClient.getGenerativeModel({
      model: modelName, // Use the model name directly as Llama is handled elsewhere
      // generationConfig: { // Optional: configure things like stopSequences
      //   maxOutputTokens: modelConfig.max_token_completion,
      //   temperature: temperature,
      // },
      // safetySettings: [{category: ..., threshold: ...}] // Optional
    });

    // --- Prepare Vertex AI Request ---
    // Extract system instruction if present and tools are enabled
    let systemInstruction: Content | undefined;
    let filteredMessages = messages;

    if (shouldBindTools) {
      const systemMessageIndex = messages.findIndex(
        (msg) => msg.role === 'system',
      );
      if (systemMessageIndex !== -1) {
        const systemMsg = messages[systemMessageIndex];
        systemInstruction = {
          role: 'system',
          parts: [{ text: systemMsg.content as string }],
        };
        filteredMessages = messages.filter(
          (_, index) => index !== systemMessageIndex,
        );
        this.logger.debug(
          `Extracted system instruction for tool-enabled Gemini call.`,
        );
      }
    }

    const geminiMessages: Content[] = this.transformMessagesToGemini(
      filteredMessages,
      shouldBindTools,
    );

    // Check if messages are empty after filtering
    if (geminiMessages.length === 0 && !systemInstruction) {
      // Also check if systemInstruction is present
      this.logger.error(
        `No valid messages or system instruction remaining after transformation for conversation ${conversationId}`,
      );
      throw new BadRequestException(
        'Input results in no valid content to send to the model.',
      );
    }

    // --- Create Stream ---
    const passThroughStream = new PassThrough();

    // "Prime the pump" - write an initial comment/event immediately
    // to help establish the SSE connection before async operations.
    passThroughStream.write(': init\n\n');

    const streamAndLog = async () => {
      let fullResponseText = '';
      let completionTokens = 0; // Gemini API might provide these differently
      let promptTokens = 0; // Or might need calculation
      let sources: Source[] = searchSources || []; // Use provided searchSources or initialize empty array
      let sourcesSent = false; // New: Flag to ensure sources are sent only once

      // If we have searchSources, send them immediately as they're already available
      if (searchSources && searchSources.length > 0) {
        this.logger.log(
          `[Vertex Gemini] Sending ${searchSources.length} pre-computed search sources to client.`,
        );
        try {
          // Validate source data structure before serialization
          const cleanedSources = searchSources
            .map((source) => ({
              title: String(source.title || '').trim(),
              link: String(source.link || '').trim(),
              snippet: String(source.snippet || '').trim(),
            }))
            .filter((source) => source.title && source.link && source.snippet);

          const sourceData = { type: 'sources', sources: cleanedSources };
          const jsonString = JSON.stringify(sourceData);
          passThroughStream.write(`data: ${jsonString}\n\n`);
          sourcesSent = true;
          this.logger.debug(
            `[Vertex Gemini] Sources chunk sent successfully (${cleanedSources.length} valid sources)`,
          );
        } catch (jsonError) {
          this.logger.error(
            `[Vertex Gemini] Failed to serialize sources for streaming: ${jsonError}`,
          );
          this.logger.debug(
            `[Vertex Gemini] Problematic sources data:`,
            searchSources,
          );
        }
      }

      try {
        if (shouldBindTools) {
          this.logger.debug(
            `Setting up AgentExecutor for Vertex Gemini (streaming).`,
          );
          // Langchain's AgentExecutor expects BaseChatModel, not VertexAI's GenerativeModel directly.
          // We need to use ChatVertexAI from @langchain/google-vertexai to bridge this.
          const langchainGeminiModel = new (
            await import('@langchain/google-vertexai')
          ).ChatVertexAI({
            model: modelName,
            temperature: temperature,
            maxOutputTokens: modelConfig.max_token_completion,
            // project and location are configured at the VertexAI client level, not here
          });

          const agent = await createOpenAIToolsAgent({
            llm: langchainGeminiModel,
            tools,
            prompt: AGENT_PROMPT,
          });
          const executor = new AgentExecutor({ agent, tools });

          // Convert Gemini messages back to Langchain BaseMessage format for the agent
          const langchainMessages: BaseMessage[] = messages.map((msg) => {
            if (msg.role === 'user')
              return new HumanMessage(msg.content as string);
            if (msg.role === 'assistant')
              return new AIMessage(msg.content as string);
            if (msg.role === 'system')
              return new SystemMessage(msg.content as string);
            return new HumanMessage(msg.content as string); // Fallback
          });

          const agentInput = {
            input: langchainMessages[langchainMessages.length - 1].content,
            chat_history: langchainMessages.slice(0, -1),
          };
          const config: RunnableConfig = {};

          this.logger.debug(
            `[LLM Request Log - Streaming Agent Vertex Gemini] Model: ${modelName}, Input: ${JSON.stringify(agentInput)}, Params: { Temp: ${temperature}, MaxTokens: ${modelConfig.max_token_completion} }`,
          );
          const langchainStream = await executor.stream(agentInput, config);

          for await (const chunk of langchainStream) {
            this.logger.debug(
              `[Agent Stream Debug] Chunk: ${JSON.stringify(chunk)}`,
            );

            const usageMetadata =
              chunk?.response_metadata?.tokenUsage ??
              chunk?.response_metadata?.usage_metadata;
            const chunkPromptTokens =
              (usageMetadata?.promptTokenCount as number) ??
              (usageMetadata?.prompt_token_count as number) ??
              0;
            const chunkCompletionTokens =
              (usageMetadata?.completionTokenCount as number) ??
              (usageMetadata?.candidates_token_count as number) ??
              0;
            if (chunkPromptTokens > 0) promptTokens = chunkPromptTokens;
            if (chunkCompletionTokens > 0)
              completionTokens += chunkCompletionTokens;

            if (!sourcesSent && Array.isArray(chunk.intermediateSteps)) {
              this.logger.debug(
                `[Agent Stream Debug] Checking intermediateSteps for sources: ${JSON.stringify(chunk.intermediateSteps)}`,
              );
              for (const step of chunk.intermediateSteps) {
                if (
                  step?.action?.tool === 'google-custom-search' &&
                  typeof step.observation === 'string'
                ) {
                  this.logger.log(
                    `[Agent Stream] Found Google Search observation, parsing...`,
                  );
                  sources = this.parseGoogleSearchResults(step.observation);
                  if (sources.length > 0) {
                    this.logger.log(
                      `[Agent Stream] Sending ${sources.length} sources to client.`,
                    );
                    const sourceData = { type: 'sources', sources: sources };
                    passThroughStream.write(
                      `data: ${JSON.stringify(sourceData)}\n\n`,
                    );
                    sourcesSent = true;
                    break;
                  }
                }
              }
            }

            const outputChunk = chunk?.output;
            if (
              outputChunk &&
              typeof outputChunk === 'string' &&
              outputChunk.length > 0
            ) {
              fullResponseText += outputChunk;
              const streamData = {
                id: `chatcmpl-${uuidv4()}`,
                object: 'chat.completion.chunk',
                created: Math.floor(Date.now() / 1000),
                model: modelName,
                conversation_uuid: dialogId,
                choices: [
                  {
                    index: 0,
                    delta: { content: outputChunk },
                    finish_reason: null,
                  },
                ],
              };
              passThroughStream.write(
                `data: ${JSON.stringify(streamData)}\n\n`,
              );
            }
          }
        } else {
          // Original Vertex AI direct streaming logic
          const streamResult: StreamGenerateContentResult =
            await generativeModel.generateContentStream({
              contents: geminiMessages,
              generationConfig: {
                temperature: temperature,
                maxOutputTokens: modelConfig.max_token_completion,
              },
              systemInstruction: systemInstruction, // Pass system instruction here
            });

          this.logger.debug(
            `Vertex message  ${JSON.stringify(geminiMessages)}`,
          );
          this.logger.debug(
            `Vertex temperature  ${JSON.stringify(temperature)}`,
          );
          this.logger.debug(
            `Vertex AI stream  ${JSON.stringify(streamResult)}`,
          );

          for await (const item of streamResult.stream) {
            if (item.candidates && item.candidates.length > 0) {
              const candidate = item.candidates[0];
              if (
                candidate.content &&
                candidate.content.parts &&
                candidate.content.parts.length > 0
              ) {
                const part = candidate.content.parts[0];
                if (part.text && part.text.trim() !== '') {
                  fullResponseText += part.text;
                  const streamData = {
                    id: `chatcmpl-${uuidv4()}`,
                    object: 'chat.completion.chunk',
                    created: Math.floor(Date.now() / 1000),
                    model: modelName,
                    conversation_uuid: dialogId,
                    choices: [
                      {
                        index: 0,
                        delta: { content: part.text },
                        finish_reason: null,
                      },
                    ],
                  };
                  passThroughStream.write(
                    `data: ${JSON.stringify(streamData)}\n\n`,
                  );
                }
              }
            }
            if (item.usageMetadata) {
              promptTokens =
                item.usageMetadata.promptTokenCount ?? promptTokens;
              completionTokens += item.usageMetadata.candidatesTokenCount ?? 0;
            }
          }
          const aggregatedResponse = await streamResult.response;
          this.logger.debug(
            `Aggregated Response: ${JSON.stringify(aggregatedResponse)}`,
          );
          if (aggregatedResponse.usageMetadata) {
            promptTokens =
              aggregatedResponse.usageMetadata.promptTokenCount ?? promptTokens;
            completionTokens = aggregatedResponse.usageMetadata.totalTokenCount
              ? aggregatedResponse.usageMetadata.totalTokenCount - promptTokens
              : completionTokens;
          }
        }

        // Send sources chunk if sources were found (and not already sent mid-stream)
        if (sources.length > 0 && !sourcesSent) {
          this.logger.log(
            `[Streaming End] Sending ${sources.length} sources to client before DONE.`,
          );
          const sourceData = { type: 'sources', sources: sources };
          passThroughStream.write(`data: ${JSON.stringify(sourceData)}\n\n`);
        }

        // --- CRITICAL: Log Assistant Message and Tokens BEFORE ending stream ---
        // This ensures all database operations complete before frontend redirects
        await this.logAssistantMessage(
          conversationId,
          dialogId,
          userMessageId,
          requestedModelName ?? modelConfig.model_name,
          temperature,
          fullResponseText,
          promptTokens,
          completionTokens,
          user.userId,
          sources, // Pass sources to logAssistantMessage
        );
        this.logger.log(
          `[Vertex Gemini] ✅ Message and ${sources.length} sources saved BEFORE ending stream for ${dialogId}`,
        );

        if (promptTokens > 0) {
          await this.updateUserMessageTokens(userMessageId, promptTokens);
        }

        // Send final [DONE] message for SSE
        const doneData = {
          id: `chatcmpl-${uuidv4()}`,
          object: 'chat.completion.chunk',
          created: Math.floor(Date.now() / 1000),
          model: modelName,
          conversation_uuid: dialogId,
          choices: [{ index: 0, delta: {}, finish_reason: 'stop' }],
          usage:
            promptTokens > 0 || completionTokens > 0
              ? {
                  prompt_tokens: promptTokens,
                  completion_tokens: completionTokens,
                  total_tokens: promptTokens + completionTokens,
                }
              : undefined,
        };
        passThroughStream.write(`data: ${JSON.stringify(doneData)}\n\n`);
        passThroughStream.write('data: [DONE]\n\n');
        await sleep(100);

        // Update token usage for monthly tracking
        if (user?.userId && requestedModelName) {
          try {
            const actualPromptTokens = promptTokens || 0;
            const actualCompletionTokens = completionTokens || 0;
            const actualTotalTokens =
              actualPromptTokens + actualCompletionTokens;

            await this.tokenUsageService.updateTokenUsage({
              username: user.userId,
              modelName: requestedModelName,
              tokenDate: getHongKongDateOnly(),
              promptTokens: actualPromptTokens,
              completionTokens: actualCompletionTokens,
              totalTokens: actualTotalTokens,
              isApi: false, // This is a web UI request, not API
            });

            this.logger.debug(
              `[Vertex Gemini] Updated token usage for ${user.userId}/${requestedModelName}: prompt=${actualPromptTokens}, completion=${actualCompletionTokens}, total=${actualTotalTokens}`,
            );
          } catch (error) {
            this.logger.error(
              `Failed to update token usage for Vertex Gemini: ${error}`,
            );
          }
        }

        // CRITICAL: Only end stream after ALL database operations are complete
        this.logger.debug(
          `[Vertex Gemini] All database operations completed, ending stream for ${dialogId}`,
        );
        passThroughStream.end();
      } catch (error: unknown) {
        const message =
          error instanceof Error
            ? error.message
            : 'Unknown error during Vertex AI stream';
        const stack = error instanceof Error ? error.stack : undefined;
        this.logger.error(
          `Error in Vertex AI stream for conversation ${conversationId}: ${message}`,
          stack,
        );
        const errorPayload = {
          error: {
            message: message,
            code:
              error instanceof Error && (error as any).code
                ? (error as any).code
                : 500,
          },
        };
        passThroughStream.write(`data: ${JSON.stringify(errorPayload)}\n\n`);
        passThroughStream.destroy(
          error instanceof Error ? error : new Error(message),
        );
      }
    };

    streamAndLog();
    return passThroughStream;
  }

  // --- Logging Methods (Copied from AzureLlmService, ensure PrismaService is available) ---
  private async logAssistantMessage(
    conversationId: number,
    dialogId: string,
    userMessageId: number,
    modelName: string,
    temperature: number | undefined,
    responseText: string,
    promptTokens: number | null,
    completionTokens: number | null,
    userId: string,
    sources: Source[] = [], // New: Added sources parameter with default empty array
  ) {
    if (!responseText || responseText.trim() === '') {
      this.logger.warn(
        `Skipping saving empty assistant message for conversation UUID ${dialogId}`,
      );
      return;
    }

    const assistantMessageUUID = uuidv4();
    const receivedAt = getHongKongTime();
    const encryptionKeyName = this.configService.getOrThrow<string>(
      'DB_ENCRYPTION_KEY_NAME',
    );
    const decryptionCertName = this.configService.getOrThrow<string>(
      'DB_DECRYPTION_CERT_NAME',
    );

    this.logger.debug(
      `Saving assistant message via sp_cvst_InsertMessageWithPrompts for conv ${dialogId}...`,
    );
    try {
      const result: { inserted_message_id: number }[] = await this.prisma
        .$queryRaw`
          EXEC sp_cvst_InsertMessageWithPrompts
            @message_uuid = ${assistantMessageUUID},
            @conversation_uuid = ${dialogId},
            @last_prompt = ${responseText},
            @temperature = ${temperature},
            @instance_name = ${null},
            @model_name = ${modelName},
            @sender = 'assistant',
            @token_spent = ${completionTokens ?? 0},
            @ssoid = ${userId},
            @received_at = ${receivedAt},
            @dialog_id = ${dialogId},
            @encryption_key_name = ${encryptionKeyName},
            @decryption_cert_name = ${decryptionCertName}`;

      if (!result || !result[0]?.inserted_message_id) {
        throw new Error(
          'Stored procedure did not return the inserted message ID.',
        );
      }
      const assistantMessageId = result[0].inserted_message_id;
      this.logger.log(
        `Assistant message saved via SP with ID: ${assistantMessageId} for conv ${dialogId}`,
      );

      // New: Save sources if available
      if (sources.length > 0) {
        await this.saveSources(assistantMessageId, sources);
      }
    } catch (dbError: unknown) {
      const message =
        dbError instanceof Error
          ? dbError.message
          : 'Unknown DB error logging assistant message via SP';
      const stack = dbError instanceof Error ? dbError.stack : undefined;
      this.logger.error(
        `Failed to log assistant message via SP for conversation ${dialogId}: ${message}`,
        stack,
      );
    }
  }

  private async updateUserMessageTokens(
    messageId: number,
    promptTokens: number,
  ) {
    try {
      await this.prisma.message.update({
        where: { message_id: messageId },
        data: { token_spent: promptTokens },
      });
      this.logger.log(
        `Updated prompt token count (${promptTokens}) for user message ID: ${messageId}`,
      );
    } catch (error: unknown) {
      const message =
        error instanceof Error
          ? error.message
          : 'Unknown error updating token count';
      const stack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Failed to update token count for user message ${messageId}: ${message}`,
        stack,
      );
    }
  }

  // New: Helper to save sources (Copied from chat-completion.service.ts)
  private async saveSources(
    messageId: number,
    sources: Source[],
  ): Promise<void> {
    if (!sources || sources.length === 0 || messageId === -1) {
      if (messageId !== -1)
        this.logger.debug(
          `[SaveSources] No sources provided for message ID ${messageId}. Skipping.`,
        );
      return;
    }
    if (!messageId) {
      this.logger.error(
        `[SaveSources] Invalid messageId provided: ${messageId}. Cannot save sources.`,
      );
      return;
    }

    this.logger.log(
      `[SaveSources] Attempting to save ${sources.length} sources for message ID ${messageId}...`,
    );

    try {
      for (const source of sources) {
        if (!source.title || !source.link || !source.snippet) {
          this.logger.warn(
            `[SaveSources] Skipping source with missing data for message ID ${messageId}: ${JSON.stringify(source)}`,
          );
          continue;
        }

        await this.prisma.$executeRaw`
                      EXEC sp_cvst_InsertMessageSource
                          @message_id = ${messageId},
                          @title = ${source.title},
                          @link = ${source.link},
                          @snippet = ${source.snippet},
                          @encryption_key_name = ${this.configService.getOrThrow<string>('DB_ENCRYPTION_KEY_NAME')},
                          @decryption_cert_name = ${this.configService.getOrThrow<string>('DB_DECRYPTION_CERT_NAME')};
                  `;
      }
      this.logger.log(
        `[SaveSources] Successfully executed SP for ${sources.length} sources for message ID ${messageId}.`,
      );
    } catch (error: any) {
      this.logger.error(
        `[SaveSources] Failed to save sources via SP for message ID ${messageId}: ${error.message}`,
        error.stack,
      );
    }
  }

  // New: Helper to parse Google Search results (Copied from chat-completion.service.ts)
  private parseGoogleSearchResults(content: string): Source[] {
    let sources: Source[] = [];
    if (!content || typeof content !== 'string') {
      this.logger.warn(
        '[SourceParsing] Received invalid or non-string content for parsing.',
      );
      return sources;
    }

    this.logger.debug(
      `[SourceParsing] Attempting to parse JSON content: "${content.substring(0, 150)}..."`,
    );

    try {
      const resultsArray = JSON.parse(content);

      if (Array.isArray(resultsArray)) {
        sources = resultsArray
          .map((item: any) => ({
            title: item.title || 'N/A',
            link: item.link || '#',
            snippet: item.snippet || 'N/A',
          }))
          .filter((source) => source.link !== '#');
      } else {
        this.logger.warn('[SourceParsing] Parsed content is not an array.');
      }
    } catch (error: any) {
      this.logger.error(
        `[SourceParsing] Failed to parse JSON content: ${error.message}`,
        error.stack,
      );
      sources = [];
    }

    this.logger.log(
      `[SourceParsing] Parsed ${sources.length} sources from JSON.`,
    );
    return sources;
  }

  /**
   * Generates non-streaming text content from a simple prompt.
   */
  async generateText(
    modelName: string,
    prompt: string,
    temperature?: number,
  ): Promise<GenerateTextResult> {
    this.logger.log(
      `Generating text with Vertex AI Gemini for model ${modelName}`,
    );

    // --- Initialize Vertex AI Client ---
    const projectId = this.configService.get<string>(
      'GOOGLE_VERTEXAI_PROJECT_ID',
    );
    const location = this.configService.get<string>('GOOGLE_VERTEXAI_REGION');

    if (!projectId || !location) {
      this.logger.error(
        'Google Project ID or Location not configured for generateText.',
      );
      throw new InternalServerErrorException(
        'Configuration error for Vertex AI.',
      );
    }

    const vertexAIClient = new VertexAI({
      project: projectId,
      location: location,
    });

    const generativeModel = vertexAIClient.getGenerativeModel({
      model: modelName,
    });

    try {
      const result: GenerateContentResult =
        await generativeModel.generateContent({
          contents: [{ role: 'user', parts: [{ text: prompt }] }],
          generationConfig: {
            temperature: temperature ?? 0.7, // Default temperature if not provided
          },
        });

      const response = result.response;
      const responseText =
        response.candidates?.[0]?.content?.parts?.[0]?.text ?? '';

      if (!responseText) {
        this.logger.warn(
          `Received empty response from Gemini model ${modelName}`,
        );
      }

      // Extract token usage from response
      const usageMetadata = response.usageMetadata;
      const promptTokens = usageMetadata?.promptTokenCount ?? 0;
      const completionTokens = usageMetadata?.candidatesTokenCount ?? 0;
      const totalTokens =
        usageMetadata?.totalTokenCount ?? promptTokens + completionTokens;

      this.logger.debug(
        `Vertex Gemini generateText token usage - prompt: ${promptTokens}, completion: ${completionTokens}, total: ${totalTokens}`,
      );

      return {
        text: responseText,
        tokenUsage: {
          promptTokens,
          completionTokens,
          totalTokens,
        },
      };
    } catch (error) {
      const message =
        error instanceof Error
          ? error.message
          : 'Unknown error during Vertex AI text generation';
      const stack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error in Vertex AI text generation for model ${modelName}: ${message}`,
        stack,
      );
      throw new InternalServerErrorException(
        'Failed to generate text with Vertex AI.',
      );
    }
  }
}
