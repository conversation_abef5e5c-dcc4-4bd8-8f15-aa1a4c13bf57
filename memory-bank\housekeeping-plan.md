# Housekeeping Plan: Remove Unused Code (NestJS & Next.js)

## Scope
- **Backend:** `hkbu-genai-platform/apps/api/` (NestJS)
- **Frontend:** `hkbu-genai-platform/apps/web/` (Next.js)
- **No "experimental" or "legacy" folders found in these paths.**

---

## Step-by-Step Plan

### 1. Inventory Code Artifacts
- **Frontend (Next.js):**
  - Components: `.tsx` files in `src/components/`, `src/app/`, and related folders.
  - Utility functions/classes: `.ts`/`.tsx` in `src/lib/`, `src/utils/`, etc.
- **Backend (NestJS):**
  - Services: `*.service.ts`
  - Controllers: `*.controller.ts`
  - Modules: `*.module.ts`
  - DTOs and other classes: `*.dto.ts`, `*.ts` in `src/`

### 2. Automated Unused Code Detection
- For each file/class/service/component:
  - Search for imports/references across the respective app directory.
  - Mark as "unused" if not imported or referenced anywhere (excluding test/config).
- Tools/approaches:
  - Use regex search to find references.
  - Optionally, use static analysis tools (e.g., `ts-prune`, `ts-unused-exports`, or custom scripts).

### 3. Generate Reviewable Report
- Create a markdown or CSV file listing all unused files/classes/services, grouped by frontend/backend.
- Include file path, type (component, service, etc.), and reason for marking as unused.

### 4. Review and Confirm
- User reviews the list and confirms which items to delete.
- (Optional) Mark any false positives to exclude from deletion.

### 5. Cleanup (in implementation mode)
- Delete confirmed unused files/classes/services.
- Run build/tests to ensure nothing is broken.

---

## Mermaid Diagram: Process Overview

```mermaid
flowchart TD
    A[Inventory code artifacts] --> B[Automated search for references/imports]
    B --> C{Is artifact referenced?}
    C -- Yes --> D[Keep]
    C -- No --> E[Add to unused list]
    E --> F[Generate reviewable report]
    F --> G[User reviews and confirms deletions]
    G --> H[Delete confirmed unused code]
    H --> I[Run build/tests]
    I --> J[End]
```

---

**Definition of unused:** Not imported or referenced anywhere in the codebase (excluding test/config).  
**No deletion will occur until you review and confirm the list.**