-- Check token usage for embedding models
-- This query helps verify that embedding models are tracking correctly

-- Check all embedding-related token usage entries
SELECT 
    username,
    model_name,
    is_api,
    SUM(token_spent) as total_tokens,
    <PERSON><PERSON>(token_spent_user) as prompt_tokens,
    <PERSON><PERSON>(token_spent_assistant) as completion_tokens,
    COUNT(*) as record_count,
    MAX(update_dt) as last_updated
FROM acl_user_token_spent
WHERE 
    username = 'sunnypoon'
    AND model_name LIKE '%embedding%'
    AND token_date = CAST(GETDATE() AS DATE)
GROUP BY username, model_name, is_api
ORDER BY model_name, is_api;

-- Check for any potential duplicate model names
SELECT 
    model_name,
    COUNT(DISTINCT model_name) as unique_names,
    COUNT(*) as total_records,
    SUM(token_spent) as total_tokens
FROM acl_user_token_spent
WHERE 
    username = 'sunnypoon'
    AND model_name LIKE '%embedding%'
    AND token_date = CAST(GETDATE() AS DATE)
GROUP BY model_name
HAVING COUNT(*) > 1
ORDER BY model_name;