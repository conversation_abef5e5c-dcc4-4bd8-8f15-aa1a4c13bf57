import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { PrismaService } from '../prisma/prisma.service';
import { MailerService } from '@nestjs-modules/mailer';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class CronService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly mailerService: MailerService,
    private readonly configService: ConfigService,
  ) {}

  @Cron('0 3 * * *')
  async handleHouseKeepingConversation() {
    if (this.configService.get<string>('RUN_CRON_JOBS') !== 'true') {
      return;
    }
    await this.prisma.$executeRawUnsafe(
      'EXEC sp_housekeep_genai_platform_conversation',
    );
  }

  @Cron('0 * * * *')
  async handleModelUsageAlert() {
    if (this.configService.get<string>('RUN_CRON_JOBS') !== 'true') {
      return;
    }
    const total_staff = parseInt(this.configService.get<string>('TOTAL_STAFF', '0'), 10);
    const total_student = parseInt(this.configService.get<string>('TOTAL_STUDENT', '0'), 10);

    const models = await this.prisma.model_list.findMany();

    for (const model of models) {
      if (!model.model_name) {
        continue;
      }
      // STAFF - Monthly
      if (model.stf_monthly_token_limit) {
        const staffMonthlyUsage: any = await this.prisma.$queryRaw`EXEC sp_cvst_GetTokenUsageMonthlyByEmployeeType 'STAFF', ${model.model_name}`;
        if (staffMonthlyUsage.length > 0) {
          const token_spent_monthly = staffMonthlyUsage[0].token_spent;
          const max_monthly_token_spent = model.stf_monthly_token_limit * total_staff;
          const threshold = this.getThreshold(token_spent_monthly, max_monthly_token_spent);
          if (model.stf_threshold !== threshold) {
            await this.prisma.model_list.update({
              where: { id: model.id },
              data: { stf_threshold: threshold },
            });
            if (threshold !== 0) {
              await this.sendEmail(
                'STAFF',
                model.model_name,
                token_spent_monthly,
                max_monthly_token_spent,
                threshold,
                'Monthly'
              );
            }
          }
        }
      }

      // STAFF - Daily
      if (model.stf_monthly_token_limit) {
        const staffDailyUsage: any = await this.prisma.$queryRaw`EXEC sp_cvst_GetTokenUsageDailyByEmployeeType 'STAFF', ${model.model_name}`;
        if (staffDailyUsage.length > 0) {
          const token_spent_daily = staffDailyUsage[0].token_spent;
          const max_daily_token_spent = (model.stf_monthly_token_limit * total_staff) / 30;
          const threshold = this.getThresholdDaily(token_spent_daily, max_daily_token_spent);
          if (model.stf_threshold_daily !== threshold) {
            await this.prisma.model_list.update({
              where: { id: model.id },
              data: { stf_threshold_daily: threshold },
            });
            if (threshold !== 0) {
              await this.sendEmail(
                'STAFF',
                model.model_name,
                token_spent_daily,
                max_daily_token_spent,
                threshold,
                'Daily'
              );
            }
          }
        }
      }

      // STUDENT - Monthly
      if (model.std_monthly_token_limit) {
        const studentMonthlyUsage: any = await this.prisma.$queryRaw`EXEC sp_cvst_GetTokenUsageMonthlyByEmployeeType 'STUDENT', ${model.model_name}`;
        if (studentMonthlyUsage.length > 0) {
          const token_spent_monthly = studentMonthlyUsage[0].token_spent;
          const max_monthly_token_spent = model.std_monthly_token_limit * total_student;
          const threshold = this.getThreshold(token_spent_monthly, max_monthly_token_spent);
          if (model.std_threshold !== threshold) {
            await this.prisma.model_list.update({
              where: { id: model.id },
              data: { std_threshold: threshold },
            });
            if (threshold !== 0) {
              await this.sendEmail(
                'STUDENT',
                model.model_name,
                token_spent_monthly,
                max_monthly_token_spent,
                threshold,
                'Monthly'
              );
            }
          }
        }
      }

      // STUDENT - Daily
      if (model.std_monthly_token_limit) {
        const studentDailyUsage: any = await this.prisma.$queryRaw`EXEC sp_cvst_GetTokenUsageDailyByEmployeeType 'STUDENT', ${model.model_name}`;
        if (studentDailyUsage.length > 0) {
          const token_spent_daily = studentDailyUsage[0].token_spent;
          const max_daily_token_spent = (model.std_monthly_token_limit * total_student) / 30;
          const threshold = this.getThresholdDaily(token_spent_daily, max_daily_token_spent);
          if (model.std_threshold_daily !== threshold) {
            await this.prisma.model_list.update({
              where: { id: model.id },
              data: { std_threshold_daily: threshold },
            });
            if (threshold !== 0) {
              await this.sendEmail(
                'STUDENT',
                model.model_name,
                token_spent_daily,
                max_daily_token_spent,
                threshold,
                'Daily'
              );
            }
          }
        }
      }
    }
  }

  private getThreshold(spent: number, max: number) {
    const usage = (spent / max) * 100;
    const thresholds = [100, 90, 80, 70, 50, 20];
    for (const threshold of thresholds) {
      if (usage >= threshold) {
        return threshold;
      }
    }
    return 0;
  }

  private getThresholdDaily(spent: number, max: number) {
    const usage = (spent / max) * 100;
    const thresholds = [80, 60, 40, 20];
    for (const threshold of thresholds) {
      if (usage >= threshold) {
        return threshold;
      }
    }
    return 0;
  }

  private async sendEmail(
    employeeType: string,
    modelName: string,
    usage: number,
    limit: number,
    threshold: number,
    period: 'Monthly' | 'Daily'
  ) {
    await this.mailerService.sendMail({
      to: this.configService.get<string>('USAGE_ALERT_EMAIL'),
      subject: `[ChatGPT] ${period} Token Limit threshold ${threshold}% Reached`,
      html: `Employee Type: ${employeeType} <br /> Model: ${modelName} <br /> Total Usage: ${usage} <br />  ${period} Token LIMIT: ${limit} <br /> reached the ${Math.round(
        (usage / limit) * 100,
      )}% of ${period.toLowerCase()} token limit.`,
    });
  }

  @Cron('0 13 * * *')
  async handleScecieEligibleStudents() {
    if (this.configService.get<string>('RUN_CRON_JOBS') !== 'true') {
      return;
    }
    const apiEndpoint = this.configService.get<string>('SCERESTAPI_ENDPOINT');
    const apiKey = this.configService.get<string>('SCERESTAPI_KEY');

    if (!apiEndpoint || !apiKey) {
      console.error('SCE REST API endpoint or key not configured.');
      return;
    }

    const response = await fetch(`${apiEndpoint}eligibleStudents`, {
      headers: {
        'x-sce-ito-buchatgpt-eligible-api-key': apiKey,
      },
      method: 'GET',
    });

    if (response.ok) {
      const students = await response.json();
      for (const student of students) {
        await this.prisma.acl_user.create({
          data: {
            username: student.studNo,
            dept_unit_code: 'SCE',
            staff_std_type: 'STUDENT',
            create_by: 'API',
            create_dt: new Date(),
          },
        });
      }
    } else {
      console.error('Failed to fetch eligible students:', await response.text());
    }
  }

  @Cron('15 21 * * *')
  async handleScecieEligibleStudents2() {
    if (this.configService.get<string>('RUN_CRON_JOBS') !== 'true') {
      return;
    }
    return this.handleScecieEligibleStudents();
  }

  @Cron('0 7 * * *')
  async handleSyncSlesMtrDept() {
    if (this.configService.get<string>('RUN_CRON_JOBS') !== 'true') {
      return;
    }
    const apiEndpoint = this.configService.get<string>('SLES_API_ENDPOINT');

    if (!apiEndpoint) {
      console.error('SLES API endpoint not configured.');
      return;
    }

    const response = await fetch(`${apiEndpoint}mtr_dept`, {
      method: 'GET',
    });

    if (response.ok) {
      const sles_data = await response.json();
      if (sles_data.isSuccess) {
        for (const dept of sles_data.data) {
          await this.prisma.mtr_dept.upsert({
            where: { dept_unit_code: dept.dept_unit_code },
            update: {
              parent_unit_code: dept.parent_unit_code,
              dept_unit_desc: dept.dept_unit_desc,
              dept_unit_chi: dept.dept_unit_chi,
              unit_stu: dept.unit_stu,
              unit_nature: dept.unit_nature,
              report_unit_code: dept.report_unit_code,
              pgm_code_ind: dept.pgm_code_ind,
              email: dept.email,
              source: dept.source,
              update_by: dept.update_by,
              update_dt: dept.update_dt,
            },
            create: {
              dept_unit_code: dept.dept_unit_code,
              parent_unit_code: dept.parent_unit_code,
              dept_unit_desc: dept.dept_unit_desc,
              dept_unit_chi: dept.dept_unit_chi,
              unit_stu: dept.unit_stu,
              unit_nature: dept.unit_nature,
              report_unit_code: dept.report_unit_code,
              pgm_code_ind: dept.pgm_code_ind,
              email: dept.email,
              source: dept.source,
              create_by: dept.create_by,
              create_dt: dept.create_dt,
              update_by: dept.update_by,
              update_dt: dept.update_dt,
            },
          });
        }
      }
    } else {
      console.error('Failed to fetch SLES mtr_dept:', await response.text());
    }
  }
}