import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from 'class-validator';

export class CreateFeedbackDto {
  @IsString()
  @IsNotEmpty({ message: 'Feedback content cannot be empty.' })
  @MaxLength(1000, {
    message: 'Feedback content cannot exceed 1000 characters.',
  })
  readonly content!: string; // Added definite assignment assertion

  // Note: The original Next.js route used ssoid from the session. We'll get this from the JWT payload (`req.user`) in the controller.
  // We don't need to include ssoid in the DTO that comes from the request body.

  // Optional fields if the frontend sends them:
  @IsString()
  @IsOptional()
  @MaxLength(50, { message: 'Subject cannot exceed 50 characters.' })
  readonly subject?: string; // Optional properties don't need assertion

  // If the frontend allows anonymous feedback or overrides the logged-in user's email:
  @IsEmail({}, { message: 'Please provide a valid email address.' })
  @IsOptional()
  @MaxLength(100, { message: 'Email cannot exceed 100 characters.' })
  readonly email?: string; // Optional properties don't need assertion
}
