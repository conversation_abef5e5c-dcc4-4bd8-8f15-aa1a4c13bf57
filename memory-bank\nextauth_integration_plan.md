# NextAuth.js Integration Plan

## Objective
Integrate NextAuth.js into the application to protect frontend routes and secure backend API endpoints. The authentication will use a custom BUAM OIDC provider. The application's root path for OAuth communication will be `https://localhost:3001/chatdemo`.

## Phase 1: Configuration & Environment Setup (Web App - `hkbu-genai-platform/apps/web`)

1.  **Update `.env` file ([`hkbu-genai-platform/apps/web/.env`](hkbu-genai-platform/apps/web/.env)):**
    *   Set `NEXTAUTH_URL=https://localhost:3001`.
    *   Set `NEXT_PUBLIC_BASE_PATH=/chatdemo`.
    *   Uncomment and set `NODE_TLS_REJECT_UNAUTHORIZED=0` (for local development with self-signed certificates).

2.  **Update `authOptions.ts` ([`hkbu-genai-platform/apps/web/src/lib/authOptions.ts`](hkbu-genai-platform/apps/web/src/lib/authOptions.ts)):**
    *   Change `secret: process.env.NEXTAUTH_SECRET` to `secret: process.env.BUAM_NEXTAUTH_JWT_SECRET` (line 132).
    *   Uncomment `import prisma from '@/lib/prisma';` (line 5).
    *   Uncomment the original logic within the `signIn` callback (lines 197-274) to re-enable database checks (for `acl_other_user`, `acl_user`, `acl_user_details`). Ensure Prisma client setup is correct and the database schema matches.
    *   The `checkEnableRestfulApi` stub function (line 8) will remain a stub returning `true` for now.

## Phase 2: Protecting Frontend Routes (Web App - `hkbu-genai-platform/apps/web`)

1.  **Implement/Verify `AuthProvider`:**
    *   Ensure [`hkbu-genai-platform/apps/web/src/app/layout.tsx`](hkbu-genai-platform/apps/web/src/app/layout.tsx) or an existing provider like [`hkbu-genai-platform/apps/web/src/app/providers/AuthProvider.tsx`](hkbu-genai-platform/apps/web/src/app/providers/AuthProvider.tsx) wraps the application with `<SessionProvider>` from `next-auth/react`.
2.  **Protect Pages/Components:**
    *   Use `useSession()` hook from `next-auth/react` or `getServerSideProps` with `getSession()` to protect routes and manage access based on authentication status.
    *   Consider creating a higher-order component (HOC) or a wrapper component for protected routes.
3.  **Create/Verify Sign-In UI:**
    *   Ensure the page at `https://localhost:3001/chatdemo/auth/signin` (derived from `NEXTAUTH_URL`, `NEXT_PUBLIC_BASE_PATH`, and `authOptions.pages.signIn`) provides a clear way for users to initiate the "buam" OAuth login flow.

## Phase 3: Securing the API (API App - `hkbu-genai-platform/apps/api`)

1.  **Configure JWT Strategy in NestJS:**
    *   Set up or update the JWT strategy (e.g., in a file like [`hkbu-genai-platform/apps/api/src/auth/jwt.strategy.ts`](hkbu-genai-platform/apps/api/src/auth/jwt.strategy.ts)).
    *   The strategy must use the `BUAM_NEXTAUTH_JWT_SECRET` (sourced from the API's environment variables) to validate incoming JWTs.
    *   Ensure the strategy correctly extracts the user payload from the validated token.
2.  **Update `general.controller.ts` ([`hkbu-genai-platform/apps/api/src/general/general.controller.ts`](hkbu-genai-platform/apps/api/src/general/general.controller.ts)):**
    *   Uncomment the global `@UseGuards(AuthGuard('jwt'))` (line 53).
    *   Remove all hardcoded user objects (e.g., `const user = { userId: 'ken-chow', ... }`).
    *   Replace hardcoded user objects with `req.user` (populated by Passport.js from the JWT) to get authenticated user details. Ensure the `AuthenticatedUser` interface matches the JWT payload.
3.  **API Environment Configuration ([`hkbu-genai-platform/apps/api/.env`](hkbu-genai-platform/apps/api/.env)):**
    *   Add `BUAM_NEXTAUTH_JWT_SECRET` to this file, ensuring its value is identical to the one in the web app's `.env` file.

## Phase 4: Testing & Refinement

1.  **Test Login Flow:**
    *   Access a protected page; verify redirection to BUAM OAuth.
    *   Log in; verify redirection back to the app (`https://localhost:3001/chatdemo/api/auth/callback/buam` then to the target page).
    *   Confirm session establishment and availability of user info.
2.  **Test API Calls:**
    *   Ensure the frontend sends the JWT in the `Authorization: Bearer <token>` header.
    *   Verify API endpoints in `general.controller.ts` correctly authorize based on the token and use the authenticated user's identity.
3.  **Test Logout Flow:**
    *   Implement and test a logout mechanism that clears the NextAuth.js session.

## Authentication Flow Diagram

```mermaid
sequenceDiagram
    participant User
    participant WebApp (Next.js @ localhost:3001/chatdemo)
    participant NextAuthJS (within WebApp)
    participant BUAM_OAuth (issuat.hkbu.edu.hk)
    participant API (NestJS)

    User->>WebApp: Access protected resource
    WebApp->>NextAuthJS: Check session
    alt No active session
        NextAuthJS-->>WebApp: Redirect to sign-in
        WebApp-->>User: Show sign-in page / redirect
        User->>WebApp: Click "Sign in with BUAM"
        WebApp->>NextAuthJS: Initiate BUAM OAuth flow
        NextAuthJS-->>BUAM_OAuth: Redirect user with auth request (code flow)
        User->>BUAM_OAuth: Authenticates
        BUAM_OAuth-->>NextAuthJS: Redirect user with authorization code (via /api/auth/callback/buam)
        NextAuthJS->>BUAM_OAuth: Exchange code for tokens (access_token, id_token)
        BUAM_OAuth-->>NextAuthJS: Returns tokens
        NextAuthJS->>BUAM_OAuth: Fetch user profile (userinfo)
        BUAM_OAuth-->>NextAuthJS: Returns user profile
        NextAuthJS-->>WebApp: Creates session (JWT), sets cookie
        WebApp-->>User: Access granted to protected resource
    else Active session
        WebApp-->>User: Access granted to protected resource
    end

    User->>WebApp: Perform action requiring API call
    WebApp->>API: Request with JWT in Authorization header
    API->>API: Validate JWT (using shared secret/key)
    alt JWT valid
        API-->>WebApp: Process request, return data
    else JWT invalid
        API-->>WebApp: Return 401 Unauthorized
    end
    WebApp-->>User: Display result