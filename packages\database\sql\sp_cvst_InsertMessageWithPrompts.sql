-- =============================================
-- Author:		<PERSON><PERSON> (AI Assistant) / Roo
-- Create date: 2025-04-10
-- Description:	Inserts a message and its associated prompt.
--              Splits the original NVARCHAR prompt into smaller text chunks,
--              encrypts each chunk individually, and stores them in ordered rows
--              in the dbo.prompt table to handle large prompts reliably.
-- Parameters:
--	 @message_uuid: Unique identifier for the message.
--   @conversation_uuid: Unique identifier for the conversation.
--   @last_prompt: The full content of the message (user or assistant).
--   @temperature: The temperature setting used for generation (if applicable).
--   @instance_name: The LLM instance name used.
--   @model_name: The specific LLM model name used.
--   @sender: 'user' or 'assistant'.
--   @token_spent: Token count for the message.
--   @ssoid: The user's SSO ID.
--   @received_at: Timestamp when the message was received/generated by the service.
--   @dialog_id: Optional dialog identifier.
--   @used_mention: Whether user explicitly used @mention for this message (BIT, optional).
--   @encryption_key_name: Name of the symmetric key for encryption.
--   @decryption_cert_name: Name of the certificate for key decryption (used to open the key).
-- Returns:
--   A single row with a column named 'inserted_message_id' containing the ID of the inserted message record.
-- History
-- When			Who			Remarks
-- ... (previous history) ...
-- 2025-04-25   Roo         Adopted text splitting logic from v7 SP before encrypting chunks individually.
-- =============================================
-- IMPORTANT: Ensure corresponding columns in dbo.message (instance_name, model_name, create_by, dialog_id) match SP parameter sizes (100/100/100/255).
-- IMPORTANT: Ensure dbo.prompt.prompt_data is VARBINARY(MAX).
-- IMPORTANT: Retrieval logic (e.g., in sp_cvst_GetDecryptedMessagesByConversationUUID) MUST handle multiple prompt rows per message,
--            order by prompt_order, decrypt each chunk, and concatenate them.
-- =============================================
ALTER PROCEDURE [dbo].[sp_cvst_InsertMessageWithPrompts] -- Use ALTER if procedure exists, CREATE if new
    @message_uuid uniqueidentifier,
    @conversation_uuid uniqueidentifier,
    @last_prompt nvarchar(MAX), -- Full prompt content
    @temperature decimal(2, 1),
    @instance_name varchar(100),
    @model_name varchar(100),
    @sender varchar(20),
    @token_spent int,
    @ssoid varchar(100),
    @received_at datetime,
    @dialog_id varchar(255),
    @used_mention bit = NULL, -- Whether user explicitly used @mention for this message (optional)
    @encryption_key_name nvarchar(128),
    @decryption_cert_name nvarchar(128)
AS
BEGIN
    SET NOCOUNT ON;
    SET XACT_ABORT ON; -- Ensures transaction rollback on error

    DECLARE @conversation_id int;
    DECLARE @inserted_message_id int = NULL;
    DECLARE @sqlOpenKey nvarchar(MAX);
    DECLARE @sqlCloseKey nvarchar(MAX);

    -- Variables for text chunking (based on v7 logic)
    DECLARE @Split INT = 3500; -- Size of NVARCHAR chunks before encryption
    DECLARE @End INT;
    DECLARE @Row INT = 0;
    DECLARE @prompt_chunk_text NVARCHAR(MAX);
    DECLARE @encrypted_chunk VARBINARY(MAX);
    DECLARE @working_prompt NVARCHAR(MAX) = @last_prompt; -- Copy original prompt to modify

    BEGIN TRY
        -- Start Transaction
        BEGIN TRANSACTION;

        -- 1. Find Conversation ID
        SELECT @conversation_id = conversation_id
        FROM dbo.conversation WITH (UPDLOCK)
        WHERE conversation_uuid = @conversation_uuid;

        IF (@conversation_id IS NULL)
        BEGIN
            SELECT @inserted_message_id as inserted_message_id;
            ROLLBACK TRANSACTION;
            RETURN;
        END

        -- 2. Update conversation update_dt
        UPDATE dbo.conversation
        SET update_dt = GETDATE()
        WHERE conversation_id = @conversation_id;

        -- 3. Insert into message table
        INSERT INTO dbo.message (
            message_uuid, conversation_id, temperature, instance_name, model_name,
            sender, token_spent, create_by, create_dt, received_at, dialog_id, used_mention
        )
        VALUES (
            @message_uuid, @conversation_id, @temperature, @instance_name, @model_name,
            @sender, @token_spent, @ssoid, GETDATE(), @received_at, @dialog_id, @used_mention
        );

        -- 4. Get the ID of the inserted message
        SET @inserted_message_id = SCOPE_IDENTITY();

        IF (@inserted_message_id IS NULL)
        BEGIN
             THROW 50001, 'Failed to insert into message table.', 1;
        END

        -- 5. Prepare and Execute Key Opening
        SET @sqlOpenKey = N'OPEN SYMMETRIC KEY ' + QUOTENAME(@encryption_key_name) + N' DECRYPTION BY CERTIFICATE ' + QUOTENAME(@decryption_cert_name) + N';';
        EXEC sp_executesql @sqlOpenKey;

        -- 6. Split original text, encrypt chunks, and insert into dbo.prompt
        SET @Row = 0; -- Reset row counter for prompt_order

        WHILE (LEN(@working_prompt) > 0)
        BEGIN
            SET @Row = @Row + 1; -- Increment prompt_order

            IF (LEN(@working_prompt) > @Split)
            BEGIN
                -- Find a suitable split point (word boundary)
                SET @End = LEN(LEFT(@working_prompt, @Split)) - CHARINDEX(' ', REVERSE(LEFT(@working_prompt, @Split)));
                -- Handle case where no space is found within the split length
                IF @End <= 0 SET @End = @Split;

                SET @prompt_chunk_text = LEFT(@working_prompt, @End);
                SET @working_prompt = SUBSTRING(@working_prompt, @End + 1, LEN(@working_prompt));
            END
            ELSE
            BEGIN
                SET @prompt_chunk_text = @working_prompt;
                SET @working_prompt = '';
            END

            -- Encrypt the current text chunk
            SET @encrypted_chunk = ENCRYPTBYKEY(KEY_GUID(@encryption_key_name), @prompt_chunk_text);

            IF (@encrypted_chunk IS NULL AND @prompt_chunk_text IS NOT NULL AND LEN(@prompt_chunk_text) > 0)
            BEGIN
                 -- Close key before throwing error
                SET @sqlCloseKey = N'CLOSE SYMMETRIC KEY ' + QUOTENAME(@encryption_key_name) + N';';
                EXEC sp_executesql @sqlCloseKey;
                THROW 50003, 'Failed to encrypt prompt chunk.', 1;
            END

            -- Insert the encrypted chunk
            INSERT INTO dbo.prompt (
                message_id,
                prompt_data,
                prompt_order
            )
            VALUES (
                @inserted_message_id,
                @encrypted_chunk, -- Insert the individually encrypted chunk
                @Row -- Use Row as prompt_order
            );
        END

        -- If the original prompt was empty or NULL, insert a single row with NULL data
        IF (@last_prompt IS NULL OR LEN(@last_prompt) = 0)
        BEGIN
             INSERT INTO dbo.prompt (
                message_id,
                prompt_data,
                prompt_order
            )
            VALUES (
                @inserted_message_id,
                NULL, -- Store NULL for empty prompt
                1
            );
        END

        -- 7. Close the Symmetric Key
        SET @sqlCloseKey = N'CLOSE SYMMETRIC KEY ' + QUOTENAME(@encryption_key_name) + N';';
        EXEC sp_executesql @sqlCloseKey;

        -- Commit Transaction
        COMMIT TRANSACTION;

        -- 8. Return the inserted message ID
        SELECT @inserted_message_id as inserted_message_id;

    END TRY
    BEGIN CATCH
        IF (XACT_STATE()) = -1
        BEGIN
            PRINT N'The transaction is in an uncommittable state. Rolling back transaction.'
            ROLLBACK TRANSACTION;
        END
        ELSE IF (XACT_STATE()) = 1
        BEGIN
            PRINT N'Rolling back the transaction.'
            ROLLBACK TRANSACTION;
        END;

        BEGIN TRY
             SET @sqlCloseKey = N'CLOSE SYMMETRIC KEY ' + QUOTENAME(@encryption_key_name) + N';';
             EXEC sp_executesql @sqlCloseKey;
        END TRY
        BEGIN CATCH
             PRINT N'Could not close symmetric key during error handling (might have been closed already).'
        END CATCH

		DECLARE @SpName varchar(100) = OBJECT_NAME(@@PROCID);
		IF OBJECT_ID('dbo.sp_system_HandleSqlError', 'P') IS NOT NULL
		BEGIN
			EXEC dbo.sp_system_HandleSqlError @SpName;
		END
		ELSE
		BEGIN
			THROW;
		END

        SELECT NULL as inserted_message_id;
        RETURN;
    END CATCH;
END
GO
