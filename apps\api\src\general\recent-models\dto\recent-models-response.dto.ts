import { ApiProperty } from '@nestjs/swagger';

export class RecentModelDto {
  @ApiProperty({
    description: 'The unique identifier of the model',
    example: 123,
  })
  id!: number;

  @ApiProperty({
    description: 'The display name of the model',
    example: 'GPT-4 Turbo',
  })
  display_name!: string | null;

  @ApiProperty({
    description: 'The internal model name',
    example: 'gpt-4-turbo',
  })
  model_name!: string | null;

  @ApiProperty({
    description: 'The deployment name of the model',
    example: 'gpt-4-turbo-deployment',
  })
  deployment_name!: string | null;

  @ApiProperty({
    description: 'The category of the model',
    example: 'general',
    nullable: true,
  })
  category!: string | null;

  @ApiProperty({
    description: 'The timestamp when the model was last used',
    example: '2024-01-15T10:30:00.000Z',
    type: String,
    format: 'date-time',
  })
  last_used_at!: string;
}

export class GetRecentModelsResponseDto {
  @ApiProperty({
    description: 'Array of recently used models',
    type: [RecentModelDto],
  })
  models!: RecentModelDto[];

  @ApiProperty({
    description: 'The total number of models returned',
    example: 5,
  })
  count!: number;

  @ApiProperty({
    description: 'The maximum number of models requested',
    example: 10,
  })
  limit!: number;
}
