import React from 'react';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import <PERSON><PERSON><PERSON> from '@/icons/ClaudeLogo';
import DeepSeekLogo from '@/icons/DeepSeekLogo';
import Gemini<PERSON>ogo from '@/icons/GeminiLogo';
import <PERSON><PERSON><PERSON>ogo from '@/icons/OpenAILogo';
import <PERSON>wen<PERSON>ogo from '@/icons/QwenLogo';
import AdobeExpressLogo from '@/icons/AdobeExpressLogo';
import Adobe<PERSON>ireflyLogo from '@/icons/AdobeFireflyLogo';
import <PERSON>lamaLogo from '@/icons/LlamaLogo';

/**
 * Returns the appropriate icon component for a given model name
 * @param modelName - The name of the model (e.g., "gpt-4.1", "claude-3-sonnet", etc.)
 * @returns A React component representing the model's icon
 */
export function getModelIcon(modelName: string | null | undefined): React.ReactElement {
  if (!modelName) {
    return <SmartToyIcon fontSize="small" />;
  }

  // Normalize the model name to lowercase for consistent matching
  const normalizedName = modelName.toLowerCase();

  // OpenAI models
  if (normalizedName.includes('gpt') || 
      normalizedName.includes('chatgpt') || 
      normalizedName.startsWith('o1') || 
      normalizedName.startsWith('o3')) {
    return <OpenAILogo />;
  }

  // Claude models
  if (normalizedName.includes('claude')) {
    return <ClaudeLogo />;
  }

  // Gemini models
  if (normalizedName.includes('gemini')) {
    return <GeminiLogo />;
  }

  // DeepSeek models
  if (normalizedName.includes('deepseek')) {
    return <DeepSeekLogo />;
  }

  // Qwen models
  if (normalizedName.includes('qwen')) {
    return <QwenLogo />;
  }

  // Llama models
  if (normalizedName.includes('llama')) {
    return <LlamaLogo />;
  }

  // Adobe models
  if (normalizedName.includes('adobe express')) {
    return <AdobeExpressLogo />;
  }
  
  if (normalizedName.includes('adobe firefly')) {
    return <AdobeFireflyLogo />;
  }

  // Default fallback icon
  return <SmartToyIcon fontSize="small" />;
}