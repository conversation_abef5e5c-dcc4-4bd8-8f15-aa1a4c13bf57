'use client';

import React, { useState, useEffect } from 'react';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
import Snackbar from '@mui/material/Snackbar';
import Alert from '@mui/material/Alert';
import Select, { SelectChangeEvent } from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import InputLabel from '@mui/material/InputLabel';
import FormHelperText from '@mui/material/FormHelperText';

// Placeholder for humanFileSize and moment, if needed and not readily available
// For now, options will be simple strings or pre-formatted.
// import { humanFileSize } from '@/lib/utils/HumanFileSize'; // Assuming similar utility exists
// import moment from 'moment'; // If complex duration formatting is needed

interface Option {
  value: string;
  displayName: string;
}

const RequestQuoteSection: React.FC = () => {
  const [selectedModel, setSelectedModel] = useState<string>('');
  const [selectedQuote, setSelectedQuote] = useState<string>('');
  const [selectedDuration, setSelectedDuration] = useState<string>('');
  const [reason, setReason] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error'>(
    'success',
  );
  const [shouldDisplayError, setShouldDisplayError] = useState(false);

  // Static options for now
  const modelOptionList: Option[] = [
    { value: 'gpt-4', displayName: 'GPT-4 Series' },
    { value: 'claude-3', displayName: 'Claude 3 Series' },
    { value: 'gemini-1.5', displayName: 'Gemini 1.5 Series' },
    // Add more models as needed or fetch dynamically later
  ];

  const quoteOptionList: Option[] = process.env
    .NEXT_PUBLIC_REQUEST_QUOTE_QUOTE_OPTION
    ? process.env.NEXT_PUBLIC_REQUEST_QUOTE_QUOTE_OPTION.split(',').map(
        (q) => ({
          value: q,
          displayName: `${parseInt(q).toLocaleString()} tokens` /* Simplified display */,
        }),
      )
    : [
        { value: '1000000', displayName: '1,000,000 tokens' },
        { value: '5000000', displayName: '5,000,000 tokens' },
      ];

  const durationOptionList: Option[] = process.env
    .NEXT_PUBLIC_REQUEST_QUOTE_DURATION_OPTION
    ? process.env.NEXT_PUBLIC_REQUEST_QUOTE_DURATION_OPTION.split(',').map(
        (d) => ({
          value: d,
          displayName: `For ${d.replace('P', '').replace('M', ' Month(s)').replace('D', ' Day(s)')}` /* Simplified display */,
        }),
      )
    : [
        { value: 'P1M', displayName: 'For 1 Month' },
        { value: 'P3M', displayName: 'For 3 Months' },
      ];

  const handleSubmit = async () => {
    if (
      !selectedModel ||
      !selectedQuote ||
      !selectedDuration ||
      !reason.trim()
    ) {
      setShouldDisplayError(true);
      setSnackbarMessage('Please fill in all fields.');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
      return;
    }
    setShouldDisplayError(false);
    setIsLoading(true);

    // Placeholder for actual submission logic
    console.log('Requesting quote with:', {
      model: selectedModel,
      quote: selectedQuote,
      duration: selectedDuration,
      reason,
    });
    await new Promise((resolve) => setTimeout(resolve, 1500));

    setSnackbarMessage('Quote request submitted successfully! (Placeholder)');
    setSnackbarSeverity('success');
    setSelectedModel('');
    setSelectedQuote('');
    setSelectedDuration('');
    setReason('');

    setIsLoading(false);
    setSnackbarOpen(true);
  };

  const handleCloseSnackbar = (
    event?: React.SyntheticEvent | Event,
    reason?: string,
  ) => {
    if (reason === 'clickaway') {
      return;
    }
    setSnackbarOpen(false);
  };

  const requestQuoteTitle =
    process.env.NEXT_PUBLIC_REQUEST_QUOTE_TITLE ??
    "Please complete the form below and click 'Submit' to request an increase in your quota.";
  const requestQuoteMessage = process.env.NEXT_PUBLIC_REQUEST_QUOTE_MESSAGE;

  return (
    <Box className="text-sm" sx={{ color: 'text.primary' }}>
      <Typography variant="body1" paragraph sx={{ fontWeight: 'medium' }}>
        {requestQuoteTitle}
      </Typography>
      {requestQuoteMessage && (
        <Typography variant="body2" paragraph>
          {requestQuoteMessage}
        </Typography>
      )}

      <FormControl
        fullWidth
        sx={{ mb: 2 }}
        error={shouldDisplayError && !selectedModel}
      >
        <InputLabel id="model-select-label">Model</InputLabel>
        <Select
          labelId="model-select-label"
          id="model-select"
          value={selectedModel}
          label="Model"
          onChange={(e: SelectChangeEvent) => setSelectedModel(e.target.value)}
          disabled={isLoading}
        >
          {modelOptionList.map((opt) => (
            <MenuItem key={opt.value} value={opt.value}>
              {opt.displayName}
            </MenuItem>
          ))}
        </Select>
        {shouldDisplayError && !selectedModel && (
          <FormHelperText>Please select a model.</FormHelperText>
        )}
      </FormControl>

      <FormControl
        fullWidth
        sx={{ mb: 2 }}
        error={shouldDisplayError && !selectedQuote}
      >
        <InputLabel id="quote-select-label">
          Requested Additional Quota
        </InputLabel>
        <Select
          labelId="quote-select-label"
          id="quote-select"
          value={selectedQuote}
          label="Requested Additional Quota"
          onChange={(e: SelectChangeEvent) => setSelectedQuote(e.target.value)}
          disabled={isLoading}
        >
          {quoteOptionList.map((opt) => (
            <MenuItem key={opt.value} value={opt.value}>
              {opt.displayName}
            </MenuItem>
          ))}
        </Select>
        {shouldDisplayError && !selectedQuote && (
          <FormHelperText>Please select a quota.</FormHelperText>
        )}
      </FormControl>

      <FormControl
        fullWidth
        sx={{ mb: 2 }}
        error={shouldDisplayError && !selectedDuration}
      >
        <InputLabel id="duration-select-label">
          Duration for Additional Quota
        </InputLabel>
        <Select
          labelId="duration-select-label"
          id="duration-select"
          value={selectedDuration}
          label="Duration for Additional Quota"
          onChange={(e: SelectChangeEvent) =>
            setSelectedDuration(e.target.value)
          }
          disabled={isLoading}
        >
          {durationOptionList.map((opt) => (
            <MenuItem key={opt.value} value={opt.value}>
              {opt.displayName}
            </MenuItem>
          ))}
        </Select>
        {shouldDisplayError && !selectedDuration && (
          <FormHelperText>Please select a duration.</FormHelperText>
        )}
      </FormControl>

      <TextField
        fullWidth
        multiline
        rows={4}
        variant="outlined"
        label="Reason for Request"
        value={reason}
        onChange={(e) => setReason(e.target.value)}
        disabled={isLoading}
        inputProps={{ maxLength: 1000 }}
        helperText={
          shouldDisplayError && !reason.trim()
            ? 'Please provide a reason.'
            : `${reason.length}/1000 characters`
        }
        error={shouldDisplayError && !reason.trim()}
        sx={{ mb: 2 }}
      />
      <Button
        variant="contained"
        color="primary"
        onClick={handleSubmit}
        disabled={isLoading}
        startIcon={
          isLoading ? <CircularProgress size={20} color="inherit" /> : null
        }
      >
        {isLoading ? 'Submitting...' : 'Submit Request'}
      </Button>
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbarSeverity}
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default RequestQuoteSection;
