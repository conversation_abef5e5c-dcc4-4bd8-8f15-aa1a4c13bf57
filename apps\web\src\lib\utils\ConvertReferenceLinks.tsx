export const ConvertReferenceLinks = (text: string): string => {
  // Convert regular links with SVG icon
  text = text.replace(
    /\[\[LINK:(https:\/\/[^:]+):([\w.-]+)\]\]/g,
    (_, uri, title) =>
      `<a href="${uri}" class="underline text-[#1a5cff] dark:text-[#60a5fa]" target="_blank">${title}<svg class="inline-block ml-1" width="20" height="20" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M10 6v2H5v11h11v-5h2v6a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V7a1 1 0 0 1 1-1h6zm11-3v8h-2V6.413l-7.793 7.794-1.414-1.414L17.585 5H13V3h8z"/></svg></a>`,
  );

  // Convert marker links without SVG icon and without underline
  text = text.replace(
    /\[\[MARKER_LINK:(https:\/\/[^:]+):(\d+)\]\]/g,
    (_, uri, number) =>
      `<a href="${uri}" class="no-underline text-[#1a5cff] dark:text-[#60a5fa]" target="_blank">[${number}]</a>`,
  );

  return text;
};

export default ConvertReferenceLinks;
