#!/usr/bin/env python3
"""
Test script for the OpenAI-compatible /models endpoint
Tests the public REST API endpoint that lists available models
No authentication required - this is a public endpoint
"""

import requests
import json
import os
from dotenv import load_dotenv
from typing import Dict, List, Any

# Load environment variables
load_dotenv()

class ModelsEndpointTester:
    def __init__(self):
        self.base_url = os.getenv('BASE_URL', 'http://localhost:3003/api/v0')
        self.headers = {
            'Content-Type': 'application/json'
        }
        # API key is not required for models endpoint (public endpoint)
    
    def test_models_endpoint(self) -> Dict[str, Any]:
        """Test the /models endpoint"""
        url = f"{self.base_url}/rest/models"
        
        print(f"Testing models endpoint: {url}")
        print("Making request to public endpoint (no authentication required)")
        
        try:
            response = requests.get(url, headers=self.headers)
            
            print(f"Status Code: {response.status_code}")
            print(f"Response Headers: {dict(response.headers)}")
            
            if response.status_code == 200:
                data = response.json()
                print("✅ Success! Models endpoint working correctly")
                print(f"Response format: {json.dumps(data, indent=2)}")
                
                # Validate OpenAI-compatible format
                self.validate_openai_format(data)
                
                return {
                    'success': True,
                    'status_code': response.status_code,
                    'data': data,
                    'model_count': len(data.get('data', [])),
                    'models': [model.get('id') for model in data.get('data', [])]
                }
            else:
                print(f"❌ Failed with status {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"Error response: {json.dumps(error_data, indent=2)}")
                except:
                    print(f"Raw error response: {response.text}")
                
                return {
                    'success': False,
                    'status_code': response.status_code,
                    'error': response.text
                }
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Request failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def validate_openai_format(self, data: Dict[str, Any]) -> None:
        """Validate that the response follows OpenAI format"""
        print("\n🔍 Validating OpenAI-compatible format...")
        
        # Check top-level structure
        assert 'object' in data, "Missing 'object' field"
        assert 'data' in data, "Missing 'data' field"
        assert data['object'] == 'list', f"Expected object='list', got '{data['object']}'"
        assert isinstance(data['data'], list), "Expected 'data' to be a list"
        
        print(f"✅ Found {len(data['data'])} models")
        
        # Check each model object
        for i, model in enumerate(data['data']):
            assert 'id' in model, f"Model {i} missing 'id' field"
            assert 'object' in model, f"Model {i} missing 'object' field"
            assert 'owned_by' in model, f"Model {i} missing 'owned_by' field"
            assert 'permission' in model, f"Model {i} missing 'permission' field"
            
            assert model['object'] == 'model', f"Model {i} object should be 'model', got '{model['object']}'"
            assert isinstance(model['permission'], list), f"Model {i} permission should be a list"
            
            print(f"✅ Model {i+1}: {model['id']} (owned by: {model['owned_by']})")
        
        print("✅ All models pass OpenAI format validation")
    
    def test_public_access(self) -> None:
        """Test that the endpoint is publicly accessible"""
        print("\n🌐 Testing public access...")
        
        # Test with minimal headers (no auth required)
        minimal_headers = {'Content-Type': 'application/json'}
        
        url = f"{self.base_url}/rest/models"
        response = requests.get(url, headers=minimal_headers)
        
        if response.status_code == 200:
            print("✅ Public access working correctly - endpoint accessible without authentication")
        else:
            print(f"⚠️  Expected 200 for public endpoint, got {response.status_code}")
        
        # Test with completely no headers
        response = requests.get(url)
        
        if response.status_code == 200:
            print("✅ Public access working correctly - endpoint accessible with no headers")
        else:
            print(f"⚠️  Expected 200 for public endpoint with no headers, got {response.status_code}")

def main():
    """Main test function"""
    print("=" * 60)
    print("Testing OpenAI-compatible /models endpoint (PUBLIC)")
    print("=" * 60)
    
    try:
        tester = ModelsEndpointTester()
        
        # Test the main endpoint
        result = tester.test_models_endpoint()
        
        if result['success']:
            print(f"\n📊 Test Results:")
            print(f"   Total models available: {result['model_count']}")
            print(f"   Models: {', '.join(result['models'])}")
            
            # Test public access
            tester.test_public_access()
            
            print("\n🎉 All tests completed successfully!")
            
        else:
            print(f"\n❌ Test failed: {result.get('error', 'Unknown error')}")
            return 1
            
    except Exception as e:
        print(f"❌ Test setup failed: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())