{
  "compilerOptions": {
    "target": "ES2017",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "allowImportingTsExtensions": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {
      "@/*": ["./src/*"],
      "@/icons/*": ["./src/assets/icons/*"], // Added alias for icons
      // Add mapping for the internal database package's generated client
      "@hkbu-genai-platform/database/generated/client": [
        "../../packages/database/prisma/generated/client"
      ]
    }
  },
  "include": ["next-env.d.ts", "src/types/environment.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
