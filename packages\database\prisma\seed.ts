import { PrismaClient } from '@hkbu-genai-platform/database/generated/client';

const prisma = new PrismaClient();

async function main() {
  // Create Tasks
  const task1 = await prisma.task.create({
    data: { name: 'General Administration' },
  });
  const task2 = await prisma.task.create({
    data: { name: 'Academic Programmes Support' },
  });
  const task3 = await prisma.task.create({
    data: { name: 'Proposals and Applications' },
  });
  const task4 = await prisma.task.create({
    data: { name: 'Data Analysis and Reporting' },
  });
  const task5 = await prisma.task.create({
    data: { name: 'Team Collaboration' },
  });
  const task6 = await prisma.task.create({
    data: { name: 'Committee Servicing' },
  });
  const task7 = await prisma.task.create({
    data: { name: 'Research Enhancement' },
  });
  const task8 = await prisma.task.create({
    data: { name: 'Admissions and Policies' },
  });
  const task9 = await prisma.task.create({
    data: { name: 'Grant and Research Management' },
  });

  // Create Prompts
  const prompts = [
    {
      system_instruction: 'You are an administrative assistant. Draft a professional email.',
      prompt_content: 'Draft an email to all staff regarding the upcoming office maintenance schedule.',
      is_default: true,
      tasks: [task1.id],
    },
    {
      system_instruction: 'You are an academic advisor. Provide guidance on course enrollment.',
      prompt_content: 'Explain the process for a student to add or drop a course after the deadline.',
      is_default: true,
      tasks: [task2.id],
    },
    {
      system_instruction: 'You are a grant writer. Draft a proposal for a research grant.',
      prompt_content: 'Write a brief proposal outline for a research project on renewable energy.',
      is_default: true,
      tasks: [task3.id],
    },
    {
      system_instruction: 'You are a data analyst. Generate a report based on the provided data.',
      prompt_content: 'Summarize the key findings from the attached student enrollment data for the last five years.',
      is_default: true,
      tasks: [task4.id],
    },
    {
      system_instruction: 'You are a project manager. Create a meeting agenda.',
      prompt_content: 'Generate an agenda for a kickoff meeting for the new campus sustainability project.',
      is_default: true,
      tasks: [task5.id],
    },
    {
      system_instruction: 'You are a committee secretary. Draft meeting minutes.',
      prompt_content: 'Draft the minutes for the latest Academic Standards Committee meeting.',
      is_default: true,
      tasks: [task6.id],
    },
    {
      system_instruction: 'You are a research assistant. Help generate ideas for a new study.',
      prompt_content: 'Brainstorm three potential research questions about the impact of AI on higher education.',
      is_default: true,
      tasks: [task7.id],
    },
    {
      system_instruction: 'You are a university administrator. Develop a new admissions policy.',
      prompt_content: 'Draft a new policy for admitting international students with non-standard qualifications.',
      is_default: true,
      tasks: [task8.id],
    },
    {
      system_instruction: 'You are a research manager. Outline a grant management plan.',
      prompt_content: 'Create an outline for managing the budget and deliverables of a multi-year research grant.',
      is_default: true,
      tasks: [task9.id],
    },
  ];

  for (const prompt of prompts) {
    const createdPrompt = await prisma.prompt_gallery.create({
      data: {
        system_instruction: prompt.system_instruction,
        prompt_content: prompt.prompt_content,
        is_default: prompt.is_default,
      },
    });

    for (const taskId of prompt.tasks) {
      await prisma.prompt_gallery_task.create({
        data: {
          prompt_gallery_id: createdPrompt.id,
          task_id: taskId,
        },
      });
    }
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });