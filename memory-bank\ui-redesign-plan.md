# HKBU GenAI Platform UI Redesign Plan

## Objective

Redesign the UI for the following sections using MUI (Material-UI) with Tailwind CSS, matching the provided design:
- Sidebar
- Models Dashboard Carousel
- ChatInterface

---

## 1. Sidebar (ChatHistorySidebar)

**Current State:**
- Uses MUI Box, Typography, List, and IconButton.
- Lacks background color, border/shadow, and extra menu items as seen in the design.
- No clear selected state for history items.
- "Signed in as", FAQ, API, and other menu items are missing.

**Plan:**
- Set a fixed width and background color (`bg-[#eaf1fa]` or similar).
- Add a right border or subtle shadow for separation.
- Add all menu items (FAQ, API, etc.) and the "Signed in as" section.
- Implement a selected state for the active history item (e.g., `bg-[#d1e3f8]`).
- Ensure the settings icon is visually separated at the bottom, possibly with a divider.
- Use MUI for structure, Tailwind for color/spacing.

---

## 2. Models Dashboard Carousel (ModelListSection, ModelCard, HomeScreen)

**Current State:**
- Uses MUI Grid for horizontal scroll, with arrow buttons.
- Always shows all models, no "show more/less".
- Section headers and "See all" links may not match the design.

**Plan:**
- Ensure horizontal scroll is smooth and visually matches the design (consistent card width, spacing).
- Style section headers to match the screenshot (font size, weight, spacing).
- Add "See all" link if needed, styled as per the screenshot.
- Ensure arrow buttons are visually aligned and do not overlap content.
- Use Tailwind for background, border, and spacing; MUI for layout and interactivity.

---

## 3. ChatInterface

**Current State:**
- Uses MUI Box, Typography, and IconButton.
- Chat area and input are present, but may not match the design in terms of border, spacing, and color.
- Input area is in a separate component (ChatInputArea).

**Plan:**
- Style the chat area with a white background, rounded corners, and a subtle border.
- Style the input area with a rounded border, shadow, and clear separation from the chat area.
- Ensure the header is centered and matches the font/weight in the screenshot.
- Use Tailwind for border, background, and spacing; MUI for structure and icons.

---

## 4. Integration & Consistency

- Ensure MUI and Tailwind are both properly configured (check package.json and tailwind.config.js).
- Use consistent color palette and spacing throughout.
- Test responsiveness and scroll behavior.

---

## UI Structure Diagram

```mermaid
graph TD
    Sidebar[Sidebar]
    MainArea[Main Area]
    Sidebar -->|Fixed width, bg, menu| MainArea
    MainArea -->|Top: Header| ChatHeader
    MainArea -->|Middle: Chat/Models| ChatArea
    MainArea -->|Bottom: Input| ChatInputArea
    MainArea -->|Home: Model Carousels| ModelListSection
    ModelListSection -->|Cards| ModelCard
```

---

## Next Steps

1. Update Sidebar to match the design and add missing menu items.
2. Refine ModelListSection and ModelCard for carousel styling and section headers.
3. Polish ChatInterface for layout, input, and chat area styling.
4. Ensure all components use both MUI and Tailwind as appropriate.