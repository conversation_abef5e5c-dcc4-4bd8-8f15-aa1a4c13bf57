-- SQL queries to check REST API token usage updates in acl_user_token_spent table

-- 1. Check all records for today for your user
SELECT 
    id,
    username,
    model_name,
    token_date,
    is_api,
    token_spent,
    token_spent_user,
    token_spent_assistant,
    message_count,
    conversation_count,
    update_dt
FROM acl_user_token_spent 
WHERE username = '<PERSON><PERSON><PERSON><PERSON>OON' 
  AND token_date >= '2025-07-22'
ORDER BY update_dt DESC;

-- 2. Check specifically for qwen-plus REST API usage (is_api = 1)
SELECT 
    id,
    username,
    model_name,
    token_date,
    is_api,
    token_spent,
    token_spent_user,
    token_spent_assistant,
    message_count,
    conversation_count,
    update_dt
FROM acl_user_token_spent 
WHERE username = '<PERSON><PERSON><PERSON><PERSON>O<PERSON>' 
  AND model_name = 'qwen-plus'
  AND is_api = 1
  AND token_date >= '2025-07-22'
ORDER BY update_dt DESC;

-- 3. Check for any qwen model variants for today
SELECT 
    id,
    username,
    model_name,
    token_date,
    is_api,
    token_spent,
    token_spent_user,
    token_spent_assistant,
    message_count,
    conversation_count,
    update_dt
FROM acl_user_token_spent 
WHERE username = '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>' 
  AND model_name LIKE '%qwen%'
  AND token_date >= '2025-07-22'
ORDER BY model_name, is_api, update_dt DESC;

-- 4. Check for llama model variants for today
SELECT 
    id,
    username,
    model_name,
    token_date,
    is_api,
    token_spent,
    token_spent_user,
    token_spent_assistant,
    message_count,
    conversation_count,
    update_dt
FROM acl_user_token_spent 
WHERE username = 'SUNNYPOON' 
  AND (model_name LIKE '%llama%' OR model_name LIKE '%maverick%')
  AND token_date >= '2025-07-22'
ORDER BY model_name, is_api, update_dt DESC;

-- 5. Check the total count of records for today (to see if anything is being inserted)
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN is_api = 1 THEN 1 END) as api_records,
    COUNT(CASE WHEN is_api = 0 THEN 1 END) as web_ui_records
FROM acl_user_token_spent 
WHERE username = 'SUNNYPOON' 
  AND token_date >= '2025-07-22';

-- 6. Check the most recent records regardless of date
SELECT TOP 10
    id,
    username,
    model_name,
    token_date,
    is_api,
    token_spent,
    token_spent_user,
    token_spent_assistant,
    message_count,
    conversation_count,
    update_dt
FROM acl_user_token_spent 
WHERE username = 'SUNNYPOON' 
ORDER BY update_dt DESC;