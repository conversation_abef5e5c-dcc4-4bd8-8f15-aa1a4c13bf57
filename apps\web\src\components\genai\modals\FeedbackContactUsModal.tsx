'use client';

import React, { Fragment, useRef, useState } from 'react';
import {
  Modal,
  SwipeableDrawer,
  useTheme,
  useMediaQuery,
  Box,
  Typography,
  TextField,
  Button,
  Tabs,
  Tab,
  CircularProgress,
  Link,
} from '@mui/material';
import DialogActions from '@mui/material/DialogActions';

interface FeedbackContactUsModalProps {
  open: boolean;
  handleClose: () => void;
  handleSubmitContact: (message: string) => void; // Changed to handle a single contact message
  isLoadingContact?: boolean; // Loading state for contact form submission
}

const style = {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: '80%',
  maxWidth: '600px', // Reduced max width for desktop
  height: '80vh',
  bgcolor: 'background.paper',
  boxShadow: 24,
  p: 0,
  display: 'flex',
  flexDirection: 'column',
  borderRadius: '16px', // Apply border-radius to the modal itself
  overflow: 'hidden', // Ensure content respects the border-radius
};

export default function FeedbackContactUsModal({
  open,
  handleClose,
  handleSubmitContact,
  isLoadingContact,
}: FeedbackContactUsModalProps) {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const messageInput = useRef<HTMLTextAreaElement | null>(null);

  const modalContent = (
    <Box
      sx={
        isMobile
          ? {
              height: '80vh',
              display: 'flex',
              flexDirection: 'column',
              overflow: 'hidden',
            }
          : style
      }
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          p: 2,
          borderBottom: 1,
          borderColor: 'divider',
        }}
      >
        <Typography variant="h6" component="h2">
          Contact Us
        </Typography>
        <Typography variant="body1" paragraph>
          For technical assistance, please contact ITO Service Centre (3411 7899,{' '}
          <Link href="mailto:<EMAIL>" target="_blank" rel="noopener noreferrer">
            <EMAIL>
          </Link>
          )
        </Typography>
      </Box>

      <Box sx={{ flexGrow: 1, p: 2, overflowY: 'auto' }}>
        <Box>
          <Typography variant="h6" component="label" sx={{ flexGrow: 1, fontWeight: 'semibold', mb: 3 }}>
            Please input your message to help us improve our service.
            Your input is valuable to us.
          </Typography>
          <TextField
            fullWidth
            multiline
            rows={8}
            placeholder="Enter your message here..."
            inputRef={messageInput}
            disabled={isLoadingContact}
            inputProps={{ maxLength: 1000 }}
            sx={{
              mb: 2,
              '& .MuiOutlinedInput-root': {
                borderRadius: '12px',
                '& fieldset': {
                  borderColor: theme.palette.primary.main,
                },
                '&:hover fieldset': {
                  borderColor: theme.palette.primary.dark,
                },
                '&.Mui-focused fieldset': {
                  borderColor: theme.palette.primary.main,
                },
              },
            }}
          />
          <Typography variant="caption" sx={{ mt: 2 }}>
            (limited to 1000 characters)
          </Typography>
          <DialogActions sx={{ px: 0, pt: 2, justifyContent: 'flex-end' }}>
            <Button onClick={handleClose} color="inherit" variant="outlined" sx={{ borderRadius: '16px' }}>
              Close
            </Button>
            <Button
              onClick={() => handleSubmitContact(messageInput.current?.value || '')}
              color="primary"
              variant="contained"
              disabled={isLoadingContact}
              sx={{ borderRadius: '16px' }}
            >
              {isLoadingContact ? <CircularProgress size={20} color="inherit" /> : 'Submit'}
            </Button>
          </DialogActions>
        </Box>
      </Box>
    </Box>
  );

  if (isMobile) {
    return (
      <SwipeableDrawer
        anchor="bottom"
        open={open}
        onClose={handleClose}
        onOpen={() => {}} // Required prop for SwipeableDrawer
        PaperProps={{
          sx: {
            borderTopLeftRadius: 16, // Apply border-radius to top-left corner
            borderTopRightRadius: 16, // Apply border-radius to top-right corner
            backdropFilter: 'blur(10px)', // Add blur effect
            backgroundColor: 'background.paper', // Ensure solid background
          },
        }}
        disableSwipeToOpen={false}
        ModalProps={{
          keepMounted: true,
        }}
      >
        <Box
          onClick={handleClose} // Add onClick to close the drawer
          sx={{
            display: 'flex',
            justifyContent: 'center',
            pt: 1, // Padding top for the handle
            pb: 0.5, // Padding bottom for the handle
            cursor: 'pointer', // Indicate it's clickable
          }}
        >
          <Box
            sx={{
              width: 40,
              height: 4,
              borderRadius: 2,
              bgcolor: 'text.secondary', // Color of the handle
            }}
          />
        </Box>
        {modalContent}
      </SwipeableDrawer>
    );
  }

  return (
    <Modal
      open={open}
      onClose={handleClose}
      disableRestoreFocus // Add this prop
      BackdropProps={{
        sx: {
          backdropFilter: 'blur(10px)',
        },
      }}
    >
      {modalContent}
    </Modal>
  );
}