import { Fragment, useState, useEffect, useRef } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { GptModel } from '@/lib/types/common'; // Correct import path
import {
  ModelHealthCheck,
  checkModelHealth,
} from '@/lib/utils/ModelHealthCheck'; // Use path alias
import {
  ArrowPathIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  InformationCircleIcon,
  StopIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';
import { useSession } from 'next-auth/react';

// Add model filter configuration
const MODEL_NAME_FILTERS: string[] = []; // Can be empty array to show all models
// Removed unused TEST_MESSAGE constant

type ModelStatus = ModelHealthCheck['status'];

interface HealthCheckModalProps {
  show: boolean;
  onClose: () => void;
  closeFn: () => void;
  modelList: GptModel[];
}

const HealthCheckModal = ({
  show,
  onClose,
  closeFn,
  modelList,
}: HealthCheckModalProps) => {
  const [modelStatuses, setModelStatuses] = useState<ModelHealthCheck[]>([]);
  const [isChecking, setIsChecking] = useState(false);
  const [lastChecked, setLastChecked] = useState<Date | null>(null);
  const [checkingModel, setCheckingModel] = useState<string | null>(null);
  const { data: session } = useSession();
  const checkInProgress = useRef(false);
  const mountCount = useRef(0);
  const abortController = useRef<AbortController | null>(null);
  // Add a generation counter to track valid checks
  const checkGeneration = useRef(0);

  // Helper to determine if any type of check is in progress
  const isAnyCheckInProgress = isChecking || checkingModel !== null;

  // Create a function to initialize models to their default pending state
  const initializeModels = (models: GptModel[]) => {
    const filteredModels = models.filter(
      (model) =>
        model.display_name !== 'Adobe Express' &&
        model.display_name !== 'Adobe Firefly' &&
        (MODEL_NAME_FILTERS.length === 0 || // If no filters, include all models
          MODEL_NAME_FILTERS.some((filter) =>
            model.model_name.toLowerCase().includes(filter.toLowerCase()),
          )),
    );

    // Initialize all models in pending state
    return filteredModels.map((model) => ({
      modelName: model.model_name,
      displayName: model.display_name,
      status: 'pending' as const,
      isChecking: false,
    }));
  };

  // Log when component mounts/unmounts
  useEffect(() => {
    const currentMount = ++mountCount.current;
    // console.log(`[Health Check Debug] Component mounted #${currentMount}`);
    return () => {
      // console.log(`[Health Check Debug] Component unmounted #${currentMount}`);
      // Cleanup on unmount
      if (abortController.current) {
        abortController.current.abort();
        abortController.current = null;
      }
    };
  }, []);

  // Log when show prop changes
  useEffect(() => {
    // console.log(`[Health Check Debug] Show prop changed:`, { show });

    // When modal is hidden, cancel any ongoing checks
    if (!show) {
      if (abortController.current) {
        abortController.current.abort();
        abortController.current = null;
      }
      setIsChecking(false);
      setCheckingModel(null);
      checkInProgress.current = false;

      // Also increment generation to prevent any pending updates
      checkGeneration.current++;
    }
  }, [show]); // Add show to dependency array so this effect runs when show changes

  // Add new useEffect for model list initialization
  useEffect(() => {
    if (show && modelList.length > 0) {
      setModelStatuses(initializeModels(modelList));
    }
  }, [show, modelList]);

  // Completely reset health check to initial state
  const stopHealthCheck = () => {
    // First abort any ongoing requests
    if (abortController.current) {
      abortController.current.abort();
      abortController.current = null;
    }

    // Increment the generation to invalidate any in-flight requests
    checkGeneration.current++;

    // Reset checking states immediately to prevent race conditions
    setIsChecking(false);
    setCheckingModel(null);
    checkInProgress.current = false;

    // Use a setTimeout to ensure the abort has had time to process
    setTimeout(() => {
      // Completely reinitialize models to their original state
      if (modelList.length > 0) {
        setModelStatuses(initializeModels(modelList));
      }
    }, 50);
  };

  const performHealthCheck = async () => {
    // Removed unused currentMount variable
    if (!session?.user?.name || checkInProgress.current) {
      return;
    }

    // Create new AbortController for this check
    if (abortController.current) {
      abortController.current.abort();
    }
    abortController.current = new AbortController();
    const signal = abortController.current.signal;

    // Store the current generation for this check
    const thisCheckGeneration = ++checkGeneration.current;

    setIsChecking(true);
    checkInProgress.current = true;

    try {
      // Update all models to checking/pending state
      setModelStatuses((prev) =>
        prev.map((model, index) => ({
          ...model,
          status: index === 0 ? 'checking' : 'pending',
          isChecking: true,
          responseTime: undefined, // Clear any previous response times
          error: undefined, // Clear any previous errors
        })),
      );

      // Check models one by one (sequential to avoid rate limits)
      const results = [];
      for (let index = 0; index < modelStatuses.length; index++) {
        // Check if aborted
        if (signal.aborted) {
          return;
        }

        const model = modelList.find(
          (m) => m.model_name === modelStatuses[index].modelName,
        );
        if (!model) continue;

        try {
          const result = await checkModelHealth(
            model,
            session.user.name,
            signal,
          );
          results.push(result);

          // Only update if this check is still valid (same generation)
          if (thisCheckGeneration === checkGeneration.current) {
            setModelStatuses((prev) =>
              prev.map((prevModel, i) => {
                if (i === index) {
                  return result;
                } else {
                  return {
                    ...prevModel,
                    status:
                      i === index + 1
                        ? 'checking'
                        : i > index + 1
                          ? 'pending'
                          : prevModel.status,
                  };
                }
              }),
            );
          }
        } catch (error) {
          // Handle individual model check error
          if (signal.aborted) return;

          const errorResult: ModelHealthCheck = {
            modelName: modelStatuses[index].modelName,
            displayName: modelStatuses[index].displayName,
            status: 'down',
            error: error instanceof Error ? error.message : 'Check failed',
          };

          results.push(errorResult);

          // Only update if this check is still valid (same generation)
          if (thisCheckGeneration === checkGeneration.current) {
            setModelStatuses((prev) =>
              prev.map((prevModel, i) => {
                if (i === index) {
                  return errorResult;
                } else {
                  return {
                    ...prevModel,
                    status:
                      i === index + 1
                        ? 'checking'
                        : i > index + 1
                          ? 'pending'
                          : prevModel.status,
                  };
                }
              }),
            );
          }
        }
      }

      // Only update last checked time if this check is still valid
      if (thisCheckGeneration === checkGeneration.current) {
        setLastChecked(new Date());
      }
    } catch (error) {
      console.error(
        `[Health Check Debug] Error in check:`, // Removed currentMount reference
        error,
      );
    } finally {
      if (!mountCount.current) {
        return;
      }

      // Only update state if this check is still valid
      if (thisCheckGeneration === checkGeneration.current) {
        setIsChecking(false);
        checkInProgress.current = false;
      }
    }
  };

  const handleSingleModelRefresh = async (modelName: string) => {
    const currentMount = mountCount.current;

    if (!session?.user?.name || checkInProgress.current || isChecking) {
      return;
    }

    // Create new AbortController for this check
    if (abortController.current) {
      abortController.current.abort();
    }
    abortController.current = new AbortController();
    const signal = abortController.current.signal;

    // Store the current generation for this check
    const thisCheckGeneration = ++checkGeneration.current;

    // Check if modal is still shown before starting
    if (!show) {
      return;
    }

    setCheckingModel(modelName);
    checkInProgress.current = true;

    try {
      // Update just this model's status to checking
      setModelStatuses((prev) =>
        prev.map((status) =>
          status.modelName === modelName
            ? {
                ...status,
                status: 'checking' as const,
                error: undefined,
                responseTime: undefined, // Clear any previous response time
              }
            : status,
        ),
      );

      const model = modelList.find((m) => m.model_name === modelName);
      if (!model) {
        throw new Error('Model not found');
      }

      const result = await checkModelHealth(model, session.user.name, signal);

      // Only update if this check is still valid (same generation)
      if (thisCheckGeneration === checkGeneration.current) {
        // Update just this model's status with the result
        setModelStatuses((prev) =>
          prev.map((status) =>
            status.modelName === modelName ? result : status,
          ),
        );

        setLastChecked(new Date());
      }
    } catch (error) {
      console.error(
        `[Health Check Debug] Error in single check #${currentMount}:`,
        error,
      );

      // Update model with error - only if this check is still valid and not aborted
      if (!signal.aborted && thisCheckGeneration === checkGeneration.current) {
        setModelStatuses((prev) =>
          prev.map((status) =>
            status.modelName === modelName
              ? {
                  ...status,
                  status: 'down' as const,
                  error:
                    error instanceof Error
                      ? error.message
                      : 'Check failed unexpectedly',
                }
              : status,
          ),
        );
      }
    } finally {
      if (!mountCount.current) {
        return;
      }

      // Only update state if this check is still valid
      if (thisCheckGeneration === checkGeneration.current) {
        setCheckingModel(null);
        checkInProgress.current = false;
      }
    }
  };

  // Get health check summary
  const getHealthSummary = () => {
    const activeCount = modelStatuses.filter(
      (m) => m.status === 'active',
    ).length;
    const unstableCount = modelStatuses.filter(
      (m) => m.status === 'unstable',
    ).length;
    const downCount = modelStatuses.filter((m) => m.status === 'down').length;
    const pendingCount = modelStatuses.filter(
      (m) => m.status === 'pending' || m.status === 'checking',
    ).length;
    const totalCount = modelStatuses.length;

    return {
      activeCount,
      unstableCount,
      downCount,
      pendingCount,
      totalCount,
      activePercentage:
        totalCount > 0 && activeCount > 0
          ? Math.round((activeCount / totalCount) * 100)
          : 0,
      isInitialState: !lastChecked && pendingCount === totalCount,
    };
  };

  const getStatusIcon = (status: ModelStatus) => {
    switch (status) {
      case 'active':
        return <CheckCircleIcon className="h-6 w-6 text-emerald-500" />;
      case 'unstable':
        return <ExclamationTriangleIcon className="h-6 w-6 text-amber-500" />;
      case 'down':
        return <XCircleIcon className="h-6 w-6 text-rose-500" />;
      case 'checking':
        return (
          <div className="h-6 w-6 rounded-full border-2 border-t-transparent border-blue-500 animate-spin" />
        );
      case 'pending':
        return (
          <div className="h-6 w-6 rounded-full border border-dashed border-purple-300" />
        );
      default:
        return null;
    }
  };

  const getStatusColor = (status: ModelStatus) => {
    switch (status) {
      case 'active':
        return 'bg-gradient-to-r from-emerald-500 to-teal-500';
      case 'unstable':
        return 'bg-gradient-to-r from-amber-400 to-amber-500';
      case 'down':
        return 'bg-gradient-to-r from-rose-500 to-pink-500';
      case 'checking':
        return 'bg-gradient-to-r from-blue-400 to-blue-500';
      case 'pending':
        return 'bg-gradient-to-r from-purple-400 to-purple-500';
      default:
        return 'bg-gray-400';
    }
  };

  const getResponseTimeClass = (responseTime?: number) => {
    if (!responseTime) return '';
    if (responseTime < 5000) return 'text-emerald-500';
    if (responseTime < 10000) return 'text-amber-500';
    return 'text-rose-500';
  };

  // Render a model status card with enhanced visual design
  const renderModelCard = (modelStatus: ModelHealthCheck) => {
    const isCheckingThis =
      checkingModel === modelStatus.modelName ||
      modelStatus.status === 'checking';

    return (
      <div
        key={modelStatus.modelName}
        className={`relative overflow-hidden rounded-xl bg-white dark:bg-gray-800 shadow-md transition-all duration-300 hover:shadow-lg ${
          isCheckingThis ? 'ring-2 ring-blue-400' : ''
        }`}
      >
        {/* Status indicator bar at top */}
        <div className={`h-1.5 w-full ${getStatusColor(modelStatus.status)}`} />

        <div className="p-4">
          <div className="flex items-start justify-between mb-3">
            <h3 className="font-medium text-gray-900 dark:text-white">
              {modelStatus.displayName}
            </h3>
            <div className="flex items-center">
              {getStatusIcon(modelStatus.status)}
            </div>
          </div>

          <div className="flex flex-col">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-500 dark:text-gray-400">Status</span>
              <span
                className={`font-medium ${
                  modelStatus.status === 'active'
                    ? 'text-emerald-500'
                    : modelStatus.status === 'unstable'
                      ? 'text-amber-500'
                      : modelStatus.status === 'down'
                        ? 'text-rose-500'
                        : modelStatus.status === 'checking'
                          ? 'text-blue-500'
                          : 'text-purple-500'
                }`}
              >
                {modelStatus.status.charAt(0).toUpperCase() +
                  modelStatus.status.slice(1)}
              </span>
            </div>

            {modelStatus.responseTime && (
              <div className="flex items-center justify-between text-sm mt-2">
                <span className="text-gray-500 dark:text-gray-400">
                  Response time
                </span>
                <div className="flex items-center">
                  <span
                    className={`font-medium ${getResponseTimeClass(
                      modelStatus.responseTime,
                    )}`}
                  >
                    {Math.round(modelStatus.responseTime)}ms
                  </span>

                  {/* Visual response time bar */}
                  <div className="ml-2 h-1.5 w-12 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                    <div
                      className={`h-full ${getResponseTimeClass(
                        modelStatus.responseTime,
                      )}`}
                      style={{
                        width: `${Math.min(
                          100,
                          (modelStatus.responseTime / 30000) * 100,
                        )}%`,
                        transition: 'width 0.5s ease-in-out',
                      }}
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Error message with better styling */}
            {modelStatus.error && (
              <div className="mt-3 p-2 text-xs bg-rose-50 dark:bg-rose-900/30 border border-rose-200 dark:border-rose-800 rounded text-rose-600 dark:text-rose-300">
                {modelStatus.error}
              </div>
            )}
          </div>

          {/* Actions footer */}
          <div className="mt-4 flex justify-end">
            {(modelStatus.status === 'down' ||
              modelStatus.status === 'unstable') && (
              <button
                onClick={() => handleSingleModelRefresh(modelStatus.modelName)}
                disabled={isAnyCheckInProgress}
                className="inline-flex items-center px-2 py-1 text-xs font-medium rounded-md
                  text-white bg-indigo-600 hover:bg-indigo-500 
                  disabled:opacity-50 disabled:cursor-not-allowed
                  transition-all duration-150"
                title="Refresh this model"
              >
                <ArrowPathIcon
                  className={`h-3 w-3 mr-1 ${
                    checkingModel === modelStatus.modelName
                      ? 'animate-spin'
                      : ''
                  }`}
                />
                Retry
              </button>
            )}
          </div>
        </div>

        {/* Animation overlay for checking state */}
        {modelStatus.status === 'checking' && (
          <div className="absolute inset-0 bg-gradient-to-r from-blue-400/10 to-indigo-400/10 pointer-events-none">
            <div className="absolute inset-0 blur-sm bg-gradient-to-r from-transparent via-blue-400/20 to-transparent animate-pulse"></div>
          </div>
        )}
      </div>
    );
  };

  const summary = getHealthSummary();

  return (
    <Transition.Root show={show} as={Fragment}>
      <Dialog as="div" className="relative z-30" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 dark:bg-[#4a4a4aa0] backdrop-blur-sm transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-2xl bg-white dark:bg-[#2f2f2f] px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-3xl sm:p-6">
                {/* Add close button */}
                <button
                  type="button"
                  className="absolute top-3 right-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                  onClick={() => {
                    if (abortController.current) {
                      abortController.current.abort();
                      abortController.current = null;
                    }
                    checkGeneration.current++;
                    closeFn();
                  }}
                >
                  <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                </button>

                <div>
                  <div className="mt-3 text-center sm:mt-5">
                    <Dialog.Title
                      as="h3"
                      className="text-lg font-semibold leading-6 text-gray-900 dark:text-white"
                    >
                      LLM Health Monitor
                    </Dialog.Title>

                    {/* Health Summary Card - Always visible */}
                    <div className="mt-4 p-4 bg-gradient-to-r from-indigo-500/10 to-purple-500/10 rounded-xl">
                      <div className="flex justify-between items-center">
                        <div className="flex flex-col items-start">
                          {summary.isInitialState ? (
                            <div className="flex items-center text-sm text-indigo-500 dark:text-indigo-400">
                              <InformationCircleIcon className="h-5 w-5 mr-1" />
                              <span>Ready to check model status</span>
                            </div>
                          ) : (
                            <div className="text-sm text-gray-500 dark:text-gray-300">
                              {lastChecked
                                ? `Last checked: ${lastChecked.toLocaleTimeString()}`
                                : 'Not checked yet'}
                            </div>
                          )}
                          <div className="font-medium text-xl mt-1">
                            {summary.isInitialState ? (
                              <span className="text-gray-500 dark:text-gray-400">
                                {summary.totalCount} models ready for health
                                check
                              </span>
                            ) : (
                              <>
                                <span className="text-emerald-500">
                                  {summary.activeCount}
                                </span>
                                <span className="text-gray-400 px-1">/</span>
                                <span className="text-gray-500">
                                  {summary.totalCount}
                                </span>
                                <span className="text-sm font-normal text-gray-500 ml-1">
                                  models active
                                </span>
                              </>
                            )}
                          </div>
                        </div>

                        {/* Visual health indicator */}
                        <div className="flex flex-col items-center">
                          <div className="relative w-16 h-16">
                            {summary.isInitialState ? (
                              <div className="h-16 w-16 flex items-center justify-center">
                                <ArrowPathIcon className="h-8 w-8 text-indigo-400 opacity-50" />
                              </div>
                            ) : (
                              <svg
                                className="w-full h-full"
                                viewBox="0 0 36 36"
                              >
                                <circle
                                  cx="18"
                                  cy="18"
                                  r="16"
                                  fill="none"
                                  className="stroke-gray-200 dark:stroke-gray-700"
                                  strokeWidth="2"
                                />
                                <circle
                                  cx="18"
                                  cy="18"
                                  r="16"
                                  fill="none"
                                  className={`${
                                    summary.activePercentage > 80
                                      ? 'stroke-emerald-500'
                                      : summary.activePercentage > 50
                                        ? 'stroke-amber-400'
                                        : summary.activePercentage > 0
                                          ? 'stroke-rose-500'
                                          : 'stroke-gray-400'
                                  }`}
                                  strokeWidth="2"
                                  strokeDasharray="100"
                                  strokeDashoffset={
                                    100 - summary.activePercentage
                                  }
                                  strokeLinecap="round"
                                  transform="rotate(-90 18 18)"
                                />
                                <text
                                  x="18"
                                  y="20"
                                  className="text-xs font-medium fill-gray-700 dark:fill-gray-300"
                                  dominantBaseline="middle"
                                  textAnchor="middle"
                                >
                                  {summary.activePercentage}%
                                </text>
                              </svg>
                            )}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            System Health
                          </div>
                        </div>
                      </div>

                      {/* Status counts */}
                      <div className="flex justify-between mt-4 text-sm">
                        <div className="flex items-center">
                          <div className="w-3 h-3 rounded-full bg-emerald-500 mr-1"></div>
                          <span className="text-gray-600 dark:text-gray-300">
                            {summary.activeCount} Active
                          </span>
                        </div>
                        <div className="flex items-center">
                          <div className="w-3 h-3 rounded-full bg-amber-500 mr-1"></div>
                          <span className="text-gray-600 dark:text-gray-300">
                            {summary.unstableCount} Unstable
                          </span>
                        </div>
                        <div className="flex items-center">
                          <div className="w-3 h-3 rounded-full bg-rose-500 mr-1"></div>
                          <span className="text-gray-600 dark:text-gray-300">
                            {summary.downCount} Down
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="mt-4 flex justify-end px-4 space-x-3">
                      {isAnyCheckInProgress ? (
                        <>
                          <button
                            onClick={stopHealthCheck}
                            className="inline-flex items-center px-4 py-2 text-sm font-medium rounded-lg 
                              text-white bg-gradient-to-r from-rose-600 to-pink-600
                              hover:from-rose-500 hover:to-pink-500
                              focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-rose-500
                              transition-all duration-200 shadow-md hover:shadow-lg"
                          >
                            <StopIcon className="h-4 w-4 mr-2" />
                            Stop & Reset
                          </button>

                          <div
                            className="inline-flex items-center px-4 py-2 text-sm font-medium rounded-lg 
                            text-white bg-gradient-to-r from-blue-600 to-indigo-600 opacity-70
                            transition-all duration-200 shadow-md"
                          >
                            <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
                            {isChecking
                              ? 'Checking All...'
                              : checkingModel
                                ? `Checking ${
                                    modelList
                                      .find(
                                        (m) => m.model_name === checkingModel,
                                      )
                                      ?.display_name?.split(' ')[0] || 'Model'
                                  }...`
                                : 'Checking...'}
                          </div>
                        </>
                      ) : (
                        <button
                          onClick={performHealthCheck}
                          disabled={isAnyCheckInProgress}
                          className="inline-flex items-center px-4 py-2 text-sm font-medium rounded-lg 
                            text-white bg-gradient-to-r from-indigo-600 to-purple-600 
                            hover:from-indigo-500 hover:to-purple-500
                            focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500
                            disabled:opacity-50 disabled:cursor-not-allowed
                            transition-all duration-200 shadow-md hover:shadow-lg"
                        >
                          <ArrowPathIcon className="h-4 w-4 mr-2" />
                          {lastChecked
                            ? 'Refresh Models'
                            : 'Start Health Check'}
                        </button>
                      )}
                    </div>

                    <div className="mt-6">
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                        {modelStatuses.map(renderModelCard)}
                      </div>
                    </div>
                  </div>
                </div>
                <div className="mt-6 sm:mt-8">
                  <button
                    type="button"
                    className="w-full flex justify-center items-center rounded-lg bg-white dark:bg-gray-800
                      px-3 py-3 text-sm font-medium text-gray-700 dark:text-gray-200
                      shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-600
                      hover:bg-gray-50 dark:hover:bg-gray-700"
                    onClick={() => {
                      // First abort any ongoing checks
                      if (abortController.current) {
                        abortController.current.abort();
                        abortController.current = null;
                      }
                      // Increment generation to prevent stale updates
                      checkGeneration.current++;
                      // Then call the close function
                      closeFn();
                    }}
                  >
                    Close
                  </button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
};

export default HealthCheckModal;
