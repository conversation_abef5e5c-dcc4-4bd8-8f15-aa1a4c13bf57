import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { OcrService } from './ocr.service';
import { KeywordExtractionService } from './keyword-extraction.service';
import { SearchContextService } from './search-context.service';
import { FileProcessingService } from './file-processing.service';
import { LlmModule } from '../llm/llm.module'; // Import LlmModule for LlmConfigService

@Module({
  imports: [ConfigModule, LlmModule], // Import LlmModule for KeywordExtractionService
  providers: [
    OcrService,
    KeywordExtractionService,
    SearchContextService,
    FileProcessingService,
  ],
  exports: [
    OcrService,
    KeywordExtractionService,
    SearchContextService,
    FileProcessingService,
  ], // Export new services
})
export class UtilsModule {}
