import { Injectable, Logger } from '@nestjs/common';
import { InjectRedis } from '@nestjs-modules/ioredis';
import { Redis } from 'ioredis';
import { PrismaService } from '../../prisma/prisma.service';
import { ModelMappingService } from './model-mapping.service';
import { getHongKongTime } from '../utils/timezone.util';

export interface RateLimitStatus {
  model: string;
  limit: number;
  used: number;
  remaining: number;
  resetAt: string;
}

@Injectable()
export class RateLimitService {
  private readonly logger = new Logger(RateLimitService.name);
  private readonly RATE_LIMIT_PER_MODEL = 60; // 60 requests per minute
  private readonly TTL_SECONDS = 60; // 1 minute

  constructor(
    @InjectRedis() private readonly redis: Redis,
    private readonly prisma: PrismaService,
    private readonly modelMappingService: ModelMappingService,
  ) {}

  async getRateLimitStatus(
    userId: string,
    modelName: string,
  ): Promise<RateLimitStatus> {
    // Check all possible model name variants
    const modelVariants =
      this.modelMappingService.getModelNameVariants(modelName);
    let totalUsed = 0;
    let minTtl = -1;

    try {
      // Check usage across all model name variants
      for (const variant of modelVariants) {
        const key = `rate_limit:${variant}:${userId}`;
        const count = await this.redis.get(key);
        const ttl = await this.redis.ttl(key);

        if (count) {
          totalUsed += parseInt(count, 10);
          if (ttl > 0 && (minTtl === -1 || ttl < minTtl)) {
            minTtl = ttl;
          }
        }
      }

      const remaining = Math.max(0, this.RATE_LIMIT_PER_MODEL - totalUsed);
      const resetAt =
        minTtl > 0
          ? new Date(Date.now() + minTtl * 1000).toISOString()
          : new Date(Date.now() + this.TTL_SECONDS * 1000).toISOString();

      return {
        model: modelName,
        limit: this.RATE_LIMIT_PER_MODEL,
        used: totalUsed,
        remaining,
        resetAt,
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Error getting rate limit status: ${errorMessage}`);
      // Return default status on error
      return {
        model: modelName,
        limit: this.RATE_LIMIT_PER_MODEL,
        used: 0,
        remaining: this.RATE_LIMIT_PER_MODEL,
        resetAt: new Date(Date.now() + this.TTL_SECONDS * 1000).toISOString(),
      };
    }
  }

  async getAllModelUsage(userId: string): Promise<RateLimitStatus[]> {
    try {
      // Get all available models from database
      const models = await this.prisma.model_list.findMany({
        where: {
          api_status: 'A', // Active
          rec_status: 'A', // Active
        },
        select: {
          deployment_name: true,
          display_name: true,
        },
      });

      // Get rate limit status for each model
      const usagePromises = models.map(async (model) => {
        const modelName = model.deployment_name || model.display_name;
        if (!modelName) return null;

        return this.getRateLimitStatus(userId, modelName);
      });

      const results = await Promise.all(usagePromises);
      return results.filter(
        (result): result is RateLimitStatus => result !== null,
      );
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Error getting all model usage: ${errorMessage}`);
      return [];
    }
  }

  async logRateLimitHit(
    userId: string,
    modelName: string,
    apiKey?: string,
  ): Promise<void> {
    try {
      // Log to database for analytics
      await this.prisma.log_rate_limit.create({
        data: {
          username: userId,
          api_key: apiKey || null,
          identifier: `rate_limit:${modelName}:${userId}`,
          model_name: modelName,
          limit: this.RATE_LIMIT_PER_MODEL,
          remaining: 0,
          create_dt: getHongKongTime(),
        },
      });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Error logging rate limit hit: ${errorMessage}`);
    }
  }

  async getRemainingRequests(
    userId: string,
    modelName: string,
  ): Promise<number> {
    const key = `rate_limit:${modelName}:${userId}`;

    try {
      const count = await this.redis.get(key);
      const used = count ? parseInt(count, 10) : 0;
      return Math.max(0, this.RATE_LIMIT_PER_MODEL - used);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Error getting remaining requests: ${errorMessage}`);
      return this.RATE_LIMIT_PER_MODEL; // Return full limit on error
    }
  }
}
