import { Injectable, Logger } from '@nestjs/common';
import * as mammoth from 'mammoth';
import * as XLSX from 'xlsx';
import PptxParser from 'node-pptx-parser';
import * as fs from 'fs/promises';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { OcrService } from './ocr.service'; // Import OcrService

@Injectable()
export class FileProcessingService {
  private readonly logger = new Logger(FileProcessingService.name);

  constructor(private readonly ocrService: OcrService) {} // Inject OcrService

  async extractText(file: {
    mimeType: string;
    content: string; // Base64 encoded content
    filename: string;
    dept_unit_code: string; // Add department code for OCR
  }): Promise<string> {
    const { mimeType, content, filename, dept_unit_code } = file;
    const buffer = Buffer.from(content, 'base64');

    // Handle image and PDF types with OCR
    if (mimeType.startsWith('image/') || mimeType === 'application/pdf') {
      this.logger.log(
        `Using OCR service for file: ${filename} (MIME: ${mimeType})`,
      );
      try {
        return await this.ocrService.extractTextFromFile(
          buffer,
          dept_unit_code,
          filename,
        );
      } catch (error) {
        this.logger.error(
          `Error processing file with OCR service ${filename}:`,
          error,
        );
        throw new Error(`Failed to extract text from file: ${filename}`);
      }
    }

    switch (mimeType) {
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        try {
          const result = await mammoth.extractRawText({ buffer });
          return result.value;
        } catch (error) {
          this.logger.error(`Error processing DOCX file ${filename}:`, error);
          throw new Error(`Failed to extract text from DOCX file: ${filename}`);
        }
      case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
        try {
          const workbook = XLSX.read(buffer, { type: 'buffer' });
          let fullText = '';
          workbook.SheetNames.forEach((sheetName) => {
            fullText += `Sheet: ${sheetName}\n\n`;
            const worksheet = workbook.Sheets[sheetName];
            const jsonData = XLSX.utils.sheet_to_json(worksheet, {
              header: 1,
            });

            if (jsonData.length > 0) {
              const headers = jsonData[0] as string[];
              const rows = jsonData.slice(1) as any[][];

              // Create Markdown table
              fullText += `| ${headers.join(' | ')} |\n`;
              fullText += `| ${headers.map(() => '---').join(' | ')} |\n`;
              rows.forEach((row) => {
                fullText += `| ${row.join(' | ')} |\n`;
              });
            }
            fullText += '\n';
          });
          return fullText;
        } catch (error) {
          this.logger.error(`Error processing XLSX file ${filename}:`, error);
          throw new Error(`Failed to extract text from XLSX file: ${filename}`);
        }
      case 'application/vnd.openxmlformats-officedocument.presentationml.presentation':
        const tempDir = path.join(__dirname, '..', '..', 'temp');
        const tempFilePath = path.join(tempDir, `${uuidv4()}-${filename}`);
        try {
          await fs.mkdir(tempDir, { recursive: true });
          await fs.writeFile(tempFilePath, buffer);
          const parser = new PptxParser(tempFilePath);
          const text = await parser.extractText();
          if (Array.isArray(text)) {
            return text.map((slide) => slide.text).join('\n\n');
          }
          return text || '';
        } catch (error) {
          this.logger.error(`Error processing PPTX file ${filename}:`, error);
          throw new Error(`Failed to extract text from PPTX file: ${filename}`);
        } finally {
          // Clean up the temporary file
          try {
            await fs.unlink(tempFilePath);
          } catch (cleanupError) {
            this.logger.error(
              `Error cleaning up temporary file ${tempFilePath}:`,
              cleanupError,
            );
          }
        }
      default:
        // Unsupported file type
        this.logger.warn(
          `Unsupported file type for standard extraction: ${mimeType}`,
        );
        throw new Error(`Unsupported file type: ${mimeType}`);
    }
  }
}
