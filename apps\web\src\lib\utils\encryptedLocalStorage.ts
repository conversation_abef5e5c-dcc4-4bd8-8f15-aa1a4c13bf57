import CryptoJS from 'crypto-js';

// Helper function to construct the encryption key from environment variables
// IMPORTANT: Ensure NEXT_PUBLIC_ENCRYPTION_KEY_1 to NEXT_PUBLIC_ENCRYPTION_KEY_5 are defined in your .env file for this app.
function getCryptKey() {
  const key1 = process.env.NEXT_PUBLIC_ENCRYPTION_KEY_1 || '';
  const key2 = process.env.NEXT_PUBLIC_ENCRYPTION_KEY_2 || '';
  const key3 = process.env.NEXT_PUBLIC_ENCRYPTION_KEY_3 || '';
  const key4 = process.env.NEXT_PUBLIC_ENCRYPTION_KEY_4 || '';
  const key5 = process.env.NEXT_PUBLIC_ENCRYPTION_KEY_5 || '';

  // Reconstruct the key format used in the original code
  const prefix = key1 + '@' + key2;
  const middle = key4 + ':' + key3;
  const suffix = key5 + '_' + key5; // Assuming the original logic meant to use key5 twice

  return prefix + '[' + middle + ']' + suffix;
}

function getItem(key: string): string | null {
  if (typeof window === 'undefined') {
    // Return null or handle appropriately if localStorage is not available (e.g., during SSR)
    return null;
  }
  const storedString = localStorage.getItem(key);
  try {
    if (storedString === undefined || storedString === null) {
      // Key not found or value is null/undefined
      return null;
    }
    const cryptKey = getCryptKey();
    if (!cryptKey || cryptKey.length < 10) {
      // Basic check if the key seems valid
      console.error('Encryption key is missing or invalid. Cannot decrypt.');
      // Fallback: return the raw string? Or null? Depends on desired behavior.
      // Returning raw string might expose encrypted data if keys are missing. Null is safer.
      return null;
    }
    const bytes = CryptoJS.AES.decrypt(storedString, cryptKey);
    const decryptedValue = JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
    // Ensure the decrypted value is a string before returning, or adjust return type
    return typeof decryptedValue === 'string'
      ? decryptedValue
      : JSON.stringify(decryptedValue);
  } catch (error) {
    // If decryption fails, it might be unencrypted data or corrupted.
    // Return the raw string as a fallback, assuming it might be plain text.
    // Log the error for debugging.
    console.warn(
      `Failed to decrypt localStorage item '${key}'. Returning raw value. Error:`,
      error,
    );
    return storedString;
  }
}

function setItem(key: string, value: string | object): void {
  if (typeof window === 'undefined') {
    // Do nothing or handle appropriately if localStorage is not available
    return;
  }
  try {
    if (value === undefined || value === null) {
      console.warn(
        `Attempted to set null or undefined value for localStorage key '${key}'. Removing item instead.`,
      );
      localStorage.removeItem(key);
      return;
    }

    const cryptKey = getCryptKey();
    if (!cryptKey || cryptKey.length < 10) {
      // Basic check
      console.error(
        'Encryption key is missing or invalid. Saving value unencrypted.',
      );
      // Fallback: save unencrypted
      localStorage.setItem(
        key,
        typeof value === 'string' ? value : JSON.stringify(value),
      );
      return;
    }

    const valueToEncrypt =
      typeof value === 'string' ? value : JSON.stringify(value);
    const encryptedValue = CryptoJS.AES.encrypt(
      valueToEncrypt, // Encrypt the raw string or stringified object
      cryptKey,
    ).toString();
    localStorage.setItem(key, encryptedValue);
  } catch (error) {
    // If encryption fails for some reason, fallback to storing unencrypted?
    // Or maybe better to not store it to avoid inconsistencies.
    console.error(
      `Failed to encrypt and set localStorage item '${key}'. Value not stored. Error:`,
      error,
    );
    // Optionally, could store unencrypted as a last resort:
    // localStorage.setItem(key, typeof value === 'string' ? value : JSON.stringify(value));
  }
}

function removeItem(key: string): void {
  if (typeof window !== 'undefined') {
    localStorage.removeItem(key);
  }
}

// Encapsulate as a module or class
const EncryptedLocalStorage = {
  getItem,
  setItem,
  removeItem, // Added removeItem for completeness
};

export default EncryptedLocalStorage;
