/**
 * Utility functions for normalizing model names to ensure consistency
 * between different selection methods (model cards vs dropdowns)
 */

/**
 * Mapping of model names that need normalization
 * Maps from the full model name (as stored in database) to the normalized name
 */
const MODEL_NAME_NORMALIZATION_MAP: Record<string, string> = {
  // DeepSeek models
  'deepseek-v3-hkbu': 'deepseek-v3',

  // ChatGPT models - normalize to chatgpt prefix for consistency
  'gpt-4.1': 'GPT-4.1',
  'chatgpt-4.1': 'GPT-4.1',
  'gpt-4o': 'GPT-4o',
  'chatgpt-4o': 'GPT-4o',
  'gpt-4o-mini': 'GPT-4o-mini',
  'chatgpt-4o-mini': 'GPT-4o-mini',
  'chatgpt-4.1-mini': 'GPT-4.1-mini',
  'gpt-4.1-mini': 'GPT-4.1-mini',

  // O1 models - keep as simple o1 format
  o1: 'o1',
  'chatgpt-o1': 'o1',
  'gpt-o1': 'o1',

  // O3 models - keep as simple o3 format
  'o3-mini': 'o3-mini',
  'chatgpt-o3-mini': 'o3-mini',
  'gpt-o3-mini': 'o3-mini',

  // Add other model name mappings here as needed
  // This ensures all models have consistent display names
};

/**
 * Normalizes a model name by removing institution-specific suffixes and
 * applying any custom mappings defined in MODEL_NAME_NORMALIZATION_MAP
 *
 * @param modelName - The model name to normalize
 * @returns The normalized model name
 */
export function normalizeModelName(
  modelName: string | null | undefined,
): string | null {
  if (!modelName) {
    return null;
  }

  // Check if we have a specific mapping for this model name
  if (MODEL_NAME_NORMALIZATION_MAP[modelName]) {
    return MODEL_NAME_NORMALIZATION_MAP[modelName];
  }

  // Generic normalization: remove common institution suffixes
  const suffixesToRemove = ['-hkbu', '-HKBU'];

  for (const suffix of suffixesToRemove) {
    if (modelName.endsWith(suffix)) {
      return modelName.slice(0, -suffix.length);
    }
  }

  // Return original name if no normalization needed
  return modelName;
}

/**
 * Checks if a model name needs normalization
 *
 * @param modelName - The model name to check
 * @returns True if the model name needs normalization
 */
export function needsNormalization(
  modelName: string | null | undefined,
): boolean {
  if (!modelName) {
    return false;
  }

  return normalizeModelName(modelName) !== modelName;
}

/**
 * Gets the reverse mapping - from normalized name back to full name
 * This might be useful for API calls that expect the full model name
 *
 * @param normalizedName - The normalized model name
 * @returns The full model name if a mapping exists, otherwise the normalized name
 */
export function getFullModelName(
  normalizedName: string | null | undefined,
): string | null {
  if (!normalizedName) {
    return null;
  }

  // Find the full name by looking for the normalized name in the values
  const entry = Object.entries(MODEL_NAME_NORMALIZATION_MAP).find(
    ([, normalized]) => normalized === normalizedName,
  );

  return entry ? entry[0] : normalizedName;
}

/**
 * Common model display name mappings for when exact matches aren't found
 * This helps provide user-friendly names even when the availableModels list doesn't contain a perfect match
 */
const DISPLAY_NAME_FALLBACK_MAP: Record<string, string> = {
  // GPT models - various common patterns
  'gpt-4o': 'GPT-4o',
  'chatgpt-4o': 'GPT-4o',
  'gpt-4o-mini': 'GPT-4o-mini',
  'chatgpt-4o-mini': 'GPT-4o-mini',
  'gpt-4.1': 'GPT-4.1',
  'chatgpt-4.1': 'GPT-4.1',
  'gpt-4.1-mini': 'GPT-4.1-mini',
  'chatgpt-4.1-mini': 'GPT-4.1-mini',
  'o1': 'o1',
  'chatgpt-o1': 'o1',
  'gpt-o1': 'o1',
  'o3-mini': 'o3-mini',
  'chatgpt-o3-mini': 'o3-mini',
  'gpt-o3-mini': 'o3-mini',
  
  // Claude models
  'claude-3-5-sonnet': 'Claude-3.5-Sonnet',
  'claude-3.5-sonnet': 'Claude-3.5-Sonnet',
  'claude-3-opus': 'Claude-3-Opus',
  'claude-3-haiku': 'Claude-3-Haiku',
  
  // Gemini models
  'gemini-1.5-pro': 'Gemini-1.5-Pro',
  'gemini-1.5-flash': 'Gemini-1.5-Flash',
  'gemini-2.0-flash': 'Gemini-2.0-Flash',
  'gemini-2.0-flash-exp': 'Gemini-2.0-Flash-Experimental',
  'gemini-2.0-flash-lite': 'Gemini-2.0-Flash-Lite',
  'gemini-2.5-flash': 'Gemini-2.5-Flash',
  
  // DeepSeek models
  'deepseek-v3': 'DeepSeek-V3',
  'deepseek-v3-hkbu': 'DeepSeek-V3',
  'deepseek-chat': 'DeepSeek-Chat',
  
  // Qwen models
  'qwen-max': 'Qwen-Max',
  'qwen-plus': 'Qwen-Plus',
  'qwen-turbo': 'Qwen-Turbo',
  
  // Llama models
  'llama-3.3-70b': 'Llama-3.3-70B',
  'llama-3.2-90b': 'Llama-3.2-90B',
  'llama-3.1-405b': 'Llama-3.1-405B',
  'llama-4-maverick': 'LLama 4 Maverick',
};

/**
 * Gets the display name for a given model name by looking it up in the available models
 * This is used to convert API names (model_name) back to user-friendly display names
 *
 * @param modelName - The model name (API identifier) to look up
 * @param availableModels - Array of available models with display_name and model_name
 * @returns The display name if found, otherwise falls back to normalized model name
 */
export function getDisplayNameFromModelName<
  T extends { model_name: string; display_name: string },
>(modelName: string | null | undefined, availableModels: T[]): string | null {
  if (!modelName) {
    console.log(`[getDisplayNameFromModelName] ❌ No model name provided`);
    return null;
  }

  if (!availableModels || availableModels.length === 0) {
    console.log(
      `[getDisplayNameFromModelName] ⚠️ No available models provided, using fallback map:`,
      {
        modelName,
        availableModelsCount: availableModels?.length || 0,
      },
    );
    return getFallbackDisplayName(modelName);
  }

  // Strategy 1: Try exact match by model_name
  const exactMatch = availableModels.find(
    (model) => model.model_name === modelName,
  );

  if (exactMatch?.display_name) {
    console.log(`[getDisplayNameFromModelName] ✅ Exact match found:`, {
      apiName: modelName,
      displayName: exactMatch.display_name,
      matchType: 'exact',
    });
    return exactMatch.display_name;
  }

  // Strategy 2: Try fuzzy match by removing version suffixes
  // Remove patterns like -YYYY-MM-DD, -vN.N, -YYYY, etc.
  const versionSuffixRegex = /-(?:\d{4}-\d{2}-\d{2}|v?\d+(?:\.\d+)*|\d{4})$/;
  const baseModelName = modelName.replace(versionSuffixRegex, '');

  if (baseModelName !== modelName) {
    const fuzzyMatch = availableModels.find(
      (model) => model.model_name === baseModelName,
    );

    if (fuzzyMatch?.display_name) {
      console.log(`[getDisplayNameFromModelName] ✅ Fuzzy match found:`, {
        originalApiName: modelName,
        baseModelName,
        displayName: fuzzyMatch.display_name,
        matchType: 'fuzzy',
        transformation: `${modelName} → ${baseModelName}`,
      });
      return fuzzyMatch.display_name;
    }
  }

  // Strategy 3: Try display_name exact match (in case model_name and display_name are swapped)
  const displayNameMatch = availableModels.find(
    (model) => model.display_name === modelName,
  );

  if (displayNameMatch?.display_name) {
    console.log(`[getDisplayNameFromModelName] ✅ Display name match found:`, {
      apiName: modelName,
      displayName: displayNameMatch.display_name,
      matchType: 'display-name-exact',
    });
    return displayNameMatch.display_name;
  }

  // Strategy 4: Try partial matching (contains) - prioritize longest/most specific matches
  const baseModelNameForPartial = modelName.replace(versionSuffixRegex, '');
  const potentialPartialMatches = availableModels.filter(
    (model) =>
      modelName.includes(model.model_name) ||
      model.model_name.includes(baseModelNameForPartial) ||
      baseModelNameForPartial.includes(model.model_name),
  );

  if (potentialPartialMatches.length > 0) {
    // Sort by specificity: longest model_name first (most specific match)
    const sortedMatches = potentialPartialMatches.sort(
      (a, b) => b.model_name.length - a.model_name.length,
    );

    const bestPartialMatch = sortedMatches[0];

    console.log(`[getDisplayNameFromModelName] ✅ Partial match found:`, {
      apiName: modelName,
      baseModelNameForPartial,
      potentialMatches: potentialPartialMatches.map((m) => ({
        model_name: m.model_name,
        display_name: m.display_name,
      })),
      selectedMatch: {
        model_name: bestPartialMatch.model_name,
        display_name: bestPartialMatch.display_name,
      },
      matchType: 'partial-longest',
    });

    return bestPartialMatch.display_name;
  }

  // Strategy 5: Try case-insensitive matches
  const caseInsensitiveMatch = availableModels.find(
    (model) => 
      model.model_name.toLowerCase() === modelName.toLowerCase() ||
      model.display_name.toLowerCase() === modelName.toLowerCase(),
  );

  if (caseInsensitiveMatch?.display_name) {
    console.log(`[getDisplayNameFromModelName] ✅ Case-insensitive match found:`, {
      apiName: modelName,
      displayName: caseInsensitiveMatch.display_name,
      matchType: 'case-insensitive',
    });
    return caseInsensitiveMatch.display_name;
  }

  // Strategy 6: Use fallback mapping for common model names
  const fallbackDisplayName = getFallbackDisplayName(modelName);
  if (fallbackDisplayName) {
    console.log(`[getDisplayNameFromModelName] ✅ Fallback mapping found:`, {
      apiName: modelName,
      displayName: fallbackDisplayName,
      matchType: 'fallback-map',
    });
    return fallbackDisplayName;
  }

  // Strategy 7: Last resort - use normalization with better formatting
  const normalizedName = normalizeModelName(modelName);
  const formattedName = formatModelNameForDisplay(normalizedName || modelName);
  
  console.log(`[getDisplayNameFromModelName] 🔄 Final fallback to formatted name:`, {
    apiName: modelName,
    baseModelName: baseModelName !== modelName ? baseModelName : 'no-change',
    normalizedName,
    formattedName,
    matchType: 'formatted-fallback',
    availableModels: availableModels.map((m) => ({
      model_name: m.model_name,
      display_name: m.display_name,
    })),
  });

  return formattedName;
}

/**
 * Gets a fallback display name from the predefined mapping
 *
 * @param modelName - The model name to look up
 * @returns The mapped display name or null if not found
 */
function getFallbackDisplayName(modelName: string): string | null {
  // Try exact match first
  if (DISPLAY_NAME_FALLBACK_MAP[modelName]) {
    return DISPLAY_NAME_FALLBACK_MAP[modelName];
  }

  // Try with version suffixes removed
  const versionSuffixRegex = /-(?:\d{4}-\d{2}-\d{2}|v?\d+(?:\.\d+)*|\d{4})$/;
  const baseModelName = modelName.replace(versionSuffixRegex, '');
  
  if (baseModelName !== modelName && DISPLAY_NAME_FALLBACK_MAP[baseModelName]) {
    return DISPLAY_NAME_FALLBACK_MAP[baseModelName];
  }

  // Try case-insensitive match
  const lowerModelName = modelName.toLowerCase();
  const matchingKey = Object.keys(DISPLAY_NAME_FALLBACK_MAP).find(
    key => key.toLowerCase() === lowerModelName,
  );
  
  if (matchingKey) {
    return DISPLAY_NAME_FALLBACK_MAP[matchingKey];
  }

  return null;
}

/**
 * Formats a model name for better display when no other options are available
 *
 * @param modelName - The model name to format
 * @returns A better formatted version of the model name
 */
function formatModelNameForDisplay(modelName: string): string {
  // Remove common institutional suffixes
  let formatted = modelName.replace(/-hkbu$/i, '');
  
  // Capitalize first letters and handle common patterns
  formatted = formatted
    .split('-')
    .map(part => {
      // Handle special cases
      if (part.toLowerCase() === 'gpt') return 'GPT';
      if (part.toLowerCase() === 'api') return 'API';
      if (part.toLowerCase() === 'ai') return 'AI';
      if (part.match(/^v?\d+(\.\d+)*$/)) return part; // Keep version numbers as-is
      if (part.match(/^\d{4}$/)) return part; // Keep years as-is
      
      // Capitalize first letter
      return part.charAt(0).toUpperCase() + part.slice(1).toLowerCase();
    })
    .join('-');

  return formatted;
}
