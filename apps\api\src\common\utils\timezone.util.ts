/**
 * Hong Kong timezone utility functions
 * Provides consistent timezone handling across the application
 */

/**
 * Get current time in Hong Kong timezone (UTC+8)
 * This ensures consistent timezone handling across all database operations
 * to match SQL Server GETDATE() behavior
 */
export function getHongKongTime(): Date {
  const now = new Date();
  return new Date(now.getTime() + (8 * 60 * 60 * 1000));
}

/**
 * Get Hong Kong date only (start of day) in Hong Kong timezone
 * Used for daily grouping and date-based queries
 */
export function getHongKongDateOnly(date?: Date): Date {
  const targetDate = date || new Date();
  const hkTime = new Date(targetDate.getTime() + (8 * 60 * 60 * 1000));
  return new Date(Date.UTC(hkTime.getUTCFullYear(), hkTime.getUTCMonth(), hkTime.getUTCDate()));
}