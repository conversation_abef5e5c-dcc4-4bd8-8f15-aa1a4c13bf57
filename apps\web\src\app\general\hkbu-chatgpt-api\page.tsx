'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Paper from '@mui/material/Paper';
import Alert from '@mui/material/Alert';
import Container from '@mui/material/Container';
import { styled } from '@mui/material/styles';

// Styled components for better layout
const StyledHeader = styled(Box)(({ theme }) => ({
  position: 'fixed',
  top: 0,
  left: 0,
  right: 0,
  zIndex: 1000,
  backdropFilter: 'blur(16px)',
  backgroundColor: 'rgba(255, 255, 255, 0.6)',
  borderBottom: '1px solid rgba(0, 0, 0, 0.1)',
  minHeight: '64px',
  display: 'flex',
  alignItems: 'center',
  [theme.breakpoints.up('lg')]: {
    minHeight: '80px',
  },
}));

const StyledMain = styled(Box)(({ theme }) => ({
  marginTop: '64px',
  [theme.breakpoints.up('lg')]: {
    marginTop: '80px',
  },
  minHeight: 'calc(100svh - 64px)',
  [theme.breakpoints.up('lg')]: {
    minHeight: 'calc(100svh - 80px)',
  },
  display: 'flex',
  justifyContent: 'center',
}));

const CodeBlock = styled('pre')(({ theme }) => ({
  backgroundColor: '#f5f5f5',
  padding: theme.spacing(2),
  borderRadius: theme.shape.borderRadius,
  overflow: 'auto',
  fontSize: '0.875rem',
  fontFamily: 'monospace',
  border: '1px solid #e0e0e0',
  '& code': {
    whiteSpace: 'pre-wrap',
    wordBreak: 'break-word',
  },
}));

const ImageContainer = styled(Box)(({ theme }) => ({
  margin: `${theme.spacing(2)} 0`,
  '& img': {
    maxWidth: '100%',
    height: 'auto',
    border: '1px solid #e0e0e0',
    borderRadius: theme.shape.borderRadius,
  },
}));

export default function HkbuChatgptApiPage() {
  const { data: session, status } = useSession();
  const basePath = process.env.NEXT_PUBLIC_BASE_PATH || '';

  // Check if user has REST API access
  const isUserApiAuthorized = session?.user?.rest === true;

  // Handle anchor link navigation after component mounts
  useEffect(() => {
    const handleAnchorNavigation = () => {
      const hash = window.location.hash;
      if (hash) {
        const element = document.querySelector(hash);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth' });
        }
      }
    };

    // Use setTimeout to ensure the page is fully rendered before scrolling
    const timer = setTimeout(handleAnchorNavigation, 100);
    return () => clearTimeout(timer);
  }, [session, isUserApiAuthorized]); // Re-run when session changes

  // Show loading state
  if (status === 'loading') {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100svh',
        }}
      >
        <Typography>Loading...</Typography>
      </Box>
    );
  }

  // Show access denied for unauthenticated users or users without REST API access
  if (!session || !isUserApiAuthorized) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          <Typography variant="h6" gutterBottom>
            Access Denied
          </Typography>
          <Typography>
            You need to be signed in with REST API access to view this page.
            Please contact your administrator to request access.
          </Typography>
        </Alert>
      </Container>
    );
  }

  return (
    <>
      <StyledHeader>
        <Container maxWidth="xl">
          <Box sx={{ display: 'flex', alignItems: 'center', px: 1 }}>
            <img
              src={`${basePath}/assets/images/hkbu_logo.png`}
              alt="HKBU"
              style={{
                height: '32px',
                display: 'block',
              }}
            />
            <img
              src={`${basePath}/assets/images/hkbu_logo_horizontal.png`}
              alt="HKBU"
              style={{
                height: '32px',
                display: 'none',
              }}
              className="hidden lg:block"
            />
            <Box
              sx={{
                ml: 2,
                pl: 2,
                borderLeft: '1px solid #9ca3af',
                fontSize: { xs: '1.25rem', lg: '2rem' },
                fontWeight: 'bold',
                background: 'linear-gradient(to right, #059669, #0f766e)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                fontFamily: 'Poppins, sans-serif',
              }}
            >
              HKBU GenAI Platform API
            </Box>
          </Box>
        </Container>
      </StyledHeader>

      <StyledMain>
        <Container maxWidth="lg" sx={{ py: 4 }}>
          <Paper elevation={1} sx={{ p: 4 }}>
            <Typography variant="h4" component="h1" gutterBottom>
              HKBU GenAI Platform API Documentation
            </Typography>

            <Typography variant="h6" component="h2" gutterBottom sx={{ mt: 4 }}>
              Verifying the API Key
            </Typography>

            <Typography variant="body1" paragraph>
              API key access to HKBU GenAI Platform API verification.
            </Typography>

            <Box component="ul" sx={{ pl: 3, '& > li': { mb: 3 } }}>
              <Box component="li">
                <Typography
                  variant="h6"
                  component="h3"
                  gutterBottom
                  id="postman"
                >
                  Postman for Windows
                </Typography>
                <Box component="ol" sx={{ pl: 2, '& > li': { mb: 2 } }}>
                  <Box component="li">
                    <Typography variant="body1" paragraph>
                      <a
                        href="https://www.postman.com/downloads/?utm_source=postman-home"
                        target="_blank"
                        rel="noopener noreferrer"
                        style={{
                          color: '#1976d2',
                          textDecoration: 'underline',
                        }}
                      >
                        Download
                      </a>{' '}
                      and start Postman.
                    </Typography>
                  </Box>
                  <Box component="li">
                    <Typography variant="body1" paragraph>
                      Formulate a POST request to{' '}
                      <code>
                        https://genai.hkbu.edu.hk/api/v0/rest/deployments/gpt-4o-mini/chat/completions?api-version=2024-05-01-preview
                      </code>
                      , and configure the header as outlined below:
                    </Typography>
                    <ImageContainer>
                      <img
                        src={`${basePath}/assets/images/postman1_v2.png`}
                        alt="Postman header configuration"
                      />
                    </ImageContainer>
                  </Box>
                  <Box component="li">
                    <Typography variant="body1" paragraph>
                      Configure the body as outlined below:
                    </Typography>
                    <ImageContainer>
                      <img
                        src={`${basePath}/assets/images/postman2_v2.png`}
                        alt="Postman body configuration"
                      />
                    </ImageContainer>
                  </Box>
                  <Box component="li">
                    <Typography variant="body1" paragraph>
                      Send the request to obtain a response.
                    </Typography>
                    <ImageContainer>
                      <img
                        src={`${basePath}/assets/images/postman3_v2.png`}
                        alt="Postman response"
                      />
                    </ImageContainer>
                  </Box>
                </Box>
              </Box>

              <Box component="li">
                <Typography variant="h6" component="h3" gutterBottom id="curl">
                  Command line - Curl
                </Typography>
                <Box component="ol" sx={{ pl: 2, '& > li': { mb: 2 } }}>
                  <Box component="li">
                    <Typography variant="body1" paragraph>
                      Launch a terminal, command prompt, or powershell on your
                      personal computer.
                    </Typography>
                  </Box>
                  <Box component="li">
                    <Typography variant="body1" paragraph>
                      Input the command given below:
                    </Typography>
                    <CodeBlock>
                      <code>
                        {`curl https://genai.hkbu.edu.hk/api/v0/rest/deployments/gpt-4o-mini/chat/completions?api-version=2024-05-01-preview \\
    -H "Content-Type: application/json" \\
    -H "api-key: <YOUR_API_KEY>" \\
    -d '{"messages":[{"role": "user", "content": "Hello!"}]}'`}
                      </code>
                    </CodeBlock>
                  </Box>
                  <Box component="li">
                    <Typography variant="body1" paragraph>
                      Example response:
                    </Typography>
                    <CodeBlock>
                      <code>
                        {`{
  "id": "chatcmpl-4kGh7iXtjW4lc9eGhff6Hp8C7btdQ",
  "object": "chat.completion",
  "created": 1693903613,
  "model": "gpt-4o-mini",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "Hello! How can I assist you today?"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 9,
    "completion_tokens": 9,
    "total_tokens": 18
  }
}`}
                      </code>
                    </CodeBlock>
                  </Box>
                </Box>
              </Box>

              <Box component="li">
                <Typography
                  variant="h6"
                  component="h3"
                  gutterBottom
                  id="python"
                >
                  Python Example
                </Typography>
                <Box component="ol" sx={{ pl: 2, '& > li': { mb: 2 } }}>
                  <Box component="li">
                    <Typography variant="body1" paragraph>
                      Copy the function given below:
                    </Typography>
                    <CodeBlock>
                      <code>
                        {`import requests

apiKey = "<YOUR_API_KEY>"
basicUrl = "https://genai.hkbu.edu.hk/api/v0/rest"
modelName = "gpt-4o-mini"
apiVersion = "2024-05-01-preview"

def submit(message):
    conversation = [{"role": "user", "content": message}]
    url = basicUrl + "/deployments/" + modelName + "/chat/completions/?api-version=" + apiVersion
    headers = { 'Content-Type': 'application/json', 'api-key': apiKey }
    payload = { 'messages': conversation }
    response = requests.post(url, json=payload, headers=headers)

    if response.status_code == 200:
        data = response.json()
        return data
    else:
        return 'Error:', response.status_code, response.text`}
                      </code>
                    </CodeBlock>
                  </Box>
                  <Box component="li">
                    <Typography variant="body1" paragraph>
                      Call submit function and print out the result:
                    </Typography>
                    <CodeBlock>
                      <code>
                        {`result = submit("Hello!")
print(result)`}
                      </code>
                    </CodeBlock>
                  </Box>
                  <Box component="li">
                    <Typography variant="body1" paragraph>
                      Example response:
                    </Typography>
                    <CodeBlock>
                      <code>
                        {`{
  "id": "chatcmpl-8BeLThmRfxlGEWnBB4nUgZ5lYiP3J",
  "object": "chat.completion",
  "created": 1697787387,
  "model": "gpt-4o-mini",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "Hello! How can I assist you today?"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 8,
    "completion_tokens": 9,
    "total_tokens": 17
  }
}`}
                      </code>
                    </CodeBlock>
                  </Box>
                </Box>
              </Box>
            </Box>
          </Paper>
        </Container>
      </StyledMain>
    </>
  );
}
