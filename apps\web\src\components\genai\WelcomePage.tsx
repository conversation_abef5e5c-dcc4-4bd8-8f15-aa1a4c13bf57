'use client';

import React from 'react';
import { signIn } from 'next-auth/react';
import { Box, Button, Typography, Link, Paper } from '@mui/material';
import { keyframes } from '@mui/material/styles';
import HkbuLogo from '@/assets/icons/HkbuLogo';
import ForumOutlinedIcon from '@mui/icons-material/ForumOutlined';

const fadeIn = keyframes`
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

const WelcomePage: React.FC = () => {
  const backgroundImageUrl = `${process.env.NEXT_PUBLIC_BASE_PATH || ''}/hkbu_campus.jpg`;
  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100svh',
        width: '100%',
        position: 'relative',
        overflow: 'hidden',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: `url(${backgroundImageUrl})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          filter: 'blur(8px)',
          zIndex: -1,
        },
      }}
    >
      <Paper
        elevation={12}
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'space-around',
          p: { xs: 4, sm: 6 },
          borderRadius: '16px',
          bgcolor: 'rgba(255, 255, 255, 0.85)',
          backdropFilter: 'blur(10px)',
          width: { xs: '90%', sm: 'auto' },
          maxWidth: '500px',
          minHeight: '600px',
          animation: `${fadeIn} 0.8s ease-out`,
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: 2,
          }}
        >
          <Box sx={{ width: 64, height: 64 }}>
            <HkbuLogo />
          </Box>
          <Typography
            variant="h4"
            component="h1"
            fontWeight={700}
            color="primary.main"
            sx={{
              fontSize: { xs: '1.8rem', sm: '2.2rem' },
            }}
          >
            HKBU GenAI Platform
          </Typography>
          <Typography
            variant="subtitle1"
            color="text.secondary"
            fontStyle="italic"
            sx={{
              fontSize: { xs: '0.9rem', sm: '1rem' },
            }}
          >
            Provided to all HKBU staff and students
          </Typography>
        </Box>

        <ForumOutlinedIcon
          sx={{
            fontSize: { xs: '70px', sm: '90px' },
            color: 'primary.dark',
            my: 3,
          }}
        />

        <Button
          variant="contained"
          size="large"
          onClick={() => signIn('buam')}
          sx={{
            py: '12px',
            px: '40px',
            borderRadius: '50px',
            fontWeight: 700,
            fontSize: { xs: '0.9rem', sm: '1.1rem' },
            textTransform: 'none',
            boxShadow: '0 4px 15px rgba(0, 0, 0, 0.2)',
            transition: 'transform 0.2s, box-shadow 0.2s',
            '&:hover': {
              transform: 'translateY(-3px)',
              boxShadow: '0 6px 20px rgba(0, 0, 0, 0.3)',
            },
          }}
        >
          Sign in with HKBU SSOid
        </Button>

        <Box sx={{ textAlign: 'center', mt: 4 }}>
          <Typography
            variant="caption"
            display="block"
            color="text.secondary"
            sx={{ fontSize: '0.7rem' }}
          >
            For technical assistance, please contact ITO Service Centre (3411
            7899,{' '}
            <Link
              href="mailto:<EMAIL>"
              target="_blank"
              rel="noopener noreferrer"
            >
              <EMAIL>
            </Link>
            ).
          </Typography>
          <Typography
            variant="caption"
            display="block"
            color="text.secondary"
            sx={{ mt: 1, fontSize: '0.7rem' }}
          >
            Copyright © 2023-2024. Hong Kong Baptist University. All Rights
            Reserved.{' '}
            <Link
              href="https://bupdpo.hkbu.edu.hk/en/policies-and-procedures/pps-pics.html"
              target="_blank"
              rel="noopener noreferrer"
            >
              Privacy Policy
            </Link>
          </Typography>
        </Box>
      </Paper>
    </Box>
  );
};

export default WelcomePage;
