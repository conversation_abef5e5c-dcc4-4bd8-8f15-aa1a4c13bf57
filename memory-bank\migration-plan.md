# HKBU GenAI Platform Migration Plan (Revised 2025-04-14)

## 1. Current Status Summary

Based on analysis of the `hkbu-genai-platform` repository as of 2025-04-14:

*   **Project Structure:** Turborepo monorepo with NestJS backend (`apps/api`), Next.js frontend (`apps/web`), and shared packages (`packages/database`, etc.).
*   **Backend (`apps/api`):**
    *   Core chat completion functionality is partially migrated using Langchain, NestJS modules (`ChatCompletionModule`, `LlmModule`), and Prisma.
    *   Existing SQL Server Stored Procedures (SPs) for message history (`sp_cvst_GetDecryptedMessagesByConversationUUID`) and insertion (`sp_cvst_InsertMessageWithPrompts`) are called directly via Prisma raw queries. **(Needs verification if this is the final intended strategy)**.
    *   Authentication (JWT), LLM abstraction, and Prisma integration are in place.
    *   Endpoints for OCR, Feedback, T&C, Get Notify, API Key generation, and Model List exist in `GeneralController`, likely migrated.
*   **Frontend (`apps/web`):**
    *   Significant migration/development using Next.js App Router, Redux Toolkit for state management, and numerous UI components (chat interface, modals, controls).
    *   Authentication (NextAuth.js) is integrated.
*   **Scope Clarification:** DALL-E and Web Scraping features from the original `gpt/` project are **out of scope** for this migration.

## 2. Revised Migration Plan

The focus is now on migrating the remaining backend features, verifying existing code, ensuring full integration, and implementing tests.

1.  **Backend: Implement Remaining Endpoints**
    *   Implement Usage endpoint logic in `GeneralService`.
    *   Implement Set Notify endpoint logic in `GeneralService`.
2.  **Verification:**
    *   Verify implementation of existing migrated features (Chat Completion, OCR, Feedback, T&C, Get Notify, Auth, LLM abstraction, SP calls) in `apps/api`.
    *   Verify implementation of existing frontend components and pages in `apps/web`.
    *   Verify frontend-backend integration for all features.
3.  **Testing:**
    *   Implement unit, integration, and potentially end-to-end tests for backend services and controllers.
    *   Implement tests for frontend components and integration points.
4.  **Deployment:**
    *   Update/verify deployment scripts (`gitlab/`) for the completed monorepo structure.

## 3. Implementation Details: Remaining Backend Endpoints

These endpoints should be added to `GeneralController` and `GeneralService` within `hkbu-genai-platform/apps/api/src/general/`.

### 3.1. Usage Endpoint (`/general/usage`)

*   **Goal:** Replicate functionality of `gpt/app/api/general/usage/route.ts`. Provide token usage information for a specific model for the logged-in user.
*   **Controller (`GeneralController`):**
    *   Add endpoint: `@Get('usage/:deploymentName')` or `@Post('usage')` (if `deploymentName` sent in body). Prefer GET if `deploymentName` is part of the resource path.
    *   Use `@UseGuards(AuthGuard('jwt'))`.
    *   Extract `deploymentName` from params or body.
    *   Extract `user` (`userId`, `type`) from the authenticated request (`req.user`).
    *   Call `generalService.getUsageInfo(user, deploymentName)`.
*   **Service (`GeneralService`):**
    *   Implement `async getUsageInfo(user: AuthenticatedUser, deploymentName: string): Promise<UsageInfoDto>`
    *   Inject `PrismaService`.
    *   **Option A (Replicate SP Logic - Recommended for testability):**
        1.  Fetch model details: `await prisma.model_list.findFirst({ where: { deployment_name: deploymentName, /* + access checks */ } })`. Handle not found.
        2.  Fetch user-specific limit: `await prisma.acl_user_token_limit.findUnique({ where: { username_model_list_id: { username: user.userId, model_list_id: model.id } } })`.
        3.  Determine `monthly_token_limit` (user override or default from `model_list` based on `user.type`).
        4.  Calculate start/end of current month.
        5.  Fetch monthly spent tokens: `await prisma.acl_user_token_spent.aggregate({ where: { username: user.userId, model_name: model.model_name, token_date: { gte: startOfMonth, lte: endOfMonth } }, _sum: { token_spent: true } })`. Handle null sum.
        6.  Fetch last update time (optional, maybe from the latest `acl_user_token_spent` record).
        7.  Calculate `quota_left`, `token_spent_percentage`.
        8.  Calculate `next_quota_renew` date.
        9.  Return data in a `UsageInfoDto`.
    *   **Option B (Call SPs - If strategy confirmed):**
        1.  Call `sp_model_GetModelListByDeploymentlName_v2` via `$queryRaw`.
        2.  Call `sp_acl_GetUserTokenLimitByUsername_v2` via `$queryRaw`.
        3.  Call `sp_cvst_GetTokenUsageMonthly_v2` via `$queryRaw`.
        4.  Combine results and return data.
*   **DTOs:** Create `UsageInfoDto` for the response structure.
*   **Rate Limiting:** Apply appropriate rate limiting (`@nestjs/throttler`).

### 3.2. Set Notify Endpoint (`/general/notify`)

*   **Goal:** Replicate functionality of `gpt/app/api/general/set_notify/route.ts`. Mark the user as having seen/acknowledged notifications.
*   **Controller (`GeneralController`):**
    *   Add endpoint: `@Put('notify')` or `@Patch('notify')`.
    *   Use `@UseGuards(AuthGuard('jwt'))`.
    *   Extract `user` (`userId`) from the authenticated request (`req.user`).
    *   Call `generalService.setNotifyStatus(user.userId)`.
    *   Return `{ success: true }`.
*   **Service (`GeneralService`):**
    *   Implement `async setNotifyStatus(userId: string): Promise<{ success: boolean }>`
    *   Inject `PrismaService`.
    *   **Option A (Replicate SP Logic - Recommended):**
        1.  Use `prisma.notify.upsert` to create or update the record for the `userId`.
        2.  `where: { ssoid: userId }`
        3.  `update: { notified: 'Y', update_by: userId, update_dt: new Date() }`
        4.  `create: { ssoid: userId, notified: 'Y', create_by: userId, create_dt: new Date() }`
        5.  Return `{ success: true }`.
    *   **Option B (Call SP - If strategy confirmed):**
        1.  Call `sp_notify_InsertNotified` via `$executeRaw`.
        2.  Return `{ success: true }`.
*   **DTOs:** Simple response DTO `{ success: boolean }`.
*   **Rate Limiting:** Apply appropriate rate limiting.

## 4. Verification and Testing

*   Thoroughly review and test the existing migrated code in `apps/api` and `apps/web`.
*   Test the newly implemented Usage and Set Notify endpoints.
*   Perform integration testing between frontend components and all backend endpoints.
*   Implement unit tests for services and controllers where appropriate.