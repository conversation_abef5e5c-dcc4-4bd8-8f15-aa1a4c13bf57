{"models": [{"name": "qwen-plus", "deployment_name": "qwen-plus", "description": "Qwen Plus model", "timeout": 30, "supports_system_messages": true, "supports_temperature": true, "api_version": "v1"}, {"name": "qwen-max", "deployment_name": "qwen-max-2025-01-25", "description": "Qwen2.5-Max model", "timeout": 30, "supports_system_messages": true, "supports_temperature": true, "api_version": "v1"}, {"name": "deepseek-r1", "deployment_name": "deepseek-r1-hkbu", "description": "DeepSeek R1 model", "timeout": 30, "supports_system_messages": true, "supports_temperature": true, "api_version": "2024-05-01-preview"}, {"name": "deepseek-v3", "deployment_name": "deepseek-v3-hkbu", "description": "DeepSeek V3 model", "timeout": 30, "supports_system_messages": true, "supports_temperature": true, "api_version": "2024-05-01-preview"}, {"name": "o3-mini", "deployment_name": "chatgpt-o3-mini", "description": "OpenAI O3 Mini model", "timeout": 30, "supports_system_messages": false, "supports_temperature": false, "api_version": "2024-12-01-preview"}, {"name": "o1", "deployment_name": "chatgpt-o1", "description": "OpenAI O1 model", "timeout": 60, "supports_system_messages": false, "supports_temperature": false, "api_version": "2024-12-01-preview"}, {"name": "gpt-4.1", "deployment_name": "chatgpt-4.1", "description": "GPT-4.1 model", "timeout": 30, "supports_system_messages": true, "supports_temperature": true, "api_version": "2024-12-01-preview"}, {"name": "gpt-4.1-mini", "deployment_name": "chatgpt-4.1-mini", "description": "GPT-4.1 Mini model", "timeout": 30, "supports_system_messages": true, "supports_temperature": true, "api_version": "2024-12-01-preview"}, {"name": "gemini-2.5-flash", "deployment_name": "gemini-2.5-flash", "description": "Google Gemini 2.5 Flash model", "timeout": 30, "supports_system_messages": true, "supports_temperature": false, "api_version": ""}, {"name": "gemini-2.5-pro", "deployment_name": "gemini-2.5-pro", "description": "Google Gemini 2.5 Pro model", "timeout": 30, "supports_system_messages": true, "supports_temperature": false, "api_version": ""}, {"name": "llama-4-maverick", "deployment_name": "llama-4-maverick-17b-128e-instruct-maas", "description": "Llama 4 Maverick model", "timeout": 30, "supports_system_messages": true, "supports_temperature": true, "api_version": "20240723"}, {"name": "text-embedding-3-large", "deployment_name": "text-embedding-3-large", "description": "Azure OpenAI text-embedding-3-large", "model_type": "embedding", "timeout": 30, "api_version": "2024-02-01"}, {"name": "text-embedding-3-small", "deployment_name": "text-embedding-3-small", "description": "Azure OpenAI text-embedding-3-small", "model_type": "embedding", "timeout": 30, "api_version": "2024-02-01"}, {"name": "text-embedding-3-large-preview", "deployment_name": "text-embedding-3-large", "description": "Azure OpenAI text-embedding-3-large (2024-05-01-preview)", "model_type": "embedding", "timeout": 30, "api_version": "2024-05-01-preview"}, {"name": "text-embedding-3-small-preview", "deployment_name": "text-embedding-3-small", "description": "Azure OpenAI text-embedding-3-small (2024-05-01-preview)", "model_type": "embedding", "timeout": 30, "api_version": "2024-05-01-preview"}], "default_parameters": {"temperature": 0.7, "stream": false, "api_version": "2024-02-01"}}