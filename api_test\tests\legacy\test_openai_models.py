#!/usr/bin/env python3
"""
Dedicated test script for OpenAI o1 and o3-mini models via the HKBU GenAI Platform.
These models have special requirements:
- They don't support system messages
- They may require specific API versions
- They use specific Azure instances
"""

import json
import os
import sys
import time
import requests
from dotenv import load_dotenv
from typing import Dict, Any, List


def load_config() -> Dict[str, str]:
    """Load configuration from .env file."""
    load_dotenv()
    
    api_key = os.getenv("API_KEY")
    base_url = os.getenv("BASE_URL")
    
    if not api_key:
        raise ValueError("API_KEY not found in .env file")
    if not base_url:
        raise ValueError("BASE_URL not found in .env file")
    
    return {
        "api_key": api_key,
        "base_url": base_url
    }


def load_models_config() -> Dict[str, Any]:
    """Load models configuration from models.json."""
    config_path = os.path.join(os.path.dirname(__file__), "../../config/models.json")
    if not os.path.exists(config_path):
        raise ValueError("models.json not found. Please create it first.")
    
    with open(config_path, 'r') as f:
        return json.load(f)


def test_openai_model(config: Dict[str, str], model_name: str, 
                      test_cases: List[Dict[str, str]]) -> List[Dict[str, Any]]:
    """Test an OpenAI model with multiple test cases."""
    
    # Load model configuration to get deployment name and capabilities
    models_config = load_models_config()
    deployment_name = model_name  # fallback
    model_info = None
    
    for model in models_config.get("models", []):
        if model["name"] == model_name:
            deployment_name = model["deployment_name"]
            model_info = model
            break
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{'='*60}")
        print(f"Test Case {i}/{len(test_cases)}: {test_case['name']}")
        print(f"{'='*60}")
        
        # Construct the URL using model name instead of deployment name
        # This is a temporary fix for models where deployment_name != model_name
        url = f"{config['base_url']}/rest/deployments/{model_name}/chat/completions"
        
        # Query parameters
        params = {
            "api-version": test_case.get("api_version", "2024-02-01")
        }
        
        # Headers
        headers = {
            "Content-Type": "application/json",
            "api-key": config["api_key"]
        }
        
        # Build messages - NO system messages for o1/o3-mini
        messages = test_case.get("messages", [
            {
                "role": "user",
                "content": test_case.get("prompt", "Hello, how are you?")
            }
        ])
        
        # Build payload based on model capabilities
        supports_temperature = True
        if model_info:
            supports_temperature = model_info.get("supports_temperature", True)
        
        # Request payload
        payload = {
            "messages": messages,
            "stream": test_case.get("stream", False)
        }
        
        # Only add temperature if model supports it
        if supports_temperature:
            payload["temperature"] = test_case.get("temperature", 0.7)
        
        # Add any additional parameters
        if "max_tokens" in test_case:
            payload["max_tokens"] = test_case["max_tokens"]
        
        print(f"🚀 Testing model: {model_name} (deployment: {deployment_name})")
        print(f"📍 URL: {url}")
        print(f"⚙️  API Version: {params['api-version']}")
        print(f"💬 Messages: {json.dumps(messages, indent=2)}")
        if "temperature" in payload:
            print(f"🌡️  Temperature: {payload['temperature']}")
        else:
            print(f"ℹ️  Temperature parameter disabled for {model_name}")
        
        start_time = time.time()
        
        try:
            # Make the request
            response = requests.post(
                url,
                params=params,
                headers=headers,
                json=payload,
                timeout=test_case.get("timeout", 60)
            )
            
            elapsed_time = time.time() - start_time
            
            print(f"\n📊 Status Code: {response.status_code}")
            print(f"⏱️  Response Time: {elapsed_time:.2f}s")
            
            if response.status_code == 200:
                print("✅ Request successful!")
                
                response_data = response.json()
                
                # Extract assistant's response
                assistant_message = None
                if "choices" in response_data and len(response_data["choices"]) > 0:
                    assistant_message = response_data["choices"][0]["message"]["content"]
                    print(f"\n🤖 Assistant Response:\n{assistant_message}")
                
                # Display usage
                usage = response_data.get("usage", {})
                print(f"\n📊 Token Usage:")
                print(f"   Prompt Tokens: {usage.get('prompt_tokens', 'N/A')}")
                print(f"   Completion Tokens: {usage.get('completion_tokens', 'N/A')}")
                print(f"   Total Tokens: {usage.get('total_tokens', 'N/A')}")
                
                # Check for conversation_uuid
                if "conversation_uuid" in response_data:
                    print(f"\n⚠️  WARNING: Response contains conversation_uuid!")
                
                results.append({
                    "test_case": test_case["name"],
                    "success": True,
                    "status_code": response.status_code,
                    "response_time": elapsed_time,
                    "tokens": usage.get('total_tokens', 0),
                    "response": assistant_message
                })
                
            else:
                print(f"❌ Request failed!")
                print(f"Response Headers: {dict(response.headers)}")
                print(f"Response Body: {response.text}")
                
                # Try to parse error details
                try:
                    error_data = response.json()
                    if "error" in error_data:
                        print(f"\nError Details:")
                        print(f"  Type: {error_data['error'].get('type', 'N/A')}")
                        print(f"  Message: {error_data['error'].get('message', 'N/A')}")
                        print(f"  Code: {error_data['error'].get('code', 'N/A')}")
                except:
                    pass
                
                results.append({
                    "test_case": test_case["name"],
                    "success": False,
                    "status_code": response.status_code,
                    "response_time": elapsed_time,
                    "error": response.text[:500]
                })
                
        except requests.exceptions.Timeout:
            print(f"❌ Timeout after {test_case.get('timeout', 60)}s")
            results.append({
                "test_case": test_case["name"],
                "success": False,
                "status_code": 0,
                "response_time": test_case.get('timeout', 60),
                "error": "Timeout"
            })
            
        except Exception as e:
            print(f"❌ Exception: {type(e).__name__}: {e}")
            results.append({
                "test_case": test_case["name"],
                "success": False,
                "status_code": 0,
                "response_time": time.time() - start_time,
                "error": str(e)
            })
    
    return results


def main():
    """Main function to test OpenAI models."""
    
    # Test cases for o1 and o3-mini models
    test_cases = [
        {
            "name": "Basic greeting (no system message)",
            "prompt": "Hello! Please respond with a simple greeting.",
            "temperature": 0.7,
            "api_version": "2024-12-01-preview"
        },
        {
            "name": "Simple math question",
            "prompt": "What is 25 + 37? Please provide just the numeric answer.",
            "temperature": 0.1,
            "api_version": "2024-12-01-preview"
        },
        {
            "name": "Reasoning task",
            "prompt": "If I have 3 apples and give away 2, then buy 5 more, how many apples do I have? Show your reasoning step by step.",
            "temperature": 0.5,
            "api_version": "2024-12-01-preview"
        },
        {
            "name": "Code generation",
            "prompt": "Write a Python function that calculates the factorial of a number. Include proper error handling.",
            "temperature": 0.3,
            "api_version": "2024-12-01-preview"
        },
        {
            "name": "Multi-turn conversation simulation",
            "messages": [
                {"role": "user", "content": "My name is Alice."},
                {"role": "assistant", "content": "Hello Alice! It's nice to meet you. How can I help you today?"},
                {"role": "user", "content": "What's my name?"}
            ],
            "temperature": 0.5,
            "api_version": "2024-12-01-preview"
        }
    ]
    
    try:
        config = load_config()
        
        print("🚀 HKBU GenAI Platform - OpenAI Models Test Suite")
        print("=" * 60)
        print(f"📍 API Endpoint: {config['base_url']}")
        print(f"🔑 API Key: {config['api_key'][:8]}...")
        print("\nTesting o1 and o3-mini models with various scenarios...")
        
        # Test o1 model
        print("\n\n" + "="*60)
        print("TESTING O1 MODEL")
        print("="*60)
        o1_results = test_openai_model(config, "o1", test_cases)
        
        # Test o3-mini model
        print("\n\n" + "="*60)
        print("TESTING O3-MINI MODEL")
        print("="*60)
        o3_mini_results = test_openai_model(config, "o3-mini", test_cases)
        
        # Print summary
        print("\n\n" + "="*60)
        print("TEST SUMMARY")
        print("="*60)
        
        for model_name, results in [("o1", o1_results), ("o3-mini", o3_mini_results)]:
            successful = sum(1 for r in results if r["success"])
            total = len(results)
            
            print(f"\n{model_name.upper()} Model:")
            print(f"  Success Rate: {successful}/{total} ({successful/total*100:.1f}%)")
            
            for result in results:
                status = "✅" if result["success"] else "❌"
                print(f"  {status} {result['test_case']}", end="")
                if result["success"]:
                    print(f" - {result['response_time']:.2f}s, {result.get('tokens', 0)} tokens")
                else:
                    print(f" - {result.get('error', 'Unknown error')[:60]}...")
        
        # Overall success
        all_results = o1_results + o3_mini_results
        total_success = sum(1 for r in all_results if r["success"])
        
        print(f"\n{'='*60}")
        print(f"Overall Success Rate: {total_success}/{len(all_results)} ({total_success/len(all_results)*100:.1f}%)")
        
        # Exit with appropriate code
        sys.exit(0 if total_success == len(all_results) else 1)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()