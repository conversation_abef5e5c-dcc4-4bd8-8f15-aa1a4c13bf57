'use client'

import { usePathname, useSearchParams } from 'next/navigation'
import { useEffect } from 'react'
import Script from 'next/script'

const GTM_ID = process.env.NEXT_PUBLIC_GOOGLE_MEASUREMENT_ID

declare global {
  interface Window {
    gtag: (...args: any[]) => void;
  }
}

export const pageview = (url: string) => {
  if (typeof window.gtag === 'function') {
    window.gtag('config', GTM_ID, {
      page_path: url,
    })
  }
}

export const event = ({ action, category, label, value }: { action: string, category: string, label: string, value: number }) => {
  if (typeof window.gtag === 'function') {
    window.gtag('event', action, {
      event_category: category,
      event_label: label,
      value: value,
    })
  }
}

export default function GoogleAnalytics() {
  const pathname = usePathname()
  const searchParams = useSearchParams()

  useEffect(() => {
    const url = pathname + searchParams.toString()
    pageview(url)
  }, [pathname, searchParams])

  useEffect(() => {
    const handleScroll = () => {
      const scrollPercent = (window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100;
      if (scrollPercent >= 25 && scrollPercent < 50) {
        event({ action: 'scroll', category: 'scroll_depth', label: '25%', value: 25 });
      } else if (scrollPercent >= 50 && scrollPercent < 75) {
        event({ action: 'scroll', category: 'scroll_depth', label: '50%', value: 50 });
      } else if (scrollPercent >= 75 && scrollPercent < 100) {
        event({ action: 'scroll', category: 'scroll_depth', label: '75%', value: 75 });
      } else if (scrollPercent >= 100) {
        event({ action: 'scroll', category: 'scroll_depth', label: '100%', value: 100 });
      }
    };

    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  if (String(process.env.NODE_ENV) !== 'production' && String(process.env.NODE_ENV) !== 'uat') {
    return null
  }

  return (
    <>
      <Script
        strategy="afterInteractive"
        src={`https://www.googletagmanager.com/gtag/js?id=${GTM_ID}`}
      />
      <Script
        id="gtag-init"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${GTM_ID}');
          `,
        }}
      />
    </>
  )
}