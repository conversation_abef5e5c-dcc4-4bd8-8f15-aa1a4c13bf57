import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";
//import eslintPluginPrettierRecommended from "eslint-plugin-prettier/recommended";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),
  //eslintPluginPrettierRecommended,
  // Override specific rules
  {
    files: ["**/*.ts", "**/*.tsx"],
    rules: {
      "@typescript-eslint/no-unused-vars": 0, // Keep unused vars as warning
      "@typescript-eslint/no-explicit-any": 0, // Set explicit any to warning
      "react/no-unescaped-entities": 0, // Allow unescaped entities in JSX
      "react/no-children-prop": 0, // Allow unescaped entities in JSX
      "prefer-const": 0, // Allow unescaped entities in JSX
      /*'prettier/prettier': [
        'error',
        {
          'endOfLine': 'auto',
        }
      ]
        */
    },
  },
];

export default eslintConfig;
