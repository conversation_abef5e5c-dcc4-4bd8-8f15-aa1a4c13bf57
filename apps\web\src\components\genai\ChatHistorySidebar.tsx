'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import PromptGalleryModal from './modals/PromptGalleryModal';
import Box from '@mui/material/Box';
import Hkbu<PERSON>ogo from '@/assets/icons/HkbuLogo';
import Typography from '@mui/material/Typography';
import List from '@mui/material/List';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import IconButton from '@mui/material/IconButton';
import CircularProgress from '@mui/material/CircularProgress';
import SettingsIcon from '@mui/icons-material/Settings';
import LibraryBooksIcon from '@mui/icons-material/LibraryBooks'; // New import for Prompt Gallery icon
import ChatBubbleOutlineIcon from '@mui/icons-material/ChatBubbleOutline';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import ApiIcon from '@mui/icons-material/Api';
import BuildIcon from '@mui/icons-material/Build';
import DescriptionIcon from '@mui/icons-material/Description';
import PrivacyTipIcon from '@mui/icons-material/PrivacyTip';
import FeedbackIcon from '@mui/icons-material/Feedback';
import PaletteIcon from '@mui/icons-material/Palette';
import LogoutIcon from '@mui/icons-material/Logout';
import AccountCircleIcon from '@mui/icons-material/AccountCircle'; // New import for profile icon
import Menu from '@mui/material/Menu'; // New import for Menu component
import MenuItem from '@mui/material/MenuItem'; // New import for MenuItem component
import EditNoteIcon from '@mui/icons-material/EditNote'; // New import for New Chat pen-on-paper icon
import {
  useGetConversationHistoryQuery,
  ConversationHistoryItem,
  useSubmitFeedbackMutation,
} from '@/lib/store/apiSlice';
import { useDispatch, useSelector } from 'react-redux';
import { useSession, signOut } from 'next-auth/react';
import { selectIsAuthenticated } from '@/lib/store/authSlice';
import {
  messageCleared,
  setSelectedNextModelName,
} from '@/lib/store/chatSlice';
import { setSelectedPrompt } from '@/lib/store/promptSlice';
import { openTncModal, setTncModalShowAgree } from '@/lib/store/uiSlice';
import ExpandIcon from '@/assets/icons/ExpandIcon';
import { Collapse, useMediaQuery, useTheme } from '@mui/material';
import HistoryIcon from '@mui/icons-material/History';
import { modelInfo } from './model/ModelInfo';
import { useChatLayout } from '@/contexts/ChatLayoutContext';
import AppearanceModal from './modals/AppearanceModal';
import SignOutConfirmationModal from './modals/SignOutConfirmationModal';
import PrivacyPolicyModal from './modals/PrivacyPolicyModal';
import FeedbackContactUsModal from './modals/FeedbackContactUsModal';
import { event } from '@/components/genai/GoogleAnalytics';

export const SIDEBAR_WIDTH_EXPANDED = 308;
export const SIDEBAR_WIDTH_COLLAPSED = 86;

const ModelIcon = ({ modelName }: { modelName?: string | null }) => {
  return (
    <Box
      sx={{
        width: 29,
        height: 29,
        alignItems: 'center',
        justifyContent: 'center',
        display: 'flex',
      }}
    >
      {modelName ? modelInfo[modelName]?.svg : <ChatBubbleOutlineIcon />}
    </Box>
  );
};

export default function ChatHistorySidebar({ isBlocked = false }: { isBlocked?: boolean }) {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));
  const router = useRouter();
  const dispatch = useDispatch();
  const [isPromptGalleryOpen, setIsPromptGalleryOpen] = useState(false);
  const [isAppearanceModalOpen, setIsAppearanceModalOpen] = useState(false);
  const [isSignOutConfirmModalOpen, setIsSignOutConfirmModalOpen] =
    useState(false);
  const [isPrivacyPolicyModalOpen, setIsPrivacyPolicyModalOpen] =
    useState(false); // New state for Privacy Policy modal
  const [isFeedbackContactUsModalOpen, setIsFeedbackContactUsModalOpen] =
    useState(false);
  const [isSigningOut, setIsSigningOut] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null); // State for menu anchor
  const isMenuOpen = Boolean(anchorEl); // State to check if menu is open
  const { isSidebarExpanded, toggleSidebar, closeSidebarOnMobile } =
    useChatLayout();

  // Debug logging for sidebar component
  useEffect(() => {
    console.log('[ChatHistorySidebar] Component state:', {
      isMobile,
      isTablet,
      isSidebarExpanded,
      isBlocked,
      timestamp: new Date().toISOString()
    });
  }, [isMobile, isTablet, isSidebarExpanded, isBlocked]);
  const params = useParams();
  const chatId = params?.chatId;
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const { data: session } = useSession();
  const accessToken = session?.accessToken;

  const [submitFeedback, { isLoading: isSubmittingFeedback }] =
    useSubmitFeedbackMutation();

  const {
    data: historyPageData,
    isLoading,
    isError,
  } = useGetConversationHistoryQuery(
    { page: 1, limit: 10 },
    { skip: !isAuthenticated || !accessToken },
  );

  const recentHistoryItems = historyPageData?.items;

  const groupHistoryByDate = (items: ConversationHistoryItem[] | undefined) => {
    if (!items) return {};
    return items.reduce(
      (acc, item) => {
        const date = new Date(item.updated_at);
        const formattedDate = `${String(date.getMonth() + 1).padStart(2, '0')}/${date.getFullYear()}`;
        if (!acc[formattedDate]) {
          acc[formattedDate] = [];
        }
        acc[formattedDate].push(item);
        return acc;
      },
      {} as Record<string, ConversationHistoryItem[]>,
    );
  };

  const groupedRecentHistory = groupHistoryByDate(recentHistoryItems);
  const sortedDateGroups = Object.keys(groupedRecentHistory).sort((a, b) => {
    const dateA = new Date(a.split('/').reverse().join('-'));
    const dateB = new Date(b.split('/').reverse().join('-'));
    return dateB.getTime() - dateA.getTime();
  });

  const [collapsedGroups, setCollapsedGroups] = useState<{
    [key: string]: boolean;
  }>({});

  const handleToggleGroup = (group: string) => {
    setCollapsedGroups((prev) => ({
      ...prev,
      [group]: !prev[group],
    }));
    event({
      action: 'click',
      category: 'sidebar',
      label: 'toggle_history_group',
      value: 1,
    });
  };

  const handleNewChat = () => {
    closeSidebarOnMobile();
    router.push('/');
    event({
      action: 'click',
      category: 'sidebar',
      label: 'new_chat',
      value: 1,
    });
  };

  const handleOpenPromptGallery = () => {
    setIsPromptGalleryOpen(true);
    closeSidebarOnMobile();
    event({
      action: 'click',
      category: 'sidebar',
      label: 'open_prompt_gallery',
      value: 1,
    });
  };

  const handleClosePromptGallery = () => {
    setIsPromptGalleryOpen(false);
  };

  const handlePromptSelect = (prompt: string) => {
    if (chatId || window.location.pathname.includes('/chat/new')) {
      dispatch(setSelectedPrompt(prompt));
    } else {
      dispatch(messageCleared());
      dispatch(setSelectedPrompt(prompt));
      dispatch(setSelectedNextModelName('gpt-4-turbo-2024-04-09'));
      router.push('/');
    }
    setIsPromptGalleryOpen(false);
  };

  const handleMenuOpen = (mouseEvent: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(mouseEvent.currentTarget);
    event({
      action: 'click',
      category: 'sidebar',
      label: 'open_settings_menu',
      value: 1,
    });
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleMenuItemClick = (action: () => void) => {
    handleMenuClose();
    closeSidebarOnMobile();
    action();
    event({
      action: 'click',
      category: 'sidebar',
      label: 'click_settings_menu_item',
      value: 1,
    });
  };

  const handleSignOut = async () => {
    setIsSigningOut(true);
    const basePath = process.env.NEXT_PUBLIC_BASE_PATH || '';
    try {
      await signOut({ callbackUrl: `${window.location.origin}${basePath}/` });
    } catch (error) {
      console.error('Sign out error:', error);
      setIsSigningOut(false);
    }
  };

  const handleFeedbackSubmit = async (content: string) => {
    try {
      await submitFeedback({ content }).unwrap();
      // Optionally, show a success message to the user
      alert('Feedback submitted successfully!');
      setIsFeedbackContactUsModalOpen(false);
    } catch (error) {
      // Optionally, show an error message to the user
      alert('Failed to submit feedback. Please try again.');
      console.error('Failed to submit feedback:', error);
    }
  };

  const basePath = process.env.NEXT_PUBLIC_BASE_PATH || '';

  return (
    <>
      <PromptGalleryModal
        open={isPromptGalleryOpen}
        handleClose={handleClosePromptGallery}
        handlePromptSelect={handlePromptSelect}
        isExistingChat={!!chatId}
      />
      <FeedbackContactUsModal
        open={isFeedbackContactUsModalOpen}
        handleClose={() => setIsFeedbackContactUsModalOpen(false)}
        handleSubmitContact={handleFeedbackSubmit}
        isLoadingContact={isSubmittingFeedback}
      />
      <AppearanceModal
        open={isAppearanceModalOpen}
        handleClose={() => setIsAppearanceModalOpen(false)}
      />
      <SignOutConfirmationModal
        open={isSignOutConfirmModalOpen}
        handleClose={() => setIsSignOutConfirmModalOpen(false)}
        handleConfirm={handleSignOut}
        isSigningOut={isSigningOut}
      />
      <PrivacyPolicyModal
        open={isPrivacyPolicyModalOpen}
        handleClose={() => setIsPrivacyPolicyModalOpen(false)}
      />
      <Box
        sx={{
          width: {
            xs: isSidebarExpanded ? SIDEBAR_WIDTH_EXPANDED : 0,
            md: isSidebarExpanded
              ? SIDEBAR_WIDTH_EXPANDED
              : Math.max(SIDEBAR_WIDTH_COLLAPSED, 50), // Ensure minimum width on desktop
          },
          minWidth: {
            xs: 0,
            md: isSidebarExpanded ? SIDEBAR_WIDTH_EXPANDED : 50, // Minimum width safeguard
          },
          maxWidth: '100%',
          transition: 'width 0.2s ease-out',
          height: '100dvh',
          position: 'fixed',
          top: 0,
          left: 0,
          zIndex: 1300,
          display: 'flex',
          flexDirection: 'column',
          bgcolor: {
            xs: 'background.paper', // Set background to solid for mobile
            md: 'background.paper',
          },
          borderRight: '1px solid',
          borderColor: 'divider',
          overflow: 'hidden',
          backdropFilter: {
            xs: 'none', // Remove blur for mobile
            md: 'none',
          },
          pointerEvents: isBlocked ? 'none' : 'auto',
          opacity: isBlocked ? 0.5 : 1,
          // Ensure sidebar is always visible on desktop unless explicitly hidden
          visibility: {
            xs: isSidebarExpanded ? 'visible' : 'hidden',
            md: 'visible', // Always visible on desktop
          },
        }}
      >
        <Box
          display={'flex'}
          position={'relative'}
          flexDirection={isSidebarExpanded ? 'row' : 'column'}
          sx={{ mb: isSidebarExpanded ? 0 : '34px' }}
        >
          <Link href="/" passHref legacyBehavior>
            <Box
              onClick={closeSidebarOnMobile}
              sx={{
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                height: '48px',
                margin: '20px 22px',
              }}
            >
              <Box
                sx={{
                  height: '48px',
                  width: '42px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <HkbuLogo height={40} />
              </Box>
              <Collapse
                in={isSidebarExpanded}
                orientation="horizontal"
                easing={'ease-out'}
                timeout={200}
              >
                <Box
                  display={'flex'}
                  flexDirection={'row'}
                  alignItems={'center'}
                >
                  <Typography
                    sx={{
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      color: 'primary.main',
                      fontWeight: 700,
                      fontSize: '18px',
                      lineHeight: '40px',
                      p: '0 6px',
                    }}
                  >
                    HKBU GenAI Platform
                  </Typography>
                </Box>
              </Collapse>
            </Box>
          </Link>

          <Box
            sx={{
              position: 'absolute',
              width: isSidebarExpanded ? '36px' : '61px',
              borderRadius: isSidebarExpanded ? '100%' : '8px',
              height: '36px',
              transform: {
                xs: 'none',
                md: isSidebarExpanded
                  ? 'rotate(0.5turn) translateY(-50%)'
                  : 'translateX(50%)',
              },
              right: {
                xs: 5,
                md: isSidebarExpanded ? 5 : '50%',
              },
              bottom: {
                xs: '50%',
                md: isSidebarExpanded ? '50%' : -30,
              },
              '&:hover': {
                bgcolor: 'action.hover',
              },
              justifyContent: 'center',
              alignItems: 'center',
              transition: 'all 0.2s ease',
              display: { xs: 'none', md: 'flex' },
              color: 'text.primary',
            }}
            onClick={toggleSidebar}
          >
            <ExpandIcon />
          </Box>
        </Box>

        {/* New Chat Button */}
        <Box sx={{ px: 2, mb: 1.5 }}>
          <Box
            onClick={handleNewChat}
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: isSidebarExpanded ? 'flex-start' : 'center',
              py: 1.25,
              px: isSidebarExpanded ? 2 : 1,
              borderRadius: 2,
              cursor: 'pointer',
              bgcolor: 'primary.main',
              color: 'primary.contrastText',
              fontWeight: 500,
              fontSize: '14px',
              boxShadow: theme.shadows[2],
              transition: 'all 0.2s ease-in-out',
              '&:hover': {
                bgcolor: 'primary.dark',
                boxShadow: theme.shadows[4],
                transform: 'translateY(-1px)',
              },
              '&:active': {
                transform: 'translateY(0)',
                boxShadow: theme.shadows[2],
              },
              '&:focus-visible': {
                outline: '2px solid',
                outlineColor: 'primary.light',
                outlineOffset: '2px',
              },
            }}
            role="button"
            tabIndex={0}
            aria-label="Start a new chat conversation"
          >
            <EditNoteIcon 
              sx={{ 
                fontSize: isSidebarExpanded ? 18 : 20,
                mr: isSidebarExpanded ? 1.5 : 0,
              }} 
            />
            {isSidebarExpanded && (
              <Typography
                component="span"
                sx={{
                  fontWeight: 'inherit',
                  fontSize: 'inherit',
                  whiteSpace: 'nowrap',
                }}
              >
                New Chat
              </Typography>
            )}
          </Box>
        </Box>

        {/* Prompt Gallery Button */}
        <Box sx={{ px: 2, mb: 1.5 }}>
          <Box
            onClick={handleOpenPromptGallery}
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: isSidebarExpanded ? 'flex-start' : 'center',
              py: 1.25,
              px: isSidebarExpanded ? 2 : 1,
              borderRadius: 2,
              cursor: 'pointer',
              bgcolor: 'secondary.main',
              color: 'secondary.contrastText',
              fontWeight: 500,
              fontSize: '14px',
              boxShadow: theme.shadows[2],
              transition: 'all 0.2s ease-in-out',
              '&:hover': {
                bgcolor: 'secondary.dark',
                boxShadow: theme.shadows[4],
                transform: 'translateY(-1px)',
              },
              '&:active': {
                transform: 'translateY(0)',
                boxShadow: theme.shadows[2],
              },
              '&:focus-visible': {
                outline: '2px solid',
                outlineColor: 'secondary.light',
                outlineOffset: '2px',
              },
            }}
            role="button"
            tabIndex={0}
            aria-label="Open prompt gallery"
          >
            <LibraryBooksIcon
              sx={{
                fontSize: isSidebarExpanded ? 18 : 20,
                mr: isSidebarExpanded ? 1.5 : 0,
              }}
            />
            {isSidebarExpanded && (
              <Typography
                component="span"
                sx={{
                  fontWeight: 'inherit',
                  fontSize: 'inherit',
                  whiteSpace: 'nowrap',
                }}
              >
                Prompt Gallery
              </Typography>
            )}
          </Box>
        </Box>

        {/* History Section Divider */}
        <Box sx={{ px: 2, mt: 1, mb: 1.5 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            {isSidebarExpanded && (
              <Box
                sx={{
                  height: '1px',
                  flex: 1,
                  bgcolor: 'divider',
                }}
              />
            )}
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                py: 0.75,
                px: isSidebarExpanded ? 1.5 : 1,
                borderRadius: 1.5,
                cursor: isSidebarExpanded ? 'default' : 'pointer',
                transition: 'all 0.2s ease',
                '&:hover': isSidebarExpanded ? {} : {
                  bgcolor: 'action.hover',
                },
                pointerEvents: isSidebarExpanded ? 'none' : 'auto',
              }}
              onClick={isSidebarExpanded ? undefined : toggleSidebar}
            >
              <HistoryIcon
                sx={{
                  fontSize: isSidebarExpanded ? 16 : 20,
                  color: 'text.secondary',
                  mr: isSidebarExpanded ? 1 : 0,
                }}
              />
              {isSidebarExpanded && (
                <Typography
                  sx={{
                    fontWeight: 600,
                    fontSize: '11px',
                    color: 'text.secondary',
                    textTransform: 'uppercase',
                    letterSpacing: '0.8px',
                    whiteSpace: 'nowrap',
                  }}
                >
                  History
                </Typography>
              )}
            </Box>
            {isSidebarExpanded && (
              <Box
                sx={{
                  height: '1px',
                  flex: 1,
                  bgcolor: 'divider',
                }}
              />
            )}
          </Box>
        </Box>

        {isSidebarExpanded && isAuthenticated && accessToken && (
          <Box
            sx={{
              flexGrow: 1,
              overflowY: 'auto',
              overflowX: 'hidden',
              px: 2,
              pb: 1,
            }}
          >
            {isLoading && (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 2 }}>
                <CircularProgress size={24} />
              </Box>
            )}
            {isError && (
              <Typography
                variant="body2"
                sx={{ color: 'text.secondary', textAlign: 'center', p: 1 }}
              >
                Failed to load history
              </Typography>
            )}
            {!isLoading &&
              !isError &&
              (!recentHistoryItems || recentHistoryItems.length === 0) && (
                <Typography
                  variant="body2"
                  sx={{ color: 'text.secondary', textAlign: 'center', p: 1 }}
                >
                  No recent history
                </Typography>
              )}
            {sortedDateGroups.map((dateGroup) => (
              <Box key={dateGroup} sx={{ mb: 1 }}>
                {' '}
                {/* Reduced margin bottom */}
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    cursor: 'pointer',
                    py: 0.125, // Even further reduced vertical padding
                    px: 1,
                    borderRadius: 20,
                    '&:hover': { bgcolor: 'action.hover' },
                  }}
                  onClick={() => handleToggleGroup(dateGroup)}
                >
                  <Typography
                    variant="caption"
                    sx={{
                      color: 'text.secondary',
                      fontWeight: 500,
                      fontSize: '11px', // Even further reduced font size
                      flexGrow: 1,
                    }}
                  >
                    {dateGroup}
                  </Typography>
                  <ExpandMoreIcon
                    sx={{
                      color: 'text.secondary',
                      transition: 'transform 0.2s',
                      transform: collapsedGroups[dateGroup]
                        ? 'rotate(-90deg)'
                        : 'rotate(0deg)',
                    }}
                    fontSize="small"
                  />
                </Box>
                {!collapsedGroups[dateGroup] && (
                  <List
                    dense
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      py: 0,
                      borderLeft: '1px solid',
                      borderColor: 'divider',
                      rowGap: '1px', // Even further reduced row gap
                    }}
                  >
                    {groupedRecentHistory[dateGroup].map(
                      (item: ConversationHistoryItem) => {
                        const isSelected = chatId === item.id;
                        return (
                          <Link
                            href={`/chat/${item.id}`}
                            key={item.id}
                            passHref
                            legacyBehavior
                          >
                            <ListItemButton
                              selected={isSelected}
                              onClick={() => {
                                closeSidebarOnMobile();
                                event({
                                  action: 'click',
                                  category: 'sidebar',
                                  label: 'view_conversation_from_history',
                                  value: 1,
                                });
                              }}
                              sx={{
                                py: 0, // Set vertical padding to 0
                                px: 1.5, // Adjusted horizontal padding
                                borderRadius: 2,
                                '&:hover': {
                                  bgcolor: 'action.hover',
                                  borderRadius: 2,
                                },
                                '&.Mui-selected': {
                                  bgcolor: 'action.selected',
                                  borderRadius: 2,
                                },
                                height: 36, // Aligned with other items
                                minHeight: 36, // Ensure minimum height is also set
                              }}
                            >
                              <ListItemText
                                primary={item.title || 'Untitled Chat'}
                                primaryTypographyProps={{
                                  sx: {
                                    color: 'text.primary',
                                    fontSize: '12px', // Further reduced font size
                                    whiteSpace: 'nowrap',
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                  },
                                }}
                              />
                            </ListItemButton>
                          </Link>
                        );
                      },
                    )}
                  </List>
                )}
              </Box>
            ))}
            {recentHistoryItems && recentHistoryItems.length > 0 && (
              <Link href={`/chat/history`} passHref legacyBehavior>
                <ListItemButton
                  onClick={() => {
                    closeSidebarOnMobile();
                    event({
                      action: 'click',
                      category: 'sidebar',
                      label: 'view_all_history',
                      value: 1,
                    });
                  }}
                  sx={{
                    bgcolor: 'action.selected',
                    justifyContent: 'center',
                    height: 28, // Reduced height
                    minHeight: 28, // Ensure minimum height is also set
                    color: 'text.primary',
                    borderRadius: 20,
                    '&:hover': { bgcolor: 'action.hover' },
                    border: '1px solid',
                    borderColor: 'divider',
                  }}
                >
                  <ListItemText
                    primary="View all"
                    primaryTypographyProps={{
                      sx: { fontSize: '12px', textAlign: 'center' }, // Further reduced font size
                    }}
                  />
                </ListItemButton>
              </Link>
            )}
          </Box>
        )}

        {/* Profile/Settings Menu */}
        <Box
          sx={{
            mt: 'auto', // Push to bottom
            mb: { xs: 1, md: '36px' },
            mx: 1.5,
            py: 0,
          }}
        >
          <ListItemButton
            onClick={(event) => handleMenuOpen(event)}
            sx={{
              py: 0,
              px: 1.5,
              borderRadius: 2,
              '&:hover': { bgcolor: 'action.hover', borderRadius: 2 },
              height: 36,
              minHeight: 36,
            }}
          >
            <ListItemIcon
              sx={{
                minWidth: isSidebarExpanded ? 'unset' : '100%',
                mr: isSidebarExpanded ? 1.5 : 0,
                justifyContent: 'center',
              }}
            >
              <AccountCircleIcon
                sx={{
                  width: isSidebarExpanded ? 16 : 24,
                  height: isSidebarExpanded ? 16 : 24,
                  color: 'text.primary',
                }}
              />
            </ListItemIcon>
            <Collapse
              in={isSidebarExpanded}
              orientation="horizontal"
              easing={'ease-out'}
              timeout={200}
            >
              <ListItemText
                primary="Profile & Settings"
                primaryTypographyProps={{
                  sx: {
                    color: 'text.primary',
                    fontSize: '12px',
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                  },
                }}
              />
            </Collapse>
          </ListItemButton>
          <Menu
            anchorEl={anchorEl}
            open={isMenuOpen}
            onClose={handleMenuClose}
            MenuListProps={{
              'aria-labelledby': 'basic-button',
            }}
          >
            {session?.user?.email && (
              <MenuItem disabled sx={{ opacity: '1 !important' }}>
                <ListItemText
                  primary="Signed in as"
                  secondary={session.user.email}
                  primaryTypographyProps={{
                    fontSize: '12px',
                    color: 'text.secondary',
                  }}
                  secondaryTypographyProps={{
                    fontSize: '14px',
                    color: 'text.primary',
                    fontWeight: 500,
                  }}
                />
              </MenuItem>
            )}
            <MenuItem
              onClick={() =>
                handleMenuItemClick(
                  () => (window.location.href = `${basePath}/settings/faq`),
                )
              }
            >
              <ListItemIcon>
                <HelpOutlineIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>FAQ</ListItemText>
            </MenuItem>
            <MenuItem
              onClick={() =>
                handleMenuItemClick(
                  () =>
                    (window.location.href = `${basePath}/settings/api-docs`),
                )
              }
            >
              <ListItemIcon>
                <ApiIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>API</ListItemText>
            </MenuItem>
            <MenuItem
              onClick={() =>
                handleMenuItemClick(
                  () =>
                    (window.location.href = `${basePath}/settings/prompt-engineering`),
                )
              }
            >
              <ListItemIcon>
                <BuildIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Prompt Engineering</ListItemText>
            </MenuItem>
            <MenuItem
              onClick={() =>
                handleMenuItemClick(() => {
                  dispatch(setTncModalShowAgree(false));
                  dispatch(openTncModal());
                })
              }
            >
              <ListItemIcon>
                <DescriptionIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Terms and Conditions</ListItemText>
            </MenuItem>
            <MenuItem
              onClick={() =>
                handleMenuItemClick(() => setIsPrivacyPolicyModalOpen(true))
              }
            >
              <ListItemIcon>
                <PrivacyTipIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Privacy Policy</ListItemText>
            </MenuItem>
            <MenuItem
              onClick={() =>
                handleMenuItemClick(() => setIsFeedbackContactUsModalOpen(true))
              }
            >
              <ListItemIcon>
                <FeedbackIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Feedback and Contact Us</ListItemText>
            </MenuItem>
            <MenuItem
              onClick={() =>
                handleMenuItemClick(() => setIsAppearanceModalOpen(true))
              }
            >
              <ListItemIcon>
                <PaletteIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Appearance</ListItemText>
            </MenuItem>
            <MenuItem
              onClick={() =>
                handleMenuItemClick(() => setIsSignOutConfirmModalOpen(true))
              }
              disabled={isSigningOut || status === 'unauthenticated'}
            >
              <ListItemIcon>
                <LogoutIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>
                {isSigningOut ? 'Signing Out...' : 'Sign Out'}
              </ListItemText>
            </MenuItem>
            <Box
              sx={{
                px: 2,
                py: 1,
                mt: 1,
                borderTop: '1px solid',
                borderColor: 'divider',
              }}
            >
              <Typography
                variant="caption"
                color="text.secondary"
                sx={{ maxWidth: '200px', display: 'block' }}
              >
                Copyright 2025. Office of Information Technology. All Rights
                Reserved.
              </Typography>
            </Box>
          </Menu>
        </Box>
      </Box>
    </>
  );
}
