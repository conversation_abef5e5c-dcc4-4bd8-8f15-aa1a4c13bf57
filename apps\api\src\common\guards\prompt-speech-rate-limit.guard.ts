import {
  Injectable,
  CanActivate,
  ExecutionContext,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { InjectRedis } from '@nestjs-modules/ioredis';
import { Redis } from 'ioredis';
import { AuthenticatedUser } from '../../auth/user.interface';

@Injectable()
export class PromptSpeechRateLimitGuard implements CanActivate {
  private readonly logger = new Logger(PromptSpeechRateLimitGuard.name);
  private readonly RATE_LIMIT = 60; // requests per minute
  private readonly TTL_SECONDS = 60; // 1 minute

  constructor(@InjectRedis() private readonly redis: Redis) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user: AuthenticatedUser | undefined = request.user;

    // Determine if this request belongs to prompt-rewrite or speech scope
    const scope = this.extractScope(request);
    if (!scope) {
      // Not a target endpoint – let it pass
      return true;
    }

    const identifier = user?.userId ?? this.getClientIp(request);
    if (!identifier) {
      // Fallback: allow if we cannot determine identifier
      return true;
    }

    const key = `rate_limit:${scope}:${identifier}`;

    try {
      const currentCountStr = await this.redis.get(key);
      const count = currentCountStr ? parseInt(currentCountStr, 10) : 0;
      const ttl = await this.redis.ttl(key);

      // Check limit before incrementing
      if (count >= this.RATE_LIMIT) {
        const resetAt = new Date(
          Date.now() + (ttl > 0 ? ttl * 1000 : this.TTL_SECONDS * 1000),
        );
        this.throwRateLimitExceeded(scope, identifier, ttl, resetAt);
      }

      const newCount = await this.redis.incr(key);
      if (newCount === 1) {
        await this.redis.expire(key, this.TTL_SECONDS);
      }

      // Set rate-limit headers on response
      const response = context.switchToHttp().getResponse();
      const updatedTtl = await this.redis.ttl(key);
      const setHeader = (name: string, value: string | number) => {
        if (typeof response.header === 'function') {
          response.header(name, value.toString());
        } else if (typeof response.setHeader === 'function') {
          response.setHeader(name, value.toString());
        }
      };
      setHeader('X-RateLimit-Limit', this.RATE_LIMIT);
      setHeader(
        'X-RateLimit-Remaining',
        Math.max(0, this.RATE_LIMIT - newCount),
      );
      setHeader(
        'X-RateLimit-Reset',
        new Date(
          Date.now() +
            (updatedTtl > 0 ? updatedTtl * 1000 : this.TTL_SECONDS * 1000),
        ).toISOString(),
      );

      return true;
    } catch (err) {
      if (err instanceof HttpException) throw err;
      const message = err instanceof Error ? err.message : 'Unknown error';
      this.logger.error(`Redis error in rate limiting: ${message}`);
      return true; // fail-open strategy
    }
  }

  private extractScope(request: any): 'prompt_rewrite' | 'speech' | null {
    const url: string = request.originalUrl || request.url || '';
    if (url.includes('/prompt-rewrite')) return 'prompt_rewrite';
    if (url.includes('/speech')) return 'speech';
    return null;
  }

  private getClientIp(request: any): string | null {
    return (
      request.ip ||
      request.headers?.['x-forwarded-for']?.split(',').shift() ||
      request.connection?.remoteAddress ||
      null
    );
  }

  private throwRateLimitExceeded(
    scope: string,
    identifier: string,
    ttl: number,
    resetAt: Date,
  ): never {
    this.logger.warn(
      `Rate limit exceeded for identifier ${identifier} on scope ${scope}.`,
    );
    throw new HttpException(
      {
        statusCode: HttpStatus.TOO_MANY_REQUESTS,
        message: `Rate limit exceeded for ${scope}`,
        error: 'Too Many Requests',
        retryAfter: ttl > 0 ? ttl : this.TTL_SECONDS,
        limit: this.RATE_LIMIT,
        remaining: 0,
        reset: resetAt.toISOString(),
      },
      HttpStatus.TOO_MANY_REQUESTS,
    );
  }
}
