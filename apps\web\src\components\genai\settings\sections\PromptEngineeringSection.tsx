'use client';

import React, { useState, Fragment } from 'react';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import Link from '@mui/material/Link'; // Using MUI Link

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`setting-tabpanel-${index}`}
      aria-labelledby={`setting-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: { xs: 1, sm: 2 }, typography: 'body2' }}>{children}</Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `setting-tab-${index}`,
    'aria-controls': `setting-tabpanel-${index}`,
  };
}

// Simplified static display for conversation examples
const StaticConversationDisplay: React.FC<{
  conversation: Array<{ role: string; content: string }>;
}> = ({ conversation }) => (
  <Box
    sx={{
      border: '1px dashed grey',
      p: 1,
      my: 1,
      borderRadius: 1,
      fontSize: '0.8rem',
    }}
  >
    {conversation.map((msg, idx) => (
      <Box key={idx} sx={{ mb: 0.5 }}>
        <Typography
          variant="caption"
          sx={{
            fontWeight: 'bold',
            color: msg.role === 'user' ? 'primary.main' : 'secondary.main',
          }}
        >
          {msg.role === 'user' ? 'User:' : 'Assistant:'}
        </Typography>
        <Typography
          variant="body2"
          component="pre"
          sx={{ whiteSpace: 'pre-wrap', fontFamily: 'monospace', ml: 1 }}
        >
          {msg.content}
        </Typography>
      </Box>
    ))}
  </Box>
);

// Simplified static display for prompt examples
const StaticPromptDisplay: React.FC<{
  text: string;
  promptExample?: string;
}> = ({ text, promptExample }) => (
  <Box
    sx={{
      border: '1px solid lightgrey',
      p: 2,
      my: 1,
      borderRadius: 1,
      backgroundColor: 'grey.50',
    }}
  >
    <Typography
      variant="body2"
      dangerouslySetInnerHTML={{ __html: text.replace(/<br>/g, '<br />') }}
      sx={{ mb: promptExample ? 1 : 0 }}
    />
    {promptExample && (
      <Box mt={1} p={1} sx={{ backgroundColor: 'grey.200', borderRadius: 1 }}>
        <Typography variant="caption" sx={{ fontWeight: 'bold' }}>
          Example Prompt to Try:
        </Typography>
        <Typography
          variant="body2"
          component="pre"
          sx={{
            whiteSpace: 'pre-wrap',
            fontFamily: 'monospace',
            fontSize: '0.75rem',
          }}
        >
          {promptExample}
        </Typography>
      </Box>
    )}
  </Box>
);

const PromptEngineeringSection: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const titleStyle = { fontWeight: 'bold', display: 'inline' };
  const hyperLinkStyle = { color: 'primary.main' }; // Adapt to MUI theme

  // Placeholder for isStudent, default to false (staff view) for content selection
  // This logic might need to be adapted if user type is available via session in this component
  const isStudent = false;

  return (
    <Box sx={{ width: '100%' }}>
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          aria-label="Prompt Engineering and Temperature Tabs"
        >
          <Tab
            label="Prompt Engineering"
            {...a11yProps(0)}
            sx={{ textTransform: 'none', fontSize: '0.9rem' }}
          />
          <Tab
            label="Temperature"
            {...a11yProps(1)}
            sx={{ textTransform: 'none', fontSize: '0.9rem' }}
          />
        </Tabs>
      </Box>
      <TabPanel value={tabValue} index={0}>
        <div>
          <Typography
            variant="subtitle1"
            component="p"
            sx={{ ...titleStyle, textDecoration: 'underline' }}
            gutterBottom
          >
            Introduction
          </Typography>
          <Typography paragraph>
            The concept of prompt engineering might sound complicated when you
            first hear about it. However, at its core, it's about crafting
            questions or statements that guide AI in providing the answers or
            outcomes you're seeking.
          </Typography>
        </div>

        <Box mt={2}>
          <Typography
            variant="subtitle1"
            component="p"
            sx={{ ...titleStyle, textDecoration: 'underline' }}
            gutterBottom
          >
            Here introduces some common prompting techniques:
          </Typography>
          <Typography paragraph>
            The following techniques are applicable to any language models.
          </Typography>
          <ol className="pl-3 list-decimal">
            {isStudent ? (
              <Box mb={2}>
                <li>
                  <Typography sx={titleStyle} variant="body2">
                    Draft an email
                  </Typography>
                </li>
                <Typography variant="body2" paragraph>
                  Application: Create a professional email template for
                  professors
                </Typography>
                <StaticPromptDisplay
                  text="I’m a student in your [Course Name] class. Please help me craft a professional email to Professor [Professor's Name] to ask for an extension on the upcoming [Assignment Name] due on [Date]. Explain that I am struggling with [Reason for needing an extension] and would appreciate the opportunity to submit the assignment by [Proposed New Due Date]."
                  promptExample="I’m a student in your [Course Name] class. Please help me craft a professional email to Professor [Professor's Name] to ask for an extension on the upcoming [Assignment Name] due on [Date]. Explain that I am struggling with [Reason for needing an extension] and would appreciate the opportunity to submit the assignment by [Proposed New Due Date]."
                />
              </Box>
            ) : (
              <Box mb={2}>
                <li>
                  <Typography sx={titleStyle} variant="body2">
                    Draft an email
                  </Typography>
                </li>
                <Typography variant="body2" paragraph>
                  Application: Informing students about an event cancellation
                </Typography>
                <StaticPromptDisplay
                  text="Generate an email to inform students in [Course Name] that the scheduled [Event Name] on [Date] has been cancelled due to [Reason for cancellation]. Provide alternative options for students to [Alternative action/information]."
                  promptExample="Generate an email to inform students in [Course Name] that the scheduled [Event Name] on [Date] has been cancelled due to [Reason for cancellation]. Provide alternative options for students to [Alternative action/information]."
                />
              </Box>
            )}
            <Box mb={2}>
              <li>
                <Typography sx={titleStyle} variant="body2">
                  Zero-Shot Chain of Thought (Detailed in{' '}
                  <Link
                    sx={hyperLinkStyle}
                    href="https://arxiv.org/pdf/2211.01910.pdf"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    this research
                  </Link>
                  )
                </Typography>
              </li>
              <Typography variant="body2" paragraph>
                Application: Enhances AI's reasoning ability
              </Typography>
              <StaticPromptDisplay
                text='Including the phrase "Let’s think step by step" in prompts for reasoning tasks can significantly improve performance'
                promptExample="Let’s think step by step, [Your Question]"
              />
              <Accordion
                sx={{
                  boxShadow: 'none',
                  '&:before': { display: 'none' },
                  border: '1px solid rgba(0,0,0,.125)',
                  mt: 1,
                }}
              >
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography variant="caption">
                    Use Case: Simple Math Calculation
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <StaticConversationDisplay
                    conversation={[
                      {
                        role: 'user',
                        content:
                          'What is the sum of 5,3,17,15,13,51 then multiply the result with 60? Just output the answer directly',
                      },
                      { role: 'assistant', content: '2280' },
                      { role: 'user', content: 'Think it again step by step' },
                      {
                        role: 'assistant',
                        content:
                          'I apologize for the error in my first response. Sum of the numbers is 104. Multiply the sum by 60 = 6240',
                      },
                    ]}
                  />
                </AccordionDetails>
              </Accordion>
            </Box>
            <Box mb={2}>
              <li>
                <Typography sx={titleStyle} variant="body2">
                  In-Context Learning (Detailed in{' '}
                  <Link
                    sx={hyperLinkStyle}
                    href="https://arxiv.org/pdf/2005.14165.pdf"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    this research
                  </Link>
                  )
                </Typography>
              </li>
              <Typography variant="body2" paragraph>
                Overview: Guides AI with example inputs and expected outputs
                <br />
                Application: When information cannot be found in AI’s knowledge
                base
              </Typography>
              <StaticPromptDisplay
                text="Framework:<br>[Introduction] A brief explanation of the task<br>[Examples] A few examples showing the input followed by the expected output<br>[Instructions] Clear instructions on what the model is expected to do"
                promptExample="Task: [Your Task Description]\nExamples:\n[A Few Input and Output Examples]\nInstructions:\n[A Clear Instruction, e.g. Label this sentence]"
              />
              <Accordion
                sx={{
                  boxShadow: 'none',
                  '&:before': { display: 'none' },
                  border: '1px solid rgba(0,0,0,.125)',
                  mt: 1,
                }}
              >
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography variant="caption">
                    Use Case: Sentiment Analysis
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <StaticConversationDisplay
                    conversation={[
                      {
                        role: 'user',
                        content:
                          "Task: Classify the sentiment of the text (Positive / Negative)\n\nExamples\nExample 1:\nText: I had an amazing day at the park with my friends.\nSentiment: Positive\n\nExample 2:\nText: The weather today is gloomy and it's making me feel sad.\nSentiment: Negative\n\nExpected output: You shall only return the result (Positive/Negative)\n\nText: The movie was a breathtaking rollercoaster of emotions, beautifully crafted.\nSentiment: ?",
                      },
                      { role: 'assistant', content: 'Positive' },
                    ]}
                  />
                </AccordionDetails>
              </Accordion>
            </Box>
            <Box mb={2}>
              <li>
                <Typography sx={titleStyle} variant="body2">
                  Role Assignment Using the RTF Framework (Learn more{' '}
                  <Link
                    sx={hyperLinkStyle}
                    href="https://www.thepromptwarrior.com/p/5-prompt-frameworks-level-prompts"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    here
                  </Link>
                  )
                </Typography>
              </li>
              <Typography variant="body2" paragraph>
                Overview: Assign specific roles to the language model to tailor
                its responses
              </Typography>
              <StaticPromptDisplay
                text="RTF Framework:<br> Act like a [the Role you want AI to take]. Give me a [Task] in [Format] format."
                promptExample="Act like a [the Role you want AI to take]. Give me a [Task] in [Format] format."
              />
              <Accordion
                sx={{
                  boxShadow: 'none',
                  '&:before': { display: 'none' },
                  border: '1px solid rgba(0,0,0,.125)',
                  mt: 1,
                }}
              >
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography variant="caption">
                    Use Case 1: Study Buddy
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Typography variant="body2" paragraph>
                    To inquire about a specific topic and test your
                    understanding, use the following prompt
                  </Typography>
                  <StaticPromptDisplay
                    text="You are an expert in [topic]. (Role) Teach me the [Any theorem/topic/rule name] and include a test in MC format (Task & Format) at the end, and let me know if my answers are correct after I respond, without providing the answers beforehand"
                    promptExample="You are an expert in [topic]. Teach me the [Any theorem/topic/rule name] and include a test in [MC format] at the end, and let me know if my answers are correct after I respond, without providing the answers beforehand"
                  />
                </AccordionDetails>
              </Accordion>
              <Accordion
                sx={{
                  boxShadow: 'none',
                  '&:before': { display: 'none' },
                  border: '1px solid rgba(0,0,0,.125)',
                  mt: 1,
                }}
              >
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography variant="caption">
                    Use Case 2: Text Summarization
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <StaticPromptDisplay
                    text="You are an expert in [topic]. (Role) I've uploaded a series of articles and papers [list of file names]. Can you summarize the key findings in point form (Task & Format) and how they relate to my research topic [specify topic]? [Upload Document]"
                    promptExample="You are an expert in [topic]. I've uploaded a series of articles and papers [list of file names]. Can you summarize the key findings in [point form] and how they relate to my research topic [specify topic]? [**Upload Your Document**]"
                  />
                </AccordionDetails>
              </Accordion>
            </Box>
            <Box mb={2}>
              <li>
                <Typography sx={titleStyle} variant="body2">
                  LEVER prompt (Learn more{' '}
                  <Link
                    sx={hyperLinkStyle}
                    href="https://www.thepromptwarrior.com/p/5-prompt-frameworks-level-prompts"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    here
                  </Link>
                  )
                </Typography>
              </li>
              <Typography variant="body2" paragraph>
                Overview: A way that allows you to adjust AI outputs in a more
                fine-tuned way
              </Typography>
              <Typography variant="body2">Framework:</Typography>
              <ol className="pl-8 list-decimal">
                <li>
                  <Typography variant="body2">
                    You ask the AI to rate its output on a scale of 1-10. You
                    also define the scale, e.g. 1 is very casual, 10 is very
                    professional
                  </Typography>
                </li>
                <li>
                  <Typography variant="body2">
                    The AI will give you the answer (e.g. 6/10)
                  </Typography>
                </li>
                <li>
                  <Typography variant="body2">
                    You then adjust it to your desired number (e.g. Turn it to a
                    8/10)
                  </Typography>
                </li>
              </ol>
              <Accordion
                sx={{
                  boxShadow: 'none',
                  '&:before': { display: 'none' },
                  border: '1px solid rgba(0,0,0,.125)',
                  mt: 1,
                }}
              >
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography variant="caption">
                    Use Case: Adjust the professionality of your email
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <StaticPromptDisplay
                    text="On a scale of 1-10, If 1 is a very casual sounding email a 10 is a very professional sound email. How would you rate this email? [Email content]"
                    promptExample="On a scale of 1-10, If 1 is a very casual sounding email a 10 is a very professional sound email. How would you rate this email?\n### Email content###\n[Your Email Content]"
                  />
                  <StaticConversationDisplay
                    conversation={[
                      {
                        role: 'user',
                        content:
                          'On a scale of 1-10, If 1 is a very casual sounding email a 10 is a very professional sound email. How would you rate this email? \n### Email content###\n hey bro, I wanna take a leave tmr. bye. Ben',
                      },
                      {
                        role: 'assistant',
                        content: 'I would rate that email as a 1',
                      },
                      { role: 'user', content: 'Turn it into 3' },
                      {
                        role: 'assistant',
                        content:
                          'Hey,\n I hope this message finds you well. I would like to kindly request a leave for tomorrow. Thank you.\n Best regards,\n Ben',
                      },
                    ]}
                  />
                </AccordionDetails>
              </Accordion>
            </Box>
          </ol>
        </Box>
      </TabPanel>
      <TabPanel value={tabValue} index={1}>
        <div>
          <Typography
            variant="subtitle1"
            component="p"
            sx={{ ...titleStyle, textDecoration: 'underline' }}
            gutterBottom
          >
            Introduction
          </Typography>
          <Typography paragraph>
            Temperature (0-1) affects response creativity or randomness: higher
            values increase diversity, while lower values makes the output more
            deterministic. You can learn more{' '}
            <Link
              sx={hyperLinkStyle}
              href="https://community.openai.com/t/cheat-sheet-mastering-temperature-and-top-p-in-chatgpt-api/172683"
              target="_blank"
              rel="noopener noreferrer"
            >
              here
            </Link>
            .
          </Typography>

          <Box my={2}>
            <Typography
              variant="body1"
              component="p"
              sx={titleStyle}
              gutterBottom
            >
              Example Temperature Settings for Various Tasks
            </Typography>
            <Box sx={{ overflowX: 'auto' }}>
              <table className="table-auto w-full border-collapse border border-gray-400 text-xs">
                <thead>
                  <tr>
                    <th className="border border-gray-300 p-2">Task</th>
                    <th className="border border-gray-300 p-2">
                      Temperature Setting
                    </th>
                    <th className="border border-gray-300 p-2">Rationale</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td className="border border-gray-300 p-2">
                      Generating essay outlines
                    </td>
                    <td className="border border-gray-300 p-2">
                      Low (e.g., 0.3)
                    </td>
                    <td className="border border-gray-300 p-2">
                      A low temperature can help produce more structured and
                      coherent outlines, focusing on commonly accepted formats
                      and logical flow without too much deviation.
                    </td>
                  </tr>
                  <tr>
                    <td className="border border-gray-300 p-2">
                      Brainstorming session for projects
                    </td>
                    <td className="border border-gray-300 p-2">
                      Medium (e.g., 0.5-0.6)
                    </td>
                    <td className="border border-gray-300 p-2">
                      A medium temperature strikes a balance between creativity
                      and relevance, generating a variety of ideas that are both
                      innovative and applicable to the project at hand.
                    </td>
                  </tr>
                  <tr>
                    <td className="border border-gray-300 p-2">
                      Creating discussion questions for literature classes
                    </td>
                    <td className="border border-gray-300 p-2">
                      Medium to High (e.g., 0.6-0.8)
                    </td>
                    <td className="border border-gray-300 p-2">
                      Encouraging diverse perspectives and interpretations in
                      literature discussions benefits from a bit more creativity
                      and openness in the questions generated.
                    </td>
                  </tr>
                </tbody>
              </table>
            </Box>
          </Box>
        </div>
      </TabPanel>
    </Box>
  );
};

export default PromptEngineeringSection;
