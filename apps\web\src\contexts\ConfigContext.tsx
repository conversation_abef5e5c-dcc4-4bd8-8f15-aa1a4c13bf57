'use client';

import React, { createContext, useContext } from 'react';
import { useConfig, FrontendConfig } from '@/lib/config';

interface ConfigContextType {
  config: FrontendConfig | null;
  error: Error | null;
}

const ConfigContext = createContext<ConfigContextType | undefined>(undefined);

export function ConfigProvider({ children }: { children: React.ReactNode }) {
  const { config, error } = useConfig();

  return (
    <ConfigContext.Provider value={{ config, error }}>
      {children}
    </ConfigContext.Provider>
  );
}

export function useConfigContext() {
  const context = useContext(ConfigContext);
  if (context === undefined) {
    throw new Error('useConfigContext must be used within a ConfigProvider');
  }
  return context;
}