# Multi-LLM Capability Implementation Plan

This plan outlines the frontend changes required to allow users to select different Large Language Models (LLMs) within a single conversation via a dropdown/search list and @mentions.

**Approved:** 2025-04-30

**Goals:**

1.  Modify the chat input area to include model selection UI (Dropdown/Search + @mention).
2.  Update frontend state management to track the model selected for the *next* message.
3.  Modify the message sending logic to include the selected model in the API request.
4.  Ensure the input area is disabled during AI response generation.

**Diagrams:**

```mermaid
sequenceDiagram
    participant User
    participant ChatInputUI
    participant FrontendState (Redux)
    participant ApiSlice (RTK Query)
    participant BackendAPI

    User->>ChatInputUI: Types "@gpt"
    ChatInputUI->>FrontendState: Fetch available models (if needed)
    FrontendState-->>ChatInputUI: Provides model list
    ChatInputUI->>ChatInputUI: Shows @mention suggestions (e.g., gpt-4, gemini-pro)
    User->>ChatInputUI: Selects "gpt-4" from suggestions
    ChatInputUI->>FrontendState: Updates selectedNextModel state ("gpt-4")
    User->>ChatInputUI: Enters prompt text
    User->>ChatInputUI: Clicks Send Button
    ChatInputUI->>FrontendState: Reads selectedNextModel ("gpt-4") and prompt
    ChatInputUI->>ApiSlice: Triggers chatCompletion mutation (model="gpt-4", prompt="...")
    Note over ChatInputUI, FrontendState: Disable Input Area (isThinking=true)
    ApiSlice->>BackendAPI: POST /general/chat/completions (model="gpt-4", ...)
    BackendAPI-->>ApiSlice: SSE Stream Start
    ApiSlice->>FrontendState: Updates message content, thinking state
    BackendAPI-->>ApiSlice: SSE Stream End
    ApiSlice->>FrontendState: Updates final message, isThinking=false
    Note over ChatInputUI, FrontendState: Enable Input Area (isThinking=false)
    FrontendState->>ChatInputUI: Renders updated messages
```

```mermaid
graph TD
    subgraph Frontend Changes
        A[Chat Input UI] --> B(Model Selection Component);
        A --> C(@Mention Handler);
        A --> D(Disable Logic);
        B --> E{Redux State};
        C --> E;
        D --> E;
        A --> F(Send Message Logic);
        F --> E;
        F --> G[apiSlice];
        E --> A;
        E --> G;
    end

    subgraph Backend (No Changes Needed)
        H[general.controller.ts];
        I[chat-completion.service.ts];
        J[Database];
    end

    G --> H;
    H --> I;
    I --> J;
```

**Detailed Steps:**

1.  **State Management (Redux - `chatSlice.ts` or new slice):**
    *   Introduce a new state variable, e.g., `selectedNextModelName: string | null`, initialized to `null`.
    *   Add a new reducer action, e.g., `setSelectedNextModelName(modelName: string | null)`, to update this state.

2.  **UI - Input Area (`ChatMessageInput.tsx` or similar):**
    *   **Dropdown/Search:**
        *   Integrate a dropdown or searchable input component (e.g., using Material UI's `Autocomplete`) near the main text input.
        *   Populate this component with the `availableModels` from the `modelSlice`.
        *   On selection, dispatch `setSelectedNextModelName` with the chosen `model_name`.
        *   Display the currently selected model (or a default indicator) in the component.
    *   **@Mention:**
        *   Integrate a library or custom logic to detect `@` triggers within the text input.
        *   When `@` is typed, fetch `availableModels` and display a suggestion list filtered based on subsequent typing.
        *   On selection from the suggestion list, insert the full model name (or a display name) into the input and dispatch `setSelectedNextModelName` with the chosen `model_name`. Remove the `@mention` trigger text.
    *   **Disabling Input:**
        *   Use the existing `isThinking` state from `chatSlice`.
        *   Wrap the text input, model selection dropdown/search, @mention trigger, and send button in a container or use individual `disabled` props bound to `isThinking`.

3.  **Logic - Sending Message (`apiSlice.ts` - `chatCompletion` mutation):**
    *   Modify the `queryFn` within the `chatCompletion` mutation.
    *   Before making the `fetch` call, retrieve `selectedNextModelName` from the state.
    *   Determine the `model` to send in the request body:
        *   If `selectedNextModelName` is set, use it.
        *   If `selectedNextModelName` is `null`, use the default model associated with the current conversation (ensure this default model name is accessible, perhaps stored alongside `conversationId` in `chatSlice`).
    *   Pass this determined `model` name in the `requestBody`.
    *   **Crucially:** After sending the message (i.e., after the `fetch` call is initiated but before the response stream starts), dispatch `setSelectedNextModelName(null)` to reset the selection for the *next* user message.
    *   Update the optimistic `addUserMessage` dispatch to use the determined `model` name for `model_display_name`.

4.  **History Display (`ChatMessage.tsx` or similar):**
    *   Verify that the component rendering individual messages uses the `message.model_display_name` property (fetched via `getHistoryMessages`) to show the appropriate icon or name. (User confirmed this is likely already working).

---

**Summary of Changes:**

*   **New State:** `selectedNextModelName` in Redux.
*   **New Reducer:** `setSelectedNextModelName`.
*   **UI Components:** Add Dropdown/Search for models, implement @mention suggestion logic.
*   **UI Logic:** Disable input area based on `isThinking`.
*   **API Logic:** Modify `chatCompletion` to read `selectedNextModelName`, determine the model for the request, pass it in the API call, and reset `selectedNextModelName`. Update optimistic message with the correct model name.