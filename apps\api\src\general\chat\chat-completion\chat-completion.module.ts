import { Module, forwardRef } from '@nestjs/common'; // Import forwardRef
import { ConfigModule } from '@nestjs/config'; // Import ConfigModule if not globally available
import { LlmModule } from '../../../llm/llm.module'; // <-- Corrected relative path again
import { ChatCompletionService } from './chat-completion.service';
import { ChatCompletionsController } from './chat-completion.controller'; // Corrected import name
import { PrismaModule } from '../../../prisma/prisma.module'; // <-- Correct relative path for local PrismaModule
import { UtilsModule } from '../../../utils/utils.module'; // Import UtilsModule instead of GeneralModule
import { GeneralModule } from '../../general.module'; // <-- Import GeneralModule
import { AuthModule } from '../../../auth/auth.module'; // Import AuthModule for ApiKeyAuthGuard dependencies

@Module({
  imports: [
    ConfigModule, // Make sure ConfigService is available
    AuthModule, // Import AuthModule for ApiKeyAuthGuard dependencies
    LlmModule,
    PrismaModule, // <-- ADD PrismaModule here
    UtilsModule, // Import UtilsModule to access OcrService
    forwardRef(() => GeneralModule), // <-- Use forwardRef for GeneralModule
  ],
  controllers: [ChatCompletionsController], // Corrected controller name
  providers: [
    ChatCompletionService,
    // PrismaService is typically provided by PrismaModule, so it's removed from providers unless explicitly needed.
    // If PrismaService is NOT exported by PrismaModule, add it back here:
    // PrismaService,
  ],
  exports: [ChatCompletionService], // Export service if it needs to be used by other modules
})
export class ChatCompletionModule {}
