#!/usr/bin/env python3
"""
Comprehensive test script to test all models via the HKBU GenAI Platform Chat Completion API.
Tests all models defined in models.json and provides a summary report.
"""

import json
import os
import sys
import time
import argparse
import concurrent.futures
from datetime import datetime
from typing import Dict, Any, List, Optional
import requests
from dotenv import load_dotenv


def load_config() -> Dict[str, str]:
    """Load configuration from .env file."""
    load_dotenv()
    
    api_key = os.getenv("API_KEY")
    base_url = os.getenv("BASE_URL")
    
    if not api_key:
        raise ValueError("API_KEY not found in .env file.")
    if not base_url:
        raise ValueError("BASE_URL not found in .env file.")
    
    return {
        "api_key": api_key,
        "base_url": base_url
    }


def load_models_config() -> Dict[str, Any]:
    """Load models configuration from models.json."""
    config_path = os.path.join(os.path.dirname(__file__), "../../config/models.json")
    if not os.path.exists(config_path):
        raise ValueError("models.json not found. Please create it first.")
    
    with open(config_path, 'r') as f:
        return json.load(f)


def test_single_model(config: Dict[str, str], model_info: Dict[str, Any], 
                     prompt: str, default_params: Dict[str, Any]) -> Dict[str, Any]:
    """Test a single model and return results."""
    
    model_name = model_info["name"]
    deployment_name = model_info["deployment_name"]
    timeout = model_info.get("timeout", 30)
    
    # Construct the URL using model name instead of deployment name
    # This is a temporary fix for models where deployment_name != model_name
    url = f"{config['base_url']}/rest/deployments/{model_name}/chat/completions"
    
    # Query parameters - use model-specific api_version if available
    api_version = model_info.get("api_version", default_params.get("api_version", "2024-02-01"))
    params = {
        "api-version": api_version
    }
    
    # Headers
    headers = {
        "Content-Type": "application/json",
        "api-key": config["api_key"]
    }
    
    # Build messages based on model support for system messages
    supports_system_messages = model_info.get("supports_system_messages", True)
    messages = []
    if supports_system_messages:
        messages.append({
            "role": "system",
            "content": "You are a helpful assistant."
        })
    messages.append({
        "role": "user",
        "content": prompt
    })
    
    # Build payload based on model support for temperature
    supports_temperature = model_info.get("supports_temperature", True)
    
    # Request payload
    payload = {
        "messages": messages,
        "stream": default_params.get("stream", False)
    }
    
    # Only add temperature if model supports it
    if supports_temperature:
        payload["temperature"] = default_params.get("temperature", 0.7)
    
    start_time = time.time()
    
    try:
        # Make the request
        response = requests.post(
            url,
            params=params,
            headers=headers,
            json=payload,
            timeout=timeout
        )
        
        elapsed_time = time.time() - start_time
        
        if response.status_code == 200:
            response_data = response.json()
            
            # Extract assistant's response
            assistant_message = None
            if "choices" in response_data and len(response_data["choices"]) > 0:
                assistant_message = response_data["choices"][0]["message"]["content"]
            
            # Extract usage info
            usage = response_data.get("usage", {})
            
            # Check for conversation_uuid (should not be present)
            has_conversation_uuid = "conversation_uuid" in response_data
            
            return {
                "success": True,
                "model": model_name,
                "description": model_info.get("description", ""),
                "status_code": response.status_code,
                "response_time": elapsed_time,
                "prompt_tokens": usage.get('prompt_tokens', 0),
                "completion_tokens": usage.get('completion_tokens', 0),
                "total_tokens": usage.get('total_tokens', 0),
                "response": assistant_message,
                "has_conversation_uuid": has_conversation_uuid,
                "error": None
            }
            
        else:
            return {
                "success": False,
                "model": model_name,
                "description": model_info.get("description", ""),
                "status_code": response.status_code,
                "response_time": elapsed_time,
                "prompt_tokens": 0,
                "completion_tokens": 0,
                "total_tokens": 0,
                "response": None,
                "has_conversation_uuid": False,
                "error": f"HTTP {response.status_code}: {response.text[:200]}"
            }
            
    except requests.exceptions.Timeout:
        return {
            "success": False,
            "model": model_name,
            "description": model_info.get("description", ""),
            "status_code": 0,
            "response_time": timeout,
            "prompt_tokens": 0,
            "completion_tokens": 0,
            "total_tokens": 0,
            "response": None,
            "has_conversation_uuid": False,
            "error": f"Timeout after {timeout}s"
        }
        
    except Exception as e:
        return {
            "success": False,
            "model": model_name,
            "description": model_info.get("description", ""),
            "status_code": 0,
            "response_time": time.time() - start_time,
            "prompt_tokens": 0,
            "completion_tokens": 0,
            "total_tokens": 0,
            "response": None,
            "has_conversation_uuid": False,
            "error": str(e)
        }


def test_all_models_sequential(config: Dict[str, str], models: List[Dict[str, Any]], 
                              prompt: str, default_params: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Test all models sequentially."""
    results = []
    total_models = len(models)
    
    for idx, model_info in enumerate(models, 1):
        print(f"\n[{idx}/{total_models}] Testing {model_info['name']}...")
        result = test_single_model(config, model_info, prompt, default_params)
        results.append(result)
        
        # Brief status
        if result["success"]:
            print(f"  ✅ Success - {result['response_time']:.2f}s - {result['total_tokens']} tokens")
        else:
            print(f"  ❌ Failed - {result['error']}")
    
    return results


def test_all_models_parallel(config: Dict[str, str], models: List[Dict[str, Any]], 
                            prompt: str, default_params: Dict[str, Any], 
                            max_workers: int = 5) -> List[Dict[str, Any]]:
    """Test all models in parallel."""
    results = []
    
    print(f"\n🚀 Testing {len(models)} models in parallel (max {max_workers} workers)...")
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks
        future_to_model = {
            executor.submit(test_single_model, config, model_info, prompt, default_params): model_info
            for model_info in models
        }
        
        # Process completed tasks
        for future in concurrent.futures.as_completed(future_to_model):
            model_info = future_to_model[future]
            try:
                result = future.result()
                results.append(result)
                
                # Brief status
                if result["success"]:
                    print(f"  ✅ {result['model']:20} - {result['response_time']:.2f}s")
                else:
                    print(f"  ❌ {result['model']:20} - {result['error']}")
                    
            except Exception as e:
                print(f"  ❌ {model_info['name']:20} - Exception: {e}")
                results.append({
                    "success": False,
                    "model": model_info["name"],
                    "description": model_info.get("description", ""),
                    "status_code": 0,
                    "response_time": 0,
                    "prompt_tokens": 0,
                    "completion_tokens": 0,
                    "total_tokens": 0,
                    "response": None,
                    "has_conversation_uuid": False,
                    "error": str(e)
                })
    
    return results


def print_summary(results: List[Dict[str, Any]], start_time: float):
    """Print a comprehensive summary of all test results."""
    
    total_time = time.time() - start_time
    successful = [r for r in results if r["success"]]
    failed = [r for r in results if not r["success"]]
    
    print("\n" + "=" * 80)
    print("📊 TEST SUMMARY")
    print("=" * 80)
    
    print(f"\n⏱️  Total Execution Time: {total_time:.2f}s")
    print(f"📈 Success Rate: {len(successful)}/{len(results)} ({len(successful)/len(results)*100:.1f}%)")
    
    # Successful models
    if successful:
        print(f"\n✅ SUCCESSFUL MODELS ({len(successful)}):")
        print("-" * 80)
        print(f"{'Model':25} {'Response Time':15} {'Tokens':10} {'Status'}")
        print("-" * 80)
        
        for result in sorted(successful, key=lambda x: x['response_time']):
            status = "⚠️ Has UUID" if result['has_conversation_uuid'] else "✓"
            print(f"{result['model']:25} {result['response_time']:>12.2f}s "
                  f"{result['total_tokens']:>10} {status}")
    
    # Failed models
    if failed:
        print(f"\n❌ FAILED MODELS ({len(failed)}):")
        print("-" * 80)
        print(f"{'Model':25} {'Error'}")
        print("-" * 80)
        
        for result in failed:
            error_msg = result['error'][:50] + "..." if len(result['error']) > 50 else result['error']
            print(f"{result['model']:25} {error_msg}")
    
    # Performance statistics
    if successful:
        avg_response_time = sum(r['response_time'] for r in successful) / len(successful)
        min_response_time = min(r['response_time'] for r in successful)
        max_response_time = max(r['response_time'] for r in successful)
        total_tokens = sum(r['total_tokens'] for r in successful)
        
        print("\n📊 PERFORMANCE STATISTICS:")
        print("-" * 80)
        print(f"  Average Response Time: {avg_response_time:.2f}s")
        print(f"  Fastest Response: {min_response_time:.2f}s")
        print(f"  Slowest Response: {max_response_time:.2f}s")
        print(f"  Total Tokens Used: {total_tokens}")
    
    # Warnings
    uuid_warnings = [r for r in successful if r['has_conversation_uuid']]
    if uuid_warnings:
        print("\n⚠️  WARNINGS:")
        print("-" * 80)
        print(f"  {len(uuid_warnings)} model(s) returned conversation_uuid (unexpected for REST API)")
        for result in uuid_warnings:
            print(f"    - {result['model']}")
    
    print("\n" + "=" * 80)


def save_results(results: List[Dict[str, Any]], filename: Optional[str] = None):
    """Save test results to a JSON file."""
    if not filename:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"test_results_{timestamp}.json"
    
    output_data = {
        "test_date": datetime.now().isoformat(),
        "total_models": len(results),
        "successful": len([r for r in results if r["success"]]),
        "failed": len([r for r in results if not r["success"]]),
        "results": results
    }
    
    with open(filename, 'w') as f:
        json.dump(output_data, f, indent=2)
    
    print(f"\n💾 Results saved to: {filename}")


def main():
    """Main function to run all tests."""
    parser = argparse.ArgumentParser(
        description="Test all models via HKBU GenAI Platform API"
    )
    parser.add_argument(
        "-p", "--prompt",
        help="Custom prompt to send to all models",
        default="What is 2+2? Please provide only the numeric answer."
    )
    parser.add_argument(
        "--parallel",
        action="store_true",
        help="Run tests in parallel (faster but may hit rate limits)"
    )
    parser.add_argument(
        "--workers",
        type=int,
        default=5,
        help="Number of parallel workers (default: 5)"
    )
    parser.add_argument(
        "--save",
        help="Save results to JSON file",
        nargs='?',
        const=True,
        default=False
    )
    parser.add_argument(
        "--models",
        help="Comma-separated list of specific models to test",
        default=None
    )
    
    args = parser.parse_args()
    
    try:
        # Load configurations
        config = load_config()
        models_config = load_models_config()
        
        # Get models to test
        all_models = models_config.get("models", [])
        if args.models:
            # Filter to specific models
            requested_models = [m.strip() for m in args.models.split(",")]
            models_to_test = [m for m in all_models if m["name"] in requested_models]
            
            if len(models_to_test) != len(requested_models):
                found_models = [m["name"] for m in models_to_test]
                not_found = [m for m in requested_models if m not in found_models]
                print(f"⚠️  Models not found in config: {', '.join(not_found)}")
        else:
            models_to_test = all_models
        
        if not models_to_test:
            print("❌ No models to test!")
            sys.exit(1)
        
        # Print test configuration
        print("🚀 HKBU GenAI Platform - Comprehensive Model Testing")
        print("=" * 80)
        print(f"📍 API Endpoint: {config['base_url']}")
        print(f"🔑 API Key: {config['api_key'][:8]}...")
        print(f"📝 Test Prompt: {args.prompt}")
        print(f"🎯 Models to Test: {len(models_to_test)}")
        print(f"⚡ Execution Mode: {'Parallel' if args.parallel else 'Sequential'}")
        
        # Start testing
        start_time = time.time()
        default_params = models_config.get("default_parameters", {})
        
        if args.parallel:
            results = test_all_models_parallel(
                config, models_to_test, args.prompt, default_params, args.workers
            )
        else:
            results = test_all_models_sequential(
                config, models_to_test, args.prompt, default_params
            )
        
        # Print summary
        print_summary(results, start_time)
        
        # Save results if requested
        if args.save:
            filename = args.save if isinstance(args.save, str) else None
            save_results(results, filename)
        
        # Exit with appropriate code
        failed_count = len([r for r in results if not r["success"]])
        sys.exit(0 if failed_count == 0 else 1)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()