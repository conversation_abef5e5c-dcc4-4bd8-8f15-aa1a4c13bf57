# Plan: Google Analytics Integration

**Goal:** Integrate Google Analytics into the web application to track user behavior, including page views, button clicks, and scroll behavior. This tracking should only be active in the `uat` and `production` environments.

**Core Problem:** The application currently does not have any analytics tracking.

**Proposed Plan:**

1.  **Create Google Analytics Component:**
    *   Create a new component at `apps/web/src/components/genai/GoogleAnalytics.tsx`.
    *   This component will include the Google Analytics script and initialization logic.
    *   It will also include functions for tracking page views and custom events.
    *   The component will only render the script if the `NODE_ENV` is `uat` or `production`.

2.  **Add Environment Variable:**
    *   Add the Google Analytics Measurement ID to the `.env` file as `NEXT_PUBLIC_GOOGLE_MEASUREMENT_ID`.

3.  **Update Layout:**
    *   Modify `apps/web/src/app/layout.tsx` to include the new `GoogleAnalytics` component.

4.  **Implement Page View Tracking:**
    *   The `GoogleAnalytics` component will use the `usePathname` and `useSearchParams` hooks from `next/navigation` to automatically track page views whenever the URL changes.

5.  **Implement Button Click Tracking:**
    *   Create a generic event tracking function in the `GoogleAnalytics` component.
    *   This function will be used to track button clicks and other custom events.
    *   I will add the event tracking logic to the key buttons in the application, including the chat interface, homepage, chat history page, model list page, sidebar, and all settings pages.

6.  **Implement Scroll Behavior Tracking:**
    *   I will add a `useEffect` hook to the `GoogleAnalytics` component to track scroll depth.
    *   This will fire events at different scroll percentages (e.g., 25%, 50%, 75%, 100%).

**Visual Flow (Page View):**

```mermaid
sequenceDiagram
    participant User
    participant Browser
    participant NextApp as Next.js App
    participant GA as Google Analytics

    User->>Browser: Navigates to a page
    Browser->>+NextApp: Requests page
    NextApp-->>-Browser: Renders page with GoogleAnalytics component
    Browser->>+GA: Loads gtag.js script
    GA-->>-Browser: Script loaded
    Browser->>GA: Sends page view event