import requests
import json
import time
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:3003/api/v0"
API_KEY = "80b60025-07b4-4097-ba1d-e14695359dd0"

# Headers for API requests
headers = {
    'Content-Type': 'application/json',
    'api-key': API_KEY
}

# Test inputs for embeddings
test_texts = [
    "The quick brown fox jumps over the lazy dog.",
    "This is a test of the embedding API to verify token counting.",
    "Token usage tracking should work correctly for embedding models."
]

def check_token_usage(model_name=None):
    """Check current token usage for a specific model or all models"""
    url = f"{BASE_URL}/rest/rate-limit/token-usage"
    response = requests.get(url, headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        usage_data = {}
        
        for usage in data.get('usage', []):
            if model_name is None or usage['modelName'] == model_name:
                usage_data[usage['modelName']] = {
                    'totalTokensUsed': usage['totalTokensUsed'],
                    'promptTokensUsed': usage['promptTokensUsed'],
                    'completionTokensUsed': usage['completionTokensUsed'],
                    'lastUpdated': usage['lastUpdated']
                }
        
        return usage_data
    else:
        print(f"Error checking token usage: {response.status_code} - {response.text}")
        return {}

def test_embedding_model(model_name, deployment_name):
    """Test a single embedding model"""
    print(f"\n{'='*60}")
    print(f"Testing Embedding Model: {model_name}")
    print(f"Deployment: {deployment_name}")
    print('='*60)
    
    # Get initial token usage
    initial_usage = check_token_usage(model_name)
    initial_tokens = initial_usage.get(model_name, {}).get('totalTokensUsed', 0)
    print(f"\nInitial token usage: {initial_tokens:,}")
    
    # Make embedding API call
    url = f"{BASE_URL}/rest/deployments/{deployment_name}/embeddings?api-version=2024-05-01-preview"
    
    payload = {
        "input": test_texts,
        "model": deployment_name
    }
    
    print(f"\nMaking embedding request with {len(test_texts)} text inputs...")
    start_time = time.time()
    
    try:
        response = requests.post(url, headers=headers, json=payload)
        elapsed = time.time() - start_time
        
        if response.status_code == 200:
            result = response.json()
            
            # Extract usage information
            usage = result.get('usage', {})
            prompt_tokens = usage.get('prompt_tokens', 0)
            total_tokens = usage.get('total_tokens', 0)
            
            print(f"Response: SUCCESS in {elapsed:.2f}s")
            print(f"Tokens reported by API: {total_tokens} (prompt: {prompt_tokens})")
            print(f"Embeddings returned: {len(result.get('data', []))}")
            
            # Wait a bit for database update
            print("\nWaiting 2 seconds for database update...")
            time.sleep(2)
            
            # Check updated token usage
            updated_usage = check_token_usage(model_name)
            updated_tokens = updated_usage.get(model_name, {}).get('totalTokensUsed', 0)
            tokens_added = updated_tokens - initial_tokens
            
            print(f"\nUpdated token usage: {updated_tokens:,}")
            print(f"Tokens added: {tokens_added}")
            
            # Verify accuracy
            if tokens_added == total_tokens:
                print(f"[PASS] Token tracking ACCURATE - {tokens_added} tokens recorded")
                return True, total_tokens
            else:
                print(f"[FAIL] Token tracking MISMATCH - Expected {total_tokens}, recorded {tokens_added}")
                return False, total_tokens
                
        else:
            print(f"Error: {response.status_code} - {response.text}")
            return False, 0
            
    except Exception as e:
        print(f"Exception: {str(e)}")
        return False, 0

def main():
    """Test all embedding models"""
    print("Embedding Models Token Usage Test")
    print("="*60)
    print(f"Testing at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Embedding models to test
    embedding_models = [
        ("text-embedding-3-large", "text-embedding-3-large"),
        ("text-embedding-3-small", "text-embedding-3-small")
    ]
    
    results = []
    total_tokens_used = 0
    
    for model_name, deployment_name in embedding_models:
        success, tokens = test_embedding_model(model_name, deployment_name)
        results.append({
            'model': model_name,
            'deployment': deployment_name,
            'success': success,
            'tokens': tokens
        })
        total_tokens_used += tokens
        
        # Small delay between tests
        time.sleep(1)
    
    # Summary
    print("\n" + "="*60)
    print("SUMMARY")
    print("="*60)
    
    successful = sum(1 for r in results if r['success'])
    
    print(f"\nModels tested: {len(results)}")
    print(f"Successful: {successful}/{len(results)}")
    print(f"Total tokens used: {total_tokens_used:,}")
    
    print("\nDetailed Results:")
    for result in results:
        status = "PASS" if result['success'] else "FAIL"
        print(f"  {result['model']}: {status} ({result['tokens']} tokens)")
    
    # Check for duplicate entries
    print("\n" + "="*60)
    print("CHECKING FOR DUPLICATE ENTRIES")
    print("="*60)
    
    all_usage = check_token_usage()
    
    # Look for potential duplicates (models with similar names)
    embedding_related = {k: v for k, v in all_usage.items() if 'embedding' in k.lower()}
    
    if len(embedding_related) > len(embedding_models):
        print("[WARNING] Found more embedding models in usage than tested!")
        print("This might indicate duplicate entries:")
        for model, usage in embedding_related.items():
            print(f"  - {model}: {usage['totalTokensUsed']} tokens")
    else:
        print("[OK] No duplicate entries detected")
    
    return successful == len(results)

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)