import {
  Inject,
  Injectable,
  Logger,
  InternalServerErrorException,
  HttpException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { Readable, PassThrough } from 'stream';
import { LlmStreamOptions } from './llm.service';
import { v4 as uuidv4 } from 'uuid';
import { getHongKongTime } from '../common/utils/timezone.util'; // Added import for uuidv4
// Removed static import: import fetch from 'node-fetch';

// Define Source interface for search results
interface Source {
  title: string;
  link: string;
  snippet: string;
}

@Injectable()
export class AzureAiserviceLlmService {
  // private readonly logger = new Logger(AzureAiserviceLlmService.name); // Test comment
  private readonly logger = new Logger(AzureAiserviceLlmService.name);
  private readonly encryptionKeyName: string;
  private readonly decryptionCertName: string;

  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
  ) {
    const encryptionKeyName = this.configService.get<string>(
      'DB_ENCRYPTION_KEY_NAME',
    );
    const decryptionCertName = this.configService.get<string>(
      'DB_DECRYPTION_CERT_NAME',
    );

    if (!encryptionKeyName || !decryptionCertName) {
      throw new InternalServerErrorException(
        'Database encryption configuration is missing.',
      );
    }

    this.encryptionKeyName = encryptionKeyName;
    this.decryptionCertName = decryptionCertName;
  }

  async createChatCompletionStream(
    options: LlmStreamOptions,
  ): Promise<Readable> {
    const {
      modelConfig,
      messages,
      temperature,
      conversationId,
      dialogId,
      userMessageId,
      user,
      searchSources,
      requestedModelName,
    } = options;
    this.logger.log(`options: ${JSON.stringify(options)}`);
    this.logger.log(
      `Creating Azure AI Service stream for ${modelConfig.deployment_name} by user ${user.userId}`,
    );

    try {
      // Get endpoint and key using dedicated env vars first, then fallback to generic
      const deploymentNameUpper = modelConfig.deployment_name?.toUpperCase();
      let endpoint =
        modelConfig.endpoint_url || // Prefer URL from DB first
        (deploymentNameUpper
          ? this.configService.get<string>(
              `AZURE_AI_SERVICE_${deploymentNameUpper}_ENDPOINT`,
            )
          : undefined) ||
        this.configService.get<string>('AZURE_AI_SERVICE_ENDPOINT');
      const apiKey =
        modelConfig.api_key ||
        (deploymentNameUpper
          ? this.configService.get<string>(`AZURE_AI_SERVICE_KEY`)
          : undefined) ||
        this.configService.get<string>('AZURE_AI_SERVICE_KEY');

      if (!endpoint || !apiKey) {
        this.logger.error(
          `Azure AI Service endpoint or API key not configured for ${modelConfig.deployment_name}`,
        );
        throw new InternalServerErrorException(
          `Configuration missing for Azure AI Service model ${modelConfig.deployment_name}`,
        );
      }

      // Ensure api-version is present
      const apiVersion =
        modelConfig.api_version ||
        this.configService.get<string>(
          `AZURE_AI_SERVICE_${deploymentNameUpper}_API_VERSION`,
        ) ||
        this.configService.get<string>('AZURE_AI_SERVICE_API_VERSION') ||
        '2024-05-01-preview';

      // Ensure endpoint ends with proper API path
      if (!endpoint.endsWith('/chat/completions')) {
        endpoint = endpoint.replace(/\/+$/, '') + '/chat/completions';
      }
      endpoint = `${endpoint}?api-version=${apiVersion}`;

      // Prepare request payload
      const payload = {
        messages: messages,
        temperature: temperature ?? 1.0,
        stream: true,
        // Use dept_unit_code instead of departmentCode
        model: `${modelConfig.deployment_name}-${modelConfig.instanceName?.toLowerCase()}`,
        stream_options: { include_usage: true },
      };

      this.logger.log(`Request payload: ${JSON.stringify(payload)}`);

      // Dynamically import node-fetch
      const { default: fetch } = await import('node-fetch');
      // Make request with proper error handling
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'api-key': apiKey,
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        let errorMessage = `Azure AI Service request failed with status ${response.status}`;
        try {
          const errorData = await response.json();
          errorMessage = errorData.error?.message || errorMessage;
        } catch {
          // If parsing error response fails, use default message
        }
        this.logger.error(errorMessage);
        throw new HttpException(errorMessage, response.status);
      }

      if (!response.body) {
        throw new InternalServerErrorException(
          'No response stream from Azure AI Service',
        );
      }

      // Create PassThrough stream for accumulation and processing
      const passthrough = new PassThrough();
      let accumulatedContent = '';
      let promptTokens = 0;
      let completionTokens = 0;

      // Send sources chunk if searchSources are provided
      if (searchSources && searchSources.length > 0) {
        this.logger.log(
          `[Azure AI Service] Sending ${searchSources.length} pre-computed search sources to client.`,
        );
        try {
          // Validate source data structure before serialization
          const cleanedSources = searchSources
            .map((source) => ({
              title: String(source.title || '').trim(),
              link: String(source.link || '').trim(),
              snippet: String(source.snippet || '').trim(),
            }))
            .filter((source) => source.title && source.link && source.snippet);

          const sourceData = { type: 'sources', sources: cleanedSources };
          const jsonString = JSON.stringify(sourceData);
          passthrough.write(`data: ${jsonString}\n\n`);
          this.logger.debug(
            `[Azure AI Service] Sources chunk sent successfully (${cleanedSources.length} valid sources)`,
          );
        } catch (jsonError) {
          this.logger.error(
            `[Azure AI Service] Failed to serialize sources for streaming: ${jsonError}`,
          );
          this.logger.debug(
            `[Azure AI Service] Problematic sources data:`,
            searchSources,
          );
        }
      }

      // Process the Node.js Readable stream from node-fetch
      const nodeReadableStream = response.body as Readable;

      nodeReadableStream.on('data', (chunkBuffer: Buffer) => {
        try {
          const chunk = chunkBuffer.toString('utf-8');
          const lines = chunk.split('\n').filter((line) => line.trim());

          for (const line of lines) {
            if (!line.startsWith('data:')) continue;
            const data = line.slice(5).trim();

            if (data === '[DONE]') {
              // End of stream handling - moved to 'end' event
              continue;
            }

            try {
              const parsedData = JSON.parse(data);
              if (parsedData.choices?.[0]?.delta?.content) {
                const content = parsedData.choices[0].delta.content;
                accumulatedContent += content;
                passthrough.write(content);
              }

              // Collect token usage if available in any data chunk
              // DeepSeek sends usage in the *last* data chunk after finish_reason with empty choices array
              if (parsedData.usage) {
                const oldPrompt = promptTokens;
                const oldCompletion = completionTokens;

                // Handle both naming conventions
                promptTokens =
                  parsedData.usage.prompt_tokens ||
                  parsedData.usage.promptTokens ||
                  promptTokens;
                completionTokens =
                  parsedData.usage.completion_tokens ||
                  parsedData.usage.completionTokens ||
                  completionTokens;

                // Log when we actually get new token values
                if (
                  promptTokens !== oldPrompt ||
                  completionTokens !== oldCompletion
                ) {
                  this.logger.debug(
                    `[DeepSeek/Azure AI Service] Token usage received - prompt: ${promptTokens} (was ${oldPrompt}), completion: ${completionTokens} (was ${oldCompletion}), total: ${promptTokens + completionTokens}`,
                  );
                }
              }

              // Debug log for chunks with finish_reason or empty choices (DeepSeek pattern)
              if (
                parsedData.choices?.length === 0 ||
                parsedData.choices?.[0]?.finish_reason
              ) {
                this.logger.debug(
                  `[DeepSeek/Azure AI Service] Special chunk - choices.length: ${parsedData.choices?.length}, finish_reason: ${parsedData.choices?.[0]?.finish_reason}, has usage: ${!!parsedData.usage}`,
                );
              }
            } catch (parseError) {
              const errorMessage =
                parseError instanceof Error
                  ? parseError.message
                  : String(parseError);
              this.logger.warn(
                `Failed to parse stream data line: ${errorMessage}. Data: "${data}"`,
              );
              // continue to next line
            }
          }
        } catch (chunkProcessingError) {
          const errorMessage =
            chunkProcessingError instanceof Error
              ? chunkProcessingError.message
              : String(chunkProcessingError);
          this.logger.error(`Error processing data chunk: ${errorMessage}`);
          // passthrough.destroy(chunkProcessingError instanceof Error ? chunkProcessingError : new Error(errorMessage));
        }
      });

      nodeReadableStream.on('end', async () => {
        this.logger.log(
          `Azure AI Service stream ended. Final tokens - prompt: ${promptTokens}, completion: ${completionTokens}, total: ${promptTokens + completionTokens}`,
        );

        // Send token usage data as a special chunk before ending the stream
        if (promptTokens > 0 || completionTokens > 0) {
          const tokenUsageChunk = {
            type: 'token_usage',
            usage: {
              prompt_tokens: promptTokens,
              completion_tokens: completionTokens,
              total_tokens: promptTokens + completionTokens,
            },
          };
          passthrough.write(`data: ${JSON.stringify(tokenUsageChunk)}\n\n`);
          this.logger.debug(
            `[Azure AI Service] Sent token usage chunk to parent stream: ${JSON.stringify(tokenUsageChunk.usage)}`,
          );
        }

        try {
          // dialogId from options is the conversation_uuid (as set in ChatCompletionService)
          // conversationId from options is the integer db conversation_id
          // userMessageId from options is the integer db user_message_id
          const conversationUuidForSP = dialogId; // This is the Conversation UUID string
          const dbConversationIdForLog = conversationId; // Integer ID, potentially for logging or if SP changes

          if (accumulatedContent && conversationUuidForSP && userMessageId) {
            const assistantMessageUuid = uuidv4(); // Generate a new UUID for this assistant's message

            // First, save the message
            await this.saveMessageWithSP(
              conversationUuidForSP, // Pass the string conversation_uuid
              assistantMessageUuid, // Pass the new string message_uuid for the assistant's message
              accumulatedContent,
              temperature,
              requestedModelName ?? modelConfig.deployment_name, // Use requested model name for consistency, fallback to deployment_name
              modelConfig.instanceName, // This is the instance_name for the SP
              'assistant',
              completionTokens,
              user.userId,
              getHongKongTime(), // received_at for the SP
              assistantMessageUuid, // Pass assistantMessageUuid as dialog_id for the SP
            );
            this.logger.log(
              `Assistant message saved. UUID: ${assistantMessageUuid}, ConvUUID: ${conversationUuidForSP}, Length: ${accumulatedContent.length}, Tokens: ${completionTokens}`,
            );

            // Then save sources if available - need to get the message ID first
            // This MUST complete before ending the stream to prevent race conditions
            if (searchSources && searchSources.length > 0) {
              try {
                // Get the message ID for the assistant message we just saved
                const savedMessage = await this.prisma.message.findFirst({
                  where: { message_uuid: assistantMessageUuid },
                  select: { message_id: true },
                });

                if (savedMessage) {
                  await this.saveSources(
                    savedMessage.message_id,
                    searchSources,
                  );
                  this.logger.log(
                    `[Azure AI Service] ✅ Successfully saved ${searchSources.length} sources for message ${savedMessage.message_id} BEFORE ending stream`,
                  );
                } else {
                  this.logger.warn(
                    `[Azure AI Service] Could not find saved message with UUID ${assistantMessageUuid} to save sources`,
                  );
                }
              } catch (sourceError) {
                const errorMessage =
                  sourceError instanceof Error
                    ? sourceError.message
                    : String(sourceError);
                this.logger.error(
                  `[Azure AI Service] Failed to save sources: ${errorMessage}`,
                );
                // Don't fail the entire stream if source saving fails, but log it clearly
              }
            } else {
              this.logger.debug(
                `[Azure AI Service] No sources to save for message ${assistantMessageUuid}`,
              );
            }

            // Update token counts
            if (promptTokens > 0) {
              await this.updateUserMessageTokens(userMessageId, promptTokens);
              this.logger.log(
                `User message tokens updated for userMessageId ${userMessageId}: ${promptTokens}`,
              );
            }
          } else {
            this.logger.warn(
              `Stream ended but no accumulated content or necessary IDs (conversationUuid: ${conversationUuidForSP}, userMessageId: ${userMessageId}) to save message.`,
            );
          }

          // CRITICAL: Only end the passthrough stream after ALL database operations are complete
          // This ensures that sources are saved before the frontend redirects
          this.logger.debug(
            `[Azure AI Service] All database operations completed, ending passthrough stream for ${conversationUuidForSP}`,
          );
          passthrough.end();
        } catch (saveError) {
          const errorMessage =
            saveError instanceof Error ? saveError.message : String(saveError);
          this.logger.error(
            `Error during stream end processing (saveMessage/updateTokens): ${errorMessage}`,
          );
          // End the stream even on error to prevent hanging
          passthrough.end();
        }
      });

      nodeReadableStream.on('error', (streamError: Error) => {
        this.logger.error(
          `Azure AI Service stream error: ${streamError.message}`,
          streamError.stack,
        );
        passthrough.destroy(streamError);
      });

      return passthrough;
    } catch (error) {
      // Check if error is an instance of Error before accessing message and stack
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`Azure AI Service error: ${errorMessage}`, errorStack);
      throw error instanceof HttpException
        ? error
        : new InternalServerErrorException(
            'Azure AI Service processing failed',
          );
    }
  }

  private async saveMessageWithSP(
    conversationUuid: string, // String Conversation UUID
    messageUuid: string, // String Message UUID for this new message
    content: string,
    temperature: number | undefined,
    modelName: string, // Corresponds to @model_name in SP
    instanceName: string | undefined, // Corresponds to @instance_name in SP
    sender: 'user' | 'assistant',
    tokenSpent: number,
    userId: string,
    receivedAt: Date, // Corresponds to @received_at in SP
    dialogIdForSP?: string, // Corresponds to @dialog_id in SP
  ): Promise<void> {
    try {
      this.logger.debug(
        `Saving message to DB: convUUID=${conversationUuid}, msgUUID=${messageUuid}, sender=${sender}, model=${modelName}, instance=${instanceName}, receivedAt=${receivedAt.toISOString()}, dialogIdForSP=${dialogIdForSP}`,
      );
      await this.prisma.$queryRaw`
        EXEC sp_cvst_InsertMessageWithPrompts
          @conversation_uuid = ${conversationUuid},
          @message_uuid = ${messageUuid},
          @last_prompt = ${content},
          @temperature = ${temperature},
          @model_name = ${modelName},
          @instance_name = ${instanceName},
          @sender = ${sender},
          @token_spent = ${tokenSpent},
          @ssoid = ${userId},
          @received_at = ${receivedAt},
          @dialog_id = ${dialogIdForSP ?? null},
          @encryption_key_name = ${this.encryptionKeyName},
          @decryption_cert_name = ${this.decryptionCertName}`;
    } catch (error) {
      // Check if error is an instance of Error before accessing message
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error(`Failed to save message: ${errorMessage}`);
      // Non-critical error, don't throw
    }
  }

  private async updateUserMessageTokens(
    messageId: number,
    tokenCount: number,
  ): Promise<void> {
    try {
      await this.prisma.message.update({
        where: { message_id: messageId },
        data: { token_spent: tokenCount },
      });
    } catch (error) {
      // Check if error is an instance of Error before accessing message
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error(`Failed to update message tokens: ${errorMessage}`);
      // Non-critical error, don't throw
    }
  }

  // Helper to save sources
  private async saveSources(
    messageId: number,
    sources: Source[],
  ): Promise<void> {
    if (!sources || sources.length === 0 || messageId === -1) {
      if (messageId !== -1)
        this.logger.debug(
          `[SaveSources] No sources provided for message ID ${messageId}. Skipping.`,
        );
      return;
    }
    if (!messageId) {
      this.logger.error(
        `[SaveSources] Invalid messageId provided: ${messageId}. Cannot save sources.`,
      );
      return;
    }

    this.logger.log(
      `[SaveSources] Attempting to save ${sources.length} sources for message ID ${messageId}...`,
    );

    try {
      for (const source of sources) {
        if (!source.title || !source.link || !source.snippet) {
          this.logger.warn(
            `[SaveSources] Skipping source with missing data for message ID ${messageId}: ${JSON.stringify(source)}`,
          );
          continue;
        }

        await this.prisma.$executeRaw`
          EXEC sp_cvst_InsertMessageSource
            @message_id = ${messageId},
            @title = ${source.title},
            @link = ${source.link},
            @snippet = ${source.snippet},
            @encryption_key_name = ${this.encryptionKeyName},
            @decryption_cert_name = ${this.decryptionCertName};
        `;
      }
      this.logger.log(
        `[SaveSources] Successfully executed SP for ${sources.length} sources for message ID ${messageId}.`,
      );
    } catch (error: any) {
      this.logger.error(
        `[SaveSources] Failed to save sources via SP for message ID ${messageId}: ${error.message}`,
        error.stack,
      );
    }
  }
}
