{
  "extends": "../../packages/tsconfig/base.json",
  "compilerOptions": {
    "module": "commonjs", // Revert back to commonjs
    "declaration": true,
    "removeComments": true,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "allowSyntheticDefaultImports": true,
    "target": "ES2023",
    "sourceMap": true,
    "outDir": "./dist",
    "baseUrl": "./",
    "incremental": true,
    "skipLibCheck": true,
    "strictNullChecks": true,
    "forceConsistentCasingInFileNames": true,
    "noImplicitAny": false,
    "strictBindCallApply": false,
    "noFallthroughCasesInSwitch": false,
    // Added paths configuration for workspace packages
    "paths": {
      "@hkbu-genai-platform/database": ["../../packages/database/src/index.ts"],
      "@hkbu-genai-platform/database/*": ["../../packages/database/src/*"]
    }
  },
  "watchOptions": { "watchFile": "fixedPollingInterval" },
}
