import { NextAuthOptions, User } from 'next-auth'; // Import User type
import type { OAuthConfig, OAuthUserConfig } from 'next-auth/providers/oauth'; // Import provider types
import { HttpsProxyAgent } from 'https-proxy-agent';
import axios, { AxiosError } from 'axios'; // Import AxiosError for type checking
import fetch from 'node-fetch';
import jwt from 'jsonwebtoken'; // Import jsonwebtoken

const basePath = process.env.NEXT_PUBLIC_BASE_PATH;

// TODO: Implement or import this check properly
async function checkEnableRestfulApi(
  _userId: string | undefined | null,
  _deptCode: string | undefined | null,
): Promise<boolean> {
  console.warn(
    'checkEnableRestfulApi: Using stub implementation, returning true',
  );
  return true;
}

type BUAMToken = {
  scope: string;
  expires_in: number;
  token_type: string;
  access_token: string;
};

type BUAMUserPortfolio = {
  email: string;
  ssoid: string;
  dept_unit_code: string;
  employee_type: 'STUDENT' | 'STAFF' | 'OTHER';
  full_name: string;
};

interface BUAMProfile {
  id: string;
  name: string;
  email: string;
  employee_type: 'STUDENT' | 'STAFF' | 'OTHER';
  dept_unit_code: string;
}

function getProxyAgent() {
  const proxyUrl = process.env.HTTPS_AGENT;
  if (proxyUrl) {
    console.log('Using HTTPS proxy agent:', proxyUrl);
    return new HttpsProxyAgent(proxyUrl);
  }
  return undefined;
}

async function makeTokenRequest(context: any) {
  if (
    !context.provider.clientId ||
    !context.provider.clientSecret ||
    !context.provider.callbackUrl ||
    !context.params?.code ||
    !context.provider.token?.url
  ) {
    console.error('makeTokenRequest: Missing required context properties.');
    throw new Error('Invalid token request context.');
  }
  console.log(
    'makeTokenRequest: Preparing to request token with context:' +
      context.provider.callbackUrl,
  );
  const formData = new URLSearchParams();
  formData.append('grant_type', 'authorization_code');
  formData.append('client_id', context.provider.clientId);
  formData.append('client_secret', context.provider.clientSecret);
  formData.append('redirect_uri', context.provider.callbackUrl);
  formData.append('code', context.params.code);

  const agent = getProxyAgent();

  try {
    const response = await fetch(context.provider.token.url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      agent,
      body: formData.toString(),
    });
    if (!response.ok) {
      throw new Error(`${response.status} ${await response.text()}`);
    }
    const data = (await response.json()) as BUAMToken;

    return {
      access_token: data.access_token,
      token_type: data.token_type || 'Bearer',
      expires_in: data.expires_in,
      expires_at: Math.floor(Date.now() / 1000) + data.expires_in,
    };
  } catch (error: unknown) {
    if (axios.isAxiosError(error)) {
      console.error(
        'BUAM Token Request Failed (Axios):',
        error.response?.status,
        error.response?.data || error.message,
      );
    } else {
      console.error('BUAM Token Request Failed (Unknown):', error);
    }
    throw new Error('BUAM Token Request Failed');
  }
}

async function makeUserInfoRequest(context: any) {
  if (!context.provider.userinfo?.url || !context.tokens?.access_token) {
    console.error('makeUserInfoRequest: Missing required context properties.');
    throw new Error('Invalid userinfo request context.');
  }
  const agent = getProxyAgent();
  try {
    const response = await fetch(context.provider.userinfo.url, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${context.tokens.access_token}`,
        'Content-Type': 'application/json',
      },
      agent,
    });
    if (!response.ok) {
      throw new Error(`${response.status} ${await response.text()}`);
    }
    const data = (await response.json()) as BUAMUserPortfolio;
    return data;
  } catch (error: unknown) {
    if (axios.isAxiosError(error)) {
      console.error(
        'BUAM UserInfo Request Failed (Axios):',
        error.response?.status,
        error.response?.data || error.message,
      );
    } else {
      console.error('BUAM UserInfo Request Failed (Unknown):', error);
    }
    throw new Error('BUAM UserInfo Request Failed');
  }
}

export const authOptions: NextAuthOptions = {
  useSecureCookies: process.env.NEXTAUTH_URL?.startsWith('https'), // Explicitly set based on NEXTAUTH_URL protocol
  secret: process.env.BUAM_NEXTAUTH_JWT_SECRET,
  providers: [
    {
      id: 'buam',
      name: 'BUAM',
      type: 'oauth',
      version: '2.0',
      authorization: process.env.BUAM_OAUTH,
      token: {
        url: process.env.BUAM_OAUTH_TOKEN,
        async request(context) {
          const tokens = await makeTokenRequest(context);
          return { tokens };
        },
      },
      checks: ['pkce', 'state'],
      userinfo: {
        url: process.env.BUAM_OAUTH_USERINFO,
        async request(context) {
          const userinfo = await makeUserInfoRequest(context);
          return {
            id: userinfo.ssoid,
            name: userinfo.full_name,
            email: userinfo.email,
            employee_type: userinfo.employee_type,
            dept_unit_code: userinfo.dept_unit_code,
          };
        },
      },
      clientId: process.env.BUAM_OAUTH_CLIENTID,
      clientSecret: process.env.BUAM_OAUTH_CLIENTSECRET,
      profile(profile: BUAMProfile) {
        return {
          id: profile.id,
          name: profile.name,
          email: profile.email,
          employee_type: profile.employee_type,
          dept_unit_code: profile.dept_unit_code,
        };
      },
    },
  ],
  theme: {
    colorScheme: 'light',
  },
  pages: {
    signIn: `${basePath}/auth/signin`,
    error: `${basePath}/auth/error`,
  },
  callbacks: {
    async signIn({ user, profile }) {
      console.log('signIn callback triggered for user:', user.id);
      const buamProfile = profile as BUAMProfile | undefined;
      if (!buamProfile) {
        console.error('signIn: BUAM profile information is missing.');
        return false;
      }
      const dept_unit_code = buamProfile.dept_unit_code;
      const original_employee_type = buamProfile.employee_type;
      const employee_type =
        original_employee_type === 'OTHER' ? 'STAFF' : original_employee_type;
      try {
        const apiUrl = process.env.NEXT_PUBLIC_API_BASE_URL;
        const checkUserResponse = await fetch(`${apiUrl}/next-auth/check-user`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            userId: user.id,
            employeeType: original_employee_type,
            deptCode: dept_unit_code,
          }),
        });

        if (!checkUserResponse.ok) {
          console.error('signIn: check-user API call failed.');
          return false;
        }

        const { authorized } = (await checkUserResponse.json()) as {
          authorized: boolean;
        };
        if (!authorized) {
          console.log(`signIn: User ${user.id} is not authorized.`);
          return false;
        }

        const upsertUserResponse = await fetch(`${apiUrl}/next-auth/upsert-user`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            userId: user.id,
            deptCode: dept_unit_code,
            employeeType: employee_type,
          }),
        });

        if (!upsertUserResponse.ok) {
          console.error('signIn: upsert-user API call failed.');
          return false;
        }

        console.log(`signIn: User ${user.id} checks passed.`);
        return true;
      } catch (error) {
        console.error('signIn: API call failed:', error);
        return false;
      }
    },
    async redirect({ baseUrl }) {
      return baseUrl + basePath;
    },
    async jwt({ token, account, profile }) {
      const buamProfile = profile as BUAMProfile | undefined;
      if (buamProfile) {
        token.dept_unit_code = buamProfile.dept_unit_code;
        token.type =
          buamProfile.employee_type === 'OTHER'
            ? 'STAFF'
            : buamProfile.employee_type;
        token.rest = await checkEnableRestfulApi(
          token.sub,
          buamProfile.dept_unit_code,
        );
      }
      return token;
    },
    async session({ session, token }) {
      session.user.name = token.sub;
      session.user.dept_unit_code = token.dept_unit_code as string | undefined;
      session.user.type = token.type as 'STUDENT' | 'STAFF' | undefined;
      session.user.rest = token.rest as boolean | undefined;

      // Sign the NextAuth.js JWT payload to get the signed token string
      const nextAuthSecret = process.env.BUAM_NEXTAUTH_JWT_SECRET;
      if (nextAuthSecret) {
        const { iat, exp, jti, ...payloadToSign } = token || {}; // Exclude standard JWT claims
        session.accessToken = jwt.sign(payloadToSign, nextAuthSecret, {
          algorithm: 'HS256',
        });
      } else {
        console.error(
          'BUAM_NEXTAUTH_JWT_SECRET is not defined. Cannot sign JWT for session.accessToken.',
        );
        session.accessToken = undefined; // Ensure it's undefined if secret is missing
      }

      if (!session.user.id && token.sub) {
        session.user.id = token.sub;
      }
      return session;
    },
  },
  jwt: {
    encode: async ({ secret, token }) => {
      // Exclude standard JWT claims that jsonwebtoken will add itself or are not needed.
      const { iat, exp, jti, ...payloadToSign } = token || {};
      return jwt.sign(payloadToSign, secret, { algorithm: 'HS256' });
    },
    decode: async ({ secret, token }) => {
      try {
        if (typeof token !== 'string') {
          console.error('JWT decode error: Token is not a string.');
          return null;
        }
        return jwt.verify(token, secret, {
          algorithms: ['HS256'],
        }) as jwt.JwtPayload;
      } catch (error) {
        console.error('JWT decode error:', error);
        return null;
      }
    },
  },
  session: {
    strategy: 'jwt',
    maxAge:
      parseInt(process.env.BUAM_NEXTAUTH_SESSION_EXPIRY || '7', 10) *
      24 *
      60 *
      60,
  },
  debug: process.env.NODE_ENV === 'development',
};

// Handle potential NODE_TLS_REJECT_UNAUTHORIZED setting
if (process.env.NODE_TLS_REJECT_UNAUTHORIZED === '0') {
  process.env['NODE_TLS_REJECT_UNAUTHORIZED'] = '0';
  console.warn(
    'WARNING: Setting NODE_TLS_REJECT_UNAUTHORIZED=0. SSL certificate validation is disabled!',
  );
}
