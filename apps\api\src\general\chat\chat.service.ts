import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  InternalServerErrorException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../prisma/prisma.service'; // Corrected import path
import { Prisma } from '@hkbu-genai-platform/database/generated/client'; // Adjusted Prisma import path to be more specific
import { ApiProperty, ApiPropertyOptional, ApiResponse } from '@nestjs/swagger'; // Added ApiResponse, ApiPropertyOptional

// Define the structure for a single source
class SourceDto {
  @ApiProperty()
  title!: string;

  @ApiProperty()
  link!: string;

  @ApiProperty()
  snippet!: string;
}

// Define the structure expected from the stored procedure result
// Based on SP: sp_cvst_GetDecryptedMessagesByConversationUUID
class HistoryMessageDto {
  @ApiProperty()
  message_uuid!: string; // Assuming it's a string representation of UNIQUEIDENTIFIER

  @ApiProperty()
  sender!: string;

  @ApiProperty()
  create_dt!: Date;

  @ApiProperty()
  model_name!: string | null;

  @ApiProperty()
  reaction!: string | null;

  @ApiProperty()
  token_spent!: number | null;

  @ApiProperty()
  prompt_order!: number;

  @ApiProperty()
  content!: string;

  // Field directly from SP result
  sources_json?: string | null; // JSON string from the SP

  // Field to be populated after parsing sources_json
  @ApiProperty({
    type: () => [SourceDto],
    nullable: true,
    description: 'Decrypted and parsed sources',
  })
  sources?: SourceDto[] | null;
  
  @ApiPropertyOptional({
    description: 'Whether the user explicitly used @mention to select the model',
  })
  used_mention?: boolean | null;

  // conversation_title!: string; // Removed: Title will be fetched separately
}

// Define the structure for conversation parameters
class ConversationParamsDto {
  @ApiPropertyOptional()
  instructions?: string | null;

  @ApiPropertyOptional()
  pastMessagesCount?: number | null;

  @ApiPropertyOptional()
  maxResponseTokens?: number | null;

  @ApiPropertyOptional()
  temperature?: number | null;

  @ApiPropertyOptional()
  topP?: number | null;
}

// Define the structure for the combined response
class HistoryResponseDto {
  @ApiProperty({ type: [HistoryMessageDto] })
  messages!: HistoryMessageDto[];

  @ApiProperty({ description: 'The title of the conversation', nullable: true })
  title!: string | null;

  @ApiPropertyOptional({
    type: ConversationParamsDto,
    description: 'Saved model parameters for the conversation',
    nullable: true,
  })
  parameters?: ConversationParamsDto | null; // Add parameters field
}

// --- DTO for a single item in the full history list ---
class ConversationHistoryItemDto {
  @ApiProperty()
  id!: string;

  @ApiProperty()
  title!: string;

  @ApiProperty({ nullable: true })
  model!: string | null;

  @ApiProperty()
  updated_at!: Date;
}

// --- DTO for the full history response ---
class AllHistoryResponseDto {
  @ApiProperty({ type: [ConversationHistoryItemDto] })
  items!: ConversationHistoryItemDto[];
}

@Injectable()
export class ChatService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
  ) {}

  async getAllHistory(userId: string): Promise<AllHistoryResponseDto> {
    // 1. Retrieve Key/Cert names from ConfigService
    const encryptionKeyName = this.configService.get<string>(
      'DB_ENCRYPTION_KEY_NAME',
    );
    const decryptionCertName = this.configService.get<string>(
      'DB_DECRYPTION_CERT_NAME',
    );

    if (!encryptionKeyName || !decryptionCertName) {
      throw new InternalServerErrorException(
        'Database encryption configuration is missing.',
      );
    }

    // 2. Fetch all conversations for the user
    const conversations = await this.prisma.conversation.findMany({
      where: { ssoid: userId, delete_dt: null },
      orderBy: { update_dt: 'desc' },
      select: {
        conversation_uuid: true,
        conversation_title: true,
        model_name: true,
        update_dt: true,
      },
    });

    // 3. Decrypt titles in-memory
    const historyItemsPromises = conversations.map(async (conv) => {
      if (!conv.conversation_uuid || !conv.update_dt) {
        return null;
      }
      let decryptedTitle = 'Untitled Chat';
      if (conv.conversation_title) {
        try {
          type DecryptedTitleResult = { decrypted_title: string };
          const titleResult = await this.prisma.$queryRaw<
            DecryptedTitleResult[]
          >`
              OPEN SYMMETRIC KEY ${Prisma.raw(encryptionKeyName)} DECRYPTION BY CERTIFICATE ${Prisma.raw(decryptionCertName)};
              SELECT CONVERT(NVARCHAR(MAX), DecryptByKey(${conv.conversation_title})) as decrypted_title;
              CLOSE SYMMETRIC KEY ${Prisma.raw(encryptionKeyName)};
            `;
          if (titleResult.length > 0 && titleResult[0].decrypted_title) {
            decryptedTitle = titleResult[0].decrypted_title;
          }
        } catch (e) {
          console.error(
            `Failed to decrypt title for conversation ${conv.conversation_uuid}`,
            e,
          );
        }
      }
      return {
        id: conv.conversation_uuid,
        title: decryptedTitle,
        model: conv.model_name,
        updated_at: conv.update_dt,
      };
    });

    const resolvedItems = await Promise.all(historyItemsPromises);
    const historyItems: ConversationHistoryItemDto[] = resolvedItems.filter(
      (item): item is ConversationHistoryItemDto => item !== null,
    );

    return { items: historyItems };
  }

  // Update the return type to the new DTO
  async getHistoryMessages(
    chatSessionId: string,
    userId: string,
  ): Promise<HistoryResponseDto> {
    console.log(
      `[ChatService] 📞 getHistoryMessages called for conversation ${chatSessionId} by user ${userId} at ${new Date().toISOString()}`,
    );

    // 1. Retrieve Key/Cert names from ConfigService
    const encryptionKeyName = this.configService.get<string>(
      'DB_ENCRYPTION_KEY_NAME',
    );
    const decryptionCertName = this.configService.get<string>(
      'DB_DECRYPTION_CERT_NAME',
    );

    if (!encryptionKeyName || !decryptionCertName) {
      throw new InternalServerErrorException(
        'Database encryption configuration is missing.',
      );
    }

    // 2. Optional but Recommended: Authorization Check
    // 2. Fetch conversation details including parameters, encrypted title and check authorization
    const conversation = await this.prisma.conversation.findUnique({
      where: { conversation_uuid: chatSessionId },
      select: {
        ssoid: true,
        conversation_title: true, // Select encrypted title
        // Select parameter fields
        instructions: true,
        pastMessagesCount: true,
        maxResponseTokens: true,
        temperature: true,
        topP: true,
        delete_dt: true,
      },
    });

    if (!conversation || conversation.delete_dt) {
      throw new NotFoundException(
        `Conversation with UUID ${chatSessionId} not found.`,
      );
    }

    if (
      !conversation.ssoid ||
      !userId ||
      conversation.ssoid.toLowerCase() !== userId.toLowerCase()
    ) {
      throw new ForbiddenException(
        'You do not have permission to access this conversation.',
      );
    }

    // 3. Call Stored Procedure using Prisma.$queryRaw
    try {
      // 3. Fetch messages using the SP (which no longer returns title)
      // Define a type for the raw SP result including sources_json
      type HistoryMessageRawDto = HistoryMessageDto & {
        sources_json: string | null;
        is_deleted?: boolean; // Add is_deleted field
        used_mention?: boolean | null; // Add used_mention field from SP
      };

      const rawMessageResults = await this.prisma.$queryRaw<
        HistoryMessageRawDto[]
      >(
        Prisma.sql`EXEC sp_cvst_GetDecryptedMessagesByConversationUUID
          @conversation_uuid = ${chatSessionId},
          @encryption_key_name = ${encryptionKeyName},
          @decryption_cert_name = ${decryptionCertName};`,
      );

      // NOTE: The stored procedure sp_cvst_GetDecryptedMessagesByConversationUUID needs to be updated to:
      // 1. Return the is_deleted field
      // 2. Filter out messages where is_deleted = 1
      // Until the SP is updated, we'll filter here if the field is available

      // Filter out deleted messages if the field is available
      const filteredMessages = rawMessageResults.filter((msg) => {
        // If is_deleted field exists and is true, filter out the message
        if (msg.is_deleted !== undefined) {
          return !msg.is_deleted;
        }
        // If field doesn't exist (old SP), include the message
        return true;
      });

      // Process results: Parse sources_json and remove it
      console.log(
        `[ChatService] 🔍 Processing ${filteredMessages.length} messages for conversation ${chatSessionId} (filtered from ${rawMessageResults.length})`,
      );

      const messageResults: HistoryMessageDto[] = filteredMessages.map(
        (msg) => {
          let parsedSources: SourceDto[] | null = null;
          if (msg.sources_json) {
            try {
              // Ensure JSON is valid before parsing
              const potentialJson = msg.sources_json;
              // Basic check if it looks like a JSON array
              if (
                potentialJson.trim().startsWith('[') &&
                potentialJson.trim().endsWith(']')
              ) {
                parsedSources = JSON.parse(potentialJson);
                console.log(
                  `[ChatService] ✅ Successfully parsed ${parsedSources?.length || 0} sources for message ${msg.message_uuid} (${msg.sender})`,
                );
                // Optional: Add validation here to ensure each object in parsedSources matches SourceDto structure
              } else {
                console.warn(
                  `[ChatService] ⚠️ Invalid JSON structure for sources_json for message ${msg.message_uuid}: ${potentialJson}`,
                );
                parsedSources = []; // Default to empty array on invalid structure
              }
            } catch (parseError) {
              console.error(
                `[ChatService] ❌ Failed to parse sources_json for message ${msg.message_uuid}:`,
                parseError,
              );
              parsedSources = []; // Default to empty array on error
            }
          } else {
            // Log whether this was expected or not
            if (msg.sender === 'assistant') {
              console.log(
                `[ChatService] 📝 No sources_json for assistant message ${msg.message_uuid} (may be expected if no search was used)`,
              );
            }
            parsedSources = []; // Default to empty array if sources_json is null/empty
          }

          // Create a new object excluding sources_json and adding parsed sources
          const { sources_json, ...rest } = msg;
          return { ...rest, sources: parsedSources };
        },
      );

      // 4. Decrypt the title separately if it exists
      let decryptedTitle: string | null = null;
      // Use the correct field name 'conversation_title' which holds the encrypted data
      if (conversation.conversation_title) {
        // Use a temporary type for the raw query result
        type DecryptedTitleResult = { decrypted_title: string };

        // Execute raw SQL for decryption within a transaction for key management
        const titleResult = await this.prisma.$queryRaw<DecryptedTitleResult[]>(
          Prisma.sql`
            OPEN SYMMETRIC KEY ${Prisma.raw(encryptionKeyName)} DECRYPTION BY CERTIFICATE ${Prisma.raw(decryptionCertName)};
            SELECT CONVERT(NVARCHAR(MAX), DecryptByKey(${conversation.conversation_title})) as decrypted_title;
            CLOSE SYMMETRIC KEY ${Prisma.raw(encryptionKeyName)};
          `,
        );
        decryptedTitle =
          titleResult.length > 0 ? titleResult[0].decrypted_title : null;
        console.log(
          `[ChatService] Decrypted title for ${chatSessionId}:`,
          decryptedTitle,
        );
      } else {
        console.log(
          `[ChatService] No encrypted title found for ${chatSessionId}.`,
        );
      }

      // Prepare parameters DTO
      const parametersDto: ConversationParamsDto | null = conversation
        ? {
            instructions: conversation.instructions,
            pastMessagesCount: conversation.pastMessagesCount,
            maxResponseTokens: conversation.maxResponseTokens,
            temperature: conversation.temperature,
            topP: conversation.topP,
          }
        : null;

      // Count messages with sources for logging
      const messagesWithSources = messageResults.filter(
        (msg) => msg.sources && msg.sources.length > 0,
      );
      console.log(
        `[ChatService] 📤 Returning ${messageResults.length} messages for ${chatSessionId}, ${messagesWithSources.length} have sources`,
      );

      // Return the combined structure including parameters
      return {
        messages: messageResults,
        title: decryptedTitle ?? 'Untitled Chat',
        parameters: parametersDto,
      }; // Use default title if null
    } catch (error) {
      console.error(
        `Error fetching history messages for ${chatSessionId}:`,
        error,
      );
      // Log the error properly in a real application
      throw new InternalServerErrorException(
        'Failed to retrieve chat history.',
      );
    }
  }

  async generateShareLink(
    conversationUuid: string,
    userId: string,
  ): Promise<string> {
    try {
      // Call stored procedure to generate share link
      const result = await this.prisma.$queryRaw<{ ShareId: string }[]>`
        DECLARE @ShareId UNIQUEIDENTIFIER;
        EXEC sp_cvst_GenerateShareLink 
          @ConversationUuid = ${conversationUuid},
          @UserId = ${userId},
          @ShareId = @ShareId OUTPUT;
        SELECT @ShareId as ShareId;
      `;

      if (!result || result.length === 0 || !result[0].ShareId) {
        throw new InternalServerErrorException('Failed to generate share link');
      }

      return result[0].ShareId;
    } catch (error) {
      console.error('Error generating share link:', error);
      if (error instanceof InternalServerErrorException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to generate share link');
    }
  }

  async duplicateSharedConversation(
    shareId: string,
    newUserId: string,
  ): Promise<string> {
    try {
      // First, find the original conversation by share_id
      const originalConversation = await this.prisma.conversation.findFirst({
        where: { share_id: shareId },
        select: { conversation_uuid: true },
      });

      if (!originalConversation || !originalConversation.conversation_uuid) {
        throw new NotFoundException('Shared conversation not found');
      }

      // Call stored procedure to duplicate conversation
      const result = await this.prisma.$queryRaw<
        { NewConversationUuid: string; NewConversationId: number }[]
      >`
        DECLARE @NewConversationUuid UNIQUEIDENTIFIER;
        DECLARE @NewConversationId INT;
        EXEC sp_cvst_DuplicateConversation 
          @OriginalConversationUuid = ${originalConversation.conversation_uuid},
          @NewUserId = ${newUserId},
          @NewConversationUuid = @NewConversationUuid OUTPUT,
          @NewConversationId = @NewConversationId OUTPUT;
        SELECT @NewConversationUuid as NewConversationUuid, 
               @NewConversationId as NewConversationId;
      `;

      if (!result || result.length === 0 || !result[0].NewConversationUuid) {
        throw new InternalServerErrorException(
          'Failed to duplicate conversation',
        );
      }

      return result[0].NewConversationUuid;
    } catch (error) {
      console.error('Error duplicating shared conversation:', error);
      if (
        error instanceof NotFoundException ||
        error instanceof InternalServerErrorException
      ) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Failed to duplicate conversation',
      );
    }
  }

  async renameConversation(
    conversationUuid: string,
    newTitle: string,
    userId: string,
  ): Promise<void> {
    const conversation = await this.prisma.conversation.findUnique({
      where: { conversation_uuid: conversationUuid },
      select: { ssoid: true },
    });

    if (!conversation) {
      throw new NotFoundException(
        `Conversation with UUID ${conversationUuid} not found.`,
      );
    }

    if (conversation.ssoid?.toLowerCase() !== userId.toLowerCase()) {
      throw new ForbiddenException(
        'You do not have permission to rename this conversation.',
      );
    }

    const encryptionKeyName = this.configService.get<string>(
      'DB_ENCRYPTION_KEY_NAME',
    );
    const decryptionCertName = this.configService.get<string>(
      'DB_DECRYPTION_CERT_NAME',
    );

    if (!encryptionKeyName || !decryptionCertName) {
      throw new InternalServerErrorException(
        'Database encryption configuration is missing.',
      );
    }

    await this.prisma
      .$executeRaw`EXEC sp_cvst_RenameConversation @conversation_uuid = ${conversationUuid}, @new_title = ${newTitle}, @encryption_key_name = ${encryptionKeyName}, @decryption_cert_name = ${decryptionCertName}`;
  }

  async softDeleteConversation(
    conversationUuid: string,
    userId: string,
  ): Promise<void> {
    const conversation = await this.prisma.conversation.findUnique({
      where: { conversation_uuid: conversationUuid },
      select: { ssoid: true },
    });

    if (!conversation) {
      throw new NotFoundException(
        `Conversation with UUID ${conversationUuid} not found.`,
      );
    }

    if (conversation.ssoid?.toLowerCase() !== userId.toLowerCase()) {
      throw new ForbiddenException(
        'You do not have permission to delete this conversation.',
      );
    }

    await this.prisma.conversation.update({
      where: { conversation_uuid: conversationUuid },
      data: { delete_dt: new Date() },
    });
  }
}
