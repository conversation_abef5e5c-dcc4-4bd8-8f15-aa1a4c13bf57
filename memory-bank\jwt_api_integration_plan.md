# Plan to Integrate NextAuth JWT into API Requests

**Objective:** Modify the application to include the `session.accessToken` (obtained from NextAuth.js) as a Bearer token in the `Authorization` header for all relevant API requests made through `apps/web/src/lib/store/apiSlice.ts`. This will resolve 401 errors for API calls that require authentication.

## Phase 1: Make Session Token Available to Redux Store

1.  **Create an Auth Slice (`apps/web/src/lib/store/authSlice.ts`):**
    *   **Purpose:** To store the `accessToken` and authentication status globally in the Redux store.
    *   **Initial State:**
        ```typescript
        interface AuthState {
          accessToken: string | null;
          isAuthenticated: boolean;
        }

        const initialState: AuthState = {
          accessToken: null,
          isAuthenticated: false,
        };
        ```
    *   **Actions:**
        *   `setAuthSession(state, action: PayloadAction<{ accessToken: string | null; isAuthenticated: boolean }>)`: Sets the access token and authentication status.
        *   `clearAuthSession(state)`: Clears the access token and sets authentication status to false.
    *   **Reducer:** Implement reducers to handle the above actions.

2.  **Integrate Auth Slice into Root Reducer:**
    *   Modify [`apps/web/src/lib/store/rootReducer.ts`](apps/web/src/lib/store/rootReducer.ts).
    *   Import the `authReducer` from the new `authSlice.ts`.
    *   Add `auth: authReducer` to the `combineReducers` call.

3.  **Update Redux Store with Session Data from NextAuth.js:**
    *   Modify [`apps/web/src/app/providers/AuthProvider.tsx`](apps/web/src/app/providers/AuthProvider.tsx).
    *   This component already uses `SessionProvider`. We will enhance it to also act as a bridge to Redux.
    *   Create a new internal component (e.g., `SessionToReduxBridge`) that will be a child of `SessionProvider`.
    *   Inside `SessionToReduxBridge`:
        *   Use the `useSession()` hook from `next-auth/react` to get session data (`data: session, status`).
        *   Use the `useDispatch()` hook from `react-redux`.
        *   Implement a `useEffect` hook that listens to changes in `session` and `status`.
        *   When `session` or `status` changes:
            *   If `status === 'authenticated'` and `session?.accessToken` exists, dispatch `setAuthSession({ accessToken: session.accessToken, isAuthenticated: true })`.
            *   If `status === 'unauthenticated'`, dispatch `clearAuthSession()`.
    *   Render this `SessionToReduxBridge` component within the `AuthProvider`'s returned `SessionProvider`.

## Phase 2: Modify `apiSlice.ts` to Use the Token

1.  **Update `fetchBaseQuery` in [`apps/web/src/lib/store/apiSlice.ts`](apps/web/src/lib/store/apiSlice.ts):**
    *   Locate the `fetchBaseQuery` setup (around line 199).
    *   Uncomment and complete the `prepareHeaders` function:
        ```typescript
        prepareHeaders: (headers, { getState }) => {
            const token = (getState() as RootState).auth?.accessToken; // Adjust if RootState path is different
            if (token) {
                headers.set('authorization', `Bearer ${token}`);
            }
            return headers;
        },
        ```

2.  **Update `chatCompletion` `queryFn` in [`apps/web/src/lib/store/apiSlice.ts`](apps/web/src/lib/store/apiSlice.ts):**
    *   Locate the `chatCompletion` mutation's `queryFn` (around line 357).
    *   Inside the `queryFn`, before the `fetch` call (around line 391):
        *   Retrieve the token: `const token = (getState() as RootState).auth?.accessToken;`
        *   Modify the `headers` object for the `fetch` call:
            ```typescript
            const fetchHeaders: HeadersInit = { 'Content-Type': 'application/json' };
            if (token) {
                fetchHeaders['Authorization'] = `Bearer ${token}`;
            }
            // ...
            const response = await fetch(endpoint, {
                method: 'POST',
                headers: fetchHeaders, // Use the updated headers
                body: JSON.stringify(requestBody),
                signal: signal,
            });
            ```

## Phase 3: Testing

1.  **Login and Verify:**
    *   Log into the application.
    *   Open browser developer tools (Network tab).
    *   Trigger API calls that use `fetchBaseQuery` (e.g., loading models, fetching usage info). Verify the `Authorization: Bearer <token>` header is present and correct.
    *   Trigger a `chatCompletion` request. Verify the `Authorization: Bearer <token>` header is present and correct.
    *   Confirm that API responses are successful (e.g., 200 OK) and not 401.
2.  **Logout and Verify:**
    *   Log out of the application.
    *   Trigger the same API calls.
    *   Verify that the `Authorization` header is NOT present.
    *   Confirm that API endpoints protected by JWT authentication now correctly return 401 Unauthorized.
3.  **Edge Cases (Optional but Recommended):**
    *   Test behavior if the token expires (NextAuth.js should handle redirects or session updates).

## Diagram of Proposed Redux and Auth Flow

```mermaid
graph TD
    subgraph NextAuth
        NA_SessionCallback[authOptions.ts: session callback] -- populates --> NA_SessionObject(Session Object with accessToken)
    end

    subgraph ReactComponents
        RC_SessionProvider[AuthProvider.tsx: SessionProvider] -- provides --> RC_useSessionHook(useSession Hook)
        RC_useSessionHook -- Session Data --> RC_SessionToReduxBridge(AuthProvider.tsx: SessionToReduxBridge)
    end

    subgraph ReduxStore
        RS_AuthSlice[authSlice.ts]
        RC_SessionToReduxBridge -- dispatch(setAuthSession/clearAuthSession) --> RS_AuthSlice
    end

    subgraph ApiSlice
        AS_ApiSlice[apiSlice.ts]
        RS_AuthSlice -- accessToken via getState() --> AS_PrepareHeaders(apiSlice.prepareHeaders)
        RS_AuthSlice -- accessToken via getState() --> AS_ChatQueryFn(apiSlice.chatCompletion.queryFn)
        AS_PrepareHeaders -- Adds Auth Header --> AS_BaseQueryRequests(fetchBaseQuery Requests)
        AS_ChatQueryFn -- Adds Auth Header --> AS_ChatFetchRequest(chatCompletion fetch Request)
    end

    AS_BaseQueryRequests --> BackendAPI[Backend API]
    AS_ChatFetchRequest --> BackendAPI

    style BackendAPI fill:#f9f,stroke:#333,stroke-width:2px
```

This plan outlines the necessary steps to integrate JWT authentication into the API requests.