import requests
import json
import time
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:3003/api/v0"
API_KEY = "80b60025-07b4-4097-ba1d-e14695359dd0"

headers = {
    'Content-Type': 'application/json',
    'api-key': API_KEY
}

def get_token_usage():
    """Get current token usage for all models"""
    url = f"{BASE_URL}/rest/rate-limit/token-usage"
    response = requests.get(url, headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        usage_dict = {}
        for usage in data.get('usage', []):
            usage_dict[usage['modelName']] = usage
        return usage_dict
    else:
        print(f"Error getting token usage: {response.status_code} - {response.text}")
        return {}

def consume_tokens_quickly(model_name="o1", num_requests=10):
    """Make multiple requests to consume tokens quickly"""
    print("=" * 80)
    print(f"CONSUMING TOKENS QUICKLY WITH MODEL: {model_name}")
    print("=" * 80)
    
    # Get initial token usage
    initial_usage = get_token_usage()
    
    if model_name in initial_usage:
        initial_tokens = initial_usage[model_name]['totalTokensUsed']
        remaining_tokens = initial_usage[model_name]['remaining']
        monthly_limit = initial_usage[model_name]['monthlyLimit']
        
        print(f"Initial tokens used: {initial_tokens:,}")
        print(f"Remaining tokens: {remaining_tokens:,}")
        print(f"Monthly limit: {monthly_limit:,}")
        
        # If we have very few remaining tokens, this model is good for testing
        if remaining_tokens < 1000:
            print("PERFECT! This model has very few remaining tokens.")
        elif remaining_tokens < 50000:
            print("Good! This model has relatively few remaining tokens.")
        else:
            print("Warning: This model has lots of remaining tokens. May take many requests to hit limit.")
    else:
        print(f"Model {model_name} not found in usage data")
    
    print(f"\\nMaking {num_requests} requests to consume tokens...")
    
    url = f"{BASE_URL}/rest/deployments/{model_name}/chat/completions"
    params = {"api-version": "2024-02-01"}
    
    successful_requests = 0
    blocked_requests = 0
    total_tokens_consumed = 0
    
    for i in range(1, num_requests + 1):
        # Use a longer message to consume more tokens
        data = {
            "messages": [
                {"role": "user", "content": f"Please write a detailed explanation about the number {i}. Include its mathematical properties, historical significance, and interesting facts. Make it comprehensive but concise."}
            ],
            "max_tokens": 100,  # Allow more tokens in response
            "temperature": 0.7
        }
        
        print(f"\\nRequest {i}:")
        try:
            start_time = time.time()
            response = requests.post(url, headers=headers, json=data, params=params, timeout=30)
            end_time = time.time()
            
            if response.status_code == 200:
                successful_requests += 1
                result = response.json()
                usage = result.get('usage', {})
                tokens = usage.get('total_tokens', 0)
                total_tokens_consumed += tokens
                
                print(f"   SUCCESS - {tokens} tokens used")
                print(f"   Response: {result['choices'][0]['message']['content'][:80]}...")
                
            elif response.status_code == 429:
                blocked_requests += 1
                error_data = response.json()
                print(f"   BLOCKED (429)!")
                print(f"   Message: {error_data.get('message', 'No message')}")
                
                # Check if this is token limit vs rate limit
                if any(word in str(error_data).lower() for word in ['token', 'monthly', 'limit', 'exceeded']):
                    print(f"   SUCCESS: Token limit blocking detected!")
                    print(f"   This proves the token limit system is working!")
                    break
                else:
                    print(f"   This appears to be request-based rate limiting")
                    
            else:
                print(f"   UNEXPECTED STATUS: {response.status_code}")
                print(f"   Response: {response.text[:200]}")
                
        except requests.exceptions.Timeout:
            print(f"   TIMEOUT after 30 seconds")
        except Exception as e:
            print(f"   ERROR: {str(e)}")
            
        # Small delay between requests
        time.sleep(0.5)
    
    print(f"\\n" + "=" * 80)
    print("RESULTS")
    print("=" * 80)
    print(f"Successful requests: {successful_requests}")
    print(f"Blocked requests: {blocked_requests}")
    print(f"Total tokens consumed in test: {total_tokens_consumed:,}")
    
    # Check final usage
    print(f"\\nChecking final token usage...")
    time.sleep(2)
    final_usage = get_token_usage()
    
    if model_name in final_usage:
        final_tokens = final_usage[model_name]['totalTokensUsed']
        tokens_added = final_tokens - initial_tokens if model_name in initial_usage else final_tokens
        
        print(f"Final tokens used: {final_tokens:,}")
        print(f"Tokens added by test: {tokens_added:,}")
        print(f"Remaining tokens: {final_usage[model_name]['remaining']:,}")
        
        if blocked_requests > 0:
            return True  # Successfully triggered blocking
    
    return False

def find_best_model_for_testing():
    """Find a model with low remaining tokens for testing"""
    print("=" * 80)
    print("FINDING BEST MODEL FOR TOKEN LIMIT TESTING")
    print("=" * 80)
    
    usage_data = get_token_usage()
    candidates = []
    
    for model_name, usage in usage_data.items():
        if 'embedding' in model_name.lower():
            continue
            
        remaining = usage['remaining']
        monthly_limit = usage['monthlyLimit']
        percentage_used = (usage['totalTokensUsed'] / monthly_limit) * 100 if monthly_limit > 0 else 0
        
        print(f"{model_name}:")
        print(f"  Used: {usage['totalTokensUsed']:,} / {monthly_limit:,} ({percentage_used:.1f}%)")
        print(f"  Remaining: {remaining:,}")
        
        # Prioritize models with fewer remaining tokens
        if remaining < 1000:
            candidates.append((model_name, remaining, "EXCELLENT - Very low tokens"))
        elif remaining < 10000:
            candidates.append((model_name, remaining, "GOOD - Low tokens"))
        elif remaining < 100000:
            candidates.append((model_name, remaining, "FAIR - Moderate tokens"))
        
        print()
    
    if candidates:
        # Sort by remaining tokens (lowest first)
        candidates.sort(key=lambda x: x[1])
        best_model, remaining, rating = candidates[0]
        
        print(f"RECOMMENDATION: Use '{best_model}' for testing")
        print(f"Reason: {rating} ({remaining:,} remaining)")
        return best_model, remaining
    else:
        print("No ideal candidates found. All models have high remaining tokens.")
        return None, 0

def main():
    print("TOKEN LIMIT TESTING - SIMULATION APPROACH")
    print("=" * 80)
    print(f"Timestamp: {datetime.now().isoformat()}")
    print()
    print("This test will:")
    print("1. Find a model with relatively low remaining tokens")
    print("2. Make multiple requests with longer prompts to consume more tokens")
    print("3. Try to trigger the monthly token limit blocking")
    print()
    
    # Find the best model for testing
    best_model, remaining_tokens = find_best_model_for_testing()
    
    if best_model:
        print(f"\\nTesting token limit with model: {best_model}")
        print(f"This model has {remaining_tokens:,} remaining tokens")
        
        # Estimate how many requests we might need
        estimated_requests = max(5, min(20, remaining_tokens // 1000))
        print(f"Will make up to {estimated_requests} requests to try to hit the limit")
        
        # Run the test
        success = consume_tokens_quickly(best_model, estimated_requests)
        
        if success:
            print(f"\\nSUCCESS: Token limit blocking system is working!")
        else:
            print(f"\\nNo token limit blocking occurred. The model may have too many remaining tokens.")
            print(f"To test token limits, you might need to:")
            print(f"1. Use a different user account with fewer tokens")
            print(f"2. Set lower monthly limits in the database")
            print(f"3. Make many more requests to consume all tokens")
    else:
        print(f"\\nNo suitable model found for testing.")
        print(f"All models have high remaining token counts.")

if __name__ == "__main__":
    main()