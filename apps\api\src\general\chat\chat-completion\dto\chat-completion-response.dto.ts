import { ApiProperty } from '@nestjs/swagger';

// Usage information for token consumption
export class ChatCompletionUsageDto {
  @ApiProperty({
    description: 'Number of tokens in the prompt',
    example: 12,
  })
  prompt_tokens!: number;

  @ApiProperty({
    description: 'Number of tokens in the generated completion',
    example: 25,
  })
  completion_tokens!: number;

  @ApiProperty({
    description: 'Total number of tokens used in the request (prompt + completion)',
    example: 37,
  })
  total_tokens!: number;
}

// Message object within a choice
export class ChatCompletionMessageDto {
  @ApiProperty({
    description: 'The role of the message author',
    example: 'assistant',
    enum: ['system', 'user', 'assistant'],
  })
  role!: string;

  @ApiProperty({
    description: 'The content of the message',
    example: 'Hello! I can help you write a Python function to calculate factorial. Here\'s a simple implementation:\n\n```python\ndef factorial(n):\n    if n == 0 or n == 1:\n        return 1\n    else:\n        return n * factorial(n - 1)\n```',
  })
  content!: string;
}

// Choice object containing the message and finish reason
export class ChatCompletionChoiceDto {
  @ApiProperty({
    description: 'The index of the choice',
    example: 0,
  })
  index!: number;

  @ApiProperty({
    description: 'The message generated by the model',
    type: ChatCompletionMessageDto,
  })
  message!: ChatCompletionMessageDto;

  @ApiProperty({
    description: 'The reason the model stopped generating tokens',
    example: 'stop',
    enum: ['stop', 'length', 'content_filter'],
  })
  finish_reason!: string;
}

// Main chat completion response object (OpenAI-compatible)
export class ChatCompletionResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the chat completion',
    example: 'chatcmpl-7QyqpwdfhqwajicIEznoc6Q47XAyW',
  })
  id!: string;

  @ApiProperty({
    description: 'Object type, always "chat.completion"',
    example: 'chat.completion',
  })
  object!: string;

  @ApiProperty({
    description: 'Unix timestamp (in seconds) when the chat completion was created',
    example: 1677652288,
  })
  created!: number;

  @ApiProperty({
    description: 'The model used for the chat completion',
    example: 'gpt-4',
  })
  model!: string;

  @ApiProperty({
    description: 'UUID of the conversation this completion belongs to',
    example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef',
    required: false,
  })
  conversation_uuid?: string;

  @ApiProperty({
    description: 'List of chat completion choices. Can be more than one if n is greater than 1',
    type: [ChatCompletionChoiceDto],
  })
  choices!: ChatCompletionChoiceDto[];

  @ApiProperty({
    description: 'Usage statistics for the completion request',
    type: ChatCompletionUsageDto,
  })
  usage!: ChatCompletionUsageDto;
}