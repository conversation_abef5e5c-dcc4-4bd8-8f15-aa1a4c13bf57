{"name": "@hkbu-genai-platform/web", "version": "0.1.0", "private": true, "scripts": {"load-secrets": "ts-node --esm load-secrets.ts", "dev": "next dev", "dev-ssl": "npx local-ssl-proxy --key https_cert/key.pem --cert https_cert/cert.pem --source 3001 --target 3000", "build": "next build", "start": "next start", "lint": "next lint --dir src", "prepare": "husky"}, "dependencies": {"@azure/identity": "^4.0.0", "@azure/keyvault-secrets": "^4.8.0", "@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@headlessui/react": "^2.2.1", "@heroicons/react": "^2.2.0", "@hkbu-genai-platform/database": "workspace:*", "@mui/icons-material": "^7.1.2", "@mui/material": "^7.1.2", "@mui/x-data-grid": "^8.7.0", "@reduxjs/toolkit": "^2.6.1", "@types/crypto-js": "^4.2.2", "@types/katex": "^0.16.7", "@types/lodash": "^4.17.16", "@types/marked": "^6.0.0", "@types/pizzip": "^3.0.5", "@types/swagger-ui-react": "^5.18.0", "autoprefixer": "^10.4.21", "axios": "^1.8.4", "chart.js": "^4.4.8", "crypto-js": "^4.2.0", "highlight.js": "^11.11.1", "https-proxy-agent": "^7.0.6", "isomorphic-dompurify": "^2.23.0", "jsonwebtoken": "^9.0.2", "katex": "^0.16.22", "latex.js": "^0.12.6", "lodash": "^4.17.21", "lottie-react": "^2.4.1", "marked": "^15.0.8", "marked-gfm-heading-id": "^4.1.1", "marked-mangle": "^1.1.10", "moment": "^2.30.1", "next": "15.2.4", "next-auth": "^4.24.11", "next-swagger-doc": "^0.4.1", "node-fetch": "^3.3.2", "opus-media-recorder": "^0.8.0", "pizzip": "^3.1.8", "postcss": "^8.5.3", "pptxtojson": "^1.3.1", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "react-redux": "^9.2.0", "react-syntax-highlighter": "^15.6.1", "rehype-katex": "^7.0.1", "remark-gfm": "^4.0.1", "server-only": "^0.0.1", "swagger-ui-react": "^5.24.0", "swr": "^2.3.3", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.3", "@types/dom-speech-recognition": "^0.0.6", "@types/highlight.js": "^10.1.0", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-syntax-highlighter": "^15.5.13", "eslint": "^9", "eslint-config-next": "15.2.4", "eslint-plugin-prettier": "^5.2.2", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "^3", "tailwindcss": "^4.1.3", "typescript": "^5.7.3"}}