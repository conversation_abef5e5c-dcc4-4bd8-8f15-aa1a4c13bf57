import { combineReducers } from '@reduxjs/toolkit';

// Import your slice reducers here when they are created
import uiReducer from './uiSlice';
import chatReducer from './chatSlice';
import appReducer from './appSlice';
import modelReducer from './modelSlice'; // Import the new model reducer
import authReducer from './authSlice'; // Import the auth reducer
import { apiSlice } from './apiSlice'; // Import the API slice
import themeReducer from './themeSlice';
import promptReducer from './promptSlice';

const rootReducer = combineReducers({
  // Add slice reducers to the combiner
  ui: uiReducer,
  chat: chatReducer,
  app: appReducer,
  model: modelReducer, // Add the model reducer
  auth: authReducer, // Add the auth reducer
  theme: themeReducer,
  prompt: promptReducer,
  [apiSlice.reducerPath]: apiSlice.reducer, // Add the API slice reducer
});

export type RootState = ReturnType<typeof rootReducer>;

export default rootReducer;
