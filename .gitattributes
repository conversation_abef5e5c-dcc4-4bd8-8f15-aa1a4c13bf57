# Set default behavior to automatically normalize line endings to CRLF
* text=auto eol=crlf

# Explicitly declare text files you want to always be normalized and converted to CRLF
*.ts text eol=crlf
*.tsx text eol=crlf
*.js text eol=crlf
*.jsx text eol=crlf
*.json text eol=crlf
*.md text eol=crlf
*.yml text eol=crlf
*.yaml text eol=crlf
*.css text eol=crlf
*.scss text eol=crlf
*.html text eol=crlf
*.xml text eol=crlf
*.txt text eol=crlf
*.sh text eol=crlf
*.env text eol=crlf

# Denote all files that are truly binary and should not be modified
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.pdf binary
*.zip binary
*.gz binary
*.tar binary
*.tgz binary
*.jar binary
*.war binary
*.ear binary
*.dll binary
*.exe binary
*.so binary
*.dylib binary
*.woff binary
*.woff2 binary
*.ttf binary
*.eot binary
*.mp4 binary
*.webm binary
*.ogg binary
*.mp3 binary
*.wav binary