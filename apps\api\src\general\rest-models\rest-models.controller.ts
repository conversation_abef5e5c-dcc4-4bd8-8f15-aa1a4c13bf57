import {
  Controller,
  Get,
  Logger,
  HttpException,
  HttpStatus,
  UseGuards,
} from '@nestjs/common';
import { GeneralService } from '../general.service';
import { GeneralRateLimitGuard } from '../../common/guards/general-rate-limit.guard';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { ModelsResponseDto } from './dto/rest-models-response.dto';

@ApiTags('LLM - Models (REST API)')
@Controller('rest/models')
@UseGuards(GeneralRateLimitGuard)
export class RestModelsController {
  private readonly logger = new Logger(RestModelsController.name);

  constructor(private readonly generalService: GeneralService) {}

  @Get()
  @ApiOperation({
    summary: 'Lists the currently available models (public endpoint)',
  })
  @ApiResponse({
    status: 200,
    description: 'OK - Returns list of available models',
  })
  @ApiResponse({ status: 500, description: 'Internal server error.' })
  async getModels(): Promise<ModelsResponseDto> {
    this.logger.log('Public models list request');

    try {
      // Get all publicly available models (using a generic user type)
      // For public endpoint, we'll show models available to 'STUDENT' type as baseline
      const modelListResult = await this.generalService.getModelList(
        'public',
        'STUDENT',
      );

      // Transform to OpenAI-compatible format
      const models = modelListResult.model_list.map((model) => ({
        id: model.model_name || model.display_name || 'unknown',
        object: 'model',
        owned_by: 'hkbu-genai-platform',
        permission: [],
      }));

      return {
        object: 'list',
        data: models,
      };
    } catch (error) {
      let errorMessage =
        'An unknown error occurred while fetching the models list.';
      let errorStack: string | undefined = undefined;
      if (error instanceof Error) {
        errorMessage = error.message;
        errorStack = error.stack;
      }
      this.logger.error(`Error in getModels: ${errorMessage}`, errorStack);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        'Failed to fetch models list.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
