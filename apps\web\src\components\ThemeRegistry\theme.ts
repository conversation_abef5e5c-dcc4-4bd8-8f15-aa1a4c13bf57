import { createTheme, ThemeOptions } from '@mui/material/styles';
import { red } from '@mui/material/colors';

const lightThemeOptions: ThemeOptions = {
  palette: {
    mode: 'light',
    primary: {
      main: '#134b97',
    },
    secondary: {
      main: '#047857',
    },
    error: {
      main: red.A400,
    },
    info: {
      main: 'rgba(232, 232, 232, 1)',
    },
    background: {
      default: '#ffffff',
      paper: '#f5f5f5',
    },
    text: {
      primary: 'rgb(29, 28, 27)',
      secondary: 'rgb(100, 99, 98)',
    },
  },
};

const darkThemeOptions: ThemeOptions = {
  palette: {
    mode: 'dark',
    primary: {
      main: '#2a65fd',
    },
    secondary: {
      main: '#059669',
    },
    error: {
      main: red.A400,
    },
    info: {
      main: 'rgba(64, 64, 64, 1)',
    },
    background: {
      default: '#333333',
      paper: '#212121',
    },
    text: {
      primary: 'rgb(252, 252, 252)',
      secondary: 'rgb(181, 181, 181)',
    },
  },
};

export const lightTheme = createTheme(lightThemeOptions);
export const darkTheme = createTheme(darkThemeOptions);
