import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { LlmConfigService } from '../../llm/llm-config.service'; // Adjust path
import { PrismaService } from '../../prisma/prisma.service'; // Adjust path
import { TokenUsageService } from '../../common/services/token-usage.service';
import { ApiUserPayload } from '../../auth/api-key.strategy'; // Adjust path
import { CreateEmbeddingsDto } from './embeddings.controller'; // DTO from controller

@Injectable()
export class EmbeddingsService {
  private readonly logger = new Logger(EmbeddingsService.name);

  constructor(
    private readonly llmConfigService: LlmConfigService,
    private readonly prisma: PrismaService,
    private readonly tokenUsageService: TokenUsageService,
    // Potentially other LLM-specific services if not using a generic handler
  ) {}

  async create(
    user: ApiUserPayload,
    apiVersion: string,
    modelDeploymentName: string,
    createEmbeddingsDto: CreateEmbeddingsDto,
  ): Promise<any> {
    this.logger.log(
      `Creating embeddings for model [${modelDeploymentName}], API version [${apiVersion}] by user [${user.ssoid}]`,
    );

    try {
      // Validate that input is provided
      if (!createEmbeddingsDto.input) {
        throw new HttpException(
          'The "input" field is required for embeddings',
          HttpStatus.BAD_REQUEST,
        );
      }

      // 1. Get LLM configuration using llmConfigService
      const llmConfig = await this.llmConfigService.getLlmConfig(
        user.dept_unit_code,
        modelDeploymentName,
      );

      this.logger.debug(
        `LLM Config: provider=${llmConfig.provider}, deployment=${llmConfig.deploymentName}`,
      );

      // 2. Only support Azure provider for embeddings currently
      if (llmConfig.provider !== 'azure') {
        throw new HttpException(
          `Provider ${llmConfig.provider} is not supported for embeddings. Only Azure OpenAI is supported.`,
          HttpStatus.BAD_REQUEST,
        );
      }

      // 3. Get Azure configuration
      const endpoint = llmConfig.endpointUrl;
      const apiKey = llmConfig.apiKey;
      const deploymentName = llmConfig.deploymentName;
      const effectiveApiVersion = llmConfig.apiVersion || apiVersion;

      if (!endpoint || !apiKey || !deploymentName) {
        this.logger.error(
          `Azure configuration missing for ${modelDeploymentName}: endpoint=${!!endpoint}, apiKey=${!!apiKey}, deployment=${!!deploymentName}`,
        );
        throw new HttpException(
          `Configuration missing for Azure embedding model ${modelDeploymentName}`,
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }

      // 4. Prepare Azure OpenAI embeddings endpoint
      let azureEndpoint = endpoint;
      if (!azureEndpoint.includes('/openai/deployments/')) {
        azureEndpoint =
          azureEndpoint.replace(/\/+$/, '') +
          `/openai/deployments/${deploymentName}/embeddings`;
      }
      azureEndpoint += `?api-version=${effectiveApiVersion}`;

      // 5. Prepare request payload
      const payload = {
        input: createEmbeddingsDto.input,
        encoding_format: createEmbeddingsDto.encoding_format || 'float',
        ...(createEmbeddingsDto.dimensions && {
          dimensions: createEmbeddingsDto.dimensions,
        }),
      };

      this.logger.debug(`Azure embeddings request: ${azureEndpoint}`);
      this.logger.debug(`Request payload: ${JSON.stringify(payload)}`);

      // 6. Make API call to Azure OpenAI
      const { default: fetch } = await import('node-fetch');
      const response = await fetch(azureEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'api-key': apiKey,
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        let errorMessage = `Azure embeddings request failed with status ${response.status}`;
        try {
          const errorData = await response.json();
          errorMessage = errorData.error?.message || errorMessage;
        } catch {
          // If parsing error response fails, use default message
        }
        this.logger.error(errorMessage);
        throw new HttpException(errorMessage, response.status);
      }

      // 7. Parse response
      const responseData = await response.json();
      this.logger.debug('Azure embeddings response received');

      // 8. Extract token usage and track it
      const promptTokens = responseData.usage?.prompt_tokens || 0;
      const totalTokens = responseData.usage?.total_tokens || promptTokens;

      if (totalTokens > 0) {
        await this.tokenUsageService.updateTokenUsage({
          username: user.userId,
          modelName: llmConfig.modelName || modelDeploymentName,
          tokenDate: new Date(),
          promptTokens,
          completionTokens: 0, // embeddings don't have completion tokens
          totalTokens,
          isApi: true,
        });
      }

      // 9. Return Azure response with model name from deployment
      return {
        ...responseData,
        model: modelDeploymentName,
      };
    } catch (error: any) {
      this.logger.error(
        `Error creating embeddings for ${modelDeploymentName}: ${error.message}`,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        `Failed to create embeddings: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
