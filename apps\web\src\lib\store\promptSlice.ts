import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from './store';

interface PromptState {
  selectedPrompt: string | null;
}

const initialState: PromptState = {
  selectedPrompt: null,
};

const promptSlice = createSlice({
  name: 'prompt',
  initialState,
  reducers: {
    setSelectedPrompt: (state, action: PayloadAction<string | null>) => {
      state.selectedPrompt = action.payload;
    },
  },
});

export const { setSelectedPrompt } = promptSlice.actions;

export const selectSelectedPrompt = (state: RootState) => state.prompt.selectedPrompt;

export default promptSlice.reducer;