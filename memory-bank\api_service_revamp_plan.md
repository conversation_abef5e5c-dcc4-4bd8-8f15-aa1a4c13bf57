# Plan for API Service Revamp

This plan outlines the steps to revamp the API service, moving relevant functionalities from the existing `/gpt/` codebase to the new `hkbu-genai-platform/` monorepo.

## I. Backend API (`hkbu-genai-platform/apps/api/`)

1.  **User Authentication & Authorization:**
    *   **Mechanism:** Implement API key-based authentication. The API key will be passed in the `api-key` header.
    *   **Authorization Logic:**
        *   Create a new service/module (e.g., `AuthService`) to handle API request authorization.
        *   This service will replicate the logic from [`gpt/app/common/check_enable_restful_api.ts`](gpt/app/common/check_enable_restful_api.ts:1):
            *   Accept `username` (derived from the user associated with the API key) and `dept_unit_code` (also from user data).
            *   Connect to the database and utilize the existing stored procedures:
                *   `sp_mtr_GetDeptByDeptUnitCode`
                *   `sp_acl_GetApiUserByUsername`
            *   Allow 'ITO', 'SCI', 'SCID' directly.
            *   Allow if `report_unit_code` is 'SCI'.
            *   Allow if `username` is in the dedicated API user list.
        *   This authorization check should be performed as a guard or middleware for all protected API endpoints.
    *   **User Data for API Key:** When an API key is validated, the system needs to fetch the associated user's details (ssoid/username, department, etc.) to perform the authorization checks. The `sp_acl_GenUserApiKey` (from the old project) likely stores this association, or a similar mechanism will be needed.

2.  **API Key Management Endpoint:**
    *   Create a new endpoint (e.g., `/auth/api-key` or `/user/api-key`) in the `apps/api` project.
    *   **Functionality (mirroring [`gpt/app/api/general/get_api_key/route.ts`](gpt/app/api/general/get_api_key/route.ts:1)):**
        *   Requires user session (e.g., NextAuth session from the `apps/web` frontend making the request).
        *   Implement rate limiting.
        *   Generate a UUID v4 as the API key.
        *   Store/update the API key associated with the user's `ssoid` and IP address in the database using a new or existing stored procedure (similar to `sp_acl_GenUserApiKey`).
        *   Return the new API key to the user.

3.  **LLM Model & API Version Information Endpoint:**
    *   Create an endpoint (e.g., `/llm/config` or `/llm/models-versions`).
    *   **Functionality:**
        *   Fetch available LLM models and their API versions from the database using the `sp_model_GetModelList_v6` stored procedure.
        *   **Crucially, filter these results where `api_status = 'A'` (as per user clarification).**
        *   The endpoint should return a structured list, for example:
            ```json
            [
              {
                "modelName": "GPT-4",
                "apiVersions": ["2024-02-01", "2024-05-01-preview"],
                "provider": "AzureOpenAI" // Or derive/store this info
              },
              {
                "modelName": "Claude-3",
                "apiVersions": ["20240620"],
                "provider": "Anthropic"
              }
              // ... other models
            ]
            ```
        *   This endpoint will be consumed by the new settings page in the frontend.

4.  **Core LLM API Endpoints:**
    *   Re-implement the core LLM API endpoints within `hkbu-genai-platform/apps/api/src/general/` (or a similar path, mirroring the old structure where appropriate).
    *   **Path Structure (example, to be adapted for each specific endpoint found):**
        *   `/{apiVersion}/deployments/{modelDeploymentName}/chat/completions`
        *   `/{apiVersion}/deployments/{modelDeploymentName}/chat/completions_stress` (if this is a distinct, needed functionality)
        *   `/{apiVersion}/deployments/{modelDeploymentName}/embeddings`
        *   `/{apiVersion}/deployments/{modelDeploymentName}/extensions/chat/completions` (for features like "Azure OpenAI on your data")
        *   `/{apiVersion}/deployments/{modelDeploymentName}/generate_content` (likely for specific content generation tasks, e.g., Vertex AI's `generateContent`)
        *   `/{apiVersion}/deployments/{modelDeploymentName}/llama/completion` (specific to Llama models if they have a unique completion API)
        *   `/{apiVersion}/deployments/{modelDeploymentName}/messages` (potentially for assistant/thread message management if Assistants API features are included)
        *   *(Other endpoints as identified in the old project's `[model_name]` subdirectories, such as audio transcriptions, translations, image generation, and assistants API functionalities if they were present and need to be carried over. The Swagger file I read earlier did list these but many were commented out; we should confirm which are in scope for the revamp).*
    *   **Request Handling:**
        *   Validate the incoming API key.
        *   Perform user authorization check (see I.1).
        *   Based on `modelDeploymentName`, `apiVersion`, and the specific endpoint path, route the request to the appropriate LLM service. This will involve adapting logic from files like [`gpt/app/general/rest/azure-openai.ts`](gpt/app/general/rest/azure-openai.ts:1), [`gpt/app/general/rest/vertex-ai-gemini.ts`](gpt/app/general/rest/vertex-ai-gemini.ts:1), [`gpt/app/general/rest/deployments/llm/api-handler.ts`](gpt/app/general/rest/deployments/llm/api-handler.ts:1) etc.
        *   Forward the request to the underlying LLM provider, translating parameters as needed.
        *   Handle responses, including error mapping.
        *   Implement request/response logging.

5.  **Swagger Specification Generation:**
    *   For each supported `apiVersion`, a dynamic Swagger specification needs to be generated.
    *   Adapt the approach from [`gpt/app/general/specification/[version]/swagger.ts`](gpt/app/general/specification/[version]/swagger.ts:1).
    *   The Swagger definition should:
        *   List models available for *that specific API version* (fetched from `sp_model_GetModelList_v6` with `api_status = 'A'` and filtered by the version).
        *   Define all relevant API paths for that version, including:
            *   Chat completions
            *   Embeddings
            *   `chat/completions_stress`
            *   `extensions/chat/completions`
            *   `generate_content`
            *   `llama/completion`
            *   `messages`
            *   *(And any other confirmed legacy endpoints like audio, image, assistants if in scope)*
        *   Specify `api-key` header security.
    *   Create an endpoint (e.g., `/docs/{apiVersion}/swagger.json`) that serves this generated Swagger JSON.

## II. Frontend (`hkbu-genai-platform/apps/web/`)

1.  **New Settings Page for API Service:**
    *   Create a new route and component for the API settings page (e.g., `/settings/api-service`).
    *   **Display LLM Models and API Versions:**
        *   Fetch data from the new backend endpoint (I.3 - `/llm/config`).
        *   Display the models and their available API versions in a user-friendly way (e.g., a table or list, similar to the hardcoded table in [`gpt/components/chatgpt/ApiKeyModal.tsx`](gpt/components/chatgpt/ApiKeyModal.tsx:1) but dynamic).
        *   Each API version listed should be clickable.
    *   **API Key Management Section:**
        *   Display the user's current API key (if one exists and can be fetched securely, or just allow generation).
        *   Provide a "Generate New API Key" button.
            *   Clicking this button will call the new backend endpoint for API key generation (I.2).
            *   Display the newly generated key to the user (with a copy-to-clipboard feature).
            *   Emphasize that the key should be stored securely and will only be shown once.
    *   **Access Control:** This page should only be accessible to users authorized to use the API service (based on their session data which should reflect the `checkEnableRestfulApi` logic).

2.  **Swagger UI View:**
    *   When a user clicks on an API version on the settings page:
        *   Navigate to a new view/route (e.g., `/settings/api-service/docs/{apiVersion}`).
        *   This view will contain a client component responsible for rendering Swagger UI.
        *   **Swagger UI Component:**
            *   Create a new client component (similar to [`gpt/app/general/specification/[version]/react-swagger.tsx`](gpt/app/general/specification/[version]/react-swagger.tsx:1)).
            *   It will take the `apiVersion` as a prop (or from the route).
            *   It will fetch the corresponding Swagger JSON from the backend endpoint (I.5 - `/docs/{apiVersion}/swagger.json`).
            *   Use `swagger-ui-react` (or an equivalent library) to render the Swagger UI using the fetched spec.
            *   Ensure appropriate styling and layout.

## III. Database (Stored Procedures)

*   **`sp_model_GetModelList_v6`:** Continue using this, but ensure the new backend filters by `api_status = 'A'`.
*   **`sp_mtr_GetDeptByDeptUnitCode`:** Continue using for authorization.
*   **`sp_acl_GetApiUserByUsername`:** Continue using for authorization.
*   **`sp_acl_GenUserApiKey` (or equivalent):** Ensure this procedure (or a new one) correctly stores the API key, associates it with the `ssoid`, logs the IP, and handles key rotation/invalidation if a user generates a new key.
*   **New Stored Procedure (Potentially for API Key Validation):** A procedure might be needed to efficiently validate an incoming API key and retrieve the associated user's `ssoid`, `dept_unit_code`, and other details required for authorization checks on each API call.

## IV. Project Structure & Migration Considerations

*   All new backend code will reside in `hkbu-genai-platform/apps/api/`.
*   All new frontend code will reside in `hkbu-genai-platform/apps/web/`.
*   Carefully adapt logic from the `/gpt/` directory, refactoring it to fit the architecture and coding standards of the `hkbu-genai-platform` (e.g., if `apps/api` is NestJS, adapt to NestJS modules, services, controllers, guards, etc.).
*   Ensure environment variables for database connections, LLM provider keys, Redis, etc., are correctly configured for the `hkbu-genai-platform` applications.

## Mermaid Diagram (High-Level Flow):

```mermaid
sequenceDiagram
    participant User
    participant WebApp (apps/web)
    participant ApiService (apps/api)
    participant Database
    participant LLMProvider

    User->>WebApp: Navigates to /settings/api-service
    WebApp->>ApiService: GET /llm/config (fetch models & versions)
    ApiService->>Database: EXEC sp_model_GetModelList_v6 (filter api_status='A')
    Database-->>ApiService: Model/Version List
    ApiService-->>WebApp: Model/Version List
    WebApp->>User: Displays Settings Page (models, versions, API key gen button)

    User->>WebApp: Clicks "Generate API Key"
    WebApp->>ApiService: POST /auth/api-key (with user session)
    ApiService->>Database: EXEC sp_acl_GenUserApiKey (ssoid, new_key, ip)
    Database-->>ApiService: Success
    ApiService-->>WebApp: New API Key
    WebApp->>User: Displays New API Key

    User->>WebApp: Clicks an API Version (e.g., "2024-02-01")
    WebApp->>User: Navigates to /settings/api-service/docs/2024-02-01
    WebApp->>ApiService: GET /docs/2024-02-01/swagger.json
    ApiService->>Database: EXEC sp_model_GetModelList_v6 (for this version's models)
    Database-->>ApiService: Models for this version
    ApiService->>ApiService: Generates Swagger JSON (including all relevant endpoints)
    ApiService-->>WebApp: Swagger JSON for 2024-02-01
    WebApp->>User: Renders Swagger UI

    User->>LLM_Client_App: Makes API call (e.g., /2024-02-01/deployments/gpt-4/extensions/chat/completions with api-key)
    LLM_Client_App->>ApiService: API Request with api-key
    ApiService->>Database: Validate API Key & Get User Info
    Database-->>ApiService: User Info (ssoid, dept_unit_code)
    ApiService->>ApiService: Perform Authorization Check (using user info)
    alt Authorized
        ApiService->>ApiService: Route to correct endpoint handler (e.g. extensions chat)
        ApiService->>LLMProvider: Forward LLM Request
        LLMProvider-->>ApiService: LLM Response
        ApiService-->>LLM_Client_App: API Response
    else Not Authorized
        ApiService-->>LLM_Client_App: 403 Forbidden
    end