import CryptoJS from 'crypto-js';

// IMPORTANT: This secret key seems hardcoded. Consider moving it to environment variables for better security.
const secretPass = 'XkhZG4fW2t2W';

const encryptData = (text: unknown) => {
  // Changed type from any to unknown
  const data = CryptoJS.AES.encrypt(
    JSON.stringify(text),
    secretPass,
  ).toString();

  return data;
};

const decryptData = (text: string) => {
  try {
    const bytes = CryptoJS.AES.decrypt(text, secretPass);
    const data = JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
    return data;
  } catch (error) {
    console.error('Failed to decrypt data:', error);
    // Return null or handle the error appropriately, depending on expected usage.
    // Returning the original text might be a security risk if it wasn't properly encrypted.
    return null;
  }
};

export { encryptData, decryptData };
