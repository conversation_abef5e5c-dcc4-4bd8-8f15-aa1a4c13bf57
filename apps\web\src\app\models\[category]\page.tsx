'use client';

import React from 'react';
import { useParams } from 'next/navigation';
import Typography from '@mui/material/Typography';
import Container from '@mui/material/Container';
import Box from '@mui/material/Box';
// import ModelListSection from '@/components/genai/ModelListSection'; // We might reuse this or parts of it
// import { useGetModelsQuery } from '@/lib/store/apiSlice';
// import { GptModel } from '@/lib/types/common';

export default function CategoryPage() {
  const params = useParams();
  const category = params?.category as string;

  // TODO: Fetch models for this specific category
  // const { data: allModels = [], isLoading, error } = useGetModelsQuery();
  // const categoryModels = allModels.filter((model: GptModel) => model.category === category || model.display_name.toLowerCase().includes(category)); // Adjust filtering as needed

  return (
    <Container sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography
          variant="h4"
          component="h1"
          gutterBottom
          sx={{ textTransform: 'capitalize' }}
        >
          {category ? category.replace('-', ' ') : 'Models'}
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Browse all models available in the{' '}
          {category ? category.replace('-', ' ') : ''} category.
        </Typography>
      </Box>

      {/* TODO: Display models in a grid or list */}
      {/* <ModelListSection title="" models={categoryModels} onSelectModel={() => {}} isLoading={isLoading} error={error} /> */}
      <Typography>
        Model listing for category "{category}" will be implemented here.
      </Typography>
    </Container>
  );
}
