import type { Config } from 'tailwindcss';

const config: Config = {
  // Core plugins are explicitly enabled/disabled as needed
  // Preflight base styles are generally useful but check for conflicts with MUI
  // Removed invalid corePlugins configuration block
  // preflight: true, // Default is true, explicitly set to false if needed
  // Content paths ensure Tailwind scans the correct files for class usage
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  // Theme customization allows extending or overriding Tailwind's default design system
  theme: {
    extend: {
      // Example: Extending backgroundImage for custom gradients
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic':
          'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
      },
      // Add other theme customizations here (colors, spacing, fonts, etc.)
    },
  },
  // Plugins extend Tailwind's functionality
  plugins: [],
  // Important selector strategy for MUI compatibility
  // Using a class selector like '#__next' ensures Tailwind styles have higher specificity
  // than MUI's default styles when needed, preventing conflicts.
  // Adjust the selector based on your root element if necessary.
  // important: '#__next', // Temporarily remove/comment out to diagnose styling issue
};
export default config;
