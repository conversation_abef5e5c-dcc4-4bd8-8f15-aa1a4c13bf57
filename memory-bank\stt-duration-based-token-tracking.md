# STT Duration-Based Token Tracking Implementation

## Overview
Implemented comprehensive duration-based token tracking for the Speech-to-Text (STT) service, replacing inaccurate file-size estimation with actual recording duration tracking. The system now calculates tokens based on the formula: **1 second = 1 token**.

## Problem Solved

### Issues Before Implementation
1. **Inaccurate Token Calculation**: Used file-size estimation (`fileBuffer.length / 2000`) which severely overcharged users
   - A 45KB file would estimate 23 seconds when actual duration was ~4 seconds
   - Led to massive token overcharging and poor user experience

2. **API Compatibility Issue**: Azure OpenAI GPT-4o Mini transcription model doesn't support `verbose_json` response format
   - Error: `response_format 'verbose_json' is not compatible with model 'chatgpt-4o-mini-transcribe'. Use 'json' or 'text' instead`

3. **Missing Frontend Duration Tracking**: No mechanism to track actual recording time on frontend

4. **Inconsistent Token Limits**: Environment variables not aligned with new duration-based approach

## Implementation Details

### Backend Changes (`apps/api/src/general/speech/`)

#### 1. Fixed Azure OpenAI API Compatibility (`speech.service.ts`)
- **Changed response format**: `verbose_json` → `json` (line 105)
- **Removed token extraction**: Azure OpenAI transcription API doesn't provide token usage metadata
- **Simplified response handling**: Only extract `text` field from API response

#### 2. Enhanced Controller (`speech.controller.ts`)
```typescript
// Added recordingDuration parameter extraction from multipart form data
for await (const part of parts) {
  if (part.type === 'file' && part.fieldname === 'file') {
    fileBuffer = await part.toBuffer();
  } else if (part.type === 'field' && part.fieldname === 'recordingDuration') {
    recordingDuration = parseFloat(part.value as string) || 0;
  }
}
```

#### 3. Duration-Based Token Calculation (`speech.service.ts`)
```typescript
// Updated transcribeAudio method signature
async transcribeAudio(audioBuffer: Buffer, user: AuthenticatedUser, recordingDuration: number = 0)

// Duration-based token calculation (1 second = 1 token)
inputTokens = Math.ceil(recordingDuration); // Round up to ensure at least 1 token
outputTokens = 0; // Transcription is input-only, no output tokens
totalTokens = inputTokens;
```

#### 4. Conservative Fallback Logic
```typescript
// Handle missing recording duration
if (recordingDuration <= 0) {
  const conservativeFallback = 5; // Conservative 5-second fallback
  this.logger.warn(`Recording duration not provided by frontend. Using conservative ${conservativeFallback}s fallback`);
  recordingDuration = conservativeFallback;
}

// Validation for unreasonable duration values
if (recordingDuration > 300) { // 5 minutes max
  this.logger.warn(`Recording duration seems unusually long: ${recordingDuration}s`);
}
```

### Frontend Changes (`apps/web/src/components/genai/chat/ChatInputArea.tsx`)

#### 1. Recording Timer State Management
```typescript
// Recording Duration Tracking States
const [recordingStartTime, setRecordingStartTime] = React.useState<number | null>(null);
const [recordingDuration, setRecordingDuration] = React.useState<number>(0);
const recordingTimerRef = useRef<NodeJS.Timeout | null>(null);
```

#### 2. Timer Logic Implementation
```typescript
// Start recording timer
const startTime = Date.now();
setRecordingStartTime(startTime);
setRecordingDuration(0);

// Update duration every 100ms for smooth UI updates
recordingTimerRef.current = setInterval(() => {
  const elapsed = (Date.now() - startTime) / 1000; // Convert to seconds
  setRecordingDuration(elapsed);
  
  // Auto-stop after 5 minutes (300 seconds) to prevent excessive usage
  if (elapsed >= 300) {
    console.log('[STT] Auto-stopping recording after 5 minutes');
    stopBackendRecording();
  }
}, 100);
```

#### 3. Duration Display UI Component
```typescript
// Recording Duration Display
{isBackendRecording && (
  <Box
    sx={{
      display: 'flex',
      alignItems: 'center',
      minWidth: '50px',
      height: '24px',
      px: 1,
      backgroundColor: 'error.main',
      color: 'error.contrastText',
      borderRadius: 1,
      fontSize: '0.75rem',
      fontWeight: 'bold',
      fontFamily: 'monospace',
    }}
  >
    {formatRecordingDuration(recordingDuration)}
  </Box>
)}
```

#### 4. Backend Integration
```typescript
// Send recording duration to backend
const sendAudioToBackend = useCallback(async (audioBlob: Blob) => {
  const formData = new FormData();
  formData.append('file', audioBlob, 'recording.wav');
  
  // Add recording duration to form data
  const durationInSeconds = recordingDuration;
  formData.append('recordingDuration', durationInSeconds.toString());
  
  console.log(`[STT] Sending audio with duration: ${durationInSeconds} seconds`);
  // ... rest of API call
}, [recordingDuration, /* other deps */]);
```

### Environment Configuration Updates

#### Updated Token Limits
```bash
# Previous limits (removed)
# STT_STAFF_MONTHLY_TOKEN_LIMIT=5000000   
# STT_STUDENT_MONTHLY_TOKEN_LIMIT=2500000

# New duration-based limits (1 second = 1 token)
STT_STAFF_MONTHLY_TOKEN_LIMIT=4320000     # 4.32M seconds = 1,200 hours
STT_STUDENT_MONTHLY_TOKEN_LIMIT=2160000   # 2.16M seconds = 600 hours
```

#### Environment Variables Already Configured
- Variables exist in `gitlab/script/prepare-api-env.sh`
- TokenUsageService properly reads environment-based limits for `chatgpt-4o-mini-transcribe` model
- Takes precedence over database-configured limits

## Technical Specifications

### Token Calculation Formula
```
Input Tokens = Math.ceil(recordingDurationInSeconds)
Output Tokens = 0 (transcription is input-only)
Total Tokens = Input Tokens
```

### API Response Format Changes
**Before**: Attempted to use `verbose_json` format (caused errors)
```json
{
  "task": "transcribe",
  "language": "english", 
  "duration": 9.28,
  "text": "transcribed content",
  "usage": { "prompt_tokens": 4, "total_tokens": 4 }  // NOT SUPPORTED
}
```

**After**: Uses standard `json` format (works correctly)
```json
{
  "text": "transcribed content"
}
```

### Frontend-Backend Data Flow
1. **Frontend**: Records audio and tracks duration with timer
2. **Frontend**: Sends both audio file and duration in multipart form data
3. **Backend**: Extracts duration from form data or uses 5-second fallback
4. **Backend**: Calculates tokens as `Math.ceil(duration)` 
5. **Backend**: Records actual token usage in database

## Safety Features Implemented

### Frontend Safety
- **Auto-Stop**: Recordings automatically stop after 5 minutes
- **Memory Cleanup**: Proper timer cleanup on component unmount
- **Error Handling**: Timer cleanup on recording errors
- **Real-Time Updates**: Duration updates every 100ms for smooth UX

### Backend Safety  
- **Conservative Fallback**: 5-second fallback if duration not provided (vs. previous 23+ second overcharge)
- **Duration Validation**: Warns for durations >300s or <0.5s
- **Error Logging**: Comprehensive logging for debugging
- **Dual Provider Support**: Works with both Azure OpenAI and Google Cloud fallback

## User Experience Improvements

### Visual Feedback
- **Real-Time Timer**: Red badge showing MM:SS format (e.g., "01:23")
- **Enhanced Tooltip**: Shows duration in tooltip ("Stop Mic (01:23)")
- **Consistent Design**: Matches Material-UI theme with monospace font
- **Status Indicators**: Clear visual feedback during recording states

### Accurate Billing
- **No More Overcharging**: Eliminated file-size estimation errors
- **Fair Usage**: Users only charged for actual recording time
- **Transparent Limits**: Clear monthly hour limits (1,200h staff, 600h students)

## Files Modified

### Backend Files
1. `apps/api/src/general/speech/speech.service.ts`
   - Fixed response format compatibility
   - Implemented duration-based token calculation
   - Added comprehensive error handling and logging

2. `apps/api/src/general/speech/speech.controller.ts`
   - Added recordingDuration parameter extraction
   - Implemented duration validation and fallback logic

### Frontend Files
1. `apps/web/src/components/genai/chat/ChatInputArea.tsx`
   - Added recording timer state management
   - Implemented duration tracking and display UI
   - Enhanced backend integration with duration data

### Documentation Files
1. `CLAUDE.md` - Updated STT implementation details
2. `STT_ENVIRONMENT_CONFIG.md` - Created deployment documentation
3. `memory-bank/stt-duration-based-token-tracking.md` - This comprehensive record

## Testing Verification

### Backend Testing
- ✅ API accepts both file and recordingDuration parameters
- ✅ Duration-based token calculation works correctly
- ✅ Conservative fallback prevents overcharging
- ✅ Both Azure OpenAI and Google Cloud providers work
- ✅ Token usage properly recorded in database

### Frontend Testing  
- ✅ Recording timer starts/stops correctly
- ✅ Duration display updates in real-time
- ✅ Auto-stop after 5 minutes works
- ✅ Memory cleanup prevents leaks
- ✅ Form data properly sent to backend

### Integration Testing
- ✅ End-to-end flow from recording to token calculation
- ✅ Accurate token usage tracking in rate limit system
- ✅ Environment-based limits properly enforced

## Deployment Requirements

### Environment Variables
Deployment team must set these values:
```bash
STT_STAFF_MONTHLY_TOKEN_LIMIT=4320000
STT_STUDENT_MONTHLY_TOKEN_LIMIT=2160000
```

### No Database Changes Required
- Uses existing `acl_user_token_spent` table
- Leverages existing token usage infrastructure
- Backward compatible with existing data

## Key Learnings

1. **API Limitations**: Not all AI APIs provide token usage metadata - duration-based calculation is a valid alternative
2. **File Size ≠ Duration**: File size estimation for audio duration is extremely unreliable
3. **Frontend Timing**: Real-time duration tracking provides better UX and accurate billing
4. **Conservative Fallbacks**: Better to slightly undercharge than massively overcharge users
5. **Comprehensive Testing**: End-to-end testing crucial for accurate token tracking systems

## Future Considerations

1. **Audio Quality Impact**: Consider if audio quality/compression affects token calculation
2. **Multi-Language Support**: Verify token calculation works consistently across languages  
3. **Mobile Compatibility**: Test recording timer on mobile devices
4. **Analytics**: Consider tracking recording duration statistics for usage insights
5. **Rate Limiting**: Monitor if new limits are appropriate based on actual usage patterns

## Success Metrics

- **Accuracy**: Token calculation now reflects actual usage (1 second = 1 token)
- **User Experience**: Real-time duration feedback and fair billing
- **Technical Reliability**: No more API compatibility errors or file-size estimation failures
- **Cost Management**: Proper monthly limits enforce reasonable usage patterns
- **System Integration**: Seamless integration with existing rate limiting infrastructure

This implementation provides a robust, accurate, and user-friendly foundation for STT token tracking that can serve as a model for similar duration-based services.