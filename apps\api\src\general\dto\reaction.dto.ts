import { IsString, IsIn } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class ReactionDto {
  @ApiProperty({
    description: 'The message ID to update reaction for',
    example: 'msg-uuid-123',
  })
  @IsString()
  messageId!: string;

  @ApiProperty({
    description: 'Reaction type: 1 for like, 0 for dislike, -1 to clear',
    example: 1,
    enum: [-1, 0, 1],
  })
  @IsIn([-1, 0, 1])
  reaction!: number;
}