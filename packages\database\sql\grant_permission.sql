-- acl_other_user
GRANT SELECT, UPDATE, INSERT, DELETE ON dbo.acl_other_user TO chatgptdb_user;

-- acl_user
GRANT SELECT, UPDATE, INSERT, DELETE ON dbo.acl_user TO chatgptdb_user;

-- acl_user_api
GRANT SELECT, UPDATE, INSERT, DELETE ON dbo.acl_user_api TO chatgptdb_user;

-- acl_user_api_key
GRANT SELECT, UPDATE, INSERT, DELETE ON dbo.acl_user_api_key TO chatgptdb_user;

-- acl_user_daily_usage
GRANT SELECT, UPDATE, INSERT, DELETE ON dbo.acl_user_daily_usage TO chatgptdb_user;

-- acl_user_details
GRANT SELECT, UPDATE, INSERT, DELETE ON dbo.acl_user_details TO chatgptdb_user;

-- acl_user_model_access
GRANT SELECT, UPDATE, INSERT, DELETE ON dbo.acl_user_model_access TO chatgptdb_user;

-- acl_user_token_limit
GRANT SELECT, UPDATE, INSERT, DELETE ON dbo.acl_user_token_limit TO chatgptdb_user;

-- acl_user_token_spent
GRANT SELECT, UPDATE, INSERT, DELETE ON dbo.acl_user_token_spent TO chatgptdb_user;

-- api_footprint
GRANT SELECT, UPDATE, INSERT, DELETE ON dbo.api_footprint TO chatgptdb_user;

-- api_request_prompt
GRANT SELECT, UPDATE, INSERT, DELETE ON dbo.api_request_prompt TO chatgptdb_user;

-- api_response_prompt
GRANT SELECT, UPDATE, INSERT, DELETE ON dbo.api_response_prompt TO chatgptdb_user;

-- conversation
GRANT SELECT, UPDATE, INSERT, DELETE ON dbo.conversation TO chatgptdb_user;

-- edir_staff
GRANT SELECT, UPDATE, INSERT, DELETE ON dbo.edir_staff TO chatgptdb_user;

-- feedback
GRANT SELECT, UPDATE, INSERT, DELETE ON dbo.feedback TO chatgptdb_user;

-- log_rate_limit
GRANT SELECT, UPDATE, INSERT, DELETE ON dbo.log_rate_limit TO chatgptdb_user;

-- message
GRANT SELECT, UPDATE, INSERT, DELETE ON dbo.message TO chatgptdb_user;

-- prompt
GRANT SELECT, UPDATE, INSERT, DELETE ON dbo.prompt TO chatgptdb_user;

-- model_list
GRANT SELECT, UPDATE, INSERT, DELETE ON dbo.model_list TO chatgptdb_user;

-- mtr_dept
GRANT SELECT, UPDATE, INSERT, DELETE ON dbo.mtr_dept TO chatgptdb_user;

-- mtr_mapping_temperature
GRANT SELECT, UPDATE, INSERT, DELETE ON dbo.mtr_mapping_temperature TO chatgptdb_user;

-- notify
GRANT SELECT, UPDATE, INSERT, DELETE ON dbo.notify TO chatgptdb_user;

-- request_quote
GRANT SELECT, UPDATE, INSERT, DELETE ON dbo.request_quote TO chatgptdb_user;

-- sys_job
GRANT SELECT, UPDATE, INSERT, DELETE ON dbo.sys_job TO chatgptdb_user;

-- sys_job_status
GRANT SELECT, UPDATE, INSERT, DELETE ON dbo.sys_job_status TO chatgptdb_user;

-- sys_job_type
GRANT SELECT, UPDATE, INSERT, DELETE ON dbo.sys_job_type TO chatgptdb_user;

-- sys_sql_error
GRANT SELECT, UPDATE, INSERT, DELETE ON dbo.sys_sql_error TO chatgptdb_user;

-- tnc
GRANT SELECT, UPDATE, INSERT, DELETE ON dbo.tnc TO chatgptdb_user;