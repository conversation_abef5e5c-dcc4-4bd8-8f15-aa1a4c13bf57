# Homepage UI Bug Fixes - HKBU GenAI Platform

This document tracks critical UI bug fixes for the homepage functionality.

## Recent Model Button Selection Bug Fix

**Commit:** `0593cbe` - "fix: resolve recent model button selection issue"  
**Date:** July 29, 2025  
**Branch:** `39-fix-chat-window`  
**Files Modified:** `apps/web/src/app/page.tsx`

### Problem Description

**Issue:** When users clicked any recent model button on the home page, the first button would always be selected/highlighted instead of the button they actually clicked.

**User Impact:** 
- Users could not properly select their desired recent model
- Any model selection would default to the first model in the list
- This broke the core functionality of the recent model selection interface

### Root Cause Analysis

The bug was caused by a **race condition** between explicit model selection and auto-reselection logic:

1. User clicks a specific recent model button
2. `handleExplicitModelSelection` function is called with the correct model
3. The process of stripping @mention text from the input triggers an input change event
4. The input change event triggers auto-reselection logic
5. Auto-reselection logic selects the first recent model, overwriting the user's actual selection
6. Result: First model is always selected regardless of which button was clicked

### Technical Solution

#### 1. State to Ref Conversion
**Before:**
```typescript
const [isModelButtonClicked, setIsModelButtonClicked] = useState(false);
```

**After:**
```typescript
const isModelButtonClickedRef = useRef(false);
```

**Rationale:** React state updates are asynchronous, causing delays in checking the flag. Using `useRef` provides immediate, synchronous updates that can properly guard against race conditions.

#### 2. Button Key Fix
**Before:**
```typescript
key={model.model_name}
```

**After:**
```typescript
key={`${model.model_name}-${index}`}
```

**Rationale:** Ensures React properly tracks each button instance, preventing rendering issues that could contribute to incorrect selection behavior.

#### 3. Race Condition Prevention
**Implementation:**
```typescript
const handleExplicitModelSelection = useCallback((model: GptModel) => {
  // Set flag to prevent auto-reselection
  isModelButtonClickedRef.current = true;
  
  // Perform model selection logic
  // ...
  
  // Reset flag after delay
  setTimeout(() => {
    isModelButtonClickedRef.current = false;
  }, 500);
}, []);
```

**Guard in Auto-Selection Logic:**
```typescript
// Prevent any auto-reselection if user is actively clicking model buttons
if (isModelButtonClickedRef.current) {
  console.log('Skipping auto-reselection: model button click in progress');
  return;
}
```

#### 4. Enhanced Debug Logging
Added comprehensive logging to track:
- Model button click events
- Auto-reselection prevention
- State transitions
- Flag reset operations

### Key Implementation Details

**File:** `apps/web/src/app/page.tsx`

**Main Changes:**
1. **ModelSelectionBar Component:**
   - Updated `isModelButtonClickedRef` usage throughout
   - Fixed button key props for proper React tracking
   - Enhanced click event logging

2. **Selection Logic:**
   - `handleExplicitModelSelection` now sets the ref flag immediately
   - Auto-reselection logic checks the ref flag before proceeding
   - All references to the state variable updated to use `isModelButtonClickedRef.current`

3. **Timing Management:**
   - 500ms delay before resetting the flag to ensure all async operations complete
   - Proper cleanup of timeouts to prevent memory leaks

### Testing Validation

**Test Cases Verified:**
1. ✅ Clicking first recent model button selects first model
2. ✅ Clicking second recent model button selects second model  
3. ✅ Clicking third recent model button selects third model
4. ✅ @mention text is properly stripped after model selection
5. ✅ Auto-reselection works when user manually removes @mention text
6. ✅ No interference between explicit clicks and auto-reselection

### Future Considerations

1. **State Management:** Consider moving model selection logic to Redux for better predictability
2. **Component Architecture:** Separate model selection concerns into dedicated hooks
3. **Performance:** Monitor if the 500ms delay impacts user experience
4. **Error Handling:** Add fallback mechanisms if race conditions persist

### Related Components

- **ModelSelectionBar:** Recent model button rendering and selection
- **ChatInputArea:** Input field that triggers @mention processing
- **stripSelectedModelMentions:** Utility that processes @mention text removal

This fix ensures that users can reliably select any recent model button and have that specific model properly selected and highlighted in the interface.