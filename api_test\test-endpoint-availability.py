#!/usr/bin/env python3
"""
Simple test to verify API endpoints are available and return expected authentication errors.
This confirms the endpoints exist and use the same token limiting service.
"""

import requests
import json

def test_endpoint_availability():
    base_url = "http://localhost:3003"
    
    endpoints = [
        {
            "name": "Web UI Chat Completions",
            "url": f"{base_url}/api/v0/general/chat/completions",
            "method": "POST",
            "expected_status": [401, 403]  # Should require JWT auth
        },
        {
            "name": "REST API Chat Completions",
            "url": f"{base_url}/api/v0/rest/deployments/gpt-4o-mini/chat/completions",
            "method": "POST", 
            "expected_status": [401, 403]  # Should require API key
        }
    ]
    
    print("Testing API endpoint availability...")
    
    for endpoint in endpoints:
        print(f"\nTesting: {endpoint['name']}")
        print(f"URL: {endpoint['url']}")
        
        try:
            headers = {"Content-Type": "application/json"}
            payload = {"prompt": "test"} if "general/chat" in endpoint['url'] else {"messages": [{"role": "user", "content": "test"}]}
            
            response = requests.post(
                endpoint['url'], 
                headers=headers, 
                json=payload, 
                timeout=10
            )
            
            print(f"Status: {response.status_code}")
            print(f"Response: {response.text[:200]}...")
            
            if response.status_code in endpoint['expected_status']:
                print("RESULT: Endpoint exists and requires authentication (as expected)")
            elif response.status_code == 429:
                print("RESULT: Token limit enforced - this indicates the endpoint is working!")
            elif response.status_code == 406:
                print("RESULT: Old token limit status code - should be 429 after fix")
            else:
                print(f"RESULT: Unexpected status {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            print("ERROR: Cannot connect to server. Is the API running?")
        except Exception as e:
            print(f"ERROR: {e}")
    
    print("\nNOTE: Both endpoints should use the same ChatCompletionService")
    print("      so token limits should behave identically on both.")

if __name__ == "__main__":
    test_endpoint_availability()