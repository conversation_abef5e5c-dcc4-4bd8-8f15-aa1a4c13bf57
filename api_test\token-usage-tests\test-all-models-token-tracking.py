import requests
import json
import time
from datetime import datetime
from typing import Dict, List, Tuple, Optional

# Configuration
BASE_URL = "http://localhost:3003/api/v0"
API_KEY = "80b60025-07b4-4097-ba1d-e14695359dd0"

headers = {
    'Content-Type': 'application/json',
    'api-key': API_KEY
}

def load_models_config():
    """Load models configuration from config file"""
    try:
        with open('config/models.json', 'r') as f:
            config = json.load(f)
        return config['models']
    except Exception as e:
        print(f"Error loading models config: {e}")
        return []

def get_token_usage():
    """Get current token usage for all models"""
    url = f"{BASE_URL}/rest/rate-limit/token-usage"
    response = requests.get(url, headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        usage_dict = {}
        for usage in data.get('usage', []):
            usage_dict[usage['modelName']] = usage
        return usage_dict
    else:
        print(f"Error getting token usage: {response.status_code} - {response.text}")
        return {}

def test_rest_api_call(deployment_name: str, api_version: str = None) -> Tuple[bool, int, str]:
    """Test a REST API call and return success, tokens used, and response"""
    url = f"{BASE_URL}/rest/deployments/{deployment_name}/chat/completions"
    
    data = {
        "messages": [
            {"role": "system", "content": "You are a helpful assistant. Respond briefly."},
            {"role": "user", "content": f"Say 'Hello from {deployment_name}' in exactly 5 words."}
        ],
        "temperature": 0.7,
        "max_tokens": 20
    }
    
    # Use provided api_version or default
    params = {"api-version": api_version} if api_version else {"api-version": "2024-02-01"}
    
    try:
        response = requests.post(url, headers=headers, json=data, params=params, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            content = result['choices'][0]['message']['content']
            usage = result.get('usage', {})
            total_tokens = usage.get('total_tokens', 0)
            return True, total_tokens, content
        else:
            return False, 0, f"HTTP {response.status_code}: {response.text[:100]}"
    except requests.exceptions.Timeout:
        return False, 0, "Request timeout (30s)"
    except Exception as e:
        return False, 0, f"Error: {str(e)[:100]}"

def main():
    print("=" * 80)
    print("COMPREHENSIVE TOKEN TRACKING TEST - ALL MODELS")
    print("=" * 80)
    print(f"Timestamp: {datetime.now().isoformat()}")
    print()
    
    # Load models from config
    models = load_models_config()
    if not models:
        print("ERROR: No models loaded from config")
        return
    
    # Filter out embedding models
    chat_models = [m for m in models if m.get('model_type') != 'embedding']
    
    print(f"Found {len(chat_models)} chat models to test")
    print()
    
    # Get initial token usage
    print("Getting initial token usage...")
    initial_usage = get_token_usage()
    print(f"Found usage data for {len(initial_usage)} models")
    print()
    
    # Test results
    results = []
    
    # Test each model
    print("Testing each model...")
    print("-" * 80)
    
    for i, model in enumerate(chat_models, 1):
        model_name = model['name']
        deployment_name = model['deployment_name']
        api_version = model.get('api_version', '2024-02-01')
        
        print(f"\n[{i}/{len(chat_models)}] Testing {model_name}")
        print(f"  Deployment: {deployment_name}")
        print(f"  API Version: {api_version}")
        
        # Get initial usage for this model
        initial_model_usage = initial_usage.get(model_name, {})
        initial_tokens = initial_model_usage.get('totalTokensUsed', 0)
        
        # Make API call
        success, api_tokens, response = test_rest_api_call(deployment_name, api_version)
        
        if success:
            print(f"  SUCCESS: {response}")
            print(f"  API reported tokens: {api_tokens}")
        else:
            print(f"  FAILED: {response}")
        
        # Store result
        results.append({
            'model_name': model_name,
            'deployment_name': deployment_name,
            'success': success,
            'api_tokens': api_tokens,
            'initial_tokens': initial_tokens,
            'response': response
        })
    
    # Wait for all updates
    print("\n" + "-" * 80)
    print("Waiting 15 seconds for all token usage to be recorded...")
    time.sleep(15)
    
    # Get updated usage
    print("\nGetting updated token usage...")
    updated_usage = get_token_usage()
    
    # Analyze results
    print("\n" + "=" * 80)
    print("TOKEN TRACKING RESULTS")
    print("=" * 80)
    
    successful_tracking = 0
    failed_tracking = 0
    mismatched_tracking = 0
    
    for result in results:
        if not result['success']:
            continue
            
        model_name = result['model_name']
        deployment_name = result['deployment_name']
        api_tokens = result['api_tokens']
        initial_tokens = result['initial_tokens']
        
        print(f"\n{model_name} ({deployment_name}):")
        
        # Check if usage is tracked under model name
        if model_name in updated_usage:
            updated_tokens = updated_usage[model_name].get('totalTokensUsed', 0)
            difference = updated_tokens - initial_tokens
            
            print(f"  Model name tracking: {initial_tokens} -> {updated_tokens} (+{difference})")
            
            if difference == api_tokens:
                print(f"  STATUS: PERFECT - Tokens match API report")
                successful_tracking += 1
            elif difference > 0:
                print(f"  STATUS: PARTIAL - Tokens updated but mismatch (expected +{api_tokens})")
                mismatched_tracking += 1
            else:
                print(f"  STATUS: FAILED - No token update detected")
                failed_tracking += 1
        else:
            print(f"  Model name '{model_name}' not found in usage data")
            
        # Also check deployment name
        if deployment_name != model_name and deployment_name in updated_usage:
            deployment_tokens = updated_usage[deployment_name].get('totalTokensUsed', 0)
            print(f"  WARNING: Found usage under deployment name '{deployment_name}': {deployment_tokens} tokens")
            print(f"  This indicates model name normalization issue!")
    
    # Summary
    print("\n" + "=" * 80)
    print("SUMMARY")
    print("=" * 80)
    print(f"Total models tested: {len(chat_models)}")
    print(f"Successful API calls: {sum(1 for r in results if r['success'])}")
    print(f"Failed API calls: {sum(1 for r in results if not r['success'])}")
    print()
    print(f"Token tracking results (for successful calls):")
    print(f"  PERFECT tracking: {successful_tracking}")
    print(f"  PARTIAL tracking (mismatch): {mismatched_tracking}")
    print(f"  FAILED tracking: {failed_tracking}")
    
    # Check for duplicate entries
    print("\n" + "-" * 80)
    print("CHECKING FOR DUPLICATE ENTRIES...")
    
    model_to_deployment = {m['name']: m['deployment_name'] for m in chat_models}
    duplicates_found = False
    
    for model_name, deployment_name in model_to_deployment.items():
        if model_name != deployment_name:
            if model_name in updated_usage and deployment_name in updated_usage:
                duplicates_found = True
                model_tokens = updated_usage[model_name].get('totalTokensUsed', 0)
                deployment_tokens = updated_usage[deployment_name].get('totalTokensUsed', 0)
                print(f"\nDUPLICATE FOUND:")
                print(f"  Model: {model_name} = {model_tokens} tokens")
                print(f"  Deployment: {deployment_name} = {deployment_tokens} tokens")
    
    if not duplicates_found:
        print("\nNo duplicate entries found! Model name normalization is working correctly.")
    
    print("\n" + "=" * 80)
    print("Test completed!")
    print("=" * 80)

if __name__ == "__main__":
    main()