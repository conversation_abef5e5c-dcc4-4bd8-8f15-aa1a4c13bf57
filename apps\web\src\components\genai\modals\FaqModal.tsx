import { CSSProperties, Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { useSession } from 'next-auth/react'; // Keep next-auth

// Assuming CollapsibleContainer is in the same directory or adjust path
import CollapsibleContainer from '../ui/CollapsibleContainer'; // Import the actual component

// Define props more explicitly - though these might change when parent is refactored
interface FaqModalProps {
  show: boolean;
  onClose: () => void; // Will likely be replaced by dispatch(closeFaqModal()) in parent
  openTabFn: (tab: string) => void; // Keep prop for now
  // agree, agreeFn, closeFn might be redundant if onClose handles closing
}

const FaqModal = ({ show, onClose, openTabFn }: FaqModalProps) => {
  // Removed agree, agreeFn, closeFn if covered by onClose
  const { data: session } = useSession();
  // Add null check for session before accessing user type
  // Removed unused isStudent variable
  // Removed unused basePath variable

  const titleStyle: CSSProperties = {
    // backgroundColor:'#CCC'
    fontWeight: 'bold',
  };

  // Placeholder for missing CollapsibleContainer component
  const CollapsibleContainer: React.FC<{
    title: string;
    children: React.ReactNode;
    className?: string;
  }> = (
    { title, children }, // Added types for props
  ) => (
    <details className="border border-gray-300 rounded my-2">
      <summary className="font-semibold p-2 cursor-pointer">{title}</summary>
      <div className="p-2 border-t border-gray-300">{children}</div>
    </details>
  );

  return (
    <Transition.Root show={show}>
      <Dialog as="div" className="relative z-30" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 dark:bg-[#4a4a4aa0] transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0 ">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-5xl">
                <div className="bg-white dark:bg-[#3b3b3b] px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
                  <div className="sm:flex sm:items-start">
                    <div className="mt-3 text-left sm:ml-4 sm:mt-0 sm:text-left">
                      <Dialog.Title
                        as="h3"
                        className="text-lg font-semibold leading-6 text-gray-900 dark:text-white"
                      >
                        FAQ for HKBU GenAI Platform Service
                      </Dialog.Title>
                      <div className="my-4 text-sm text-gray-900 dark:text-white">
                        <ol className="pl-2 list-decimal">
                          <li style={titleStyle} className="pt-3">
                            <p style={titleStyle}>
                              Which models are available?
                            </p>
                          </li>
                          {/* Currently the OpenAI GPT-3.5 Turbo 16K and GPT-4 Turbo 128K models
                          are available, from Microsoft Azure OpenAI platform.
                          GPT-3's knowledge is up to September 2021, while GPT-4's knowledge extends up to April 2023.  */}
                          <div className="mx-auto p-3">
                            <div className="flex flex-col overflow-auto">
                              <CollapsibleContainer title="Azure OpenAI">
                                <table className="table-auto border-collapse border border-gray-400">
                                  <thead>
                                    <tr>
                                      <th className="border border-gray-300 p-2">
                                        Model
                                      </th>
                                      <th className="border border-gray-300 p-2">
                                        Token Limit
                                      </th>
                                      <th className="border border-gray-300 p-2">
                                        Knowledge Cut-off
                                      </th>

                                      <th className="border border-gray-300 p-2">
                                        <a href="#supportvision">
                                          Support Vision
                                        </a>
                                      </th>

                                      <th className="border border-gray-300 p-2">
                                        Service Provider
                                      </th>
                                      <th className="border border-gray-300 p-2">
                                        API Available
                                      </th>
                                      <th className="border border-gray-300 p-2">
                                        Characteristic
                                      </th>
                                    </tr>
                                  </thead>
                                  <tbody>
                                    {/*<tr>
                                  
                                    <td className="border border-gray-300 p-2">
                                    <a href="https://platform.openai.com/docs/models/gpt-4-turbo-and-gpt-4" target="_blank">GPT-4 Vision</a>
                                    </td>
                                    <td className="border border-gray-300 p-2">
                                    128K
                                    </td>
                                    <td className="border border-gray-300 p-2">
                                      April 2023
                                    </td>
                                   
                                    <td className="border border-gray-300 p-2">
                                      Yes
                                    </td>
                                   
                                    <td className="border border-gray-300 p-2">
                                      Microsoft Azure OpenAI
                                    </td>
                                    <td className="border border-gray-300 p-2">
                                      Yes
                                    </td>
                                  </tr> */}
                                    <tr>
                                      <td className="border border-gray-300 p-2">
                                        <a
                                          href="https://azure.microsoft.com/en-us/blog/introducing-gpt-4o-openais-new-flagship-multimodal-model-now-in-preview-on-azure/"
                                          target="_blank"
                                        >
                                          GPT-4o
                                        </a>
                                      </td>
                                      <td className="border border-gray-300 p-2">
                                        128K
                                      </td>

                                      <td className="border border-gray-300 p-2">
                                        Oct 2023
                                      </td>

                                      <td className="border border-gray-300 p-2">
                                        No
                                      </td>

                                      <td
                                        className="border border-gray-300 p-2"
                                        rowSpan={5}
                                      >
                                        Microsoft Azure OpenAI
                                      </td>
                                      <td className="border border-gray-300 p-2">
                                        Yes
                                      </td>
                                      <td className="border border-gray-300 p-2">
                                        Fast and capable of vision-based tasks
                                      </td>
                                    </tr>
                                    <tr>
                                      <td className="border border-gray-300 p-2">
                                        <a
                                          href="https://azure.microsoft.com/en-us/blog/openais-fastest-model-gpt-4o-mini-is-now-available-on-azure-ai/"
                                          target="_blank"
                                        >
                                          GPT-4o Mini
                                        </a>
                                      </td>
                                      <td className="border border-gray-300 p-2">
                                        128K
                                      </td>
                                      <td className="border border-gray-300 p-2">
                                        Oct 2023
                                      </td>

                                      <td className="border border-gray-300 p-2">
                                        Yes
                                      </td>

                                      <td className="border border-gray-300 p-2">
                                        Yes
                                      </td>
                                      <td className="border border-gray-300 p-2">
                                        Efficient for small tasks
                                      </td>
                                    </tr>
                                    <tr>
                                      <td className="border border-gray-300 p-2">
                                        <a
                                          href="https://azure.microsoft.com/en-us/blog/introducing-o1-openais-new-reasoning-model-series-for-developers-and-enterprises-on-azure/"
                                          target="_blank"
                                        >
                                          o1 (API Only)
                                        </a>
                                      </td>
                                      <td className="border border-gray-300 p-2">
                                        128K
                                      </td>
                                      <td className="border border-gray-300 p-2">
                                        Oct 2023
                                      </td>

                                      <td className="border border-gray-300 p-2">
                                        Yes
                                      </td>

                                      <td className="border border-gray-300 p-2">
                                        Yes
                                      </td>
                                      <td className="border border-gray-300 p-2">
                                        Model offer enhanced reasoning
                                        abilities.
                                      </td>
                                    </tr>
                                    <tr>
                                      <td className="border border-gray-300 p-2">
                                        <a
                                          href="https://azure.microsoft.com/en-us/blog/introducing-o1-openais-new-reasoning-model-series-for-developers-and-enterprises-on-azure/"
                                          target="_blank"
                                        >
                                          o1-Mini (API Only)
                                        </a>
                                      </td>
                                      <td className="border border-gray-300 p-2">
                                        128K
                                      </td>
                                      <td className="border border-gray-300 p-2">
                                        Oct 2023
                                      </td>

                                      <td className="border border-gray-300 p-2">
                                        Yes
                                      </td>

                                      <td className="border border-gray-300 p-2">
                                        Yes
                                      </td>
                                      <td className="border border-gray-300 p-2">
                                        Reasoning model with a smaller size,
                                        faster response time.
                                      </td>
                                    </tr>
                                    <tr>
                                      <td className="border border-gray-300 p-2">
                                        <a
                                          href="https://azure.microsoft.com/en-us/blog/announcing-the-availability-of-the-o3-mini-reasoning-model-in-microsoft-azure-openai-service/"
                                          target="_blank"
                                        >
                                          o3-Mini
                                        </a>
                                      </td>
                                      <td className="border border-gray-300 p-2">
                                        128K
                                      </td>
                                      <td className="border border-gray-300 p-2">
                                        Jul 2024
                                      </td>

                                      <td className="border border-gray-300 p-2">
                                        Yes
                                      </td>

                                      <td className="border border-gray-300 p-2">
                                        Yes
                                      </td>
                                      <td className="border border-gray-300 p-2">
                                        Enhanced reasoning model with reasoning
                                        effort control
                                      </td>
                                    </tr>
                                  </tbody>
                                </table>
                              </CollapsibleContainer>

                              <CollapsibleContainer title="Anthropic Claude 3">
                                <table className="table-auto border-collapse border border-gray-400">
                                  <thead>
                                    <tr>
                                      <th className="border border-gray-300 p-2">
                                        Model
                                      </th>

                                      <th className="border border-gray-300 p-2">
                                        Knowledge Cut-off
                                      </th>

                                      <th className="border border-gray-300 p-2">
                                        Support Vision
                                      </th>

                                      <th className="border border-gray-300 p-2">
                                        Service Provider
                                      </th>
                                      <th className="border border-gray-300 p-2">
                                        API Available
                                      </th>
                                      <th className="border border-gray-300 p-2">
                                        Characteristic
                                      </th>
                                    </tr>
                                  </thead>
                                  <tbody>
                                    <tr>
                                      <td className="border border-gray-300 p-2">
                                        <a
                                          href="https://www.anthropic.com/news/claude-3-5-sonnet"
                                          target="_blank"
                                        >
                                          Claude 3.5 Sonnect
                                        </a>
                                      </td>

                                      <td className="border border-gray-300 p-2">
                                        Aug 2023
                                      </td>

                                      <td className="border border-gray-300 p-2">
                                        Yes
                                      </td>

                                      <td
                                        className="border border-gray-300 p-2"
                                        rowSpan={2}
                                      >
                                        Google Cloud Platform
                                      </td>
                                      <td className="border border-gray-300 p-2">
                                        Yes
                                      </td>
                                      <td className="border border-gray-300 p-2">
                                        Fast and versatile
                                      </td>
                                    </tr>

                                    <tr>
                                      <td className="border border-gray-300 p-2">
                                        <a
                                          href="https://www.anthropic.com/news/claude-3-family"
                                          target="_blank"
                                        >
                                          Claude 3 Haiku
                                        </a>
                                      </td>

                                      <td className="border border-gray-300 p-2">
                                        Aug 2023
                                      </td>

                                      <td className="border border-gray-300 p-2">
                                        Yes
                                      </td>

                                      <td className="border border-gray-300 p-2">
                                        Yes
                                      </td>
                                      <td className="border border-gray-300 p-2">
                                        Fast and capable of handling small tasks
                                      </td>
                                    </tr>
                                  </tbody>
                                </table>
                              </CollapsibleContainer>

                              <CollapsibleContainer title="Google Gemini">
                                <table className="table-auto border-collapse border border-gray-400">
                                  <thead>
                                    <tr>
                                      <th className="border border-gray-300 p-2">
                                        Model
                                      </th>

                                      <th className="border border-gray-300 p-2">
                                        Knowledge Cut-off
                                      </th>

                                      <th className="border border-gray-300 p-2">
                                        Support Vision
                                      </th>

                                      <th className="border border-gray-300 p-2">
                                        Service Provider
                                      </th>
                                      <th className="border border-gray-300 p-2">
                                        API Available
                                      </th>
                                      <th className="border border-gray-300 p-2">
                                        Characteristic
                                      </th>
                                    </tr>
                                  </thead>
                                  <tbody>
                                    <tr>
                                      <td className="border border-gray-300 p-2">
                                        <a
                                          href="https://deepmind.google/technologies/gemini/pro/"
                                          target="_blank"
                                        >
                                          Gemini 1.5 Pro
                                        </a>
                                      </td>

                                      <td className="border border-gray-300 p-2">
                                        Nov 2023
                                      </td>

                                      <td className="border border-gray-300 p-2">
                                        Yes
                                      </td>

                                      <td
                                        className="border border-gray-300 p-2"
                                        rowSpan={2}
                                      >
                                        Google Cloud Platform
                                      </td>
                                      <td className="border border-gray-300 p-2">
                                        Yes
                                      </td>
                                      <td className="border border-gray-300 p-2">
                                        Good performance for complex tasks
                                      </td>
                                    </tr>

                                    <tr>
                                      <td className="border border-gray-300 p-2">
                                        <a
                                          href="https://deepmind.google/technologies/gemini/pro/"
                                          target="_blank"
                                        >
                                          Gemini 1.5 Pro Flash
                                        </a>
                                      </td>

                                      <td className="border border-gray-300 p-2">
                                        Nov 2023
                                      </td>

                                      <td className="border border-gray-300 p-2">
                                        Yes
                                      </td>

                                      <td className="border border-gray-300 p-2">
                                        Yes
                                      </td>
                                      <td className="border border-gray-300 p-2">
                                        Fast and versatile
                                      </td>
                                    </tr>
                                  </tbody>
                                </table>
                              </CollapsibleContainer>
                              <CollapsibleContainer title="Facebook Llama">
                                <table className="table-auto border-collapse border border-gray-400">
                                  <thead>
                                    <tr>
                                      <th className="border border-gray-300 p-2">
                                        Model
                                      </th>

                                      <th className="border border-gray-300 p-2">
                                        Knowledge Cut-off
                                      </th>

                                      <th className="border border-gray-300 p-2">
                                        Support Vision
                                      </th>

                                      <th className="border border-gray-300 p-2">
                                        Service Provider
                                      </th>
                                      <th className="border border-gray-300 p-2">
                                        API Available
                                      </th>
                                      <th className="border border-gray-300 p-2">
                                        Characteristic
                                      </th>
                                    </tr>
                                  </thead>
                                  <tbody>
                                    <tr>
                                      <td className="border border-gray-300 p-2">
                                        <a
                                          href="https://ai.meta.com/blog/meta-llama-3-1/"
                                          target="_blank"
                                        >
                                          Llama 3.1 405B
                                        </a>
                                      </td>

                                      <td className="border border-gray-300 p-2">
                                        Dec 2023
                                      </td>

                                      <td className="border border-gray-300 p-2">
                                        No
                                      </td>

                                      <td className="border border-gray-300 p-2">
                                        Google Cloud Platform
                                      </td>
                                      <td className="border border-gray-300 p-2">
                                        Yes
                                      </td>
                                      <td className="border border-gray-300 p-2">
                                        Optimized for multilingual dialogue
                                      </td>
                                    </tr>
                                  </tbody>
                                </table>
                              </CollapsibleContainer>
                              <CollapsibleContainer title="DeepSeek">
                                <table className="table-auto border-collapse border border-gray-400">
                                  <thead>
                                    <tr>
                                      <th className="border border-gray-300 p-2">
                                        Model
                                      </th>

                                      <th className="border border-gray-300 p-2">
                                        Knowledge Cut-off
                                      </th>

                                      <th className="border border-gray-300 p-2">
                                        Support Vision
                                      </th>

                                      <th className="border border-gray-300 p-2">
                                        Service Provider
                                      </th>
                                      <th className="border border-gray-300 p-2">
                                        API Available
                                      </th>
                                      <th className="border border-gray-300 p-2">
                                        Characteristic
                                      </th>
                                    </tr>
                                  </thead>
                                  <tbody>
                                    <tr>
                                      <td className="border border-gray-300 p-2">
                                        <a
                                          href="https://api-docs.deepseek.com/news/news250120"
                                          target="_blank"
                                        >
                                          DeepSeek-R1
                                        </a>
                                      </td>

                                      <td className="border border-gray-300 p-2">
                                        Jan 2025
                                      </td>

                                      <td className="border border-gray-300 p-2">
                                        No
                                      </td>

                                      <td className="border border-gray-300 p-2">
                                        Microsoft Azure
                                      </td>
                                      <td className="border border-gray-300 p-2">
                                        Yes
                                      </td>
                                      <td className="border border-gray-300 p-2">
                                        Exceptional Chinese language
                                        proficiency; strong reasoning
                                      </td>
                                    </tr>
                                    <tr>
                                      <td className="border border-gray-300 p-2">
                                        <a
                                          href="https://api-docs.deepseek.com/news/news1226"
                                          target="_blank"
                                        >
                                          DeepSeek-V3
                                        </a>
                                      </td>

                                      <td className="border border-gray-300 p-2">
                                        Jul 2024
                                      </td>

                                      <td className="border border-gray-300 p-2">
                                        No
                                      </td>

                                      <td className="border border-gray-300 p-2">
                                        Microsoft Azure
                                      </td>
                                      <td className="border border-gray-300 p-2">
                                        Yes
                                      </td>
                                      <td className="border border-gray-300 p-2">
                                        Exceptional Chinese language
                                        proficiency; strong Mixture-of-Experts
                                        language model
                                      </td>
                                    </tr>
                                  </tbody>
                                </table>
                              </CollapsibleContainer>
                              <CollapsibleContainer title="Qwen">
                                <table className="table-auto border-collapse border border-gray-400">
                                  <thead>
                                    <tr>
                                      <th className="border border-gray-300 p-2">
                                        Model
                                      </th>

                                      <th className="border border-gray-300 p-2">
                                        Knowledge Cut-off
                                      </th>

                                      <th className="border border-gray-300 p-2">
                                        Support Vision
                                      </th>

                                      <th className="border border-gray-300 p-2">
                                        Service Provider
                                      </th>
                                      <th className="border border-gray-300 p-2">
                                        API Available
                                      </th>
                                      <th className="border border-gray-300 p-2">
                                        Characteristic
                                      </th>
                                    </tr>
                                  </thead>
                                  <tbody>
                                    <tr>
                                      <td className="border border-gray-300 p-2">
                                        <a
                                          href="https://www.alibabacloud.com/help/en/model-studio/developer-reference/what-is-qwen-llm"
                                          target="_blank"
                                        >
                                          Qwen2.5-Max
                                        </a>
                                      </td>

                                      <td className="border border-gray-300 p-2">
                                        Dec 2024
                                      </td>

                                      <td className="border border-gray-300 p-2">
                                        No
                                      </td>

                                      <td className="border border-gray-300 p-2">
                                        Alibaba Cloud
                                      </td>
                                      <td className="border border-gray-300 p-2">
                                        Yes
                                      </td>
                                      <td className="border border-gray-300 p-2">
                                        Exceptional performance, complex
                                        reasoning, excels in Chinese
                                        understanding.
                                      </td>
                                    </tr>
                                    <tr>
                                      <td className="border border-gray-300 p-2">
                                        <a
                                          href="https://www.alibabacloud.com/help/en/model-studio/developer-reference/what-is-qwen-llm"
                                          target="_blank"
                                        >
                                          Qwen-Plus
                                        </a>
                                      </td>

                                      <td className="border border-gray-300 p-2">
                                        Dec 2024
                                      </td>

                                      <td className="border border-gray-300 p-2">
                                        No
                                      </td>

                                      <td className="border border-gray-300 p-2">
                                        Alibaba Cloud
                                      </td>
                                      <td className="border border-gray-300 p-2">
                                        Yes
                                      </td>
                                      <td className="border border-gray-300 p-2">
                                        Balanced performance, speed,
                                        cost-effective, strong Chinese language
                                        support.
                                      </td>
                                    </tr>
                                  </tbody>
                                </table>
                              </CollapsibleContainer>
                            </div>
                            <ul className="list-disc mt-2">
                              {/*<li>
                                APIs for LLM models are maintained for backward
                                compatibility.
                              </li>
                              {/* <li>
                                For access to APIs that are not generally
                                available, please send your request to{" "}
                                <a href="mailto:<EMAIL>">
                                  <EMAIL>.
                                </a>
                              </li> */}
                            </ul>
                          </div>
                          <li style={titleStyle} className="pt-3">
                            <p style={titleStyle}>
                              How to switch between AI models?
                            </p>
                          </li>
                          You can switch between models using the navigation bar
                          in the left menu.
                          <li style={titleStyle} className="pt-3">
                            <p style={titleStyle}>Is there any usage limit?</p>
                          </li>
                          Yes, to ensure fair use of University resources, a
                          monthly usage quota has been imposed to monitor
                          consumption patterns. For students, the token
                          allowance for each model within each calendar month is
                          listed below:
                          <div className="flex flex-col overflow-auto mt-4 mb-4">
                            <CollapsibleContainer title="Azure OpenAI">
                              <table className="table-auto border-collapse border border-gray-400">
                                <thead>
                                  <tr>
                                    <th className="border border-gray-300 p-2">
                                      GPT-4o
                                    </th>
                                    <th className="border border-gray-300 p-2">
                                      GPT-4o Mini
                                    </th>
                                    <th className="border border-gray-300 p-2">
                                      o1
                                    </th>
                                    <th className="border border-gray-300 p-2">
                                      o1-mini
                                    </th>
                                    <th className="border border-gray-300 p-2">
                                      o3-mini
                                    </th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <tr>
                                    <td className="border border-gray-300 p-2">
                                      500,000
                                    </td>
                                    <td className="border border-gray-300 p-2">
                                      8,000,000
                                    </td>
                                    <td className="border border-gray-300 p-2">
                                      100,000
                                    </td>
                                    <td className="border border-gray-300 p-2">
                                      500,000
                                    </td>
                                    <td className="border border-gray-300 p-2">
                                      500,000
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </CollapsibleContainer>

                            <CollapsibleContainer title="Anthropic">
                              <table className="table-auto border-collapse border border-gray-400">
                                <thead>
                                  <tr>
                                    <th className="border border-gray-300 p-2">
                                      Claude 3.5 Sonnet
                                    </th>
                                    <th className="border border-gray-300 p-2">
                                      Claude 3 Haiku
                                    </th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <tr>
                                    <td className="border border-gray-300 p-2">
                                      200,000
                                    </td>
                                    <td className="border border-gray-300 p-2">
                                      2,000,000
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </CollapsibleContainer>

                            <CollapsibleContainer title="Google Gemini">
                              <table className="table-auto border-collapse border border-gray-400">
                                <thead>
                                  <tr>
                                    <th className="border border-gray-300 p-2">
                                      Gemini 1.5 Pro
                                    </th>
                                    <th className="border border-gray-300 p-2">
                                      Gemini 1.5 Pro Flash
                                    </th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <tr>
                                    <td className="border border-gray-300 p-2">
                                      500,000
                                    </td>
                                    <td className="border border-gray-300 p-2">
                                      5,000,000
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </CollapsibleContainer>

                            <CollapsibleContainer title="Facebook Llama">
                              <table className="table-auto border-collapse border border-gray-400">
                                <thead>
                                  <tr>
                                    <th className="border border-gray-300 p-2">
                                      Facebook Llama 3.1 405b
                                    </th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <tr>
                                    <td className="border border-gray-300 p-2">
                                      200,000
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </CollapsibleContainer>

                            <CollapsibleContainer title="DeepSeek">
                              <table className="table-auto border-collapse border border-gray-400">
                                <thead>
                                  <tr>
                                    <th className="border border-gray-300 p-2">
                                      DeepSeek-R1
                                    </th>
                                    <th className="border border-gray-300 p-2">
                                      DeepSeek-V3
                                    </th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <tr>
                                    <td className="border border-gray-300 p-2">
                                      5,000,000
                                    </td>
                                    <td className="border border-gray-300 p-2">
                                      5,000,000
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </CollapsibleContainer>

                            <CollapsibleContainer title="Qwen">
                              <table className="table-auto border-collapse border border-gray-400">
                                <thead>
                                  <tr>
                                    <th className="border border-gray-300 p-2">
                                      Qwen2.5-Max
                                    </th>
                                    <th className="border border-gray-300 p-2">
                                      Qwen-Plus
                                    </th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <tr>
                                    <td className="border border-gray-300 p-2">
                                      1,500,000
                                    </td>
                                    <td className="border border-gray-300 p-2">
                                      8,000,000
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </CollapsibleContainer>
                          </div>
                          <p>
                            Tips: You can clear the conversation history before
                            sending a new message to reduce token usage.
                          </p>
                          <li style={titleStyle} className="pt-3">
                            <p style={titleStyle}>
                              What are &quot;tokens&quot; and how to calculate
                              them?
                            </p>
                          </li>
                          Please refer to{' '}
                          <a
                            href="https://help.openai.com/en/articles/4936856-what-are-tokens-and-how-to-count-them"
                            target="_blank"
                          >
                            this link
                          </a>{' '}
                          for an explanation of token from OpenAI GPT. For
                          models from other service providers, token calculation
                          is similar to that of GPT.
                          <li style={titleStyle} className="pt-3">
                            <p style={titleStyle}>
                              What is &quot;temperature&quot;?{' '}
                            </p>
                          </li>
                          Temperature in LLM Model controls the
                          &quot;creativity&quot; or randomness of the generated
                          response. A higher temperature (e.g., 0.8) results in
                          more diverse and creative output, while a lower
                          temperature (e.g., 0.3) makes the output more
                          deterministic and focused. You can refer to{' '}
                          <a
                            href="https://community.openai.com/t/cheat-sheet-mastering-temperature-and-top-p-in-chatgpt-api-a-few-tips-and-tricks-on-controlling-the-creativity-deterministic-output-of-prompt-responses/172683"
                            target="_blank"
                          >
                            this link
                          </a>{' '}
                          for more details.
                          <li
                            style={titleStyle}
                            className="pt-3"
                            id="supportvision"
                          >
                            <p style={titleStyle}>
                              What is the Vision feature, and what file formats
                              are accepted when uploading a document to the HKBU
                              GenAI Platform?
                            </p>

                            <p className="font-normal">
                              The Vision feature allows the AI to
                              &quot;see,&quot; &quot;recognize,&quot; and
                              &quot;understand&quot; images. This capability is
                              natively supported by the GPT-4o, o1, Gemini-1.5
                              Pro, Gemini-1.5 Flash, Claude 3.5 Sonnet and
                              Claude 3 Haiku.
                              <br />
                              For other models including GPT-4o Mini, Llama 3.1,
                              DeepSeek-R1, DeepSeek-V3, Qwen2.5-Max and
                              Qwen-Plus, which do not support Vision, we have
                              applied traditional OCR technology to retrieve
                              text from various file formats. The supported
                              formats include:
                            </p>

                            <div className="mx-auto px-3">
                              <ul className="list-disc font-normal ">
                                <li>
                                  Most image file formats (jpg, png, tif, bmp,
                                  etc.)
                                </li>
                                <li>
                                  Microsoft Office files: Word documents (doc,
                                  docx), Excel worksheets (xls, xlsx),
                                  PowerPoint slides (ppt, pptx)
                                </li>
                                <li>Adobe PDFs (pdf), Text files (txt)</li>
                              </ul>
                            </div>
                          </li>
                          <li style={titleStyle} className="pt-3">
                            <p style={titleStyle}>
                              Does HKBU GenAI Platform collect data and where
                              are the data stored?
                            </p>
                          </li>
                          Yes, data will be logged to ensure the proper and fair
                          use of University resources. The data is encrypted and
                          stored in the ITO data centre. Please refer to the{' '}
                          <a
                            className="cursor-pointer"
                            onClick={() => {
                              openTabFn('tnc');
                            }}
                          >
                            Terms and Conditions
                          </a>{' '}
                          for details.
                          <li style={titleStyle} className="pt-3">
                            <p style={titleStyle}>
                              Will the data be used for model training?
                            </p>
                          </li>
                          No, the data will not be used for model training.
                          <li style={titleStyle} className="pt-3">
                            <p style={titleStyle}>
                              Is the LLM Model API connectivity available?
                            </p>
                          </li>
                          Yes, the RESTful API connectivity is in place and is
                          currently being tested by a selected group of users.
                          Rate limit has imposed to optimize the resource usage.
                          <br></br>
                          If you have any enquiries about the rate limit, please
                          contact the ITO Service Centre (3411 7899,{' '}
                          <a href="mailto:<EMAIL>" target="_blank">
                            <EMAIL>
                          </a>
                          ) or use our online feedback form.
                          {/*
                          <li style={titleStyle} className="pt-3">
                            <p style={titleStyle}>
                              Is there any image generation model?
                            </p>
                          </li>
                          Yes, please refer <a className="cursor-pointer" onClick={()=>{openTabFn("image")}}>Image Generation</a> section for more
                          details.
                          */}
                        </ol>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="bg-gray-50 dark:bg-[#4a4a4a] px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                  <button
                    type="button"
                    className="mt-3 inline-flex w-full justify-center rounded-2xl bg-white dark:bg-[#3b3b3b] px-3 py-2 text-sm font-semibold text-gray-900 dark:text-white shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 dark:hover:bg-[#4a4a4a] sm:mt-0 sm:w-auto"
                    onClick={onClose} // Use the onClose prop passed from parent
                  >
                    Close
                  </button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
};

export default FaqModal;
