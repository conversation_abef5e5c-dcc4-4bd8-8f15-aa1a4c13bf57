-- =============================================
-- Author:      <PERSON><PERSON> (AI Assistant)
-- Create date: 2025-04-24
-- Description: Encrypts and inserts a single source record into dbo.message_source
--              associated with a specific message_id.
--              <PERSON><PERSON> key opening and closing internally.
-- =============================================
CREATE PROCEDURE [dbo].[sp_cvst_InsertMessageSource]
    @message_id INT,
    @title NVARCHAR(MAX),
    @link NVARCHAR(MAX),
    @snippet NVARCHAR(MAX),
    @encryption_key_name NVARCHAR(128),  -- Input parameter for key name
    @decryption_cert_name NVARCHAR(128) -- Input parameter for cert name (needed for OPEN KEY)
AS
BEGIN
    SET NOCOUNT ON;

    -- Basic validation
    IF @message_id IS NULL OR @title IS NULL OR @link IS NULL OR @snippet IS NULL OR @encryption_key_name IS NULL OR @decryption_cert_name IS NULL
    BEGIN
        RAISERROR('All parameters (@message_id, @title, @link, @snippet, @encryption_key_name, @decryption_cert_name) must be provided.', 16, 1);
        RETURN;
    END

    -- Check if the symmetric key exists
    IF NOT EXISTS (SELECT * FROM sys.symmetric_keys WHERE name = @encryption_key_name)
    BEGIN
        RAISERROR('Symmetric key specified by @encryption_key_name (''%s'') not found.', 16, 1, @encryption_key_name);
        RETURN;
    END

    -- Check if the certificate used for the key exists
    IF NOT EXISTS (SELECT * FROM sys.certificates WHERE name = @decryption_cert_name)
    BEGIN
        RAISERROR('Certificate specified by @decryption_cert_name (''%s'') for symmetric key not found.', 16, 1, @decryption_cert_name);
        RETURN;
    END

    BEGIN TRY
        -- Open the symmetric key
        DECLARE @OpenKeySQL NVARCHAR(MAX) = N'OPEN SYMMETRIC KEY ' + QUOTENAME(@encryption_key_name) + N' DECRYPTION BY CERTIFICATE ' + QUOTENAME(@decryption_cert_name) + N';';
        EXEC sp_executesql @OpenKeySQL;

        -- Encrypt and Insert
        INSERT INTO dbo.message_source
            (message_id, title_encrypted, link_encrypted, snippet_encrypted, create_dt)
        VALUES
            (
                @message_id,
                EncryptByKey(Key_GUID(@encryption_key_name), @title),
                EncryptByKey(Key_GUID(@encryption_key_name), @link),
                EncryptByKey(Key_GUID(@encryption_key_name), @snippet),
                GETDATE()
            );

        -- Close the symmetric key
        DECLARE @CloseKeySQL NVARCHAR(MAX) = N'CLOSE SYMMETRIC KEY ' + QUOTENAME(@encryption_key_name) + N';';
        EXEC sp_executesql @CloseKeySQL;

    END TRY
    BEGIN CATCH
        -- Ensure key is closed if an error occurred before closing
        DECLARE @CheckOpenKeySQL NVARCHAR(MAX) = N'IF EXISTS (SELECT * FROM sys.openkeys WHERE key_name = ''' + REPLACE(@encryption_key_name, '''', '''''') + N''') CLOSE SYMMETRIC KEY ' + QUOTENAME(@encryption_key_name) + N';';
        EXEC sp_executesql @CheckOpenKeySQL;

        -- Re-throw the error
        ;THROW;
    END CATCH

END
GO