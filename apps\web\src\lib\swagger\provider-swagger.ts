import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/authOptions';
import 'server-only';

export interface ProviderSwaggerConfig {
  name: string;
  displayName: string;
  title: string;
  description: string;
  chipLabels: {
    provider: string;
    version: string;
  };
}

export const providerSwaggerConfigs: Record<string, ProviderSwaggerConfig> = {
  gpt: {
    name: 'gpt',
    displayName: 'GPT',
    title: 'GPT API Documentation',
    description:
      'Comprehensive API documentation for GPT models provided through Azure OpenAI services.',
    chipLabels: {
      provider: 'Azure OpenAI',
      version: '2024-12-01-preview',
    },
  },
  qwen: {
    name: 'qwen',
    displayName: 'Qwen',
    title: 'Qwen API Documentation',
    description:
      'Comprehensive API documentation for Qwen models provided through Alibaba Cloud services.',
    chipLabels: {
      provider: 'Alibaba Cloud',
      version: 'v1',
    },
  },
  gemini: {
    name: 'gemini',
    displayName: 'Gemini',
    title: 'Gemini API Documentation',
    description:
      'Comprehensive API documentation for Gemini models provided through Google Vertex AI services.',
    chipLabels: {
      provider: 'Google Vertex AI',
      version: 'Latest',
    },
  },
  llama: {
    name: 'llama',
    displayName: 'Llama',
    title: 'Llama API Documentation',
    description:
      'Comprehensive API documentation for Llama models provided through Google Vertex AI services.',
    chipLabels: {
      provider: 'Google Vertex AI',
      version: '20240723',
    },
  },
  deepseek: {
    name: 'deepseek',
    displayName: 'DeepSeek',
    title: 'DeepSeek API Documentation',
    description:
      'Comprehensive API documentation for DeepSeek models provided through DeepSeek AI services.',
    chipLabels: {
      provider: 'DeepSeek AI',
      version: '2024-05-01-preview',
    },
  },
};

export const getProviderApiDocs = async (providerName: string) => {
  const session = await getServerSession(authOptions);

  if (!session) {
    throw new Error('Unauthorized');
  }

  const basePath = process.env.NEXT_PUBLIC_BASE_PATH || '';
  const apiBaseUrl =
    process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3003';

  // Construct the API endpoint for the provider
  const endpoint = `${apiBaseUrl}/general/rest/docs/${providerName}-swagger.json`;

  // Debug logging
  console.log(`[${providerName}] Fetching swagger spec from:`, endpoint);
  console.log(`[${providerName}] API Base URL:`, apiBaseUrl);
  console.log(
    `[${providerName}] Access Token:`,
    session.user.accessToken ? 'Present' : 'Missing',
  );

  try {
    const response = await fetch(endpoint, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${session.user.accessToken}`,
      },
    });

    console.log(`[${providerName}] Response status:`, response.status);
    console.log(`[${providerName}] Response ok:`, response.ok);

    if (!response.ok) {
      const responseText = await response.text();
      console.error(`[${providerName}] Response body:`, responseText);
      throw new Error(
        `Failed to fetch ${providerName} swagger spec: ${response.status} - ${responseText}`,
      );
    }

    const spec = await response.json();
    return spec;
  } catch (error) {
    console.error(`Error fetching ${providerName} swagger spec:`, error);
    throw error;
  }
};
