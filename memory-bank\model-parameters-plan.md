# Model Parameters Feature Implementation Plan

This document outlines the plan to implement the feature allowing users to modify model parameters (instructions, history count, etc.) per conversation.

**User Interface:**

*   A new "Model Parameters" button (e.g., `<TuneIcon />`) will be added to the `ChatHeader` component (top-right).
*   Clicking this button opens a modal (`ModelParametersModal.tsx`).
*   The modal displays fields for:
    *   Model Instructions/Context (Text Area)
    *   Past Messages Included (Slider + Input)
    *   Max Response Tokens (Slider + Input)
    *   Temperature (Slider + Input)
    *   Top P (Slider + Input)
*   A "Save" button persists the changes for the current conversation.

**Persistence:**

*   Parameters are saved specifically for the *current conversation*.
*   New fields will be added to the `Conversation` table in the database.

**Plan Phases:**

**Phase 1: Backend Modifications**

1.  **Database Schema Update (`schema.prisma`):**
    *   Add optional fields to `Conversation` model: `instructions` (String?), `pastMessagesCount` (Int?), `maxResponseTokens` (Int?), `temperature` (Float?), `topP` (Float?).
    *   Generate and apply migration.
2.  **API DTO Update (`UpdateConversationParamsDto.ts`):**
    *   Create DTO for saving parameters: `chat_session_id` (String), `instructions` (String?), `pastMessagesCount` (Int?), `maxResponseTokens` (Int?), `temperature` (Float?), `topP` (Float?).
3.  **API Service Update (`chat-completion.service.ts`):**
    *   Modify chat completion logic:
        *   Fetch `Conversation` record by `chat_session_id`.
        *   Retrieve saved parameters.
        *   Use parameters when constructing LLM request (system prompt, history limit, etc.). Apply defaults if null.
4.  **API Controller & Module Update (`general.controller.ts`, `general.module.ts`):**
    *   Add `PATCH /general/chat/parameters` endpoint.
    *   Use `UpdateConversationParamsDto`.
    *   Implement controller method to call service for updating DB parameters.

**Phase 2: Frontend Modifications**

1.  **State Management (Redux - `chatSlice.ts` or new slice):**
    *   Add state variables for current conversation parameters.
    *   Add actions/reducers to update state.
    *   Update `getHistoryMessages` in `apiSlice.ts` to fetch/populate parameters.
2.  **API Client (`apiSlice.ts`):**
    *   Add `updateConversationParams` mutation calling the new backend endpoint.
3.  **UI Component (`ChatHeader.tsx`):**
    *   Add `IconButton` to open the modal.
    *   Manage modal open/closed state.
4.  **UI Component (New - `ModelParametersModal.tsx`):**
    *   Create modal dialog using MUI.
    *   Populate form with current parameters from Redux on open.
    *   On "Save": call `updateConversationParams` mutation, update Redux state, close modal. Handle errors.
5.  **Chat Page (`[chatId]/page.tsx`):**
    *   Ensure parameters are fetched on load.
    *   Ensure modal can be triggered from header.

**Data Flow Diagram (Mermaid):**

```mermaid
sequenceDiagram
    participant User
    participant ChatHeader
    participant ModelParamsModal
    participant ReduxStore
    participant ApiClient (RTK Query)
    participant BackendAPI
    participant Database

    User->>ChatHeader: Clicks 'Parameters' Icon
    ChatHeader->>ModelParamsModal: Open Modal
    ModelParamsModal->>ReduxStore: Get current params for chatId
    ReduxStore-->>ModelParamsModal: Return params (instructions, count, etc.)
    ModelParamsModal-->>User: Display current params in form
    User->>ModelParamsModal: Modifies parameters
    User->>ModelParamsModal: Clicks 'Save'
    ModelParamsModal->>ApiClient (RTK Query): Call updateConversationParams mutation (chatId, newParams)
    ApiClient (RTK Query)->>BackendAPI: PATCH /general/chat/parameters
    BackendAPI->>Database: Find Conversation by chatId & Update params
    Database-->>BackendAPI: Confirm Update
    BackendAPI-->>ApiClient (RTK Query): Return Success/Error
    ApiClient (RTK Query)->>ReduxStore: Update state with new params on success
    ApiClient (RTK Query)-->>ModelParamsModal: Notify Success/Error
    ModelParamsModal->>ChatHeader: Close Modal on Success

    %% Subsequent Chat Completion %%
    User->>ChatInputArea: Enters prompt, clicks Send
    ChatInputArea->>ChatPage: handleSubmit()
    ChatPage->>ApiClient (RTK Query): Call chatCompletion mutation (chatId, prompt)
    ApiClient (RTK Query)->>BackendAPI: POST /general/chat/completions (chatId, prompt)
    BackendAPI->>Database: Find Conversation (incl. saved params) & Get history (limited by pastMessagesCount)
    Database-->>BackendAPI: Return Conversation & Messages
    BackendAPI->>LLM API: Send request (using saved instructions, limited history, etc.)
    LLM API-->>BackendAPI: Receive response
    BackendAPI-->>ApiClient (RTK Query): Stream response
    ApiClient (RTK Query)->>ReduxStore: Update messages
    ReduxStore-->>ConversationDisplay: Update UI