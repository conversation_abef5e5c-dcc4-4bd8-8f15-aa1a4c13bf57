'use client';

import React from 'react';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Link from '@mui/material/Link';

// Placeholder: In a real scenario, you might get whitelisted status from session or context
// const { data: session } = useSession();
// const isWhitelisted = session?.user?.name && process.env.NEXT_PUBLIC_WHITELIST_LLM_HEALTH_CHECK?.split(",").map(id => id.trim().toUpperCase()).includes(session.user.name.toUpperCase());

const LlmHealthCheckSection: React.FC = () => {
  // For now, assume the user might be whitelisted to show relevant info.
  // A more robust implementation would check actual whitelist status.
  const isWhitelisted = true;

  return (
    <Box className="text-sm" sx={{ color: 'text.primary' }}>
      <Typography variant="body2" paragraph>
        The LLM Health Check provides real-time status information for the
        various Large Language Models integrated into the HKBU GenAI Platform.
        This helps in understanding model availability and performance.
      </Typography>
      {isWhitelisted ? (
        <Typography variant="body2" paragraph>
          Authorized users can typically access a dedicated interface to view
          detailed health statuses, response times, and refresh checks for each
          model. This section provides general information about the feature. If
          a live health check dashboard is available, it would be accessed
          separately.
        </Typography>
      ) : (
        <Typography variant="body2" paragraph>
          Access to the detailed LLM Health Check dashboard is typically
          restricted. This section provides general information about the
          feature.
        </Typography>
      )}
      <Typography variant="body2" paragraph>
        Key aspects monitored usually include:
      </Typography>
      <ul className="list-disc pl-5">
        <li>
          <Typography variant="body2">
            Model Status (Active, Unstable, Down)
          </Typography>
        </li>
        <li>
          <Typography variant="body2">Response Time</Typography>
        </li>
        <li>
          <Typography variant="body2">Error Messages (if any)</Typography>
        </li>
      </ul>
      <Typography variant="body2" paragraph sx={{ mt: 2 }}>
        If you are experiencing issues with a specific model, checking its
        status here (or on a dedicated dashboard) would be the first step. For
        persistent issues, please contact support via the "Contact Us" section.
      </Typography>
    </Box>
  );
};

export default LlmHealthCheckSection;
