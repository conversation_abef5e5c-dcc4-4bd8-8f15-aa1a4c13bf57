/**
 * Utility functions for handling @mention functionality
 */

/**
 * Strips selected model mentions from message content
 * This only removes model names that were actually selected via the @mention dropdown,
 * not model names that were just typed in.
 *
 * @param messageContent - The full message content
 * @param selectedModelMentions - Map of model names to their positions in the text
 * @returns The message content with selected model mentions stripped
 */
export function stripSelectedModelMentions(
  messageContent: string,
  selectedModelMentions: Map<string, number[]>,
  availableModels: Array<{ model_name: string; display_name?: string }>,
): string {
  if (
    !messageContent ||
    selectedModelMentions.size === 0 ||
    !availableModels
  ) {
    return messageContent;
  }

  // Create an array of all mentions to remove, sorted by position (descending)
  // so we can remove them without affecting the positions of earlier mentions
  const mentionsToRemove: { start: number; end: number; modelName: string }[] =
    [];

  selectedModelMentions.forEach((positions, modelName) => {
    const model = availableModels.find((m) => m.model_name === modelName);
    // If we can't find the model, we can't determine what to strip, so we skip.
    if (!model) {
      return;
    }

    const mentionText = `@${model.display_name || model.model_name}`;

    positions.forEach((startPos) => {
      // Check if the mention text actually exists at the recorded position
      if (
        startPos >= 0 &&
        messageContent.substring(startPos, startPos + mentionText.length) ===
          mentionText
      ) {
        const endPos = startPos + mentionText.length;
        // Check if there's a space after the mention and include it in the removal
        const hasSpaceAfter =
          endPos < messageContent.length && messageContent[endPos] === ' ';
        mentionsToRemove.push({
          start: startPos,
          end: hasSpaceAfter ? endPos + 1 : endPos,
          modelName: mentionText, // Use the actual mention text for debugging
        });
      }
    });
  });

  // Sort by start position in descending order so we can remove from the end first
  mentionsToRemove.sort((a, b) => b.start - a.start);

  // Remove each mention
  let result = messageContent;
  mentionsToRemove.forEach((mention) => {
    result = result.substring(0, mention.start) + result.substring(mention.end);
  });

  // Clean up any extra spaces that might be left
  return result.trim().replace(/\s+/g, ' ');
}

/**
 * Strips ALL @model mentions from message content before sending to API
 * This function uses the tracked mention positions to accurately remove mentions,
 * including those with spaces in the model names.
 *
 * @param messageContent - The full message content
 * @param selectedModelMentions - Map of model names to their positions in the text
 * @param availableModels - List of available models to match display names
 * @returns The message content with all @model mentions stripped
 */
export function stripAllModelMentions(
  messageContent: string,
  selectedModelMentions: Map<string, number[]> = new Map(),
  availableModels: Array<{ model_name: string; display_name?: string }> = [],
): string {
  if (!messageContent) {
    return messageContent;
  }

  // If we have tracked mentions, use the position-based approach (most accurate)
  if (selectedModelMentions.size > 0) {
    return stripSelectedModelMentions(
      messageContent,
      selectedModelMentions,
      availableModels,
    );
  }

  // Fallback: Find all @mentions and try to match them with available models
  // This handles cases where we don't have position tracking
  let result = messageContent;
  
  // Create a map of potential @mentions by scanning for @ symbols
  const mentionPositions: { start: number; end: number; text: string }[] = [];
  
  for (let i = 0; i < messageContent.length; i++) {
    if (messageContent[i] === '@') {
      // Found a potential mention, now try to match it with available models
      for (const model of availableModels) {
        const displayName = model.display_name || model.model_name;
        const mentionText = `@${displayName}`;
        
        if (messageContent.substring(i, i + mentionText.length) === mentionText) {
          // Check if this is a word boundary (not part of a larger word)
          const nextChar = messageContent[i + mentionText.length];
          if (!nextChar || /\s/.test(nextChar)) {
            mentionPositions.push({
              start: i,
              end: i + mentionText.length,
              text: mentionText,
            });
            break; // Found a match, don't check other models for this position
          }
        }
      }
    }
  }

  // Sort by start position in descending order and remove mentions
  mentionPositions.sort((a, b) => b.start - a.start);
  
  for (const mention of mentionPositions) {
    result = result.substring(0, mention.start) + result.substring(mention.end);
  }

  // Clean up any extra spaces that might be left
  return result.trim().replace(/\s+/g, ' ');
}

/**
 * Extracts model names from @mentions in the message content
 * @param messageContent - The message content to parse
 * @param availableModels - List of available models to match against
 * @returns The model name if found in mentions, undefined otherwise
 */
export function extractModelFromMentions(
  messageContent: string,
  availableModels: Array<{ model_name: string; display_name?: string }>,
): string | undefined {
  if (!messageContent || !availableModels || availableModels.length === 0) {
    return undefined;
  }

  // Find all @mentions in the text
  const mentionRegex = /@(\S+)/g;
  const mentions: string[] = [];
  let match;

  while ((match = mentionRegex.exec(messageContent)) !== null) {
    mentions.push(match[1]); // Get the text after @
  }

  // Find the last mention that matches an available model
  // We search from the end to get the most recent selection
  for (let i = mentions.length - 1; i >= 0; i--) {
    const mentionText = mentions[i];
    
    // Try to find a matching model
    const matchedModel = availableModels.find(
      (model) =>
        model.model_name === mentionText ||
        model.display_name === mentionText ||
        model.model_name?.toLowerCase() === mentionText.toLowerCase() ||
        model.display_name?.toLowerCase() === mentionText.toLowerCase(),
    );

    if (matchedModel) {
      return matchedModel.model_name;
    }
  }

  return undefined;
}

/**
 * Checks if a model name appears in the text as a selected mention
 * @param messageContent - The message content to check
 * @param modelName - The model name to look for
 * @param selectedModelMentions - Map of selected model mentions
 * @returns true if the model name appears as a selected mention
 */
export function hasSelectedMention(
  messageContent: string,
  modelName: string,
  selectedModelMentions: Map<string, number[]>,
): boolean {
  const positions = selectedModelMentions.get(modelName);
  if (!positions || positions.length === 0) {
    return false;
  }

  return positions.some((pos) => {
    const textAtPosition = messageContent.substring(
      pos,
      pos + modelName.length,
    );
    return textAtPosition === modelName;
  });
}
