import {
  Injectable,
  Logger,
  InternalServerErrorException,
  HttpException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { getHongKongTime, getHongKongDateOnly } from '../common/utils/timezone.util';
import { Readable, PassThrough } from 'stream';
import { LlmStreamOptions } from './llm.service';
import { ChatOpenAI } from '@langchain/openai';
import {
  AIMessage,
  HumanMessage,
  SystemMessage,
  BaseMessage,
  AIMessageChunk,
} from '@langchain/core/messages';

@Injectable()
export class AlibabaLlmService {
  private readonly logger = new Logger(AlibabaLlmService.name);
  private apiKey: string | undefined;
  private openAICompatibleBaseUrl: string | undefined;

  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
  ) {
    this.apiKey = this.configService.get<string>('ALIBABA_MODELSTUDIO_API_KEY');
    const configuredEndpoint = this.configService.get<string>(
      'ALIBABA_MODELSTUDIO_ENDPOINT',
    );
    if (configuredEndpoint) {
      this.openAICompatibleBaseUrl = `${configuredEndpoint.replace(/\/$/, '')}/compatible-mode/v1`;
    }

    if (!this.apiKey) {
      this.logger.error(
        'ALIBABA_MODELSTUDIO_API_KEY not found in environment variables!',
      );
    }
    if (!configuredEndpoint) {
      this.logger.error(
        'ALIBABA_MODELSTUDIO_ENDPOINT not found in environment variables!',
      );
    } else if (!this.openAICompatibleBaseUrl) {
      // This case should ideally not be hit if configuredEndpoint is present, but as a safeguard:
      this.logger.error(
        'Failed to construct OpenAI compatible base URL from ALIBABA_MODELSTUDIO_ENDPOINT.',
      );
    } else {
      this.logger.log(
        'AlibabaLlmService initialized for OpenAI compatible API.',
      );
    }
  }

  private transformMessagesToLangchain(
    messages: LlmStreamOptions['messages'],
  ): BaseMessage[] {
    return messages
      .map((msg) => {
        const content =
          typeof msg.content === 'string'
            ? msg.content
            : JSON.stringify(msg.content);
        switch (msg.role) {
          case 'system':
            return new SystemMessage(content);
          case 'user':
            return new HumanMessage(content);
          case 'assistant':
          case 'model':
            return new AIMessage(content);
          default:
            this.logger.warn(
              `Unknown message role '${msg.role}', defaulting to HumanMessage.`,
            );
            return new HumanMessage(content);
        }
      })
      .filter((msg) => msg.content);
  }

  async createChatCompletionStream(
    options: LlmStreamOptions,
  ): Promise<Readable> {
    const {
      modelConfig,
      messages,
      temperature,
      conversationId,
      dialogId,
      userMessageId,
      user,
    } = options;

    this.logger.log(
      `Calling Alibaba Tongyi model ${modelConfig.model_name} for user ${user.userId} via OpenAI compatible API`,
    );

    if (!this.apiKey) {
      throw new InternalServerErrorException(
        'Alibaba API Key is not configured.',
      );
    }
    if (!this.openAICompatibleBaseUrl) {
      throw new InternalServerErrorException(
        'Alibaba OpenAI compatible base URL is not configured.',
      );
    }

    const langchainMessages = this.transformMessagesToLangchain(messages);

    const chat = new ChatOpenAI({
      apiKey: this.apiKey,
      modelName: modelConfig.model_name,
      temperature: temperature ?? 0.8,
      streaming: true,
      maxTokens: modelConfig.max_tokens, // Assuming max_tokens is part of modelConfig
      // For token usage in stream, as per Alibaba docs for OpenAI SDK:
      // stream_options: { include_usage: true }
      // For ChatOpenAI, this might be passed in .stream() options or a specific property
      // Langchain's ChatOpenAI might handle this differently or it might be specific to the Python SDK.
      // We will try to get usage from the final chunk if available.
      // Alternatively, set `streamUsage: true` if supported by the JS version for this provider.
      // For now, we'll rely on the last chunk or calculate manually if necessary.
      configuration: {
        baseURL: this.openAICompatibleBaseUrl,
        // Note: The exact way to pass stream_options for usage in JS might differ or not be directly supported
        // for all providers through ChatOpenAI. This is an attempt based on common patterns.
        // If this doesn't work, token counting might need to rely on the last event or manual calculation.
        // The Alibaba documentation specifically shows `stream_options={"include_usage": True}` for their Python OpenAI SDK example.
        // For LangchainJS, `usage_metadata` on the AIMessageChunk is the more common way this is exposed if the LLM provider supports it.
        // Let's try to set a property that might be picked up, or rely on `usage_metadata` later.
        // streamUsage: true, // This is a common LangchainJS property for some models
        // Attempting to pass stream_options as per Python SDK example, though it might not be directly supported here
        // It's more likely that `streamUsage: true` at the top level of ChatOpenAI or relying on `chunk.usage_metadata` is the way.
        // For now, we keep baseURL here and will rely on usage_metadata from the stream.
      },
    });
    // A more direct way if `streamUsage` is a top-level option for ChatOpenAI:
    // const chat = new ChatOpenAI({ ..., streamUsage: true });

    const outputStream = new PassThrough();
    let fullResponseText = '';
    let completionTokens = 0;
    let promptTokens = 0;
    let errorOccurred = false;

    try {
      const stream = await chat.stream(langchainMessages);
      (async () => {
        for await (const chunk of stream) {
          if (chunk instanceof AIMessageChunk) {
            const content = chunk.content;
            if (typeof content === 'string') {
              fullResponseText += content;
              outputStream.write(content);
            }
            // Check for usage data in the chunk, especially the last one
            // Langchain JS often uses usage_metadata for stream token counts
            if (chunk.usage_metadata) {
              promptTokens = chunk.usage_metadata.input_tokens ?? promptTokens;
              completionTokens =
                chunk.usage_metadata.output_tokens ?? completionTokens;
            } else if (chunk.response_metadata?.tokenUsage) {
              // Fallback for other potential structures
              promptTokens =
                chunk.response_metadata.tokenUsage.promptTokens ?? promptTokens;
              completionTokens =
                chunk.response_metadata.tokenUsage.completionTokens ??
                completionTokens;
            }
          }
        }
        this.logger.log('Alibaba stream via ChatOpenAI ended.');
        outputStream.end();
        if (!errorOccurred) {
          // If tokens weren't updated by the stream, they might be 0 here.
          // The Alibaba OpenAI compatible API should return usage in the last stream event if include_usage is effective.
          await this.logAssistantMessage(
            conversationId,
            dialogId,
            userMessageId,
            modelConfig.model_name,
            temperature,
            fullResponseText,
            promptTokens,
            completionTokens,
            user.userId,
          );
          if (promptTokens > 0) {
            // Only update if we got prompt tokens
            await this.updateUserMessageTokens(userMessageId, promptTokens);
          }
        }
      })().catch((streamError) => {
        const message =
          streamError instanceof Error
            ? streamError.message
            : 'Unknown stream processing error';
        this.logger.error(
          `Error processing Alibaba stream from ChatOpenAI: ${message}`,
          streamError instanceof Error ? streamError.stack : undefined,
        );
        errorOccurred = true;
        outputStream.destroy(
          new InternalServerErrorException(
            `Error processing Alibaba stream: ${message}`,
          ),
        );
      });
    } catch (error) {
      const message =
        error instanceof Error
          ? error.message
          : 'Unknown error during ChatOpenAI instantiation or stream initiation';
      this.logger.error(
        `Error with ChatOpenAI for Alibaba: ${message}`,
        error instanceof Error ? error.stack : undefined,
      );
      errorOccurred = true;
      // Ensure the stream is destroyed if an error occurs before or during stream() call
      if (!outputStream.destroyed) {
        outputStream.destroy(
          new InternalServerErrorException(
            `Failed to call Alibaba service via ChatOpenAI: ${message}`,
          ),
        );
      }
    }
    return outputStream;
  }

  // --- Logging Methods ---
  private async logAssistantMessage(
    conversationId: number,
    dialogId: string,
    userMessageId: number,
    modelName: string,
    temperature: number | undefined,
    responseText: string,
    promptTokens: number | null,
    completionTokens: number | null,
    userId: string,
  ) {
    try {
      await this.prisma.message.create({
        data: {
          conversation_id: conversationId,
          temperature: temperature,
          instance_name: 'AlibabaOpenAICompatible', // Updated instance name
          model_name: modelName,
          sender: 'assistant',
          token_spent: completionTokens ?? 0,
          create_by: 'system',
          create_dt: getHongKongTime(),
          update_by: 'system',
          update_dt: getHongKongTime(),
          received_at: getHongKongTime(),
          dialog_id: dialogId,
        },
      });
      this.logger.log(
        `Logged assistant message for Alibaba (OpenAI Compatible).`,
      );

      const totalTokensSpent = (promptTokens ?? 0) + (completionTokens ?? 0);
      if (totalTokensSpent > 0) {
        await this.prisma.acl_user_token_spent.create({
          data: {
            username: userId,
            model_name: modelName,
            token_date: getHongKongDateOnly(),
            is_api: 0,
            token_spent: totalTokensSpent,
            token_spent_user: promptTokens ?? 0,
            token_spent_assistant: completionTokens ?? 0,
            message_count: 1,
            create_by: 'system',
            create_dt: getHongKongTime(),
          },
        });
        this.logger.log(
          `Logged ${totalTokensSpent} tokens for Alibaba (OpenAI Compatible).`,
        );
      }
    } catch (dbError) {
      const message =
        dbError instanceof Error ? dbError.message : 'Unknown DB error';
      this.logger.error(
        `Failed to log Alibaba assistant message/usage (OpenAI Compatible): ${message}`,
        dbError instanceof Error ? dbError.stack : undefined,
      );
    }
  }

  private async updateUserMessageTokens(
    messageId: number,
    promptTokens: number,
  ) {
    try {
      await this.prisma.message.update({
        where: { message_id: messageId },
        data: { token_spent: promptTokens },
      });
      this.logger.log(
        `Updated user message tokens for Alibaba (OpenAI Compatible).`,
      );
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(
        `Failed to update Alibaba user message tokens (OpenAI Compatible): ${message}`,
        error instanceof Error ? error.stack : undefined,
      );
    }
  }
}
