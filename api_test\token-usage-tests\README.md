# Token Usage Tests

This directory contains tests for validating REST API token usage tracking, rate limiting, and token limit enforcement.

## Test Categories

### Core Token Tracking Tests

#### `test-all-models-token-tracking.py`
Comprehensive test that validates token usage tracking across all available models.
- Tests each model's REST API endpoint
- Verifies token usage is correctly recorded in the database
- Checks for duplicate entries (model name vs deployment name)
- Provides detailed summary of tracking accuracy

#### `test-gpt41-rest-api.py`
Focused test for GPT-4.1 model to demonstrate step-by-step token usage tracking.
- Shows initial token usage
- Makes API call and reports tokens used
- Verifies database update
- Provides SQL query for manual verification

#### `test-rest-api-simple.py`
Simple test script for basic REST API token usage verification.
- Basic token usage checking
- API call testing
- Before/after comparison

#### `test-embeddings-token-usage.py`
Specialized test for embedding models token usage tracking.
- Tests text-embedding models specifically
- Validates embedding-specific token calculations
- Checks database recording for embedding requests

### Rate Limiting Tests

#### `debug-rate-limit.py`
Debug utility for investigating rate limiting behavior.
- Makes single requests with detailed header analysis
- Tests rapid requests to trigger rate limits
- Examines rate limit headers and Redis status
- Checks rate limit endpoint functionality

#### `test-aggressive-rate-limit.py`
High-concurrency test for rate limiting system.
- Uses ThreadPoolExecutor for concurrent requests
- Tests with 20-30 concurrent workers
- Attempts to trigger 429 responses quickly
- Validates rate limit headers and error messages

#### `test-multi-models-and-rate-limit.py`
Combined test for token tracking across multiple models plus rate limiting.
- Tests 8+ different models for token tracking
- Validates database token usage recording
- Includes comprehensive rate limit testing
- Provides detailed summary of both systems

### Token Limit Testing

#### `test-token-limit-blocking.py`
**SPECIAL TEST** - Uses 10000x token multiplier for rapid token limit testing.
- Designed to test monthly token limits without consuming actual tokens
- Multiplies token counts by 10000x to quickly reach limits
- Tests token limit blocking behavior
- **WARNING**: Only use with special code modifications for testing

#### `test-token-limit-simulation.py`
Alternative approach to test token limit behavior.
- Simulates various token limit scenarios
- Tests pre-limit and post-limit checking
- Validates limit enforcement without actually consuming tokens

## Usage

### Basic Testing
```bash
# Comprehensive model + rate limit test (recommended)
python test-multi-models-and-rate-limit.py

# Test all models for token tracking
python test-all-models-token-tracking.py

# Debug rate limiting issues
python debug-rate-limit.py

# Test aggressive concurrent rate limiting
python test-aggressive-rate-limit.py
```

### Specialized Testing
```bash
# Test specific model (GPT-4.1)
python test-gpt41-rest-api.py

# Test embedding models
python test-embeddings-token-usage.py

# Simple API test
python test-rest-api-simple.py

# Token limit testing (requires code modifications)
python test-token-limit-blocking.py
```

## Configuration

All tests use the following default configuration:
- **API Base URL**: `http://localhost:3003/api/v0`
- **API Key**: `80b60025-07b4-4097-ba1d-e14695359dd0`
- **Timeout**: 30 seconds for most requests
- **Models Tested**: gpt-4.1, gpt-4.1-mini, qwen-plus, qwen-max, deepseek-v3, gemini-2.5-flash, o1, o3-mini

## Requirements

```bash
pip install requests
```

**Prerequisites:**
1. API server running on `localhost:3003`
2. Valid API key configured in scripts
3. Database accessible with proper models configured
4. Redis running for rate limiting (for rate limit tests)

## Expected Behavior

### Token Tracking
- API responses should include `usage` object with token counts
- Database should record token usage within 3-10 seconds
- Model name variants should be aggregated properly

### Rate Limiting
- Request-based: 60 requests per minute per model per user
- Should return 429 status with retry information
- Headers should include rate limit information

### Token Limits
- Monthly token limits per user per model
- Users can make requests until they exceed limits
- Post-usage checking warns when limits are exceeded