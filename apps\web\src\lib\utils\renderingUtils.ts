import hljs from 'highlight.js'; // Import highlight.js
import 'highlight.js/styles/github.css'; // Import a default theme
import katex from 'katex'; // Import katex
import 'katex/dist/katex.min.css'; // Import katex styles
// Assuming 'marked' and related packages are installed
import { marked } from 'marked';
import { mangle } from 'marked-mangle';
import { gfmHeadingId } from 'marked-gfm-heading-id';

// Configure marked (do this once)
marked.use(mangle());
marked.use(gfmHeadingId({ prefix: 'chat-heading-' }));
marked.setOptions({
  renderer: new marked.Renderer(),
  // Highlighting is applied via useEffect client-side in MessageContent
  // langPrefix: "hljs language-", // Removed invalid option
  pedantic: false,
  gfm: true,
  breaks: true, // Render line breaks as <br>
  // sanitize: false, // DEPRECATED and removed. Sanitization must happen elsewhere.
  // smartypants: false, // Option likely removed in newer versions
  // xhtml: false, // Option likely removed in newer versions
});

// Basic HTML escaping function
export function escapeHtml(unsafe: string): string {
  if (!unsafe) return '';
  return unsafe
    .replace(/&/g, '&')
    .replace(/</g, '<')
    .replace(/>/g, '>')
    .replace(/"/g, '&quot;') // Correct entity
    .replace(/'/g, '&#039;');
}

// Regex for URLs - Exported
export const urlRegex =
  /(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/gi;

// Function to convert URL strings in text to clickable links - Exported
export const ConvertHyperLink = (text: string): string => {
  if (!text) return '';
  return text.replace(
    urlRegex,
    (url) =>
      `<a href="${url}" target="_blank" rel="noopener noreferrer" class="text-blue-600 dark:text-blue-400 hover:underline">${url}</a>`,
  );
};

// Function to apply basic styling to tables - Exported
export const ConvertTable = (htmlString: string): string => {
  if (!htmlString) return '';
  htmlString = htmlString.replace(
    /<table/g,
    '<table class="table-auto border-collapse border border-gray-400"',
  );
  htmlString = htmlString.replace(
    /<th/g,
    '<th class="border border-gray-300 px-4 py-2 bg-gray-100 dark:bg-gray-700"',
  );
  htmlString = htmlString.replace(
    /<td/g,
    '<td class="border border-gray-300 px-4 py-2"',
  );
  return htmlString;
};

// Placeholder for processing specific grounding markers - Exported
export const processGroundingMarkers = (text: string): string => {
  // Placeholder logic
  return text.replace(
    /\[\^(\d+)\^\]/g,
    '<sup class="bg-gray-200 px-1 rounded">$1</sup>',
  );
};

// Placeholder for styling Grounding Markers - Exported
export const ConvertGroundingMarkers = (text: string): string => {
  // This might be the same as processGroundingMarkers or apply different styling
  return text.replace(/(\s+)\[(\d+)\]/g, (match, preWhitespace, number) => {
    return `${preWhitespace}<sup class="inline-block text-center w-4 h-4 bg-blue-200 dark:bg-blue-700 text-blue-800 dark:text-blue-100 rounded-full text-xs leading-tight mx-0.5 cursor-pointer" title="Source ${number}">${number}</sup>`;
  });
};

// Placeholder for converting reference links - Exported
export const ConvertReferenceLinks = (text: string): string => {
  // Placeholder logic: Find [1] url - title patterns
  // const referencePattern = /\[(\d+)\]\s+(https?:\/\/\S+)(\s*-\s*(.*))?/;
  // Basic placeholder:
  return text;
};

// Helper function for Katex rendering
const renderLatex = (text: string): string => {
  if (!text || typeof window === 'undefined') return text;
  try {
    text = text.replace(/\$(.+?)\$/g, (match, latex) => {
      try {
        return katex.renderToString(latex, {
          throwOnError: false,
          displayMode: false,
        });
      } catch (e) {
        console.error('Katex inline err:', e);
        return match;
      }
    });
    text = text.replace(/\$\$\s*([\s\S]*?)\s*\$\$/g, (match, latex) => {
      try {
        return katex.renderToString(latex.trim(), {
          throwOnError: false,
          displayMode: true,
        });
      } catch (e) {
        console.error('Katex display err:', e);
        return match;
      }
    });
  } catch (error) {
    console.error('Katex failed:', error);
  }
  return text;
};
