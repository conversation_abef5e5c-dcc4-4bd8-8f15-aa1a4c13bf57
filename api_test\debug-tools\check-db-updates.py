import requests
import json
import time
from datetime import datetime, date

# Configuration
BASE_URL = "http://localhost:3003/api/v0"
API_KEY = "80b60025-07b4-4097-ba1d-e14695359dd0"

headers = {
    'Content-Type': 'application/json',
    'api-key': API_KEY
}

def make_rest_api_call():
    """Make a simple REST API call"""
    print("Making REST API call...")
    
    url = f"{BASE_URL}/rest/deployments/qwen-plus/chat/completions"
    data = {
        "messages": [
            {"role": "system", "content": "You are helpful."},
            {"role": "user", "content": "Say 'test' in 1 word."}
        ],
        "max_tokens": 5
    }
    
    response = requests.post(url, headers=headers, json=data, params={"api-version": "2024-02-01"})
    
    if response.status_code == 200:
        result = response.json()
        content = result['choices'][0]['message']['content']
        usage = result.get('usage', {})
        print(f"SUCCESS: {content}")
        print(f"Tokens: {usage.get('total_tokens', 0)}")
        return usage.get('total_tokens', 0)
    else:
        print(f"FAILED: {response.status_code} - {response.text}")
        return 0

def check_rate_limit_endpoint():
    """Check the rate limit endpoint to see current usage"""
    print("\nChecking token usage via rate limit endpoint...")
    
    url = f"{BASE_URL}/rest/rate-limit/token-usage"
    response = requests.get(url, headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        
        print("Current token usage data:")
        for usage in data.get('usage', []):
            model_name = usage.get('modelName', 'unknown')
            if 'qwen' in model_name.lower():
                print(f"  {model_name}:")
                print(f"    Total tokens: {usage.get('totalTokensUsed', 0)}")
                print(f"    Input tokens: {usage.get('promptTokensUsed', 0)}")
                print(f"    Output tokens: {usage.get('completionTokensUsed', 0)}")
                print(f"    Messages: {usage.get('messageCount', 0)}")
                print(f"    Last updated: {usage.get('lastUpdated', 'N/A')}")
                return usage
        
        print("  No qwen models found in usage data")
        return None
    else:
        print(f"FAILED to get usage data: {response.status_code} - {response.text}")
        return None

def main():
    print("=" * 60)
    print("DATABASE UPDATE VERIFICATION TEST")
    print("=" * 60)
    print(f"Timestamp: {datetime.now().isoformat()}")
    
    # Check initial state
    print("\n1. Checking initial token usage...")
    initial_usage = check_rate_limit_endpoint()
    
    if initial_usage:
        initial_tokens = initial_usage.get('totalTokensUsed', 0)
        print(f"Initial qwen-plus tokens: {initial_tokens}")
    else:
        print("No initial usage data found for qwen models")
        initial_tokens = 0
    
    # Make API call
    print("\n2. Making REST API call...")
    api_tokens = make_rest_api_call()
    
    if api_tokens == 0:
        print("API call failed, stopping test")
        return
    
    # Wait for database update
    print(f"\n3. Waiting 10 seconds for database to update...")
    time.sleep(10)
    
    # Check updated state
    print("\n4. Checking updated token usage...")
    updated_usage = check_rate_limit_endpoint()
    
    if updated_usage:
        updated_tokens = updated_usage.get('totalTokensUsed', 0)
        difference = updated_tokens - initial_tokens
        
        print(f"\nRESULTS:")
        print(f"  Initial tokens: {initial_tokens}")
        print(f"  API reported tokens: {api_tokens}")
        print(f"  Updated tokens: {updated_tokens}")
        print(f"  Actual difference: +{difference}")
        
        if difference == api_tokens:
            print(f"  ✅ SUCCESS: Database updated correctly!")
        elif difference > 0:
            print(f"  ⚠️  PARTIAL: Database updated but difference ({difference}) != API tokens ({api_tokens})")
        else:
            print(f"  ❌ FAILED: No database update detected")
    else:
        print("❌ FAILED: Could not get updated usage data")
    
    print(f"\n5. Additional checks...")
    print("If you're not seeing updates in [acl_user_token_spent], possible reasons:")
    print("  - The table name might be different")
    print("  - Check the is_api column (1 for REST API, 0 for Web UI)")
    print("  - Check the token_date column (should be today's date)")
    print("  - Check the model_name column (should be 'qwen-plus')")
    print("  - Database connection or transaction issues")
    
    today = date.today()
    print(f"\nSQL query to check manually:")
    print(f"SELECT * FROM acl_user_token_spent")
    print(f"WHERE username = 'SUNNYPOON'")
    print(f"  AND model_name = 'qwen-plus'")
    print(f"  AND token_date >= '{today}'")
    print(f"  AND is_api = 1")
    print(f"ORDER BY update_dt DESC;")

if __name__ == "__main__":
    main()