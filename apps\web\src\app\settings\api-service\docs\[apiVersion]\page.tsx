'use client';

import React, { useEffect, useState } from 'react';
import { useParams, useSearchParams } from 'next/navigation'; // To get apiVersion from URL and query params
import ThemedSwaggerUI from '@/lib/swagger/ThemedSwaggerUI';
import { Box, CircularProgress, Typography, Alert, Paper } from '@mui/material';
// import { Layout } from '@/components/layout'; // Assuming a layout component
// import AccessDenied from '@/components/AccessDenied'; // Assuming an AccessDenied component
import { useSession } from 'next-auth/react';

const SwaggerDocsPage = () => {
  const params = useParams();
  const searchParams = useSearchParams();
  const isFullscreen = searchParams.get('fullscreen') === 'true';
  const apiVersion = params?.apiVersion
    ? Array.isArray(params.apiVersion)
      ? params.apiVersion[0]
      : params.apiVersion
    : null;
  const { data: session, status } = useSession();

  const [spec, setSpec] = useState<Record<string, any> | null>(null);
  const [loadingSpec, setLoadingSpec] = useState(true);
  const [errorSpec, setErrorSpec] = useState<string | null>(null);

  // Placeholder for checking if user is authorized for API service
  const isUserApiAuthorized = session?.user?.rest === true;

  useEffect(() => {
    if (apiVersion && status === 'authenticated' && isUserApiAuthorized) {
      setLoadingSpec(true);
      setErrorSpec(null);
      // Adjust API path if your NestJS API is namespaced, e.g., /api/docs/${apiVersion}/swagger.json
      fetch(`/api/rest/docs/${apiVersion}/swagger.json`)
        .then(async (response) => {
          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(
              errorData.message ||
                `Failed to fetch Swagger spec for version ${apiVersion}: ${response.statusText}`,
            );
          }
          return response.json();
        })
        .then((data) => {
          setSpec(data);
        })
        .catch((err) => {
          setErrorSpec(err instanceof Error ? err.message : String(err));
          setSpec(null);
        })
        .finally(() => {
          setLoadingSpec(false);
        });
    } else if (status === 'authenticated' && !isUserApiAuthorized) {
      setErrorSpec(
        'Access Denied. You are not authorized to view API documentation.',
      );
      setLoadingSpec(false);
    } else if (!apiVersion && status !== 'loading') {
      setErrorSpec('API version not specified in the URL.');
      setLoadingSpec(false);
    }
  }, [apiVersion, status, isUserApiAuthorized]);

  if (status === 'loading' || (loadingSpec && !errorSpec)) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100svh',
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  // if (!session || !isUserApiAuthorized) {
  //     // return <Layout><AccessDenied /></Layout>;
  //     return <Typography variant="h6" color="error" sx={{p:3}}>Access Denied. You are not authorized to view API documentation.</Typography>;
  // }

  if (errorSpec) {
    return (
      <Alert severity="error" sx={{ m: 3 }}>
        {errorSpec}
      </Alert>
    );
  }

  if (!spec) {
    return (
      <Typography sx={{ p: 3 }}>
        No Swagger specification found for API version: {apiVersion}
      </Typography>
    );
  }

  // Render in fullscreen mode without margins and header
  if (isFullscreen) {
    return (
      <Box
        sx={{
          width: '100vw',
          height: '100svh',
          overflow: 'hidden',
          '.swagger-ui .topbar': { display: 'none !important' },
          '.swagger-ui': {
            height: '100svh',
            overflow: 'auto',
          },
        }}
      >
        <ThemedSwaggerUI spec={spec} />
      </Box>
    );
  }

  // Normal render with margins and header
  return (
    <Box sx={{ m: 2 }}>
      <Typography variant="h4" gutterBottom>
        API Specification for Version: {apiVersion}
      </Typography>
      <Paper
        elevation={3}
        sx={{ '.swagger-ui .topbar': { display: 'none !important' } }}
      >
        {' '}
        {/* Hides the default Swagger topbar */}
        <ThemedSwaggerUI spec={spec} />
      </Paper>
    </Box>
  );
};

export default SwaggerDocsPage;
