import {
  Controller,
  Post,
  // UploadedFile, // Removed
  // UseInterceptors, // Removed
  Req, // Added Req
  Logger,
  HttpException,
  HttpStatus,
  UseGuards,
  ForbiddenException,
} from '@nestjs/common';
// import { FileInterceptor } from '@nestjs/platform-express'; // Removed
import { SpeechService } from './speech.service';
// import { Express } from 'express'; // Removed
import { FastifyRequest } from 'fastify'; // Import FastifyRequest
import { PromptSpeechRateLimitGuard } from '../../common/guards/prompt-speech-rate-limit.guard';
import { AuthenticatedUser } from '../../auth/user.interface';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

@Controller('general/speech') // Route prefix for this controller
@UseGuards(JwtAuthGuard, PromptSpeechRateLimitGuard)
export class SpeechController {
  private readonly logger = new Logger(SpeechController.name);

  constructor(private readonly speechService: SpeechService) {}

  @Post('transcribe') // Endpoint: POST /speech/transcribe
  // @UseInterceptors(FileInterceptor('file')) // Removed
  async transcribeAudio(
    @Req() request: FastifyRequest & { user: AuthenticatedUser }, // Add user type
  ): Promise<{ transcription: string }> {
    this.logger.log(`Received request to /transcribe endpoint.`);

    // Extract and validate user
    const user = request.user;
    if (!user?.userId) {
      throw new ForbiddenException('User information not available.');
    }

    // Check if the request is multipart
    if (!request.isMultipart()) {
      this.logger.error('Request is not multipart');
      throw new HttpException(
        'Request must be multipart/form-data',
        HttpStatus.BAD_REQUEST,
      );
    }

    let fileBuffer: Buffer | null = null;
    let originalFilename: string | null = null;
    let recordingDuration = 0; // Duration in seconds

    try {
      // Process the multipart request to get both file and duration
      const parts = request.parts();

      for await (const part of parts) {
        if (part.type === 'file' && part.fieldname === 'file') {
          originalFilename = part.filename;
          fileBuffer = await part.toBuffer();
          this.logger.log(
            `Processed file: ${originalFilename}, size: ${fileBuffer?.length} bytes`,
          );
        } else if (
          part.type === 'field' &&
          part.fieldname === 'recordingDuration'
        ) {
          const durationStr = part.value as string;
          recordingDuration = parseFloat(durationStr) || 0;
          this.logger.log(
            `Received recording duration: ${recordingDuration} seconds`,
          );
        }
      }

      if (!fileBuffer || fileBuffer.length === 0) {
        this.logger.error(
          'No file data found or uploaded file buffer is empty.',
        );
        throw new HttpException(
          'No audio file uploaded or file is empty',
          HttpStatus.BAD_REQUEST,
        );
      }

      // Handle missing recording duration
      if (recordingDuration <= 0) {
        // Use conservative fallback since file-size estimation is unreliable for audio duration
        const conservativeFallback = 5; // Conservative 5-second fallback to prevent overcharging
        this.logger.warn(
          `Recording duration not provided by frontend. Using conservative ${conservativeFallback}s fallback. File size: ${fileBuffer.length} bytes`,
        );
        this.logger.warn(
          'IMPORTANT: Frontend should implement recording timer and send actual recordingDuration in form data',
        );
        recordingDuration = conservativeFallback;
      }

      // Add validation for unreasonable duration values
      if (recordingDuration > 300) {
        // 5 minutes max
        this.logger.warn(
          `Recording duration seems unusually long: ${recordingDuration}s. File size: ${fileBuffer.length} bytes. Please verify frontend timer accuracy.`,
        );
      }

      if (recordingDuration < 0.5) {
        // Less than 0.5 seconds seems too short
        this.logger.warn(
          `Recording duration seems unusually short: ${recordingDuration}s. Using minimum 1 second.`,
        );
        recordingDuration = 1;
      }

      // Optional: Add more validation like file type check if needed
      // e.g., if (data.mimetype !== 'audio/wav') { ... }

      const transcription = await this.speechService.transcribeAudio(
        fileBuffer,
        user,
        recordingDuration,
      );
      this.logger.log(
        `Successfully transcribed audio for user ${user.userId}. Returning transcription.`,
      );
      return { transcription };
    } catch (error) {
      // Type checking for unknown error
      let errorMessage = 'An unknown error occurred during transcription.';
      let errorStack: string | undefined = undefined; // Explicitly type errorStack
      if (error instanceof Error) {
        errorMessage = error.message;
        errorStack = error.stack;
        this.logger.error(
          `Error during transcription process: ${errorMessage}`,
          errorStack,
        );
      } else {
        this.logger.error(
          `Error during transcription process: ${errorMessage}`,
          error,
        );
      }
      // Rethrow a generic server error
      throw new HttpException(
        'Failed to transcribe audio',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
