import {
  Inject,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  ForbiddenException,
  NotImplementedException,
  HttpException,
  HttpStatus,
  BadRequestException,
} from '@nestjs/common'; // Added BadRequestException
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../../prisma/prisma.service';
import { GeneralService } from '../../general.service'; // Import GeneralService
import { CreateChatCompletionDto } from './dto/create-chat-completion.dto';
import { ChatCompletionResponseDto } from './dto/chat-completion-response.dto';
import { UpdateConversationParamsDto } from './dto/UpdateConversationParamsDto'; // Import the new DTO
import { LlmConfigService, LlmConfig } from '../../../llm/llm-config.service';
import { Chat<PERSON><PERSON>AI } from '@langchain/openai';
import { ChatGoogleGenerativeAI } from '@langchain/google-genai';
import { ChatAnthropic } from '@langchain/anthropic';
import { ChatAlibabaTongyi } from '@langchain/community/chat_models/alibaba_tongyi';
import { ChatVertexAI } from '@langchain/google-vertexai';
import { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { RunnableConfig } from '@langchain/core/runnables'; // Still needed for direct model calls
import {
  HumanMessage,
  AIMessage,
  SystemMessage,
  BaseMessage,
  ToolMessage, // Added import
} from '@langchain/core/messages';
import { PassThrough, Readable } from 'stream';
import { v4 as uuidv4 } from 'uuid';
import { AzureAiserviceLlmService } from '../../../llm/azure-aiservice-llm.service';
import { LlmStreamOptions } from '../../../llm/llm.service';
import { FileProcessingService } from '../../../utils/file-processing.service';
import { KeywordExtractionService } from '../../../utils/keyword-extraction.service';
import {
  SearchContextService,
  SearchResult,
  Source as SearchSource,
} from '../../../utils/search-context.service';
import { VertexGeminiService } from '../../../llm/vertex-gemini.service';
import { VertexLlamaService } from '../../../llm/vertex-llama.service'; // Import the new Llama service
import { AuthenticatedUser } from '../../../auth/user.interface'; // Import AuthenticatedUser
import { GoogleAuth } from 'google-auth-library'; // Import for Vertex AI Llama authentication
import { ModelMappingService } from '../../../common/services/model-mapping.service'; // Import for dynamic model name variants
import { TokenUsageService } from '../../../common/services/token-usage.service';
import { getHongKongTime } from '../../../common/utils/timezone.util';

// Define the structure expected from the SP retrieving decrypted messages
interface DecryptedMessageFromDB {
  sender: string;
  content: string;
  create_dt: Date;
  // Add sources_json if the SP returns it directly (though we parse it in ChatService)
  // sources_json?: string | null;
}

// Define the structure expected from the SP inserting messages
interface InsertedMessageResult {
  inserted_message_id: number;
}

// Removed SourceExtractorCallbackHandler class

// Note: Agent prompts and tool-based search logic removed
// Search functionality now handled via keyword extraction and context injection

// Helper function for sleep
const sleep = (ms: number): Promise<void> =>
  new Promise((resolve) => setTimeout(resolve, ms));

// Helper function to detect system message errors
const isSystemMessageError = (error: any, logger: Logger): boolean => {
  const errorMessage = error?.message || error?.toString() || '';
  const isSystemError =
    errorMessage.includes("does not support 'system'") ||
    errorMessage.includes("Unsupported value: 'messages[0].role'") ||
    (errorMessage.includes('400') && errorMessage.includes('system'));

  // Add debug logging for error detection
  logger.debug(
    `[DEBUG] Error detection - Message: "${errorMessage}", IsSystemError: ${isSystemError}`,
  );
  return isSystemError;
};

// Note: Agent retry logic removed - no longer needed with direct model approach

/**
 * Retry Logic for System Message Errors
 *
 * This implementation adds automatic retry functionality for system message compatibility errors.
 * When a "400 Unsupported value: 'messages[0].role' does not support 'system'" error occurs,
 * the system will automatically:
 *
 * 1. Detect the system message error pattern in the error message
 * 2. Retry the request with forceSystemMessageConversion=true
 * 3. Convert system messages to user message prefixes during retry
 * 4. Apply the conversion to both current conversation and chat history
 *
 * For streaming requests, improved error detection and logging is provided,
 * but full retry logic would require more complex stream management.
 */

// Helper function to detect system message errors
const detectSystemMessageError = (error: any): boolean => {
  if (!error || typeof error.message !== 'string') return false;

  const errorMessage = error.message.toLowerCase();
  const systemMessageErrorPatterns = [
    'messages[0].role',
    'does not support',
    'system',
    'unsupported value',
    'role.*system.*not.*support',
    'system.*message.*not.*support',
  ];

  // Check if the error contains patterns indicating system message incompatibility
  const containsSystemMessageError = systemMessageErrorPatterns.some(
    (pattern) => {
      if (pattern.includes('.*')) {
        // Use regex for more complex patterns
        const regex = new RegExp(pattern, 'i');
        return regex.test(errorMessage);
      }
      return errorMessage.includes(pattern);
    },
  );

  // Also check for 400 status codes which typically indicate client errors
  const isBadRequest =
    error.status === 400 ||
    error.statusCode === 400 ||
    (error.response && error.response.status === 400);

  return containsSystemMessageError && isBadRequest;
};

// Helper function to convert system messages to user message prefixes
const convertSystemMessagesToUserPrefix = (
  messages: BaseMessage[],
  userMessageContent: any,
  logger: Logger,
): { convertedMessages: BaseMessage[]; convertedUserContent: any } => {
  const convertedMessages: BaseMessage[] = [];
  let convertedUserContent = userMessageContent;
  let systemInstructions = '';

  // Extract system messages and collect their content
  for (const message of messages) {
    if (message instanceof SystemMessage) {
      const content =
        typeof message.content === 'string'
          ? message.content
          : JSON.stringify(message.content);
      systemInstructions += content + '\n\n';
      logger.debug(
        `Extracted system message for conversion: ${content.substring(0, 100)}...`,
      );
    } else {
      convertedMessages.push(message);
    }
  }

  // If we found system messages, prepend them to the user content
  if (systemInstructions.trim()) {
    const systemPrefix = `System Instructions: ${systemInstructions.trim()}\n\n---\n\n`;

    // Find the last human message and prepend system instructions
    for (let i = convertedMessages.length - 1; i >= 0; i--) {
      if (convertedMessages[i] instanceof HumanMessage) {
        const humanMsg = convertedMessages[i] as HumanMessage;
        if (typeof humanMsg.content === 'string') {
          convertedMessages[i] = new HumanMessage({
            content: systemPrefix + humanMsg.content,
          });
        } else if (Array.isArray(humanMsg.content)) {
          // For multimodal content, prepend to the first text part
          const content = [...humanMsg.content];
          const firstTextPart = content.find((part) => part.type === 'text');
          if (firstTextPart && 'text' in firstTextPart) {
            firstTextPart.text = systemPrefix + firstTextPart.text;
          } else {
            content.unshift({ type: 'text', text: systemPrefix });
          }
          convertedMessages[i] = new HumanMessage({ content });
        }
        break;
      }
    }

    // Also update the current user message content if it's the one being processed
    if (typeof convertedUserContent === 'string') {
      convertedUserContent = systemPrefix + convertedUserContent;
    } else if (Array.isArray(convertedUserContent)) {
      const firstTextPart = convertedUserContent.find(
        (part) => part.type === 'text',
      );
      if (firstTextPart) {
        firstTextPart.text = systemPrefix + firstTextPart.text;
      } else {
        convertedUserContent.unshift({ type: 'text', text: systemPrefix });
      }
    }

    logger.warn(
      `Converted ${messages.filter((m) => m instanceof SystemMessage).length} system messages to user message prefix`,
    );
  }

  return { convertedMessages, convertedUserContent };
};

// Note: Agent prompt helper removed - no longer needed

@Injectable()
export class ChatCompletionService {
  // Added export keyword
  private readonly logger = new Logger(ChatCompletionService.name);
  private readonly encryptionKeyName: string;
  private readonly decryptionCertName: string;

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly azureAIService: AzureAiserviceLlmService,
    private readonly llmConfigService: LlmConfigService,
    private readonly fileProcessingService: FileProcessingService, // Inject FileProcessingService
    private readonly keywordExtractionService: KeywordExtractionService, // Inject KeywordExtractionService
    private readonly searchContextService: SearchContextService, // Inject SearchContextService
    private readonly generalService: GeneralService,
    private readonly vertexGeminiService: VertexGeminiService,
    private readonly vertexLlamaService: VertexLlamaService, // Inject the new Llama service
    private readonly modelMappingService: ModelMappingService, // Inject ModelMappingService
    private readonly tokenUsageService: TokenUsageService, // Inject TokenUsageService
  ) {
    this.encryptionKeyName = this.configService.getOrThrow<string>(
      'DB_ENCRYPTION_KEY_NAME',
    );
    this.decryptionCertName = this.configService.getOrThrow<string>(
      'DB_DECRYPTION_CERT_NAME',
    );
  }

  async createCompletion(
    createChatCompletionDto: CreateChatCompletionDto,
    user: AuthenticatedUser, // Use AuthenticatedUser directly
  ): Promise<Readable | Record<string, any>> {
    this.logger.log('[REGENERATION] ============ createCompletion called ============');
    this.logger.log('[REGENERATION] Input DTO:', {
      chat_session_id: createChatCompletionDto.chat_session_id,
      prompt: createChatCompletionDto.prompt?.substring(0, 100) + '...',
      model: createChatCompletionDto.model,
      isRegeneration: createChatCompletionDto.isRegeneration,
      usedMention: createChatCompletionDto.usedMention,
      stream: createChatCompletionDto.stream
    });
    this.logger.log('[REGENERATION] User info:', {
      userId: user.userId,
      type: user.type,
      dept_unit_code: user.dept_unit_code
    });
    
    // Destructure DTO, including the new files array
    // Rename temperature from DTO to avoid conflict
    const {
      chat_session_id,
      prompt,
      model: requestedModel,
      temperature: dtoTemperature,
      stream,
      useGoogle,
      files,
      isRegeneration,
    } = createChatCompletionDto;

    const isNewConversation = !chat_session_id; // Use chat_session_id
    const currentConversationUUID = chat_session_id || uuidv4(); // Use chat_session_id
    let conversationId: number | undefined = undefined;
    let userMessageId: number | undefined = undefined;
    this.logger.log(
      `[ChatCompletionService] currentConversationUUID: ${currentConversationUUID}, isNewConversation: ${!chat_session_id}`,
    );

    // Retry mechanism for system message errors
    let retryCount = 0;
    const maxRetries = 1;
    let forceSystemMessageConversion = false;

    while (retryCount <= maxRetries) {
      try {
        return await this.executeCompletion(
          createChatCompletionDto,
          user,
          currentConversationUUID,
          isNewConversation,
          conversationId,
          userMessageId,
          forceSystemMessageConversion,
        );
      } catch (error: any) {
        // Check if this is a system message error and we haven't retried yet
        if (detectSystemMessageError(error) && retryCount < maxRetries) {
          this.logger.warn(
            `[RETRY] System message error detected for conv ${currentConversationUUID}, attempt ${retryCount + 1}: ${error.message}`,
          );
          retryCount++;
          forceSystemMessageConversion = true;

          // Reset conversation state for retry (but keep the UUID)
          conversationId = undefined;
          userMessageId = undefined;

          continue; // Retry the request
        }

        // If not a system message error or we've exceeded retries, handle normally
        this.logger.error(
          `[CREATE_COMPLETION_ERROR] Conv: ${currentConversationUUID}, User: ${user.userId}, Error: ${error.message}`,
          error.stack,
        );
        if (error instanceof HttpException) {
          throw error;
        }
        throw new InternalServerErrorException(
          'An unexpected error occurred while processing the chat completion.',
        );
      }
    }

    // This should never be reached, but adding for safety
    throw new InternalServerErrorException(
      'Maximum retries exceeded for chat completion.',
    );
  }

  private async executeCompletion(
    createChatCompletionDto: CreateChatCompletionDto,
    user: AuthenticatedUser,
    currentConversationUUID: string,
    isNewConversation: boolean,
    conversationId: number | undefined,
    userMessageId: number | undefined,
    forceSystemMessageConversion: boolean = false,
  ): Promise<Readable | Record<string, any>> {
    const {
      chat_session_id,
      prompt,
      model: requestedModel,
      temperature: dtoTemperature,
      stream,
      useGoogle,
      files,
      isRegeneration,
      usedMention,
    } = createChatCompletionDto;

    try {
      // --- Step 0: Get User Details (Dept and Type) from JWT ---
      const userId = user.userId; // Extract userId from AuthenticatedUser
      const staffStdType = user.type || 'STAFF'; // Use type from JWT, default if null
      const deptUnitCode = user.dept_unit_code || 'ITO'; // Use dept_unit_code from JWT, default if null
      this.logger.log(
        `User ${userId} type: ${staffStdType}, department: ${deptUnitCode} (from JWT)`,
      );

      // --- Step 0.5: Determine Effective Model Based on usedMention Flag ---
      let effectiveModel: string = requestedModel;
      
      // If user did NOT use @mention and this is an existing conversation,
      // use the conversation's first model instead of the requested model
      if (!usedMention && !isNewConversation) {
        const conversationFirstModel = await this.getConversationFirstModel(currentConversationUUID);
        if (conversationFirstModel) {
          effectiveModel = conversationFirstModel;
          this.logger.debug(
            `[Model Selection] Using conversation's first model '${effectiveModel}' instead of requested '${requestedModel}' (usedMention=false)`,
          );
        } else {
          // Fallback to requested model if we can't get the conversation's first model
          this.logger.warn(
            `[Model Selection] Could not get conversation's first model, using requested model '${requestedModel}'`,
          );
        }
      } else if (usedMention) {
        this.logger.debug(
          `[Model Selection] Using requested model '${effectiveModel}' (usedMention=true)`,
        );
      } else {
        // New conversation, use requested model (which will become the first model)
        this.logger.debug(
          `[Model Selection] Using requested model '${effectiveModel}' for new conversation`,
        );
      }

      // --- Step 0.6: Validate Effective Model ---
      // Get all possible variants of the effective model name
      const modelVariants =
        this.modelMappingService.getModelNameVariants(effectiveModel);
      const validModel = await this.prisma.model_list.findFirst({
        where: {
          model_name: {
            in: modelVariants, // Find model by any of its name variants
          },
          api_status: 'A',
          OR: [
            { rec_status: 'A' },
            {
              acl_user_access: {
                some: {
                  username: userId,
                  rec_status: 'A',
                  expiry_date: { gt: new Date() },
                },
              },
            },
          ],
          AND: [
            {
              OR: [
                { whitelist: staffStdType },
                { whitelist: { contains: `,${staffStdType},` } },
                { whitelist: { startsWith: `${staffStdType},` } },
                { whitelist: { endsWith: `,${staffStdType}` } },
              ],
            },
          ],
        },
        select: {
          id: true,
          deployment_name: true,
          model_name: true,
          display_name: true,
          supports_vision: true,
        }, // Added supports_vision and display_name
      });

      // Add detailed logging before the check
      this.logger.debug(
        `Validating model access: userId=${userId}, effectiveModel=${effectiveModel}, requestedModel=${requestedModel}, variants=${JSON.stringify(modelVariants)}, staffStdType=${staffStdType}, findFirstResult=${JSON.stringify(validModel)}`,
      );

      if (!validModel) {
        this.logger.warn(
          `User ${userId} requested unavailable/inaccessible model: ${effectiveModel} (original: ${requestedModel})`,
        );
        throw new NotFoundException(
          `Model '${effectiveModel}' not found or access denied.`,
        );
      }
      this.logger.debug(
        `Model ${effectiveModel} validated successfully for user ${userId}. ID: ${validModel.id}`,
      );

      // --- Record Model Usage (Async - Fire and Forget) ---
      this.generalService
        .recordModelUsage(userId, validModel.id)
        .then((result) => {
          if (result.success) {
            this.logger.log(
              `Successfully recorded usage for model ${validModel.id}, user ${userId}`,
            );
          } else {
            this.logger.warn(
              `Failed to record usage for model ${validModel.id}, user ${userId}: ${result.error}`,
            );
          }
        })
        .catch((err) => {
          // Log error but don't block chat completion
          this.logger.error(
            `Error calling recordModelUsage for model ${validModel.id}, user ${userId}: ${err.message}`,
            err.stack,
          );
        });
      // --- End Record Model Usage ---

      // --- Step 1: Ensure Conversation ---
      if (isNewConversation) {
        // Use pending parameters if provided, otherwise fall back to regular parameters or defaults
        const effectiveInstructions = createChatCompletionDto.pendingInstructions 
          ?? createChatCompletionDto.instructions 
          ?? null;
        const effectivePastMessagesCount = createChatCompletionDto.pendingPastMessagesCount 
          ?? createChatCompletionDto.pastMessagesCount 
          ?? 10;
        const effectiveMaxResponseTokens = createChatCompletionDto.pendingMaxResponseTokens 
          ?? createChatCompletionDto.maxResponseTokens 
          ?? 2000;
        const effectiveTemperatureForCreation = createChatCompletionDto.pendingTemperature 
          ?? createChatCompletionDto.temperature 
          ?? 0.6;
        const effectiveTopPForCreation = createChatCompletionDto.pendingTopP 
          ?? createChatCompletionDto.topP 
          ?? 0.6;

        // Debug logging to track which parameters are being used for new conversation
        this.logger.log(`🔥 [DEBUG] Creating new conversation with parameters:`);
        this.logger.log(JSON.stringify({
          conversationUuid: currentConversationUUID,
          pendingParams: {
            pendingInstructions: createChatCompletionDto.pendingInstructions,
            pendingPastMessagesCount: createChatCompletionDto.pendingPastMessagesCount,
            pendingMaxResponseTokens: createChatCompletionDto.pendingMaxResponseTokens,
            pendingTemperature: createChatCompletionDto.pendingTemperature,
            pendingTopP: createChatCompletionDto.pendingTopP,
          },
          regularParams: {
            instructions: createChatCompletionDto.instructions,
            pastMessagesCount: createChatCompletionDto.pastMessagesCount,
            maxResponseTokens: createChatCompletionDto.maxResponseTokens,
            temperature: createChatCompletionDto.temperature,
            topP: createChatCompletionDto.topP,
          },
          effectiveParams: {
            instructions: effectiveInstructions,
            pastMessagesCount: effectivePastMessagesCount,
            maxResponseTokens: effectiveMaxResponseTokens,
            temperature: effectiveTemperatureForCreation,
            topP: effectiveTopPForCreation,
          },
        }, null, 2));

        const newConv = await this.prisma.conversation.create({
          data: {
            conversation_uuid: currentConversationUUID,
            ssoid: userId,
            create_by: userId,
            create_dt: getHongKongTime(),
            dept_unit_code: deptUnitCode,
            model_name: validModel.model_name,
            // Use effective parameters (pending takes precedence over regular parameters)
            instructions: effectiveInstructions,
            pastMessagesCount: effectivePastMessagesCount,
            maxResponseTokens: effectiveMaxResponseTokens,
            temperature: effectiveTemperatureForCreation,
            topP: effectiveTopPForCreation,
          },
          select: { conversation_id: true },
        });
        conversationId = newConv.conversation_id;
        this.logger.log(
          `Created new conversation: ${currentConversationUUID} (ID: ${conversationId}) with parameters`,
        );
      } else {
        // Fetch existing conversation including parameters
        const existingConv = await this.prisma.conversation.findUnique({
          where: { conversation_uuid: currentConversationUUID },
          select: {
            conversation_id: true,
            instructions: true,
            pastMessagesCount: true,
            maxResponseTokens: true,
            temperature: true,
            topP: true,
          },
        });
        if (!existingConv) {
          throw new NotFoundException(
            `Conversation ${currentConversationUUID} not found.`,
          );
        }
        conversationId = existingConv.conversation_id;
        // Store fetched parameters (will be used later)
      }
      const conversationParams = !isNewConversation
        ? await this.prisma.conversation.findUnique({
            where: { conversation_uuid: currentConversationUUID },
            select: {
              instructions: true,
              pastMessagesCount: true,
              maxResponseTokens: true,
              temperature: true,
              topP: true,
            },
          })
        : null;

      // --- Process Uploaded Files ---
      let processedFileContent = ''; // For OCR text
      const imageParts: any[] = []; // For vision model image data
      const fileNamesForPrompt: string[] = []; // To include filenames in the saved user message

      if (files && files.length > 0) {
        this.logger.log(
          `Processing ${files.length} uploaded files for conversation ${currentConversationUUID}`,
        );
        for (const file of files) {
          fileNamesForPrompt.push(file.filename);
          try {
            const fileBuffer = Buffer.from(file.content, 'base64');
            const isImage = file.mimeType.startsWith('image/');
            const isVisionModel = validModel.supports_vision;

            if (isImage && isVisionModel) {
              // Format for vision model
              this.logger.debug(
                `Formatting image ${file.filename} for vision model.`,
              );
              imageParts.push({
                type: 'image_url',
                image_url: {
                  url: `data:${file.mimeType};base64,${file.content}`,
                },
              });
            } else {
              // For all other file types (including images for non-vision models), use the FileProcessingService
              this.logger.debug(
                `Processing file ${file.filename} with FileProcessingService (mime: ${file.mimeType}).`,
              );
              const extractedText =
                await this.fileProcessingService.extractText({
                  ...file,
                  dept_unit_code: deptUnitCode,
                });
              processedFileContent += `\n\n--- Content from ${file.filename} ---\n${extractedText}`;
            }
          } catch (fileError: any) {
            this.logger.error(
              `Error processing file ${file.filename}: ${fileError.message}`,
              fileError.stack,
            );
            // Throw a structured error that the frontend can parse
            throw new BadRequestException({
              message: `Failed to process file: ${file.filename}.`,
              errorCode: 'FILE_PROCESSING_FAILED',
            });
          }
        }
      }

      // --- Handle Regeneration: Soft-delete last assistant message BEFORE retrieving history ---
      if (isRegeneration && conversationId) {
        this.logger.log(
          `[REGENERATION] Starting regeneration process for conversation ${currentConversationUUID}`,
        );
        this.logger.log(
          `[REGENERATION] Conversation ID: ${conversationId}, User ID: ${userId}`,
        );

        // Find the last assistant message for this conversation
        const lastAssistantMessage = await this.prisma.message.findFirst({
          where: {
            conversation_id: conversationId,
            sender: 'assistant',
            is_deleted: false,
          },
          orderBy: {
            create_dt: 'desc',
          },
          select: {
            message_id: true,
            message_uuid: true,
            create_dt: true,
          },
        });

        this.logger.log(
          `[REGENERATION] Last assistant message search result:`,
          lastAssistantMessage ? {
            message_id: lastAssistantMessage.message_id,
            message_uuid: lastAssistantMessage.message_uuid,
            create_dt: lastAssistantMessage.create_dt
          } : 'null'
        );

        if (lastAssistantMessage) {
          // Soft delete the message
          await this.prisma.message.update({
            where: { message_id: lastAssistantMessage.message_id },
            data: {
              is_deleted: true,
              update_dt: new Date(),
              update_by: userId,
            },
          });
          this.logger.log(
            `[REGENERATION] Soft-deleted message ${lastAssistantMessage.message_uuid}`,
          );
        } else {
          this.logger.warn(
            `[REGENERATION] No assistant message found to delete for conversation ${currentConversationUUID}`,
          );
        }
      }

      // --- Retrieve and Prepare History ---
      // Use pastMessagesCount from DTO for new conversations, from DB for existing ones
      const historyLimit = isNewConversation 
        ? (createChatCompletionDto.pastMessagesCount ?? 10)
        : (conversationParams?.pastMessagesCount ?? createChatCompletionDto.pastMessagesCount ?? 10);
      const chatHistory = await this.getDecryptedChatHistory(
        currentConversationUUID,
        historyLimit,
      );
      this.logger.debug(
        `Chat history retrieved (limit: ${historyLimit}) for conversation ${currentConversationUUID}: ${chatHistory.length} messages`,
      );

      // --- Enhanced Search Context Logic ---
      let searchContext = '';
      let searchSources: SearchSource[] = [];

      if (useGoogle && this.searchContextService.isSearchAvailable()) {
        try {
          this.logger.log(
            `🔍 SEARCH ENHANCEMENT REQUESTED for conversation ${currentConversationUUID}`,
          );
          this.logger.log(
            `📝 Original Prompt: "${prompt.substring(0, 100)}${prompt.length > 100 ? '...' : ''}"`,
          );

          // Extract keywords from user prompt using GPT-4.1-mini with conversation context
          // Build context from recent user messages for better keyword generation
          const recentUserMessages = chatHistory
            .filter((msg) => msg instanceof HumanMessage)
            .slice(-3) // Get last 3 user messages for context
            .map((msg) =>
              typeof msg.content === 'string'
                ? msg.content
                : JSON.stringify(msg.content),
            );

          this.logger.debug(
            `[Keyword Context] Found ${recentUserMessages.length} recent user messages for context`,
          );

          const keywords = await this.keywordExtractionService.extractKeywords(
            prompt,
            deptUnitCode,
            recentUserMessages,
          );

          // Create search context using keywords - now returns both context and sources
          const searchResult: SearchResult =
            await this.searchContextService.createSearchContext(keywords);
          searchContext = searchResult.context;
          searchSources = searchResult.sources;

          if (searchContext && searchSources.length > 0) {
            this.logger.log(`✅ SEARCH CONTEXT INTEGRATION SUCCESSFUL`);
            this.logger.log(
              `📊 Search context length: ${searchContext.length} characters`,
            );
            this.logger.log(`📚 Found ${searchSources.length} search sources`);
            this.logger.log(
              `🔗 Context preview: "${searchContext.substring(0, 200)}${searchContext.length > 200 ? '...' : ''}"`,
            );
          } else {
            this.logger.warn(
              '⚠️ Search was requested but no context was generated',
            );
          }
        } catch (searchError) {
          const errorMessage =
            searchError instanceof Error
              ? searchError.message
              : String(searchError);
          const errorStack =
            searchError instanceof Error ? searchError.stack : undefined;
          this.logger.error(
            `❌ Error during search enhancement: ${errorMessage}`,
            errorStack,
          );
          // Continue without search context if search fails
          searchContext = '';
          searchSources = [];
        }
      } else if (useGoogle && !this.searchContextService.isSearchAvailable()) {
        this.logger.warn(
          '⚠️ Search requested but Google Search is not configured',
        );
      } else if (!useGoogle) {
        this.logger.debug(
          '🚫 Search not requested - proceeding without search enhancement',
        );
      }

      // --- Construct User Message (potentially multimodal) ---
      // Combine original prompt, search context, and OCR text
      const citationGuideline = `Please use the following search results to answer the user's question.
When you reference a search result you MUST add a citation immediately after the fact **with one single number per bracket** followed by the hyperlink, e.g.  [1](url1), [2](url2).
NEVER group numbers like [1, 2] or [3–5]. List them individually.
Do not invent URLs or information.`;

      const userPromptText =
        (searchContext
          ? citationGuideline + '\n\n' + searchContext + '\n\n'
          : '') +
        prompt +
        processedFileContent;
      let userMessageContent: any = userPromptText;

      if (searchContext) {
        this.logger.log(`🔗 ENHANCED PROMPT CREATED:`);
        this.logger.log(
          `📏 Original prompt length: ${prompt.length} characters`,
        );
        this.logger.log(
          `📏 Search context length: ${searchContext.length} characters`,
        );
        this.logger.log(
          `📏 Final enhanced prompt length: ${userPromptText.length} characters`,
        );
        this.logger.log(
          `🔍 Enhanced prompt preview: "${userPromptText.substring(0, 300)}${userPromptText.length > 300 ? '...' : ''}"`,
        );
      }

      if (imageParts.length > 0) {
        // Construct multimodal content array
        userMessageContent = [{ type: 'text', text: userPromptText }];
        userMessageContent.push(...imageParts);
        this.logger.debug(
          `Constructed multimodal message content with ${imageParts.length} images.`,
        );
      } else {
        if (!userPromptText && files && files.length > 0) {
          // Handle case where only files were uploaded, no text prompt
          userMessageContent = `[User uploaded ${files.length} file(s): ${fileNamesForPrompt.join(', ')}. Please analyze them.]`;
          this.logger.debug(
            `No text prompt, using placeholder for file analysis request.`,
          );
        } else if (!userPromptText) {
          // This case should ideally be caught by frontend validation, but handle defensively
          this.logger.error(
            `Empty prompt and no files provided for conversation ${currentConversationUUID}.`,
          );
          throw new BadRequestException(
            'Prompt cannot be empty if no files are provided.',
          );
        }
        // Otherwise, userMessageContent remains the combined text string
      }

      // --- Get LLM Config ---
      const llmConfig = await this.llmConfigService.getLlmConfig(
        deptUnitCode,
        effectiveModel,
      );

      // Handle System Message based on model support and retry logic
      // Use instructions from DTO for new conversations, or from DB for existing ones
      const systemInstructions = isNewConversation 
        ? createChatCompletionDto.instructions 
        : conversationParams?.instructions || createChatCompletionDto.instructions;
      const llmMessages: BaseMessage[] = [];
      let processedUserMessageContent = userMessageContent;

      if (systemInstructions && systemInstructions.trim().length > 0) {
        // Check if we should force system message conversion due to retry
        const shouldConvertSystemMessages =
          forceSystemMessageConversion ||
          llmConfig.supportsSystemMessages === false;

        if (!shouldConvertSystemMessages) {
          // Model supports system messages and no forced conversion - add as SystemMessage
          llmMessages.push(new SystemMessage(systemInstructions));
          this.logger.debug(
            `Added system message for model ${effectiveModel} (supports system messages)`,
          );
        } else {
          // Model doesn't support system messages OR we're forcing conversion due to retry
          if (forceSystemMessageConversion) {
            this.logger.warn(
              `[RETRY] Force converting system message to user prefix for model ${effectiveModel} due to previous error`,
            );
          } else {
            this.logger.warn(
              `Model ${effectiveModel} doesn't support system messages, converting to user message prefix`,
            );
          }

          const systemPrefix = `System Instructions: ${systemInstructions}\n\n---\n\n`;

          // If userMessageContent is a string, prepend the system instructions
          if (typeof processedUserMessageContent === 'string') {
            processedUserMessageContent =
              systemPrefix + processedUserMessageContent;
          } else if (Array.isArray(processedUserMessageContent)) {
            // For multimodal content, prepend to the first text part
            const firstTextPart = processedUserMessageContent.find(
              (part) => part.type === 'text',
            );
            if (firstTextPart) {
              firstTextPart.text = systemPrefix + firstTextPart.text;
            } else {
              // If no text part exists, add one at the beginning
              processedUserMessageContent.unshift({
                type: 'text',
                text: systemPrefix,
              });
            }
          }
        }
      }

      // Build the complete message list, potentially applying system message conversion to history as well
      if (forceSystemMessageConversion) {
        // Convert any system messages in chat history when retrying
        const { convertedMessages } = convertSystemMessagesToUserPrefix(
          [...chatHistory],
          processedUserMessageContent,
          this.logger,
        );
        llmMessages.push(...convertedMessages);
        // Only add new user message if it's not a regeneration (to avoid duplication)
        if (!isRegeneration) {
          llmMessages.push(new HumanMessage({ content: processedUserMessageContent }));
        }
      } else {
        llmMessages.push(...chatHistory);
        // Only add new user message if it's not a regeneration (to avoid duplication)
        if (!isRegeneration) {
          llmMessages.push(new HumanMessage({ content: processedUserMessageContent }));
        }
      }

      // --- Validate Message Chain for Duplicates ---
      if (llmMessages.length >= 2) {
        for (let i = 1; i < llmMessages.length; i++) {
          const currentMsg = llmMessages[i];
          const prevMsg = llmMessages[i - 1];
          
          // Check for consecutive messages from same sender with identical content
          if (currentMsg.constructor.name === prevMsg.constructor.name) {
            const currentContent = typeof currentMsg.content === 'string' ? currentMsg.content : JSON.stringify(currentMsg.content);  
            const prevContent = typeof prevMsg.content === 'string' ? prevMsg.content : JSON.stringify(prevMsg.content);
            
            if (currentContent === prevContent) {
              this.logger.warn(
                `[VALIDATION] Detected duplicate consecutive messages from ${currentMsg.constructor.name}: "${currentContent.substring(0, 100)}${currentContent.length > 100 ? '...' : ''}"`
              );
              // Log the context for debugging
              this.logger.warn(
                `[VALIDATION] Context - isRegeneration: ${isRegeneration}, total messages: ${llmMessages.length}, duplicate at positions: ${i-1}, ${i}`
              );
            }
          }
        }
      }

      // --- Log Message Chain Summary for LLM Request ---
      this.logger.debug(
        `[MESSAGE CHAIN] Prepared ${llmMessages.length} messages for LLM (isRegeneration: ${isRegeneration}):`
      );
      llmMessages.forEach((msg, index) => {
        const content = typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content);
        const preview = content.substring(0, 50) + (content.length > 50 ? '...' : '');
        this.logger.debug(`[MESSAGE CHAIN] ${index}: ${msg.constructor.name} - "${preview}"`);
      });

      // --- Save User Message ---
      // For regeneration, reuse existing user message instead of creating duplicate
      this.logger.log(`[REGENERATION] isRegeneration flag: ${isRegeneration}`);
      if (!isRegeneration) {
        this.logger.log(`[REGENERATION] Normal flow - creating new user message`);
        this.logger.log(`[REGENERATION] Prompt content: ${prompt.substring(0, 100)}...`);
        // Normal flow - save new user message
        // Correct contentToSave: ONLY save original prompt + file indicator, NOT OCR text
        const contentToSave =
          prompt +
          (fileNamesForPrompt.length > 0
            ? ` [Files: ${fileNamesForPrompt.join(', ')}]`
            : '');
        const modelIdentifierForSaving =
          validModel.model_name ?? validModel.deployment_name ?? effectiveModel;
        // Determine effective temperature for saving (used by SP, might differ from LLM call)
        const effectiveTemperatureForSaving =
          dtoTemperature ?? conversationParams?.temperature ?? 1.0;
        const savedUserMessage = await this.saveMessageWithSP(
          uuidv4(),
          currentConversationUUID,
          contentToSave,
          // Use effective temperature for saving
          effectiveTemperatureForSaving,
          llmConfig.instanceName ?? llmConfig.region ?? 'N/A',
          modelIdentifierForSaving,
          'user',
          0,
          userId,
          new Date(),
          undefined,
          usedMention,
        );
        userMessageId = savedUserMessage.inserted_message_id;
      } else {
        // Regeneration flow - find and reuse existing user message
        this.logger.log(
          `[REGENERATION] Regeneration flow - looking for existing user message`);
        this.logger.log(
          `[REGENERATION] Conversation: ${currentConversationUUID}, ID: ${conversationId}`,
        );

        const lastUserMessage = await this.prisma.message.findFirst({
          where: {
            conversation_id: conversationId,
            sender: 'user',
            is_deleted: false,
          },
          orderBy: {
            create_dt: 'desc',
          },
          select: {
            message_id: true,
            message_uuid: true,
          },
        });

        this.logger.log(
          `[REGENERATION] User message search result:`,
          lastUserMessage ? {
            message_id: lastUserMessage.message_id,
            message_uuid: lastUserMessage.message_uuid
          } : 'null'
        );

        if (lastUserMessage) {
          userMessageId = lastUserMessage.message_id;
          this.logger.log(
            `[REGENERATION] Reusing existing user message ${lastUserMessage.message_uuid} (ID: ${lastUserMessage.message_id})`,
          );
        } else {
          // Fallback: if somehow no user message found, create new one
          this.logger.warn(
            `[REGENERATION] No existing user message found, creating new one`,
          );
          const contentToSave =
            prompt +
            (fileNamesForPrompt.length > 0
              ? ` [Files: ${fileNamesForPrompt.join(', ')}]`
              : '');
          const modelIdentifierForSaving =
            validModel.model_name ??
            validModel.deployment_name ??
            requestedModel;
          const effectiveTemperatureForSaving =
            dtoTemperature ?? conversationParams?.temperature ?? 1.0;
          const savedUserMessage = await this.saveMessageWithSP(
            uuidv4(),
            currentConversationUUID,
            contentToSave,
            effectiveTemperatureForSaving,
            llmConfig.instanceName ?? llmConfig.region ?? 'N/A',
            modelIdentifierForSaving,
            'user',
            0,
            userId,
            new Date(),
            undefined,
            usedMention,
          );
          userMessageId = savedUserMessage.inserted_message_id;
        }
      }

      // --- Trigger Title Generation (Async) based on user message count ---
      if (conversationId) {
        // Ensure we have a conversation ID
        // Get user message count *after* saving the current message
        const userMessageCount = await this.getUserMessageCount(
          currentConversationUUID,
        );
        this.logger.debug(
          `[TitleGen Trigger] User message count for ${currentConversationUUID}: ${userMessageCount}`,
        );

        // Trigger on 1st message OR 5th, 10th, 15th...
        if (userMessageCount === 1) {
          this.logger.log(
            `[TitleGen Trigger] Triggering title generation for count ${userMessageCount}`,
          );
          // Don't await this, let it run in the background
          this.generateAndSaveTitle(
            currentConversationUUID,
            conversationId,
            userMessageCount,
            prompt,
          ).catch((err) =>
            this.logger.error(
              `Background title generation failed: ${err.message}`,
              err.stack,
            ),
          );
        }
      }

      // Note: Search functionality is now handled via keyword extraction and context injection
      // No tool binding is required - all models can use search via the enhanced prompt approach

      // --- Determine Effective LLM Parameters ---
      // For new conversations, use DTO values; for existing, prefer conversation params then DTO
      const effectiveTemperature = isNewConversation
        ? (dtoTemperature ?? 0.6)
        : (dtoTemperature ?? conversationParams?.temperature ?? 0.6);
      const effectiveMaxTokens = isNewConversation
        ? (createChatCompletionDto.maxResponseTokens ?? undefined)
        : (conversationParams?.maxResponseTokens ?? createChatCompletionDto.maxResponseTokens ?? undefined);
      const effectiveTopP = isNewConversation
        ? (createChatCompletionDto.topP ?? undefined)
        : (conversationParams?.topP ?? createChatCompletionDto.topP ?? undefined);
      this.logger.debug(
        `Effective LLM Params: Temp=${effectiveTemperature}, MaxTokens=${effectiveMaxTokens}, TopP=${effectiveTopP}`,
      );

      // --- Pre-request Token Limit Check (only checks if already exceeded) ---
      const tokenLimitCheck = await this.tokenUsageService.checkTokenLimit(
        user,
        effectiveModel,
      );

      if (!tokenLimitCheck.allowed) {
        this.logger.warn(
          `User ${userId} has already exceeded monthly token limit for model ${effectiveModel}: ${tokenLimitCheck.message}`,
        );
        throw new HttpException(
          tokenLimitCheck.message || 'Monthly token limit exceeded',
          HttpStatus.TOO_MANY_REQUESTS, // 429 status code for rate limiting
        );
      }

      this.logger.debug(
        `Token limit check passed. Current usage: ${tokenLimitCheck.currentUsage}, Limit: ${tokenLimitCheck.monthlyLimit}, Remaining: ${tokenLimitCheck.remaining}`,
      );

      // --- Handle Streaming vs Non-Streaming ---
      // Determine if streaming is intended (only true if explicitly set to true)
      const isStreaming = stream === true;
      this.logger.debug(
        `Stream parameter: ${stream}, Determined isStreaming: ${isStreaming}`,
      );

      if (!isStreaming) {
        // Non-streaming path (only if stream was explicitly false)
        this.logger.log(
          `Processing NON-STREAMING response for conv ${currentConversationUUID}`,
        );
        let aiContent: string = '';
        let promptTokens = 0;
        let sources: SearchSource[] = []; // Declare sources here for the non-streaming scope
        let completionTokens = 0;
        const aiMessageUUID = uuidv4();

        // --- Non-Streaming Logic ---
        if (llmConfig.provider === 'gcp-vertexai-gemini') {
          // Use VertexGeminiService for Gemini
          if (!conversationId || !userMessageId) {
            throw new InternalServerErrorException(
              'Internal IDs missing for Vertex AI Gemini request.',
            );
          }
          const vertexAiOptions: LlmStreamOptions = {
            modelConfig: {
              model_name: llmConfig.modelName ?? 'gemini-pro',
              region: llmConfig.region ?? '',
              projectId: llmConfig.projectId ?? '',
            },
            messages: llmMessages.map((m) => ({
              role:
                m instanceof HumanMessage
                  ? 'user'
                  : m instanceof AIMessage
                    ? 'assistant'
                    : 'system',
              content:
                typeof m.content === 'string'
                  ? m.content
                  : JSON.stringify(m.content),
            })),
            temperature: effectiveTemperature,
            // Add maxTokens/topP if VertexGeminiService supports them
            conversationId: conversationId,
            dialogId: currentConversationUUID,
            userMessageId: userMessageId,
            user: {
              userId: user.userId,
              dept_unit_code: user.dept_unit_code,
              type: user.type,
              email: user.email,
            }, // Pass user info from AuthenticatedUser
            useGoogle: useGoogle, // Pass the useGoogle flag
            requestedModelName: effectiveModel, // Pass the effective model name for token tracking
          };
          this.logger.debug(
            `[LLM Request Log - Non-Streaming Vertex Gemini] Options: ${JSON.stringify(vertexAiOptions)}`,
          );
          const vertexAiStream =
            await this.vertexGeminiService.createChatCompletionStream(
              vertexAiOptions,
            );
          // Adapt stream to non-stream (assuming service handles logging internally)
          aiContent = await new Promise<string>((resolve, reject) => {
            let accumulated = '';
            vertexAiStream.on('data', (chunk) => {
              try {
                const data = JSON.parse(
                  chunk.toString().replace(/^data: /, ''),
                );
                if (data.choices?.[0]?.delta?.content)
                  accumulated += data.choices[0].delta.content;
              } catch (e) {
                /* ignore non-json */
              }
            });
            vertexAiStream.on('end', () => resolve(accumulated));
            vertexAiStream.on('error', reject);
          });
          promptTokens = 0;
          completionTokens = 0; // Token count handled by service internally
          this.logger.log(
            `Vertex AI Gemini non-streaming call completed via stream adaptation.`,
          );
        } else if (llmConfig.provider === 'gcp-vertexai-llama') {
          // Use VertexLlamaService for Llama
          if (!conversationId || !userMessageId) {
            throw new InternalServerErrorException(
              'Internal IDs missing for Vertex AI Llama request.',
            );
          }
          const vertexLlamaOptions: LlmStreamOptions = {
            modelConfig: {
              model_name: llmConfig.modelName ?? 'llama-unknown',
              region: llmConfig.region ?? '',
              projectId: llmConfig.projectId ?? '',
            },
            messages: llmMessages.map((m) => ({
              role:
                m instanceof HumanMessage
                  ? 'user'
                  : m instanceof AIMessage
                    ? 'assistant'
                    : 'system',
              content:
                typeof m.content === 'string'
                  ? m.content
                  : JSON.stringify(m.content),
            })),
            temperature: effectiveTemperature,
            conversationId: conversationId,
            dialogId: currentConversationUUID,
            userMessageId: userMessageId,
            user: {
              userId: user.userId,
              dept_unit_code: user.dept_unit_code,
              type: user.type,
              email: user.email,
            }, // Pass user info from AuthenticatedUser
            requestedModelName: effectiveModel, // Pass the effective model name for token tracking
          };
          this.logger.debug(
            `[LLM Request Log - Non-Streaming Vertex Llama] Options: ${JSON.stringify(vertexLlamaOptions)}`,
          );
          const vertexLlamaStream =
            await this.vertexLlamaService.createChatCompletionStream(
              vertexLlamaOptions,
            );
          // Adapt stream to non-stream (assuming service handles logging internally)
          aiContent = await new Promise<string>((resolve, reject) => {
            let accumulated = '';
            vertexLlamaStream.on('data', (chunk) => {
              try {
                const data = JSON.parse(
                  chunk.toString().replace(/^data: /, ''),
                );
                if (data.choices?.[0]?.delta?.content)
                  accumulated += data.choices[0].delta.content;
              } catch (e) {
                /* ignore non-json */
              }
            });
            vertexLlamaStream.on('end', () => resolve(accumulated));
            vertexLlamaStream.on('error', reject);
          });
          promptTokens = 0;
          completionTokens = 0; // Token count handled by service internally
          this.logger.log(
            `Vertex AI Llama non-streaming call completed via stream adaptation.`,
          );
        } else if (llmConfig.provider === 'deepseek-aisvc') {
          const modelToUse = new ChatOpenAI(
            this.createModelConfig(
              {
                // Using ChatOpenAI for DeepSeek's OpenAI-compatible API
                apiKey: llmConfig.apiKey,
                configuration: {
                  baseURL: `${llmConfig.endpointUrl?.replace(/\/$/, '')}/v1`, // Ensure /v1 for DeepSeek
                },
                modelName:
                  validModel.deployment_name ??
                  validModel.model_name ??
                  llmConfig.modelName ??
                  'deepseek-chat',
                maxTokens: effectiveMaxTokens,
                topP: effectiveTopP,
              },
              effectiveTemperature,
              llmConfig,
            ),
          );

          // DeepSeek uses direct model invocation (no tool support needed with new search approach)
          this.logger.debug(
            `Provider deepseek-aisvc proceeding with direct model call (non-streaming).`,
          );
          const messagesToInvoke = llmMessages;
          const config: RunnableConfig = {};
          this.logger.debug(
            `[LLM Request Log - Non-Streaming Direct DeepSeek] Model: ${JSON.stringify(modelToUse.lc_kwargs)}, Messages: ${JSON.stringify(messagesToInvoke)}, Params: { Temp: ${effectiveTemperature}, MaxTokens: ${effectiveMaxTokens}, TopP: ${effectiveTopP} }`,
          );
          const aiResponse = await modelToUse.invoke(messagesToInvoke, config);
          promptTokens =
            (aiResponse.response_metadata?.tokenUsage
              ?.prompt_tokens as number) ??
            (aiResponse.response_metadata?.llmOutput?.usage
              ?.prompt_tokens as number) ??
            0;
          completionTokens =
            (aiResponse.response_metadata?.tokenUsage
              ?.completion_tokens as number) ??
            (aiResponse.response_metadata?.llmOutput?.usage
              ?.completion_tokens as number) ??
            0;
          aiContent =
            typeof aiResponse.content === 'string'
              ? aiResponse.content
              : JSON.stringify(aiResponse.content);
          sources = []; // Sources are now embedded in search context, not extracted separately
        } else if (
          ['azure', 'google', 'anthropic', 'qwen'].includes(llmConfig.provider)
        ) {
          // Handle other Langchain providers
          let modelToUse: BaseChatModel;
          switch (llmConfig.provider) {
            case 'azure':
              // Debug log Azure configuration
              this.logger.debug(
                `[Azure Config] Creating ChatOpenAI (like test) with: instance=${llmConfig.instanceName}, deployment=${validModel.deployment_name}, apiVersion=${llmConfig.apiVersion}`,
              );

              // Using exact same format as working test
              modelToUse = new ChatOpenAI(
                this.createModelConfig(
                  {
                    maxTokens: effectiveMaxTokens,
                    topP: effectiveTopP,
                    modelName:
                      validModel.deployment_name ?? 'unknown-azure-deployment',
                    streaming: false, // Explicitly set streaming to false for non-streaming
                    streamUsage: true, // Enable token usage tracking for non-streaming
                    configuration: {
                      apiKey: llmConfig.apiKey,
                      baseURL: `https://${llmConfig.instanceName}.openai.azure.com/openai/deployments/${validModel.deployment_name ?? 'unknown-azure-deployment'}`,
                      defaultQuery: { 'api-version': llmConfig.apiVersion },
                    },
                    // Removed modelKwargs with stream_options as it's only valid when streaming is true
                  },
                  effectiveTemperature,
                  llmConfig,
                ),
              );
              break;
            case 'google':
              modelToUse = new ChatGoogleGenerativeAI(
                this.createModelConfig(
                  {
                    apiKey:
                      llmConfig.apiKey ||
                      this.configService.get<string>('GOOGLE_API_KEY'),
                    model: llmConfig.modelName ?? 'gemini-pro',
                    maxOutputTokens: effectiveMaxTokens,
                    topP: effectiveTopP,
                  },
                  effectiveTemperature,
                  llmConfig,
                ),
              );
              break;
            case 'anthropic':
              modelToUse = new ChatAnthropic(
                this.createModelConfig(
                  {
                    apiKey:
                      llmConfig.apiKey ||
                      this.configService.get<string>('ANTHROPIC_API_KEY'),
                    modelName: llmConfig.modelName,
                    maxTokens: effectiveMaxTokens,
                    topP: effectiveTopP,
                  },
                  effectiveTemperature,
                  llmConfig,
                ),
              );
              break;
            case 'qwen':
              let apiKeyForQwen = llmConfig.apiKey;
              if (!apiKeyForQwen) {
                this.logger.warn(
                  `Qwen API key not found in llmConfig for provider '${llmConfig.provider}' (non-streaming), attempting to fetch from ConfigService.`,
                );
                apiKeyForQwen =
                  this.configService.get<string>('ALIBABA_API_KEY');
                if (!apiKeyForQwen) {
                  this.logger.error(
                    `Qwen API key is missing from llmConfig and could not be fetched from ConfigService using env var 'ALIBABA_API_KEY' for non-streaming.`,
                  );
                  throw new InternalServerErrorException(
                    'Qwen API key is not configured for non-streaming.',
                  );
                }
              }
              this.logger.debug(
                `[QWEN CONSTRUCTOR CHECK - Non-Streaming Tools] Passing to ChatAlibabaTongyi - alibabaApiKey: ${apiKeyForQwen ? apiKeyForQwen.substring(0, 5) + '...' + apiKeyForQwen.substring(apiKeyForQwen.length - 5) : 'NOT FOUND'}`,
              );
              modelToUse = new ChatAlibabaTongyi(
                this.createModelConfig(
                  {
                    alibabaApiKey: apiKeyForQwen, // Pass the API key using the correct constructor field
                    model:
                      validModel.deployment_name ??
                      validModel.model_name ??
                      'qwen-plus',
                    streaming: true, // Ensure streaming is enabled for tool use
                  },
                  effectiveTemperature,
                  llmConfig,
                ),
              );
              break;
            default: // Should not be reached
              this.logger.error(
                `Internal error: Unhandled non-streaming provider in switch: ${llmConfig.provider}`,
              );
              throw new InternalServerErrorException(
                `Internal error: Unhandled non-streaming provider in switch: ${llmConfig.provider}`,
              );
          }

          // === Use Model Directly (no tools needed with new search approach) ===
          this.logger.debug(
            `Provider ${llmConfig.provider} proceeding with direct model call (non-streaming).`,
          );
          const messagesToInvoke = llmMessages;
          const config: RunnableConfig = {};
          this.logger.debug(
            `[LLM Request Log - Non-Streaming Direct] Model: ${JSON.stringify(modelToUse.lc_kwargs)}, Messages: ${JSON.stringify(messagesToInvoke)}, Params: { Temp: ${effectiveTemperature}, MaxTokens: ${effectiveMaxTokens}, TopP: ${effectiveTopP} }`,
          );
          const aiResponse = await modelToUse.invoke(messagesToInvoke, config);

          // Debug log for Azure non-streaming
          if (llmConfig.provider === 'azure') {
            this.logger.debug(
              `[Azure Non-Streaming Debug] Full response structure: ${JSON.stringify(aiResponse, null, 2)}`,
            );
          }

          // Enhanced token extraction for non-streaming responses
          const responseUsage =
            aiResponse.usage_metadata ?? // Standard LangChain
            aiResponse.response_metadata?.tokenUsage ??
            aiResponse.response_metadata?.usage_metadata ??
            aiResponse.response_metadata?.usage ?? // Azure OpenAI specific
            aiResponse.response_metadata?.llmOutput?.usage ?? // Alternative Azure OpenAI location
            {};

          promptTokens =
            (responseUsage.input_tokens as number) ?? // LangChain standard
            (responseUsage.promptTokenCount as number) ??
            (responseUsage.prompt_token_count as number) ??
            (responseUsage.prompt_tokens as number) ?? // Azure OpenAI format
            (aiResponse.response_metadata?.tokenUsage
              ?.prompt_tokens as number) ??
            (aiResponse.response_metadata?.llmOutput?.usage
              ?.prompt_tokens as number) ??
            0;
          completionTokens =
            (responseUsage.output_tokens as number) ?? // LangChain standard
            (responseUsage.completionTokenCount as number) ??
            (responseUsage.candidates_token_count as number) ??
            (responseUsage.completion_tokens as number) ?? // Azure OpenAI format
            (aiResponse.response_metadata?.tokenUsage
              ?.completion_tokens as number) ??
            (aiResponse.response_metadata?.llmOutput?.usage
              ?.completion_tokens as number) ??
            0;
          aiContent =
            typeof aiResponse.content === 'string'
              ? aiResponse.content
              : JSON.stringify(aiResponse.content);
          sources = []; // Sources are now embedded in search context, not extracted separately
        } else {
          throw new NotImplementedException(
            `Support for LLM provider '${llmConfig.provider}' is not yet implemented for non-streaming.`,
          );
        }

        // === Save AI Message & Update User Tokens (Common for all non-streaming) ===
        const modelIdentifierForSaving =
          validModel.model_name ?? validModel.deployment_name ?? effectiveModel;
        const savedAiMessage = await this.saveMessageWithSP(
          aiMessageUUID,
          currentConversationUUID,
          aiContent,
          effectiveTemperature, // Use temp for saving
          llmConfig.instanceName ?? llmConfig.region ?? 'N/A',
          modelIdentifierForSaving,
          'assistant',
          completionTokens,
          user.userId,
          new Date(),
          undefined,
          undefined, // Assistant messages don't use @mention
        );
        const aiMessageId = savedAiMessage.inserted_message_id; // Get the ID of the saved AI message

        // --- Update Token Usage ---
        // Log when we're using zero tokens vs actual token counts
        if (promptTokens === 0 || completionTokens === 0) {
          this.logger.warn(
            `[Token Usage] No token usage received from ${llmConfig.provider} (${modelIdentifierForSaving}) non-streaming, recording as zero tokens.`,
          );
        } else {
          this.logger.debug(
            `[Token Usage] Received actual token counts from ${llmConfig.provider} (${modelIdentifierForSaving}) non-streaming: prompt=${promptTokens}, completion=${completionTokens}`,
          );
        }

        // Use actual token counts from LLM response, fallback to zero if not available
        const actualPromptTokens = promptTokens || 0;
        const actualCompletionTokens = completionTokens || 0;
        const actualTotalTokens = actualPromptTokens + actualCompletionTokens;

        await this.tokenUsageService.updateTokenUsage({
          username: userId,
          modelName: effectiveModel,
          tokenDate: new Date(),
          promptTokens: actualPromptTokens,
          completionTokens: actualCompletionTokens,
          totalTokens: actualTotalTokens,
          isApi: false, // This is a web UI request, not API
          conversationUuid: currentConversationUUID,
        });

        this.logger.debug(
          `Updated token usage for ${userId}/${effectiveModel}: prompt=${actualPromptTokens}, completion=${actualCompletionTokens}, total=${actualTotalTokens}`,
        );

        // Check if user has now exceeded their limit after this request
        const postUsageCheck =
          await this.tokenUsageService.checkTokenLimitPostUsage(
            user,
            effectiveModel,
          );

        if (postUsageCheck.exceeded) {
          this.logger.warn(
            `User ${userId} has exceeded monthly token limit after this request: ${postUsageCheck.message}`,
          );
        }

        // --- Save Sources (Non-Streaming) ---
        if (aiMessageId && searchSources.length > 0) {
          this.logger.log(
            `💾 Saving ${searchSources.length} search sources to database for AI message ${aiMessageId}`,
          );
          await this.saveSources(aiMessageId, searchSources);
          this.logger.log(
            `✅ Sources saved to database for message ${aiMessageId}`,
          );
        }
        // --- End Save Sources ---

        if (userMessageId && promptTokens > 0) {
          await this.updateUserMessageTokenCount(userMessageId, promptTokens);
          this.logger.log(
            `Updated user message ${userMessageId} with ${promptTokens} prompt tokens.`,
          );
        } else if (userMessageId) {
          this.logger.warn(
            `Prompt token count unavailable for user message ${userMessageId} for provider ${llmConfig.provider}.`,
          );
        }

        // --- Format and Return Response (Common for all non-streaming) ---
        return {
          id: aiMessageUUID,
          object: 'chat.completion',
          created: Math.floor(Date.now() / 1000),
          model:
            validModel.deployment_name ??
            validModel.model_name ??
            effectiveModel,
          conversation_uuid: currentConversationUUID,
          choices: [
            {
              index: 0,
              message: { role: 'assistant', content: aiContent },
              finish_reason: 'stop',
            },
          ],
          usage: {
            prompt_tokens: promptTokens,
            completion_tokens: completionTokens,
            total_tokens: promptTokens + completionTokens,
          },
          sources: searchSources, // Add search sources array to the response
        };
      } else {
        // Streaming path (stream is true or undefined)
        this.logger.log(
          `Processing STREAMING response for conv ${currentConversationUUID}`,
        );
        const passthrough = new PassThrough();
        let modelOrRunnable: BaseChatModel;

        // --- Send Sources as Streaming Chunk (if available) ---
        if (searchSources.length > 0) {
          this.logger.log(
            `📚 Streaming ${searchSources.length} search sources to frontend`,
          );
          try {
            // Validate source data structure before serialization
            const cleanedSources = searchSources
              .map((source) => ({
                title: String(source.title || '').trim(),
                link: String(source.link || '').trim(),
                snippet: String(source.snippet || '').trim(),
              }))
              .filter(
                (source) => source.title && source.link && source.snippet,
              );

            const sourcesChunk = {
              type: 'sources',
              sources: cleanedSources,
            };
            const jsonString = JSON.stringify(sourcesChunk);
            passthrough.write(`data: ${jsonString}\n\n`);
            this.logger.debug(
              `✅ Sources chunk sent to frontend (${cleanedSources.length} valid sources)`,
            );
          } catch (jsonError) {
            this.logger.error(
              `❌ Failed to serialize sources for streaming: ${jsonError}`,
            );
            this.logger.debug(`Problematic sources data:`, searchSources);
          }
        }

        // === Streaming Logic ===
        if (
          ['azure', 'google', 'anthropic', 'qwen'].includes(llmConfig.provider)
        ) {
          // Removed 'deepseek-aisvc' as it doesn't support OpenAI-compatible endpoints
          // --- Langchain Models ---
          let modelToUse: BaseChatModel;

          switch (llmConfig.provider) {
            case 'azure':
              // Debug log for streaming Azure configuration
              this.logger.debug(
                `[Azure Streaming Config] Creating ChatOpenAI for streaming with: instance=${llmConfig.instanceName}, deployment=${validModel.deployment_name}, apiVersion=${llmConfig.apiVersion}`,
              );

              // CRITICAL: Use exact working configuration from test script
              modelToUse = new ChatOpenAI(
                this.createModelConfig(
                  {
                    modelName:
                      validModel.deployment_name ?? 'unknown-azure-deployment',
                    streaming: true,
                    streamUsage: true,
                    configuration: {
                      apiKey: llmConfig.apiKey,
                      baseURL: `https://${llmConfig.instanceName}.openai.azure.com/openai/deployments/${validModel.deployment_name ?? 'unknown-azure-deployment'}`,
                      defaultQuery: { 'api-version': llmConfig.apiVersion },
                    },
                    modelKwargs: {
                      stream_options: { include_usage: true },
                    },
                  },
                  effectiveTemperature,
                  llmConfig,
                ),
              );

              // Additional debug to confirm configuration matches test
              this.logger.debug(
                `[Azure Config Debug] streamUsage=${true}, stream_options=${JSON.stringify({ include_usage: true })}, modelName=${validModel.deployment_name}`,
              );
              break;
            case 'google':
              modelToUse = new ChatGoogleGenerativeAI(
                this.createModelConfig(
                  {
                    apiKey:
                      llmConfig.apiKey ||
                      this.configService.get<string>('GOOGLE_API_KEY'),
                    model: llmConfig.modelName ?? 'gemini-pro',
                    maxOutputTokens: effectiveMaxTokens, // Add maxOutputTokens
                    topP: effectiveTopP, // Add topP
                  },
                  effectiveTemperature,
                  llmConfig,
                ),
              );
              break;
            case 'anthropic':
              modelToUse = new ChatAnthropic(
                this.createModelConfig(
                  {
                    apiKey:
                      llmConfig.apiKey ||
                      this.configService.get<string>('ANTHROPIC_API_KEY'),
                    modelName: llmConfig.modelName,
                    maxTokens: effectiveMaxTokens, // Add maxTokens
                    topP: effectiveTopP, // Add topP
                  },
                  effectiveTemperature,
                  llmConfig,
                ),
              );
              break;
            case 'qwen':
              modelToUse = new ChatOpenAI(
                this.createModelConfig(
                  {
                    // Using ChatOpenAI for Qwen's OpenAI-compatible API
                    apiKey: llmConfig.apiKey,
                    configuration: {
                      baseURL: `${llmConfig.endpointUrl?.replace(/\/$/, '')}/compatible-mode/v1`,
                    },
                    modelName:
                      validModel.deployment_name ??
                      validModel.model_name ??
                      llmConfig.modelName ??
                      'qwen-plus',
                    maxTokens: effectiveMaxTokens,
                    topP: effectiveTopP,
                    streaming: true,
                    streamUsage: true, // Enable streaming token usage extraction
                    modelKwargs: {
                      stream_options: { include_usage: true }, // Request token usage in streaming
                    },
                  },
                  effectiveTemperature,
                  llmConfig,
                ),
              );
              break;
            default: // Should not be reached
              this.logger.error(
                `Unhandled Langchain stream provider: ${llmConfig.provider}`,
              );
              throw new NotImplementedException(
                `Streaming for ${llmConfig.provider} not implemented.`,
              );
          }

          // This part is now only reached for azure, google, anthropic in streaming mode
          const config: RunnableConfig = {}; // Pass empty config or config without source handler

          // === Use Model Directly (no tools needed with new search approach) ===
          this.logger.debug(
            `Provider ${llmConfig.provider} proceeding with direct model streaming.`,
          );
          const messagesToStream = llmMessages;

          // Enhanced debug logging for Azure and Qwen
          if (llmConfig.provider === 'azure') {
            this.logger.debug(
              `[Azure Stream Config] About to stream with: apiVersion=${llmConfig.apiVersion}, streamUsage=${modelToUse.lc_kwargs?.streamUsage}, streaming=${modelToUse.lc_kwargs?.streaming}, modelKwargs=${JSON.stringify(modelToUse.lc_kwargs?.modelKwargs)}`,
            );
            this.logger.debug(
              `[Azure Full Config] Complete model config: ${JSON.stringify(
                {
                  modelName: modelToUse.lc_kwargs?.modelName,
                  streaming: modelToUse.lc_kwargs?.streaming,
                  streamUsage: modelToUse.lc_kwargs?.streamUsage,
                  configuration: modelToUse.lc_kwargs?.configuration,
                  modelKwargs: modelToUse.lc_kwargs?.modelKwargs,
                },
                null,
                2,
              )}`,
            );
          } else if (llmConfig.provider === 'qwen') {
            this.logger.debug(
              `[Qwen Stream Config] About to stream with: streamUsage=${modelToUse.lc_kwargs?.streamUsage}, streaming=${modelToUse.lc_kwargs?.streaming}, modelKwargs=${JSON.stringify(modelToUse.lc_kwargs?.modelKwargs)}`,
            );
            this.logger.debug(
              `[Qwen Full Config] Complete model config: ${JSON.stringify(
                {
                  modelName: modelToUse.lc_kwargs?.modelName,
                  streaming: modelToUse.lc_kwargs?.streaming,
                  streamUsage: modelToUse.lc_kwargs?.streamUsage,
                  configuration: modelToUse.lc_kwargs?.configuration,
                  modelKwargs: modelToUse.lc_kwargs?.modelKwargs,
                },
                null,
                2,
              )}`,
            );
          }

          this.logger.debug(
            `[LLM Request Log - Streaming Direct] Model: ${JSON.stringify(modelToUse.lc_kwargs)}, Messages: ${JSON.stringify(messagesToStream)}, Params: { Temp: ${effectiveTemperature}, MaxTokens: ${effectiveMaxTokens}, TopP: ${effectiveTopP} }`,
          );
          const langchainStream = await modelToUse.stream(
            messagesToStream,
            config,
          );
          this.processLangchainStream(
            langchainStream,
            passthrough,
            currentConversationUUID,
            effectiveTemperature,
            llmConfig,
            userId,
            userMessageId,
            searchSources,
            llmMessages, // Pass llmMessages for token counting
            validModel.deployment_name ?? undefined,
            validModel.model_name ?? undefined,
            validModel.display_name ?? undefined,
          );
          return passthrough; // Return the passthrough stream for azure, google, anthropic (Langchain handled)
        } else if (llmConfig.provider === 'gcp-vertexai-gemini') {
          // --- Vertex Gemini REST API Stream ---
          if (!conversationId || !userMessageId) {
            throw new InternalServerErrorException(
              'Internal IDs missing for Vertex AI Gemini request.',
            );
          }
          const vertexAiOptions: LlmStreamOptions = {
            modelConfig: {
              model_name: llmConfig.modelName ?? 'gemini-pro',
              region: llmConfig.region ?? '',
              projectId: llmConfig.projectId ?? '',
            },
            messages: llmMessages.map((m) => ({
              role:
                m instanceof HumanMessage
                  ? 'user'
                  : m instanceof AIMessage
                    ? 'assistant'
                    : 'system',
              content:
                typeof m.content === 'string'
                  ? m.content
                  : JSON.stringify(m.content),
            })),
            temperature: effectiveTemperature,
            // Add maxTokens/topP if VertexGeminiService supports them
            conversationId: conversationId,
            dialogId: currentConversationUUID,
            userMessageId: userMessageId,
            user: {
              userId: user.userId,
              dept_unit_code: user.dept_unit_code,
              type: user.type,
              email: user.email,
            }, // Pass user info from AuthenticatedUser
            useGoogle: useGoogle, // Pass the useGoogle flag
            searchSources: searchSources, // Pass the search sources for saving
            requestedModelName: effectiveModel, // Pass the effective model name for token tracking
          };
          this.logger.debug(
            `[LLM Request Log - Streaming Vertex Gemini] Options: ${JSON.stringify(vertexAiOptions)}`,
          );
          // Directly return the stream from VertexGeminiService
          return await this.vertexGeminiService.createChatCompletionStream(
            vertexAiOptions,
          );
        } else if (llmConfig.provider === 'gcp-vertexai-llama') {
          // --- Vertex Llama REST API Stream ---
          if (!conversationId || !userMessageId) {
            throw new InternalServerErrorException(
              'Internal IDs missing for Vertex AI Llama request.',
            );
          }
          const vertexLlamaOptions: LlmStreamOptions = {
            modelConfig: {
              model_name: llmConfig.modelName ?? 'llama-unknown',
              region: llmConfig.region ?? '',
              projectId: llmConfig.projectId ?? '',
            },
            messages: llmMessages.map((m) => ({
              role:
                m instanceof HumanMessage
                  ? 'user'
                  : m instanceof AIMessage
                    ? 'assistant'
                    : 'system',
              content:
                typeof m.content === 'string'
                  ? m.content
                  : JSON.stringify(m.content),
            })),
            temperature: effectiveTemperature,
            conversationId: conversationId,
            dialogId: currentConversationUUID,
            userMessageId: userMessageId,
            user: {
              userId: user.userId,
              dept_unit_code: user.dept_unit_code,
              type: user.type,
              email: user.email,
            }, // Pass user info from AuthenticatedUser
            searchSources: searchSources, // Pass the search sources for saving
            requestedModelName: effectiveModel, // Pass the effective model name for token tracking
          };
          this.logger.debug(
            `[LLM Request Log - Streaming Vertex Llama] Options: ${JSON.stringify(vertexLlamaOptions)}`,
          );
          // Directly return the stream from VertexLlamaService
          return await this.vertexLlamaService.createChatCompletionStream(
            vertexLlamaOptions,
          );
        } else if (llmConfig.provider === 'deepseek-aisvc') {
          // --- DeepSeek Direct Stream (Azure AI Service native API) ---
          if (!conversationId || !userMessageId) {
            throw new InternalServerErrorException(
              'Internal IDs missing for DeepSeek request.',
            );
          }
          const azureAIServiceOptions: LlmStreamOptions = {
            modelConfig: {
              deployment_name: validModel.deployment_name ?? 'deepseek',
              instanceName: llmConfig.instanceName ?? '',
              endpoint_url: llmConfig.endpointUrl ?? '',
              api_key: llmConfig.apiKey ?? '',
              api_version: llmConfig.apiVersion ?? '',
            },
            messages: llmMessages.map((m) => ({
              role:
                m instanceof HumanMessage
                  ? 'user'
                  : m instanceof AIMessage
                    ? 'assistant'
                    : 'system',
              content:
                typeof m.content === 'string'
                  ? m.content
                  : JSON.stringify(m.content),
            })),
            temperature: effectiveTemperature, // Use effective param (check if AzureAIService supports it)
            // Add maxTokens/topP if AzureAIService supports them
            conversationId: conversationId,
            dialogId: currentConversationUUID,
            userMessageId: userMessageId,
            user: {
              userId: user.userId,
              dept_unit_code: user.dept_unit_code,
              type: user.type,
              email: user.email,
            }, // Pass user info from AuthenticatedUser
            searchSources: searchSources, // Pass the search sources for saving
            requestedModelName: effectiveModel, // Pass the effective model name for consistency
          };
          this.logger.debug(
            `[LLM Request Log - Streaming DeepSeek Direct] Options: ${JSON.stringify(azureAIServiceOptions)}`,
          );
          const directStream =
            await this.azureAIService.createChatCompletionStream(
              azureAIServiceOptions,
            );
          this.processDirectStream(
            directStream,
            passthrough,
            currentConversationUUID,
            searchSources,
            validModel.deployment_name ?? undefined,
            validModel.model_name ?? undefined,
            user.userId,
            llmMessages,
            effectiveModel,
            validModel.display_name ?? undefined,
          );
          return passthrough;
        } else {
          throw new NotImplementedException(
            `Support for LLM provider '${llmConfig.provider}' is not yet implemented.`,
          );
        }
      }
    } catch (error: any) {
      // Re-throw error to be handled by retry logic in createCompletion
      throw error;
    }
  }

  // --- Helper Functions ---
  /**
   * Creates model configuration object with optional temperature parameter based on model support
   */
  private createModelConfig(
    baseConfig: any,
    effectiveTemperature: number,
    llmConfig: any,
  ): any {
    const config = { ...baseConfig };
    
    // Only add temperature if the model supports it
    if (llmConfig.supportsTemperature !== false) {
      config.temperature = effectiveTemperature;
    } else {
      this.logger.debug(
        `Skipping temperature parameter for model ${llmConfig.modelName} (not supported)`,
      );
    }
    
    return config;
  }

  // --- Stream Processors ---
  private processLangchainStream(
    langchainStream: AsyncIterable<any>,
    passthrough: PassThrough,
    conversationUUID: string,
    temperature: number | undefined,
    llmConfig: LlmConfig,
    userId: string,
    userMessageId: number,
    searchSources: SearchSource[], // Add searchSources parameter
    llmMessages: BaseMessage[], // Add llmMessages parameter for token counting
    deploymentNameFromDb?: string,
    modelNameFromDb?: string,
    displayNameFromDb?: string,
  ): void {
    let accumulatedContent = '';
    let finalAiContent = '';
    const aiMessageUUID = uuidv4();
    let promptTokens = 0;
    let completionTokens = 0;
    const modelIdentifier =
      modelNameFromDb ?? deploymentNameFromDb ?? 'unknown';
    const modelDisplayName = displayNameFromDb ?? modelIdentifier;

    (async () => {
      try {
        let chunkCount = 0;
        let hasSeenFinishReason = false;
        let chunksAfterFinish = 0;
        for await (const chunk of langchainStream) {
          chunkCount++;

          // Track chunks after finish reason for Azure/Qwen debugging
          if (hasSeenFinishReason) {
            chunksAfterFinish++;
          }
          const contentChunk = chunk?.content;

          // ENHANCED QWEN DEBUGGING: Log entire chunk structure to find where tokens are
          if (
            llmConfig.provider === 'qwen' &&
            (chunk.response_metadata?.finish_reason || chunkCount > 7)
          ) {
            // Check if this is a raw response that needs parsing
            if (chunk.raw || chunk._raw || chunk.rawResponse) {
              const rawField = chunk.raw || chunk._raw || chunk.rawResponse;
              if (typeof rawField === 'string') {
                try {
                  const parsed = JSON.parse(rawField);
                  if (parsed.usage) {
                  }
                } catch (e) {
                  // Not JSON, ignore
                }
              }
            }
          }

          // Enhanced token extraction for Azure OpenAI and other providers
          let usageMetadata =
            chunk?.usage_metadata ?? // Standard LangChain usage metadata
            chunk?.response_metadata?.tokenUsage ??
            chunk?.response_metadata?.usage_metadata ??
            chunk?.response_metadata?.usage ?? // Azure OpenAI specific
            chunk?.usage; // Direct usage object

          // QWEN SPECIFIC: Check if usage data is in response_metadata but not in the expected format
          if (
            llmConfig.provider === 'qwen' &&
            chunk?.response_metadata?.usage
          ) {
            const qwenUsage = chunk.response_metadata.usage;

            // Check if it's an empty object and we need to look elsewhere
            if (
              Object.keys(qwenUsage).length === 0 &&
              chunk.response_metadata
            ) {
              // Sometimes Qwen puts usage data directly in response_metadata
              if (
                chunk.response_metadata.prompt_tokens !== undefined ||
                chunk.response_metadata.completion_tokens !== undefined
              ) {
                usageMetadata = {
                  prompt_tokens: chunk.response_metadata.prompt_tokens,
                  completion_tokens: chunk.response_metadata.completion_tokens,
                  total_tokens: chunk.response_metadata.total_tokens,
                };
              }
            }
          }

          // Log what we found for debugging
          if (
            (llmConfig.provider === 'azure' || llmConfig.provider === 'qwen') &&
            usageMetadata
          ) {
          }

          // Extract tokens from various possible locations
          const chunkPromptTokens =
            (usageMetadata?.input_tokens as number) ?? // LangChain standard (THIS IS WHERE AZURE SENDS IT!)
            (usageMetadata?.promptTokenCount as number) ??
            (usageMetadata?.prompt_token_count as number) ??
            (usageMetadata?.prompt_tokens as number) ?? // Azure OpenAI format
            0;
          const chunkCompletionTokens =
            (usageMetadata?.output_tokens as number) ?? // LangChain standard (THIS IS WHERE AZURE SENDS IT!)
            (usageMetadata?.completionTokenCount as number) ??
            (usageMetadata?.candidates_token_count as number) ??
            (usageMetadata?.completion_tokens as number) ?? // Azure OpenAI format
            0;

          // Debug log extracted values for Qwen - log even if 0 to see what's happening
          if (llmConfig.provider === 'qwen') {
          }

          // CRITICAL: Check usage_metadata first (where Azure and Qwen actually send token data)
          if (
            (llmConfig.provider === 'azure' || llmConfig.provider === 'qwen') &&
            usageMetadata &&
            (usageMetadata.input_tokens > 0 || usageMetadata.output_tokens > 0)
          ) {
          }

          // For Azure OpenAI, also check the direct response_metadata.usage pattern
          if (chunk?.response_metadata?.usage) {
            const azureUsage = chunk.response_metadata.usage;

            // Log what we found (even if zero values) and check if it's an empty object
            if (llmConfig.provider === 'azure') {
              const isEmptyObject = Object.keys(azureUsage).length === 0;

              // If empty object, this means Azure API isn't returning token usage despite our request
              if (isEmptyObject) {
              }
            }

            // Only extract if we have actual token values (not undefined)
            if (
              azureUsage.prompt_tokens !== undefined &&
              azureUsage.completion_tokens !== undefined
            ) {
              promptTokens = azureUsage.prompt_tokens || 0;
              completionTokens = azureUsage.completion_tokens || 0;

              if (llmConfig.provider === 'azure') {
              }
            }
          }

          // Important: For streaming, the final chunk contains TOTAL tokens, not incremental
          // So we should replace, not accumulate, when we get valid token counts
          if (chunkPromptTokens > 0) {
            promptTokens = chunkPromptTokens;
            if (
              llmConfig.provider === 'azure' ||
              llmConfig.provider === 'qwen'
            ) {
            }
          }
          if (chunkCompletionTokens > 0) {
            // CRITICAL FIX: Replace instead of accumulate for completion tokens
            // Azure OpenAI sends the total count in the final chunk
            completionTokens = chunkCompletionTokens;
            if (
              llmConfig.provider === 'azure' ||
              llmConfig.provider === 'qwen'
            ) {
            }
          }
          if (typeof contentChunk === 'string' && contentChunk.length > 0) {
            accumulatedContent += contentChunk;
            const streamData = {
              id: `chatcmpl-${aiMessageUUID}`,
              object: 'chat.completion.chunk',
              created: Math.floor(Date.now() / 1000),
              model: modelIdentifier,
              model_display_name: modelDisplayName,
              conversation_uuid: conversationUUID,
              choices: [
                {
                  index: 0,
                  delta: { content: contentChunk },
                  finish_reason: null,
                },
              ],
            };
            passthrough.write(`data: ${JSON.stringify(streamData)}\n\n`);
          }
          const finishReason =
            chunk?.response_metadata?.finishReason ??
            chunk?.response_metadata?.finish_reason ??
            chunk?.choices?.[0]?.finish_reason;

          // Check if this is the final chunk (finish reason indicates end of stream)
          const isFinalChunk = finishReason === 'stop';

          // Log finish reason and current token state for Qwen debugging
          if (llmConfig.provider === 'qwen' && finishReason) {
          }

          // Log when we actually extract tokens (more reliable than metadata structure checks)
          if (
            llmConfig.provider === 'azure' &&
            (promptTokens > 0 || completionTokens > 0)
          ) {
          }

          if (isFinalChunk && llmConfig.provider === 'azure') {
          }

          // Track when we see finish_reason
          if (finishReason === 'stop' && !hasSeenFinishReason) {
            hasSeenFinishReason = true;
            if (
              llmConfig.provider === 'azure' ||
              llmConfig.provider === 'qwen'
            ) {
            }
          }

          // CRITICAL: For Azure and Qwen, wait up to 2 more chunks after finish_reason to get token usage
          let shouldBreak = false;
          if (finishReason === 'stop') {
            if (
              llmConfig.provider === 'azure' ||
              llmConfig.provider === 'qwen'
            ) {
              // If we have tokens, we can break. If not, wait up to 2 more chunks
              if (promptTokens > 0 || completionTokens > 0) {
                shouldBreak = true;
              } else if (chunksAfterFinish >= 2) {
                shouldBreak = true;
                this.logger.warn(
                  `[${llmConfig.provider} Stream] Breaking after 2 extra chunks with no token usage. Total chunks: ${chunkCount}`,
                );
              } else {
              }
            } else {
              shouldBreak = true; // For non-Azure/Qwen providers, break immediately
            }
          }

          if (shouldBreak) {
            break;
          }
        }
        finalAiContent = accumulatedContent;

        // Debug log final token counts for Azure and Qwen
        if (llmConfig.provider === 'azure') {
          const hasValidTokens = promptTokens > 0 || completionTokens > 0;

          if (!hasValidTokens) {
            this.logger.error(
              `[Azure Token CRITICAL] Azure OpenAI returned empty usage objects {} in all ${chunkCount} chunks despite stream_options.include_usage=true. This suggests:
              1. API version ${llmConfig.apiVersion} may not support token usage in streaming
              2. Deployment ${modelIdentifier} configuration issue
              3. LangChain version compatibility issue
              4. Azure API regional differences`,
            );
          }
        } else if (llmConfig.provider === 'qwen') {
          const hasValidTokens = promptTokens > 0 || completionTokens > 0;

          if (!hasValidTokens) {
            this.logger.warn(
              `[Qwen Token Warning] Qwen OpenAI-compatible API returned no token usage despite stream_options.include_usage=true. This may be expected if Qwen API doesn't support usage reporting in streaming mode.`,
            );
          }
        }

        // --- CRITICAL: Save message and sources BEFORE ending stream ---
        // This ensures all database operations complete before frontend redirects
        if (finalAiContent) {
          const savedAiMessage = await this.saveMessageWithSP(
            aiMessageUUID,
            conversationUUID,
            finalAiContent,
            temperature,
            llmConfig.instanceName ?? llmConfig.region ?? 'N/A',
            modelIdentifier,
            'assistant',
            completionTokens,
            userId,
            new Date(),
            undefined,
            undefined, // Assistant messages don't use @mention
          );
          const aiMessageId = savedAiMessage.inserted_message_id; // Get the ID of the saved AI message

          // --- Update Token Usage (Streaming) ---
          // Use actual token counts from LLM response, fallback to zero tokens if not available
          const messageContent = llmMessages
            .map((m) =>
              typeof m.content === 'string'
                ? m.content
                : JSON.stringify(m.content),
            )
            .join('');

          // Log when we're using zero tokens vs actual token counts
          if (promptTokens === 0 || completionTokens === 0) {
            this.logger.warn(
              `[Token Usage] No token usage received from ${llmConfig.provider} (${modelIdentifier}), recording as zero tokens. Provider response may not include usage data in streaming mode.`,
            );
          } else {
            this.logger.debug(
              `[Token Usage] Received actual token counts from ${llmConfig.provider} (${modelIdentifier}): prompt=${promptTokens}, completion=${completionTokens}`,
            );
          }

          const actualPromptTokens = promptTokens || 0;
          const actualCompletionTokens = completionTokens || 0;
          const actualTotalTokens = actualPromptTokens + actualCompletionTokens;

          await this.tokenUsageService.updateTokenUsage({
            username: userId,
            modelName: modelIdentifier,
            tokenDate: new Date(),
            promptTokens: actualPromptTokens,
            completionTokens: actualCompletionTokens,
            totalTokens: actualTotalTokens,
            isApi: false, // This is a web UI request, not API
            conversationUuid: conversationUUID,
          });

          this.logger.debug(
            `[Streaming] Updated token usage for ${userId}/${modelIdentifier}: prompt=${actualPromptTokens}, completion=${actualCompletionTokens}, total=${actualTotalTokens}`,
          );

          // Check if user has now exceeded their limit after this request
          if (userId) {
            const userObj = { userId } as any; // Create minimal user object for post-usage check
            const postUsageCheck =
              await this.tokenUsageService.checkTokenLimitPostUsage(
                userObj,
                modelIdentifier,
              );

            if (postUsageCheck.exceeded) {
              this.logger.warn(
                `User ${userId} has exceeded monthly token limit after this streaming request: ${postUsageCheck.message}`,
              );
            }
          }

          // --- Save Sources (Streaming) ---
          if (aiMessageId && searchSources.length > 0) {
            this.logger.log(
              `💾 Saving ${searchSources.length} search sources to database for AI message ${aiMessageId}`,
            );
            await this.saveSources(aiMessageId, searchSources);
            this.logger.log(
              `✅ Sources saved to database for message ${aiMessageId} BEFORE ending stream`,
            );
          }
          // --- End Save Sources ---

          if (userMessageId && promptTokens > 0) {
            await this.updateUserMessageTokenCount(userMessageId, promptTokens);
            this.logger.log(
              `Updated user message ${userMessageId} with ${promptTokens} prompt tokens after streaming.`,
            );
          } else if (userMessageId) {
            this.logger.warn(
              `Prompt token count unavailable for user message ${userMessageId} after streaming provider ${llmConfig.provider}.`,
            );
          }

          this.logger.log(
            `[Langchain Stream] ✅ Message and ${searchSources.length} sources saved BEFORE ending stream for ${conversationUUID}`,
          );
        }

        const doneData = {
          id: `chatcmpl-${aiMessageUUID}`,
          object: 'chat.completion.chunk',
          created: Math.floor(Date.now() / 1000),
          model: modelIdentifier,
          model_display_name: modelDisplayName,
          conversation_uuid: conversationUUID,
          choices: [{ index: 0, delta: {}, finish_reason: 'stop' }],
          usage:
            promptTokens > 0 || completionTokens > 0
              ? {
                  prompt_tokens: promptTokens,
                  completion_tokens: completionTokens,
                  total_tokens: promptTokens + completionTokens,
                }
              : undefined,
        };
        passthrough.write(`data: ${JSON.stringify(doneData)}\n\n`);
        passthrough.write('data: [DONE]\n\n');

        // CRITICAL: Only end stream after ALL database operations are complete
        this.logger.debug(
          `[Langchain Stream] All database operations completed, ending stream for ${conversationUUID}`,
        );
        passthrough.end();
      } catch (error: unknown) {
        let errorMessage = 'Unknown stream processing error';
        let errorStack: string | undefined;
        if (error instanceof Error) {
          errorMessage = error.message;
          errorStack = error.stack;
        }

        // Check if this is a system message error and log appropriately
        if (detectSystemMessageError(error as any)) {
          this.logger.error(
            `[STREAM_SYSTEM_MESSAGE_ERROR] System message error in stream for conv ${conversationUUID}: ${errorMessage}. Consider using forceSystemMessageConversion=true on retry.`,
            errorStack,
          );
          passthrough.write(
            `data: ${JSON.stringify({ error: { message: 'System message not supported by model. Please try again.', code: 400 } })}\n\n`,
          );
        } else {
          this.logger.error(
            `Error processing Langchain stream for conv ${conversationUUID}: ${errorMessage}`,
            errorStack,
          );
          passthrough.write(
            `data: ${JSON.stringify({ error: { message: 'Error processing stream', code: 500 } })}\n\n`,
          );
        }
        passthrough.end();
      }
    })();
  }

  // processLangchainStreamWithTools method removed - no longer needed with new search approach

  // parseGoogleSearchResults method removed - search results now handled by SearchContextService

  private processDirectStream(
    directStream: Readable,
    passthrough: PassThrough,
    conversationUUID: string,
    searchSources: SearchSource[],
    deploymentNameFromDb?: string,
    modelNameFromDb?: string,
    userId?: string,
    llmMessages?: BaseMessage[],
    requestedModelName?: string,
    displayNameFromDb?: string,
  ): void {
    const modelIdentifier =
      modelNameFromDb ?? deploymentNameFromDb ?? 'deepseek-aisvc';
    const modelDisplayName = displayNameFromDb ?? modelIdentifier;
    const aiMessageUUID = `direct-${uuidv4()}`;
    let accumulatedContent = '';
    let promptTokens = 0;
    let completionTokens = 0;

    directStream.on('data', (chunk) => {
      const contentChunk = chunk.toString('utf-8');
      if (contentChunk) {
        // Check if this is already SSE formatted data (sources) or raw content
        if (contentChunk.startsWith('data: ')) {
          // This is already properly formatted SSE data (like sources), pass it through directly
          passthrough.write(contentChunk);

          // Try to extract token usage from SSE data if available
          try {
            const lines = contentChunk.split('\n');
            for (const line of lines) {
              if (line.startsWith('data: ') && !line.includes('[DONE]')) {
                const jsonStr = line.substring(6);
                const data = JSON.parse(jsonStr);

                // Debug log for DeepSeek token extraction - log all chunks for debugging
                if (data.id && (data.usage || data.choices)) {
                  this.logger.debug(
                    `[DeepSeek Stream] Chunk: choices.length=${data.choices?.length}, finish_reason=${data.choices?.[0]?.finish_reason}, usage=${JSON.stringify(data.usage)}`,
                  );
                }

                // DeepSeek sends usage in final chunk with empty choices array
                // Handle special token_usage chunk from Azure AI Service
                if (data.type === 'token_usage' && data.usage) {
                  const oldPrompt = promptTokens;
                  const oldCompletion = completionTokens;

                  promptTokens = data.usage.prompt_tokens || promptTokens;
                  completionTokens =
                    data.usage.completion_tokens || completionTokens;

                  this.logger.debug(
                    `[DeepSeek Token Extracted] Received token_usage chunk from Azure AI Service: prompt=${promptTokens} (was ${oldPrompt}), completion=${completionTokens} (was ${oldCompletion}), total=${promptTokens + completionTokens}`,
                  );
                  continue; // Don't pass this special chunk to the client
                }

                // Handle regular usage data in streaming chunks
                if (data.usage) {
                  const oldPrompt = promptTokens;
                  const oldCompletion = completionTokens;

                  // Handle both field naming conventions
                  promptTokens =
                    data.usage.prompt_tokens ||
                    data.usage.promptTokens ||
                    promptTokens;
                  completionTokens =
                    data.usage.completion_tokens ||
                    data.usage.completionTokens ||
                    completionTokens;

                  if (
                    promptTokens !== oldPrompt ||
                    completionTokens !== oldCompletion
                  ) {
                    this.logger.debug(
                      `[DeepSeek Token Extracted] Found usage data in chunk: prompt=${promptTokens} (was ${oldPrompt}), completion=${completionTokens} (was ${oldCompletion}), total=${promptTokens + completionTokens}`,
                    );
                  }
                }

                // Also check if tokens might be in response_metadata (edge case)
                if (data.response_metadata?.tokenUsage) {
                  const usage = data.response_metadata.tokenUsage;
                  promptTokens =
                    usage.promptTokens || usage.prompt_tokens || promptTokens;
                  completionTokens =
                    usage.completionTokens ||
                    usage.completion_tokens ||
                    completionTokens;
                  this.logger.debug(
                    `[DeepSeek Token Extracted] Found tokens in response_metadata: prompt=${promptTokens}, completion=${completionTokens}`,
                  );
                }
              }
            }
          } catch (e) {
            this.logger.debug(`[DeepSeek Stream] Parse error: ${e}`);
          }
        } else {
          // This is raw content, wrap it in SSE format and accumulate
          accumulatedContent += contentChunk;
          const streamData = {
            id: `chatcmpl-${aiMessageUUID}`,
            object: 'chat.completion.chunk',
            created: Math.floor(Date.now() / 1000),
            model: modelIdentifier,
            model_display_name: modelDisplayName,
            conversation_uuid: conversationUUID,
            choices: [
              {
                index: 0,
                delta: { content: contentChunk },
                finish_reason: null,
              },
            ],
          };
          passthrough.write(`data: ${JSON.stringify(streamData)}\n\n`);
        }
      }
    });

    directStream.on('end', async () => {
      this.logger.log(
        `Direct stream ended for conv ${conversationUUID} (DeepSeek). Final accumulated tokens - prompt: ${promptTokens}, completion: ${completionTokens}, total: ${promptTokens + completionTokens}`,
      );

      // Update token usage if we have the necessary parameters
      if (userId && requestedModelName) {
        try {
          // Use actual token counts from stream, fallback to zero if not available
          const actualPromptTokens = promptTokens || 0;
          const actualCompletionTokens = completionTokens || 0;
          const actualTotalTokens = actualPromptTokens + actualCompletionTokens;

          // Log token usage status for DeepSeek
          if (actualTotalTokens === 0) {
            this.logger.warn(
              `[DeepSeek Token Usage] No token usage received from DeepSeek stream for ${requestedModelName}, recording as zero tokens. Raw tokens were: prompt=${promptTokens}, completion=${completionTokens}`,
            );
          } else {
            this.logger.log(
              `[DeepSeek Token Usage] Successfully extracted actual token counts from DeepSeek stream: prompt=${actualPromptTokens}, completion=${actualCompletionTokens}, total=${actualTotalTokens}`,
            );
          }

          await this.tokenUsageService.updateTokenUsage({
            username: userId,
            modelName: requestedModelName,
            tokenDate: new Date(),
            promptTokens: actualPromptTokens,
            completionTokens: actualCompletionTokens,
            totalTokens: actualTotalTokens,
            isApi: false, // This is a web UI request, not API
            conversationUuid: conversationUUID,
          });

          this.logger.debug(
            `[DeepSeek Streaming] Updated token usage for ${userId}/${requestedModelName}: prompt=${actualPromptTokens}, completion=${actualCompletionTokens}, total=${actualTotalTokens}`,
          );

          // Check if user has now exceeded their limit after this request
          if (userId) {
            const userObj = { userId } as any; // Create minimal user object for post-usage check
            const postUsageCheck =
              await this.tokenUsageService.checkTokenLimitPostUsage(
                userObj,
                requestedModelName,
              );

            if (postUsageCheck.exceeded) {
              this.logger.warn(
                `User ${userId} has exceeded monthly token limit after this DeepSeek streaming request: ${postUsageCheck.message}`,
              );
            }
          }
        } catch (error) {
          this.logger.error(
            `Failed to update token usage for DeepSeek streaming: ${error}`,
          );
        }
      }

      const doneData = {
        id: `chatcmpl-${aiMessageUUID}`,
        object: 'chat.completion.chunk',
        created: Math.floor(Date.now() / 1000),
        model: modelIdentifier,
        model_display_name: modelDisplayName,
        conversation_uuid: conversationUUID,
        choices: [{ index: 0, delta: {}, finish_reason: 'stop' }],
      };
      passthrough.write(`data: ${JSON.stringify(doneData)}\n\n`);
      passthrough.write('data: [DONE]\n\n');
      passthrough.end();
    });

    directStream.on('error', (error) => {
      // Check if this is a system message error and log appropriately
      if (detectSystemMessageError(error)) {
        this.logger.error(
          `[STREAM_SYSTEM_MESSAGE_ERROR] System message error in direct stream for conv ${conversationUUID}: ${error.message}. Consider using forceSystemMessageConversion=true on retry.`,
          error.stack,
        );
        passthrough.write(
          `data: ${JSON.stringify({ error: { message: 'System message not supported by model. Please try again.', code: 400 } })}\n\n`,
        );
      } else {
        this.logger.error(
          `Error in direct stream for conv ${conversationUUID} (DeepSeek): ${error.message}`,
          error.stack,
        );
        passthrough.write(
          `data: ${JSON.stringify({ error: { message: 'Error in backend stream processing', code: 500 } })}\n\n`,
        );
      }
      passthrough.destroy(error);
    });
  }

  // --- Helper to get and decrypt chat history via SP ---
  private async getDecryptedChatHistory(
    conversationUUID: string,
    limit?: number, // Add optional limit parameter
  ): Promise<BaseMessage[]> {
    this.logger.debug(
      `Retrieving history for conversation: ${conversationUUID}`,
    );
    try {
      const results: DecryptedMessageFromDB[] = await this.prisma
        .$queryRaw`EXEC sp_cvst_GetDecryptedMessagesByConversationUUID @conversation_uuid = ${conversationUUID}, @encryption_key_name = ${this.encryptionKeyName}, @decryption_cert_name = ${this.decryptionCertName}`;
      if (!results || results.length === 0) return [];
      results.sort(
        (a, b) =>
          new Date(a.create_dt).getTime() - new Date(b.create_dt).getTime(),
      );

      // Apply limit after fetching and sorting
      const limitedResults =
        limit !== undefined && limit >= 0 ? results.slice(-limit) : results;

      // Filter out empty messages *before* mapping
      const validResults = limitedResults.filter(
        (msg) =>
          typeof msg.content === 'string' && // Ensure it's a string
          msg.content.trim() !== '' && // Ensure it's not empty/whitespace
          msg.content !== 'Source Title is NULL' && // Exclude specific error strings if needed
          msg.content !== 'Decryption/Conversion Failed',
      );

      return validResults.map((msg) => {
        // Use validResults here
        const content = msg.content; // Now guaranteed to be a non-empty string
        if (msg.sender.toLowerCase() === 'user')
          return new HumanMessage({ content: content });
        if (msg.sender.toLowerCase() === 'assistant') {
          // Strip thinking blocks from assistant messages to prevent them from being included in context
          const strippedContent = this.stripThinkingBlocks(content);
          return new AIMessage({ content: strippedContent });
        }
        this.logger.warn(`Unknown sender role in history: ${msg.sender}`);
        return new AIMessage({ content: `[Role: ${msg.sender}] ${content}` });
      });
    } catch (error: unknown) {
      this.logger.error(
        `Database error in getDecryptedChatHistory for conv ${conversationUUID}: ${error}`,
        error instanceof Error ? error.stack : undefined,
      );
      throw new InternalServerErrorException(
        `Failed to retrieve chat history.`,
      );
    }
  }

  // --- Helper to strip thinking blocks from assistant message content ---
  private stripThinkingBlocks(content: string): string {
    if (!content) return content;

    const thinkStartTag = '<think>';
    const thinkEndTag = '</think>';
    let parsedRegular = '';
    let inThink = false;
    let currentSegment = '';
    const startsWithThink = content.trim().startsWith(thinkStartTag);
    let hasProcessedThinking = false;

    for (let i = 0; i < content.length; i++) {
      if (content.substring(i, i + thinkStartTag.length) === thinkStartTag) {
        if (!inThink && startsWithThink && !hasProcessedThinking && parsedRegular.trim() === '') {
          // This is a thinking block at the start - exclude it
          if (currentSegment.length > 0) parsedRegular += currentSegment;
          currentSegment = '';
          inThink = true;
          i += thinkStartTag.length - 1;
          continue;
        } else {
          // Include the <think> tag as regular content (not a valid thinking block)
          currentSegment += content[i];
          continue;
        }
      }
      if (content.substring(i, i + thinkEndTag.length) === thinkEndTag) {
        if (inThink) {
          // End of thinking block - exclude the content and continue
          currentSegment = '';
          inThink = false;
          hasProcessedThinking = true;
          i += thinkEndTag.length - 1;
          continue;
        } else {
          // Include the </think> tag as regular content
          currentSegment += content[i];
          continue;
        }
      }
      currentSegment += content[i];
    }

    // Add any remaining content
    if (!inThink) {
      parsedRegular += currentSegment;
    }

    return parsedRegular;
  }

  // --- Helper to update conversation parameters ---
  async updateConversationParams(
    updateDto: UpdateConversationParamsDto,
    userId: string, // Ensure the user owns the conversation
  ): Promise<void> {
    const {
      chat_session_id,
      instructions,
      pastMessagesCount,
      maxResponseTokens,
      temperature,
      topP,
    } = updateDto;

    // Debug logging to track received values in service
    this.logger.log(`🔧 [DEBUG] updateConversationParams called with:`);
    this.logger.log(JSON.stringify({
      userId,
      chat_session_id,
      receivedValues: {
        instructions,
        pastMessagesCount,
        maxResponseTokens,
        temperature,
        topP,
      },
      valueTypes: {
        instructions: typeof instructions,
        pastMessagesCount: typeof pastMessagesCount,
        maxResponseTokens: typeof maxResponseTokens,
        temperature: typeof temperature,
        topP: typeof topP,
      },
    }, null, 2));

    this.logger.log(
      `User ${userId} updating parameters for conversation ${chat_session_id}`,
    );

    try {
      // Verify conversation exists and belongs to the user
      const conversation = await this.prisma.conversation.findUnique({
        where: { conversation_uuid: chat_session_id },
        select: { 
          ssoid: true,
          // Select current parameters to compare
          instructions: true,
          pastMessagesCount: true,
          maxResponseTokens: true,
          temperature: true,
          topP: true,
        },
      });

      if (!conversation) {
        throw new NotFoundException(
          `Conversation ${chat_session_id} not found.`,
        );
      }
      if (conversation.ssoid !== userId) {
        throw new ForbiddenException(
          `User ${userId} does not have permission to update conversation ${chat_session_id}.`,
        );
      }

      // Log current vs new values
      this.logger.log(`🔧 [DEBUG] Current vs New parameter values:`);
      this.logger.log(JSON.stringify({
        currentValues: {
          instructions: conversation.instructions,
          pastMessagesCount: conversation.pastMessagesCount,
          maxResponseTokens: conversation.maxResponseTokens,
          temperature: conversation.temperature,
          topP: conversation.topP,
        },
        newValues: {
          instructions,
          pastMessagesCount,
          maxResponseTokens,
          temperature,
          topP,
        },
      }, null, 2));

      // Prepare update data with explicit type handling
      const updateData = {
        instructions,
        pastMessagesCount: pastMessagesCount !== undefined ? pastMessagesCount : null,
        maxResponseTokens: maxResponseTokens !== undefined ? maxResponseTokens : null,
        temperature: temperature !== undefined ? Number(temperature) : null,
        topP: topP !== undefined ? Number(topP) : null,
        update_by: userId,
        update_dt: new Date(),
      };

      this.logger.log(`🔧 [DEBUG] Prisma update data:`);
      this.logger.log(JSON.stringify({
        updateData,
        updateDataTypes: {
          instructions: typeof updateData.instructions,
          pastMessagesCount: typeof updateData.pastMessagesCount,
          maxResponseTokens: typeof updateData.maxResponseTokens,
          temperature: typeof updateData.temperature,
          topP: typeof updateData.topP,
        },
      }, null, 2));

      // Update the conversation parameters
      const updateResult = await this.prisma.conversation.update({
        where: { conversation_uuid: chat_session_id },
        data: updateData,
      });

      this.logger.log(`🔧 [DEBUG] Prisma update result:`);
      this.logger.log(JSON.stringify({
        updatedFields: {
          instructions: updateResult.instructions,
          pastMessagesCount: updateResult.pastMessagesCount,
          maxResponseTokens: updateResult.maxResponseTokens,
          temperature: updateResult.temperature,
          topP: updateResult.topP,
        },
      }, null, 2));

      // Post-update verification: Query the database to confirm the values were actually saved
      this.logger.log(`🔧 [DEBUG] Post-update verification: Re-querying database`);
      const verificationQuery = await this.prisma.conversation.findUnique({
        where: { conversation_uuid: chat_session_id },
        select: {
          instructions: true,
          pastMessagesCount: true,
          maxResponseTokens: true,
          temperature: true,
          topP: true,
          update_dt: true,
        },
      });

      this.logger.log(`🔧 [DEBUG] Post-update verification result:`);
      this.logger.log(JSON.stringify({
        actualDatabaseValues: {
          instructions: verificationQuery?.instructions,
          pastMessagesCount: verificationQuery?.pastMessagesCount,
          maxResponseTokens: verificationQuery?.maxResponseTokens,
          temperature: verificationQuery?.temperature,
          topP: verificationQuery?.topP,
          update_dt: verificationQuery?.update_dt,
        },
        expectedValues: {
          instructions,
          pastMessagesCount,
          maxResponseTokens,
          temperature,
          topP,
        },
        valuesMatch: {
          instructions: verificationQuery?.instructions === instructions,
          pastMessagesCount: verificationQuery?.pastMessagesCount === pastMessagesCount,
          maxResponseTokens: verificationQuery?.maxResponseTokens === maxResponseTokens,
          temperature: verificationQuery?.temperature === temperature,
          topP: verificationQuery?.topP === topP,
        },
      }, null, 2));

      this.logger.log(
        `Successfully updated parameters for conversation ${chat_session_id}`,
      );
    } catch (error: any) {
      this.logger.error(`🔧 [DEBUG] Error in updateConversationParams:`);
      this.logger.error(JSON.stringify({
        errorMessage: error.message,
        errorType: error.constructor.name,
        conversationId: chat_session_id,
        userId,
      }, null, 2));
      
      this.logger.error(
        `Failed to update parameters for conversation ${chat_session_id}: ${error.message}`,
        error.stack,
      );
      if (error instanceof HttpException) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Failed to update conversation parameters.',
      );
    }
  }

  // --- Helper to save a message via SP ---
  private async saveMessageWithSP(
    messageUUID: string,
    conversationUUID: string,
    content: string,
    temperature: number | undefined,
    instanceName: string,
    modelName: string,
    sender: 'user' | 'assistant',
    completionTokens: number,
    userId: string,
    receivedAt: Date,
    dialogId?: string,
    usedMention?: boolean,
  ): Promise<InsertedMessageResult> {
    // Add check to prevent saving empty assistant messages here too, for consistency
    if (sender === 'assistant' && (!content || content.trim() === '')) {
      this.logger.warn(
        `Skipping saving empty assistant message via SP for conv ${conversationUUID}`,
      );
      // Need to return a valid InsertedMessageResult structure, even if dummy
      // Or throw an error, but skipping might be better if the calling code handles it
      // Returning a dummy ID might cause issues if the ID is used later (e.g., for saving sources)
      // Let's throw an error for now, or adjust based on how calling code uses the result.
      // For now, let's just log and return a dummy/invalid ID to avoid breaking flow,
      // but this needs careful consideration based on how the ID is used.
      this.logger.warn(
        `Returning dummy ID for skipped empty assistant message save.`,
      );
      return { inserted_message_id: -1 }; // Indicate skipped save
    }

    this.logger.debug(`Saving message via sp_cvst_InsertMessageWithPrompts...`);
    try {
      const result: InsertedMessageResult[] = await this.prisma
        .$queryRaw`EXEC sp_cvst_InsertMessageWithPrompts @message_uuid = ${messageUUID}, @conversation_uuid = ${conversationUUID}, @last_prompt = ${content}, @temperature = ${temperature}, @instance_name = ${instanceName}, @model_name = ${modelName}, @sender = ${sender}, @token_spent = ${completionTokens}, @ssoid = ${userId}, @received_at = ${receivedAt}, @dialog_id = ${dialogId ?? null}, @used_mention = ${usedMention ?? null}, @encryption_key_name = ${this.encryptionKeyName}, @decryption_cert_name = ${this.decryptionCertName}`;
      if (!result || !result[0]?.inserted_message_id) {
        throw new Error(
          'Stored procedure did not return the inserted message ID.',
        );
      }
      this.logger.debug(`Message ${result[0].inserted_message_id} saved.`);
      return result[0];
    } catch (error: any) {
      this.logger.error(
        `Failed to save message via SP for conv ${conversationUUID}: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException('Failed to save message.');
    }
  }

  // --- Helper to update user message token count ---
  private async updateUserMessageTokenCount(
    messageId: number,
    promptTokens: number,
  ): Promise<void> {
    if (!messageId || messageId === -1 || promptTokens <= 0) return; // Also check for dummy ID
    try {
      await this.prisma.message.update({
        where: { message_id: messageId },
        data: { token_spent: promptTokens },
      });
    } catch (error: unknown) {
      let errorMessage = `Failed to update token count for user message ${messageId}`;
      if (error instanceof Error) {
        errorMessage = `${errorMessage}: ${error.message}`;
      } else {
        errorMessage = `${String(error)}`;
      }
      this.logger.error(
        errorMessage,
        error instanceof Error ? error.stack : undefined,
      );
    }
  }

  // --- Helper to get user message count ---
  private async getUserMessageCount(conversationUUID: string): Promise<number> {
    try {
      const conversation = await this.prisma.conversation.findUnique({
        where: { conversation_uuid: conversationUUID },
        select: { conversation_id: true },
      });
      if (!conversation) {
        this.logger.warn(
          `[TitleGen] Conversation not found when counting messages: ${conversationUUID}`,
        );
        return 0;
      }
      const count = await this.prisma.message.count({
        where: {
          conversation_id: conversation.conversation_id,
          sender: 'user',
        },
      });
      return count;
    } catch (error: any) {
      this.logger.error(
        `[TitleGen] Failed to count user messages for conv ${conversationUUID}: ${error.message}`,
        error.stack,
      );
      return 0; // Return 0 on error to prevent incorrect title generation triggers
    }
  }

  // --- Helper: Get conversation's first model name ---
  private async getConversationFirstModel(conversationUUID: string): Promise<string | null> {
    try {
      const conversation = await this.prisma.conversation.findUnique({
        where: { conversation_uuid: conversationUUID },
        select: { model_name: true },
      });
      
      if (!conversation || !conversation.model_name) {
        this.logger.warn(
          `[getConversationFirstModel] No model_name found for conversation ${conversationUUID}`,
        );
        return null;
      }
      
      this.logger.debug(
        `[getConversationFirstModel] Found first model '${conversation.model_name}' for conversation ${conversationUUID}`,
      );
      return conversation.model_name;
    } catch (error: any) {
      this.logger.error(
        `[getConversationFirstModel] Failed to get first model for conversation ${conversationUUID}: ${error.message}`,
        error.stack,
      );
      return null;
    }
  }

  // --- Helper: Generate and Save Conversation Title (Async) ---
  private async generateAndSaveTitle(
    conversationUUID: string,
    conversationId: number,
    userMessageCount: number,
    currentPrompt: string, // The content of the message that triggered this
  ): Promise<void> {
    this.logger.log(
      `[TitleGen] Starting title generation for conv ${conversationUUID} (ID: ${conversationId}, UserMsgCount: ${userMessageCount})`,
    );
    try {
      // 1. Select Model (Assume 'gpt-4.1-mini' deployment exists via Azure)
      //    Attempt to get config for a specific title-gen model. Fallback might be needed.
      let titleGenModelConfig: LlmConfig | null = null;
      try {
        // Use the existing getLlmConfig with a default department ('ITO') and the requested 'gpt-4.1-mini' model name
        titleGenModelConfig = await this.llmConfigService.getLlmConfig(
          'ITO',
          'gpt-4.1-mini',
        );
      } catch (e: any) {
        this.logger.warn(
          `[TitleGen] Could not find config for title gen model 'gpt-4.1-mini' using dept 'ITO': ${e.message}.`,
        );
        // Optional: Fallback to another model like the one used for the chat? Or just fail title gen.
        // For now, we'll let it fail if the specific model isn't found.
      }

      // Assuming gpt-4.1-mini is also an Azure deployment for consistency
      if (!titleGenModelConfig || titleGenModelConfig.provider !== 'azure') {
        throw new Error(
          'Title generation model config (gpt-4.1-mini) not found or not Azure.',
        );
      }

      const titleGenModel = new ChatOpenAI({
        temperature: 0.5, // Lower temp for more deterministic title
        modelName: titleGenModelConfig.deploymentName, // Use the specific deployment
        maxRetries: 1, // Reduce retries for title gen
        configuration: {
          apiKey: titleGenModelConfig.apiKey,
          baseURL: `https://${titleGenModelConfig.instanceName}.openai.azure.com/openai/deployments/${titleGenModelConfig.deploymentName}`,
          defaultQuery: { 'api-version': titleGenModelConfig.apiVersion },
        },
      });

      // 2. Determine Input Content and Create Prompt
      let titleInputContent: string;
      let promptTemplate: string;

      if (userMessageCount === 1) {
        titleInputContent = currentPrompt;
        promptTemplate = `Summarize the following user query into a concise chat title of 7 words or less. Output only the title itself, nothing else.\n\nQuery: "{content}"\n\nTitle:`;
      } else {
        // Fetch last 5 user messages
        const fullHistory =
          await this.getDecryptedChatHistory(conversationUUID);
        const userMessages = fullHistory.filter(
          (msg) => msg instanceof HumanMessage,
        );
        const lastFiveUserMessages = userMessages.slice(-5); // Get the last 5
        // Ensure content is string and join
        titleInputContent = lastFiveUserMessages
          .map((msg) =>
            typeof msg.content === 'string'
              ? msg.content
              : JSON.stringify(msg.content),
          )
          .join('\n---\n'); // Join messages with a separator

        promptTemplate = `Generate a concise, factual title (max 7 words) based on the main subject of these 5 user messages. Output only the title.\n\nMessages:\n{content}\n\nTitle:`;
      }

      const finalPrompt = promptTemplate.replace(
        '{content}',
        titleInputContent,
      );
      const messages = [new HumanMessage(finalPrompt)];

      // 3. Invoke LLM
      this.logger.debug(
        `[TitleGen] Invoking ${titleGenModelConfig.deploymentName} for title...`,
      );
      const response = await titleGenModel.invoke(messages);
      let generatedTitle =
        typeof response.content === 'string' ? response.content.trim() : '';
      this.logger.debug(`[TitleGen] Raw title response: "${generatedTitle}"`);

      // 4. Process Title
      if (generatedTitle) {
        // Remove potential quotes and limit words
        generatedTitle = generatedTitle.replace(/^["']|["']$/g, ''); // Remove surrounding quotes
        const words = generatedTitle.split(/\s+/).filter((w) => w.length > 0); // Filter empty strings
        if (words.length > 7) {
          generatedTitle = words.slice(0, 7).join(' ') + '...';
        }

        // 5. Update Database via Stored Procedure
        // Assume SP 'sp_cvst_UpdateConversationTitle' takes conversation_id and the plain text title
        // and handles encryption before updating the conversation_title (Bytes) field.
        this.logger.debug(
          `[TitleGen] Calling sp_cvst_UpdateConversationTitle for conv UUID ${conversationUUID} with title: "${generatedTitle}"`,
        );
        const affectedRows = await this.prisma
          .$executeRaw`EXEC sp_cvst_UpdateConversationTitle @conversation_uuid = ${conversationUUID}, @title = ${generatedTitle}, @encryption_key_name = ${this.encryptionKeyName}, @decryption_cert_name = ${this.decryptionCertName}`;

        if (affectedRows > 0) {
          this.logger.log(
            `[TitleGen] Successfully called SP to update title for conv ${conversationUUID}`,
          );
        } else {
          this.logger.warn(
            `[TitleGen] SP sp_cvst_UpdateConversationTitle reported 0 rows affected for conv ID ${conversationId}.`,
          );
        }
      } else {
        this.logger.warn(
          `[TitleGen] Title generation produced empty content for conv ${conversationUUID}.`,
        );
      }
    } catch (error: any) {
      this.logger.error(
        `[TitleGen] Failed to generate title for conv ${conversationUUID}: ${error.message}`,
        error.stack,
      );
      // Do not re-throw, allow main chat flow to continue
    }
  }
  // --- End Title Generation Helper ---

  // --- Helper: Save Sources (New) ---
  private async saveSources(
    messageId: number,
    sources: SearchSource[],
  ): Promise<void> {
    if (!sources || sources.length === 0 || messageId === -1) {
      // Also check for dummy ID
      if (messageId !== -1)
        this.logger.debug(
          `[SaveSources] No sources provided for message ID ${messageId}. Skipping.`,
        );
      return;
    }
    if (!messageId) {
      this.logger.error(
        `[SaveSources] Invalid messageId provided: ${messageId}. Cannot save sources.`,
      );
      return;
    }

    this.logger.log(
      `[SaveSources] Attempting to save ${sources.length} sources for message ID ${messageId}...`,
    );

    try {
      // Iterate through sources and call the stored procedure for each
      for (const source of sources) {
        // Validate source data (basic check)
        if (!source.title || !source.link || !source.snippet) {
          this.logger.warn(
            `[SaveSources] Skipping source with missing data for message ID ${messageId}: ${JSON.stringify(source)}`,
          );
          continue;
        }

        try {
          // Try the stored procedure first
          await this.prisma.$executeRaw`
                        EXEC sp_cvst_InsertMessageSource
                            @message_id = ${messageId},
                            @title = ${source.title},
                            @link = ${source.link},
                            @snippet = ${source.snippet},
                            @encryption_key_name = ${this.encryptionKeyName},
                            @decryption_cert_name = ${this.decryptionCertName};
                    `;
        } catch (spError: any) {
          this.logger.warn(
            `[SaveSources] SP failed for source, attempting direct insert: ${spError.message}`,
          );

          // Fallback: Try direct insert with manual key management
          let keyOpened = false;
          try {
            // Open the symmetric key
            await this.prisma.$executeRaw`
                            OPEN SYMMETRIC KEY ${this.encryptionKeyName}
                            DECRYPTION BY CERTIFICATE ${this.decryptionCertName};
                        `;
            keyOpened = true;

            // Perform the insert operation
            await this.prisma.$executeRaw`
                            INSERT INTO message_source (message_id, title_encrypted, link_encrypted, snippet_encrypted, create_dt)
                            VALUES (
                                ${messageId},
                                EncryptByKey(Key_GUID(${this.encryptionKeyName}), ${source.title}),
                                EncryptByKey(Key_GUID(${this.encryptionKeyName}), ${source.link}),
                                EncryptByKey(Key_GUID(${this.encryptionKeyName}), ${source.snippet}),
                                GETDATE()
                            );
                        `;

            this.logger.log(
              `[SaveSources] Direct insert successful for source: ${source.title}`,
            );
          } catch (directError: any) {
            this.logger.error(
              `[SaveSources] Both SP and direct insert failed for source: ${directError.message}`,
            );
            // Key will be closed in finally block regardless of error
          } finally {
            // CRITICAL: Always close the symmetric key if it was opened
            if (keyOpened) {
              try {
                await this.prisma.$executeRaw`
                                    CLOSE SYMMETRIC KEY ${this.encryptionKeyName};
                                `;
                this.logger.debug(
                  `[SaveSources] Symmetric key closed successfully`,
                );
              } catch (closeError: any) {
                this.logger.error(
                  `[SaveSources] Failed to close symmetric key: ${closeError.message}`,
                );

                // Last resort: Try conditional close
                try {
                  await this.prisma.$executeRaw`
                                        IF EXISTS (SELECT * FROM sys.openkeys WHERE key_name = ${this.encryptionKeyName})
                                            CLOSE SYMMETRIC KEY ${this.encryptionKeyName};
                                    `;
                } catch (lastResortError) {
                  this.logger.error(
                    `[SaveSources] Last resort key close also failed: ${lastResortError}`,
                  );
                }
              }
            }
          }
        }
      }
      this.logger.log(
        `[SaveSources] Successfully processed ${sources.length} sources for message ID ${messageId}.`,
      );
    } catch (error: any) {
      this.logger.error(
        `[SaveSources] Failed to save sources for message ID ${messageId}: ${error.message}`,
        error.stack,
      );
    }
  }
  // --- End Save Sources Helper ---

  // --- Helper functions for legacy Langchain logic (without tools) ---
  private async handleLegacyLangchainInvoke(
    llmConfig: LlmConfig,
    deploymentNameFromDb: string | undefined,
    modelNameFromDb: string | undefined,
    temperature: number | undefined,
    llmMessages: BaseMessage[],
    requestedModel: string | undefined,
    conversationUUID: string,
    user: AuthenticatedUser,
    userMessageId: number | undefined, // Changed userId to user
  ): Promise<{
    aiContent: string;
    promptTokens: number;
    completionTokens: number;
  }> {
    let chatModel: BaseChatModel;
    switch (llmConfig.provider) {
      case 'azure':
        chatModel = new ChatOpenAI(
          this.createModelConfig(
            {
              openAIApiKey: llmConfig.apiKey,
              modelName: deploymentNameFromDb ?? 'unknown-azure-deployment',
            },
            temperature ?? 1.0,
            llmConfig,
          ),
        );
        break;
      case 'google':
        chatModel = new ChatGoogleGenerativeAI(
          this.createModelConfig(
            {
              apiKey:
                llmConfig.apiKey ||
                this.configService.get<string>('GOOGLE_API_KEY'),
              model: modelNameFromDb ?? 'gemini-pro',
            },
            temperature ?? 1.0,
            llmConfig,
          ),
        );
        break;
      case 'anthropic':
        chatModel = new ChatAnthropic(
          this.createModelConfig(
            {
              apiKey:
                llmConfig.apiKey ||
                this.configService.get<string>('ANTHROPIC_API_KEY'),
              modelName: modelNameFromDb,
            },
            temperature ?? 1.0,
            llmConfig,
          ),
        );
        break;
      case 'qwen':
        let apiKeyForQwenLegacyInvoke = llmConfig.apiKey;
        if (!apiKeyForQwenLegacyInvoke) {
          this.logger.warn(
            `Qwen API key not found in llmConfig for provider '${llmConfig.provider}' (legacy invoke), attempting to fetch from ConfigService.`,
          );
          apiKeyForQwenLegacyInvoke =
            this.configService.get<string>('ALIBABA_API_KEY');
          if (!apiKeyForQwenLegacyInvoke) {
            this.logger.error(
              `Qwen API key is missing from llmConfig and could not be fetched from ConfigService using env var 'ALIBABA_API_KEY' for legacy invoke.`,
            );
            throw new InternalServerErrorException(
              'Qwen API key is not configured for legacy invoke.',
            );
          }
        }
        this.logger.debug(
          `[QWEN CONSTRUCTOR CHECK - Legacy Invoke] Passing to ChatAlibabaTongyi - alibabaApiKey: ${apiKeyForQwenLegacyInvoke ? apiKeyForQwenLegacyInvoke.substring(0, 5) + '...' + apiKeyForQwenLegacyInvoke.substring(apiKeyForQwenLegacyInvoke.length - 5) : 'NOT FOUND'}`,
        );
        chatModel = new ChatAlibabaTongyi(
          this.createModelConfig(
            {
              alibabaApiKey: apiKeyForQwenLegacyInvoke,
              model: modelNameFromDb ?? deploymentNameFromDb ?? 'qwen-plus',
            },
            temperature ?? 1.0,
            llmConfig,
          ),
        );
        break;
      // Removed 'gcp-vertexai-gemini' case as it's handled by VertexGeminiService
      default:
        this.logger.error(
          `Unhandled Langchain provider in legacy invoke: ${llmConfig.provider}`,
        );
        throw new InternalServerErrorException(
          `Unhandled Langchain provider in legacy invoke: ${llmConfig.provider}`,
        );
    }
    const aiResponse = await chatModel.invoke(llmMessages);
    const aiContent =
      typeof aiResponse.content === 'string'
        ? aiResponse.content
        : JSON.stringify(aiResponse.content);
    const promptTokens =
      (aiResponse.response_metadata?.tokenUsage?.promptTokenCount as number) ??
      (aiResponse.response_metadata?.usage_metadata
        ?.prompt_token_count as number) ??
      0;
    const completionTokens =
      (aiResponse.response_metadata?.tokenUsage
        ?.completionTokenCount as number) ??
      (aiResponse.response_metadata?.usage_metadata
        ?.candidates_token_count as number) ??
      0;
    if (promptTokens === 0 || completionTokens === 0) {
      this.logger.warn(
        `Token usage extraction failed/missing for legacy non-streaming ${llmConfig.provider}.`,
      );
    } else {
      this.logger.debug(
        `Legacy Tokens for ${llmConfig.provider}: P=${promptTokens}, C=${completionTokens}`,
      );
    }
    return { aiContent, promptTokens, completionTokens };
  }

  private async handleLegacyLangchainStream(
    llmConfig: LlmConfig,
    deploymentNameFromDb: string | undefined,
    modelNameFromDb: string | undefined,
    temperature: number | undefined,
    llmMessages: BaseMessage[],
    passthrough: PassThrough,
    conversationUUID: string,
    user: AuthenticatedUser,
    userMessageId: number | undefined, // Changed userId to user
  ): Promise<Readable> {
    let chatModel: BaseChatModel;
    switch (llmConfig.provider) {
      case 'azure':
        chatModel = new ChatOpenAI(
          this.createModelConfig(
            {
              openAIApiKey: llmConfig.apiKey,
              modelName: deploymentNameFromDb ?? 'unknown-azure-deployment',
            },
            temperature ?? 1.0,
            llmConfig,
          ),
        );
        break;
      case 'google':
        chatModel = new ChatGoogleGenerativeAI(
          this.createModelConfig(
            {
              apiKey:
                llmConfig.apiKey ||
                this.configService.get<string>('GOOGLE_API_KEY'),
              model: modelNameFromDb ?? 'gemini-pro',
            },
            temperature ?? 1.0,
            llmConfig,
          ),
        );
        break;
      case 'anthropic':
        chatModel = new ChatAnthropic(
          this.createModelConfig(
            {
              apiKey:
                llmConfig.apiKey ||
                this.configService.get<string>('ANTHROPIC_API_KEY'),
              modelName: modelNameFromDb,
            },
            temperature ?? 1.0,
            llmConfig,
          ),
        );
        break;
      case 'qwen':
        let apiKeyForQwenLegacyStream = llmConfig.apiKey;
        if (!apiKeyForQwenLegacyStream) {
          this.logger.warn(
            `Qwen API key not found in llmConfig for provider '${llmConfig.provider}' (legacy stream), attempting to fetch from ConfigService.`,
          );
          apiKeyForQwenLegacyStream =
            this.configService.get<string>('ALIBABA_API_KEY');
          if (!apiKeyForQwenLegacyStream) {
            this.logger.error(
              `Qwen API key is missing from llmConfig and could not be fetched from ConfigService using env var 'ALIBABA_API_KEY' for legacy stream.`,
            );
            throw new InternalServerErrorException(
              'Qwen API key is not configured for legacy stream.',
            );
          }
        }
        this.logger.debug(
          `[QWEN CONSTRUCTOR CHECK - Legacy Stream] Passing to ChatAlibabaTongyi - alibabaApiKey: ${apiKeyForQwenLegacyStream ? apiKeyForQwenLegacyStream.substring(0, 5) + '...' + apiKeyForQwenLegacyStream.substring(apiKeyForQwenLegacyStream.length - 5) : 'NOT FOUND'}`,
        );
        chatModel = new ChatAlibabaTongyi({
          alibabaApiKey: apiKeyForQwenLegacyStream,
          model: modelNameFromDb ?? deploymentNameFromDb ?? 'qwen-plus',
          temperature: temperature ?? 1.0,
        });
        break;
      // Removed 'gcp-vertexai-gemini' case as it's handled by VertexGeminiService
      default:
        this.logger.error(
          `Unhandled Langchain provider in legacy stream: ${llmConfig.provider}`,
        );
        throw new InternalServerErrorException(
          `Unhandled Langchain provider in legacy stream: ${llmConfig.provider}`,
        );
    }
    if (!userMessageId) {
      throw new InternalServerErrorException(
        'User message ID missing for legacy stream.',
      );
    }
    const langchainStream = await chatModel.stream(llmMessages);
    // Pass the necessary validated model names to the processor
    this.processLangchainStream(
      langchainStream,
      passthrough,
      conversationUUID,
      temperature,
      llmConfig,
      user.userId,
      userMessageId,
      [], // empty searchSources
      llmMessages, // Pass llmMessages for token counting
      deploymentNameFromDb,
      modelNameFromDb,
    ); // Changed userId to user.userId, added empty searchSources
    return passthrough;
  }

  /**
   * Create chat completion for REST API calls without creating conversations
   * This method is specifically for external API usage and:
   * - Does NOT create or update conversations
   * - Does NOT save messages to the database
   * - DOES track token usage with is_api = 1
   * - Returns OpenAI-compatible format
   */
  async createRestApiCompletion(
    dto: CreateChatCompletionDto,
    user: AuthenticatedUser,
  ): Promise<ChatCompletionResponseDto> {
    this.logger.log(
      `[REST API] Processing chat completion for user ${user.userId}, model: ${dto.model}`,
    );

    const {
      prompt,
      messages: dtoMessages,
      model: requestedModel,
      temperature,
      stream,
    } = dto;
    const deptUnitCode = user.dept_unit_code;

    // Get LLM configuration
    const llmConfig = await this.llmConfigService.getLlmConfig(
      deptUnitCode,
      requestedModel,
    );

    // Create message array for the LLM
    let messages: BaseMessage[];

    if (dtoMessages && dtoMessages.length > 0) {
      // Handle full message structure (for vision prompts)
      this.logger.log(
        `[REST API] Processing ${dtoMessages.length} messages including potential vision content`,
      );

      messages = dtoMessages.map((msg) => {
        if (msg.role === 'system') {
          return new SystemMessage(
            typeof msg.content === 'string'
              ? msg.content
              : JSON.stringify(msg.content),
          );
        } else if (msg.role === 'assistant') {
          return new AIMessage(
            typeof msg.content === 'string'
              ? msg.content
              : JSON.stringify(msg.content),
          );
        } else {
          // For user messages, handle both string and vision content
          if (typeof msg.content === 'string') {
            return new HumanMessage(msg.content);
          } else if (Array.isArray(msg.content)) {
            // Vision content - pass the array directly to HumanMessage
            return new HumanMessage({ content: msg.content });
          } else {
            return new HumanMessage(JSON.stringify(msg.content));
          }
        }
      });
    } else {
      // Handle simple prompt (backward compatibility)
      messages = [new HumanMessage(prompt)];
    }

    // Helper function to calculate total content length for token estimation
    const calculateContentLength = (): number => {
      if (dtoMessages && dtoMessages.length > 0) {
        return dtoMessages.reduce((total, msg) => {
          if (typeof msg.content === 'string') {
            return total + msg.content.length;
          } else if (Array.isArray(msg.content)) {
            // For vision content, count only text parts
            const textLength = msg.content
              .filter((item) => item.type === 'text')
              .reduce(
                (textTotal, item) =>
                  textTotal +
                  (item.type === 'text' ? item.text || '' : '').length,
                0,
              );
            return total + textLength;
          }
          return total;
        }, 0);
      } else {
        return prompt?.length || 0;
      }
    };

    const totalContentLength = calculateContentLength();

    // Generate a unique ID for this completion
    const completionId = `chatcmpl-${uuidv4()}`;
    const created = Math.floor(Date.now() / 1000);

    if (stream) {
      // For streaming, we need to handle token counting differently
      this.logger.warn('[REST API] Streaming not yet implemented for REST API');
      throw new NotImplementedException(
        'Streaming is not yet supported for REST API calls',
      );
    }

    try {
      // Get the appropriate LLM model
      let aiResponse: string = '';
      let promptTokens = 0;
      let completionTokens = 0;

      // Direct model invocation based on provider
      switch (llmConfig.provider) {
        case 'azure':
          const azureModel = new ChatOpenAI({
            temperature: temperature ?? 1.0,
            modelName: llmConfig.deploymentName,
            streaming: false, // Explicitly set to false
            streamUsage: true, // Enable token usage tracking
            configuration: {
              apiKey: llmConfig.apiKey,
              baseURL: `https://${llmConfig.instanceName}.openai.azure.com/openai/deployments/${llmConfig.deploymentName}`,
              defaultQuery: { 'api-version': llmConfig.apiVersion },
            },
          });
          const azureResult = await azureModel.invoke(messages);
          aiResponse = azureResult.content.toString();

          // Debug log the full Azure result structure
          this.logger.debug(
            `[Azure REST API Debug] Full result structure: ${JSON.stringify(azureResult, null, 2)}`,
          );

          // Enhanced token extraction for Azure OpenAI
          const azureUsage =
            azureResult.usage_metadata ?? // Standard LangChain
            azureResult.response_metadata?.tokenUsage ??
            azureResult.response_metadata?.usage_metadata ??
            azureResult.response_metadata?.usage ?? // Azure OpenAI specific
            azureResult.response_metadata?.llmOutput?.usage ?? // Alternative location
            {};

          // Extract actual tokens if available
          promptTokens =
            (azureUsage.input_tokens as number) ?? // LangChain standard
            (azureUsage.prompt_tokens as number) ?? // Azure OpenAI format
            (azureUsage.promptTokenCount as number) ??
            (azureResult.response_metadata?.tokenUsage
              ?.prompt_tokens as number) ??
            (azureResult.response_metadata?.llmOutput?.usage
              ?.prompt_tokens as number) ??
            0;
          completionTokens =
            (azureUsage.output_tokens as number) ?? // LangChain standard
            (azureUsage.completion_tokens as number) ?? // Azure OpenAI format
            (azureUsage.completionTokenCount as number) ??
            (azureResult.response_metadata?.tokenUsage
              ?.completion_tokens as number) ??
            (azureResult.response_metadata?.llmOutput?.usage
              ?.completion_tokens as number) ??
            0;

          // Use zero tokens as fallback instead of estimation
          if (promptTokens === 0 || completionTokens === 0) {
            this.logger.warn(
              `[REST API Token Usage] No token usage received from Azure OpenAI (${llmConfig.deploymentName}), recording as zero tokens.`,
            );
            promptTokens = promptTokens || 0;
            completionTokens = completionTokens || 0;
          } else {
            this.logger.debug(
              `[REST API Token Usage] Received actual token counts from Azure OpenAI (${llmConfig.deploymentName}): prompt=${promptTokens}, completion=${completionTokens}`,
            );
          }
          break;

        case 'google':
          const googleModel = new ChatGoogleGenerativeAI({
            model: llmConfig.deploymentName || 'gemini-pro',
            apiKey: llmConfig.apiKey,
            temperature: temperature ?? 1.0,
          });
          const googleResult = await googleModel.invoke(messages);
          aiResponse = googleResult.content.toString();
          promptTokens = 0; // Google provider doesn't provide token usage in REST API
          completionTokens = 0;
          break;

        case 'anthropic':
          const anthropicModel = new ChatAnthropic({
            modelName: llmConfig.deploymentName || 'claude-3-opus-20240229',
            anthropicApiKey: llmConfig.apiKey,
            temperature: temperature ?? 1.0,
          });
          const anthropicResult = await anthropicModel.invoke(messages);
          aiResponse = anthropicResult.content.toString();
          promptTokens = 0; // Anthropic provider doesn't provide token usage in REST API
          completionTokens = 0;
          break;

        case 'qwen':
          // Use ChatOpenAI with Qwen's OpenAI-compatible endpoint
          const qwenModel = new ChatOpenAI({
            apiKey: llmConfig.apiKey,
            configuration: {
              baseURL: `${llmConfig.endpointUrl?.replace(/\/$/, '')}/compatible-mode/v1`,
            },
            modelName:
              llmConfig.deploymentName || llmConfig.modelName || 'qwen-plus',
            temperature: temperature ?? 1.0,
          });
          const qwenResult = await qwenModel.invoke(messages);
          aiResponse = qwenResult.content.toString();

          // Try to extract token usage from response metadata
          const qwenUsage =
            qwenResult.response_metadata?.tokenUsage ||
            qwenResult.response_metadata?.llmOutput?.usage;
          if (qwenUsage) {
            promptTokens = qwenUsage.prompt_tokens || 0;
            completionTokens = qwenUsage.completion_tokens || 0;
          } else {
            promptTokens = 0;
            completionTokens = 0;
          }
          break;

        case 'gcp-vertexai-gemini':
          // Use native Vertex AI SDK for Gemini models
          try {
            // Dynamically import Vertex AI SDK
            const { VertexAI } = await import('@google-cloud/vertexai');

            const projectId = llmConfig.projectId;
            const location = llmConfig.region || 'us-central1';

            if (!projectId) {
              throw new Error(
                'Google Cloud project ID not configured for Vertex AI',
              );
            }

            // Initialize Vertex AI client
            const vertexAI = new VertexAI({
              project: projectId,
              location: location,
            });

            // Get the generative model
            const model = vertexAI.getGenerativeModel({
              model:
                llmConfig.deploymentName || llmConfig.modelName || 'gemini-pro',
            });

            // Convert messages to Vertex AI format
            const vertexMessages = messages.map((msg) => ({
              role: msg instanceof HumanMessage ? 'user' : 'model',
              parts: [
                {
                  text:
                    typeof msg.content === 'string'
                      ? msg.content
                      : JSON.stringify(msg.content),
                },
              ],
            }));

            // Generate content
            const result = await model.generateContent({
              contents: vertexMessages,
              generationConfig: {
                temperature: temperature ?? 1.0,
              },
            });

            const response = result.response;
            aiResponse =
              response.candidates?.[0]?.content?.parts?.[0]?.text || '';

            // Extract token usage if available
            if (response.usageMetadata) {
              promptTokens = response.usageMetadata.promptTokenCount || 0;
              completionTokens =
                response.usageMetadata.candidatesTokenCount || 0;
            } else {
              promptTokens = 0;
              completionTokens = 0;
            }
          } catch (vertexError: any) {
            this.logger.error(
              '[REST API] Failed to call Vertex AI Gemini:',
              vertexError,
            );
            throw new InternalServerErrorException(
              `Vertex AI Gemini error: ${vertexError.message || 'Unknown error'}`,
            );
          }
          break;

        case 'gcp-vertexai-llama':
          // Vertex AI Llama uses a custom OpenAPI-compatible endpoint
          try {
            // Get Google Auth token
            const googleAuth = new GoogleAuth({
              scopes: 'https://www.googleapis.com/auth/cloud-platform',
            });
            const token = await googleAuth.getAccessToken();

            if (!token) {
              throw new Error('Failed to obtain Google Auth token');
            }

            // Determine the appropriate region and model based on the requested model
            // Llama 4 models use us-east5 region, while Llama 3.1 uses us-central1
            const isLlama4 =
              dto.model.toLowerCase().includes('llama-4') ||
              dto.model.toLowerCase().includes('llama4') ||
              dto.model.toLowerCase().includes('maverick');

            const region = isLlama4 ? 'us-east5' : 'us-central1';
            const effectiveModelName = isLlama4
              ? 'meta/llama-4-maverick-17b-128e-instruct-maas'
              : 'meta/llama-3.1-405b-instruct-maas';

            // Use ChatOpenAI with custom endpoint configuration
            const llamaModel = new ChatOpenAI({
              temperature: temperature ?? 1.0,
              modelName: effectiveModelName,
              configuration: {
                apiKey: 'dummy', // ChatOpenAI requires an apiKey even if not used
                baseURL: `https://${region}-aiplatform.googleapis.com/v1/projects/${llmConfig.projectId}/locations/${region}/endpoints/openapi`,
                defaultHeaders: {
                  Authorization: `Bearer ${token}`,
                },
              },
            });

            const llamaResult = await llamaModel.invoke(messages);
            aiResponse = llamaResult.content.toString();
            promptTokens = 0; // Vertex AI Llama doesn't provide token usage in REST API
            completionTokens = 0;
          } catch (authError) {
            this.logger.error(
              '[REST API] Failed to authenticate for Vertex AI Llama:',
              authError,
            );
            throw new InternalServerErrorException(
              'Failed to authenticate with Google Cloud',
            );
          }
          break;

        case 'deepseek-aisvc':
          // DeepSeek via Azure AI Service
          try {
            // Use the Azure AI Service endpoint format for DeepSeek
            const endpoint =
              llmConfig.endpointUrl?.replace(/\/+$/, '') + '/chat/completions';
            const url = `${endpoint}?api-version=${llmConfig.apiVersion}`;

            // Get API key with fallback to environment variable
            const apiKey =
              llmConfig.apiKey ||
              this.configService.get<string>('AZURE_AI_SERVICE_KEY');

            if (!apiKey) {
              throw new InternalServerErrorException(
                'DeepSeek API key not found in config or environment (AZURE_AI_SERVICE_KEY)',
              );
            }

            const headers = {
              'Content-Type': 'application/json',
              'api-key': apiKey,
            };

            const payload = {
              messages: messages.map((msg) => ({
                role:
                  msg instanceof HumanMessage
                    ? 'user'
                    : msg instanceof AIMessage
                      ? 'assistant'
                      : 'system',
                content:
                  typeof msg.content === 'string'
                    ? msg.content
                    : JSON.stringify(msg.content),
              })),
              temperature: temperature ?? 1.0,
              model: `${llmConfig.deploymentName}-${llmConfig.instanceName?.toLowerCase()}`,
            };

            this.logger.log(
              `[REST API] DeepSeek request to ${url} with payload: ${JSON.stringify(payload)}`,
            );

            // Dynamically import fetch
            const { default: fetch } = await import('node-fetch');

            const response = await fetch(url, {
              method: 'POST',
              headers,
              body: JSON.stringify(payload),
            });

            if (!response.ok) {
              let errorMessage = `DeepSeek AI Service request failed with status ${response.status}`;
              try {
                const errorData = await response.json();
                errorMessage = errorData.error?.message || errorMessage;
              } catch {
                // If parsing error response fails, use default message
              }
              this.logger.error(`[REST API] DeepSeek error: ${errorMessage}`);
              throw new HttpException(errorMessage, response.status);
            }

            const responseData = await response.json();

            if (responseData.choices && responseData.choices.length > 0) {
              aiResponse = responseData.choices[0].message?.content || '';
            }

            // Extract token usage if available
            if (responseData.usage) {
              promptTokens = responseData.usage.prompt_tokens || 0;
              completionTokens = responseData.usage.completion_tokens || 0;
            } else {
              // Use zero tokens as fallback
              promptTokens = 0;
              completionTokens = 0;
            }
          } catch (deepseekError: any) {
            this.logger.error(
              '[REST API] Failed to call DeepSeek AI Service:',
              deepseekError,
            );
            throw new InternalServerErrorException(
              `DeepSeek AI Service error: ${deepseekError.message || 'Unknown error'}`,
            );
          }
          break;

        default:
          // For other providers, fall back to a simple error for now
          throw new BadRequestException(
            `Provider ${llmConfig.provider} is not yet supported for REST API calls. Supported providers: azure, google, anthropic, qwen, gcp-vertexai-gemini, gcp-vertexai-llama, deepseek-aisvc`,
          );
      }

      const totalTokens = promptTokens + completionTokens;

      // Update token usage with is_api = 1
      await this.updateApiTokenUsage(
        user.userId,
        llmConfig.modelName || requestedModel,
        totalTokens,
        promptTokens,
        completionTokens,
        user,
      );

      // Return OpenAI-compatible response format
      return {
        id: completionId,
        object: 'chat.completion',
        created,
        model: requestedModel,
        choices: [
          {
            index: 0,
            message: {
              role: 'assistant',
              content: aiResponse,
            },
            finish_reason: 'stop',
          },
        ],
        usage: {
          prompt_tokens: promptTokens,
          completion_tokens: completionTokens,
          total_tokens: totalTokens,
        },
      };
    } catch (error: any) {
      this.logger.error(
        `[REST API] Error during completion: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Update token usage specifically for API calls
   */
  private async updateApiTokenUsage(
    userId: string,
    modelName: string,
    totalTokens: number,
    promptTokens: number,
    completionTokens: number,
    user?: AuthenticatedUser,
  ): Promise<void> {
    try {
      this.logger.debug(
        `[REST API] Updating API token usage: user=${userId}, model=${modelName}, total=${totalTokens}`,
      );

      // Use TokenUsageService to ensure consistent model name normalization
      await this.tokenUsageService.updateTokenUsage({
        username: userId,
        modelName: modelName,
        tokenDate: new Date(),
        promptTokens: promptTokens,
        completionTokens: completionTokens,
        totalTokens: totalTokens,
        isApi: true, // This is a REST API request
        // Note: REST API calls may not have conversation context, so no conversationUuid
      });

      this.logger.debug('[REST API] Token usage updated successfully');

      // Check if user has now exceeded their limit after this request
      if (user) {
        const postUsageCheck =
          await this.tokenUsageService.checkTokenLimitPostUsage(
            user,
            modelName,
          );

        if (postUsageCheck.exceeded) {
          this.logger.warn(
            `[REST API] User ${userId} has exceeded monthly token limit after this request: ${postUsageCheck.message}`,
          );
        }
      }
    } catch (error: any) {
      this.logger.error(
        `[REST API] Failed to update token usage: ${error.message}`,
        error.stack,
      );
      // Don't throw - token tracking failure shouldn't break the API response
    }
  }
}
