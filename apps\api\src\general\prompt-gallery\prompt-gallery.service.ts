import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { CreatePromptGalleryDto } from './dto/create-prompt-gallery.dto';
import { UpdatePromptGalleryDto } from './dto/update-prompt-gallery.dto';

@Injectable()
export class PromptGalleryService {
  constructor(private prisma: PrismaService) {}

  async create(createPromptGalleryDto: CreatePromptGalleryDto, userId: string) {
    const { system_instruction, prompt_content, is_default, task_ids } =
      createPromptGalleryDto;
    const promptGallery = await this.prisma.prompt_gallery.create({
      data: {
        system_instruction,
        prompt_content,
        is_default: is_default ?? false,
        user_id: userId,
        prompt_gallery_task: {
          create: (task_ids || []).map((id) => ({
            task: {
              connect: {
                id,
              },
            },
          })),
        },
      },
    });
    return promptGallery;
  }

  async findAll(userId: string) {
    return this.prisma.prompt_gallery.findMany({
      where: {
        OR: [
          {
            is_default: true,
          },
          {
            user_id: userId,
          },
        ],
      },
      include: {
        prompt_gallery_task: {
          include: {
            task: true,
          },
        },
      },
    });
  }

  async findOne(id: number) {
    return this.prisma.prompt_gallery.findUnique({
      where: { id },
      include: {
        prompt_gallery_task: {
          include: {
            task: true,
          },
        },
      },
    });
  }

  async update(id: number, updatePromptGalleryDto: UpdatePromptGalleryDto) {
    const { task_ids, ...data } = updatePromptGalleryDto;
    const updateData: any = { ...data };

    if (task_ids && task_ids.length > 0) {
      await this.prisma.prompt_gallery_task.deleteMany({
        where: { prompt_gallery_id: id },
      });
      updateData.prompt_gallery_task = {
        create: task_ids.map((taskId) => ({
          task: {
            connect: {
              id: taskId,
            },
          },
        })),
      };
    }

    return this.prisma.prompt_gallery.update({
      where: { id },
      data: updateData,
    });
  }

  async remove(id: number) {
    await this.prisma.prompt_gallery_task.deleteMany({
      where: { prompt_gallery_id: id },
    });
    return this.prisma.prompt_gallery.delete({
      where: { id },
    });
  }
}
