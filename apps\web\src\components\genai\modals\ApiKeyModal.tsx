import { Fragment, useEffect, useState } from 'react'; // Removed useContext, useRef
import { Dialog, Transition } from '@headlessui/react';
// Removed old context import: import { AppContext } from "../../context/AppContext";
import { useAppDispatch, useAppSelector } from '@/lib/store/hooks'; // Import Redux hooks
import { useGetApiKeyMutation } from '@/lib/store/apiSlice'; // Import the hook
import { Alert, Snackbar } from '@mui/material';
import { closeApiKeyModal } from '@/lib/store/uiSlice'; // Import close action

const dummyApiKey = 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx';

const ApiKeyModal = () => {
  const basePath = process.env.NEXT_PUBLIC_BASE_PATH;

  const dispatch = useAppDispatch(); // Use Redux dispatch
  const showModal = useAppSelector((state) => state.ui.showApiKeyModal); // Select state from Redux
  const [apiKey, setApiKey] = useState<string | undefined>(dummyApiKey);
  const [showCopied, setShowCopied] = useState(false);
  const [copy, setCopy] = useState(false);

  // Instantiate the mutation hook
  const [getApiKeyTrigger, { isLoading: isGeneratingApiKey }] =
    useGetApiKeyMutation();

  const onClickRegenerate = async () => {
    setApiKey(undefined); // Show loading indicator immediately
    try {
      const result = await getApiKeyTrigger({ deploymentName: '' }).unwrap(); // Trigger the mutation
      setApiKey(result.api_key ?? ''); // Set API key from response
    } catch (error) {
      console.error('Failed to generate API key:', error);
      // Handle error appropriately, maybe show a toast
      setApiKey(dummyApiKey); // Revert to dummy or show error message
    }
  };

  // Updated onClickClose to use Redux dispatch
  const onClickClose = () => {
    dispatch(closeApiKeyModal());
  };

  const onClickApiKey = () => {
    if (apiKey !== undefined && apiKey !== dummyApiKey) {
      navigator.clipboard.writeText(apiKey);
      setCopy((prev) => !prev);
    }
  };

  useEffect(() => {
    if (apiKey !== undefined && apiKey !== dummyApiKey) {
      setShowCopied(true);
      const timeout = setTimeout(() => {
        setShowCopied(false);
      }, 2000);

      return () => clearTimeout(timeout);
    }
  }, [copy, apiKey]); // Added missing apiKey dependency

  const clearApiKey = () => {
    setApiKey(dummyApiKey);
  };

  return (
    <>
      <Transition.Root
        show={showModal} // Use Redux state here
        beforeEnter={clearApiKey}
        afterLeave={clearApiKey}
      >
        <Dialog as="div" className="relative z-30" onClose={() => {}}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-500 dark:bg-[#4a4a4aA0] bg-opacity-75 transition-opacity" />
          </Transition.Child>

          <div className="fixed inset-0 z-10 overflow-y-auto">
            <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                enterTo="opacity-100 translate-y-0 sm:scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              >
                <Dialog.Panel className="grow relative transform overflow-hidden rounded-lg bg-white dark:bg-[#3b3b3b] text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-4xl">
                  <div className="flex flex-col bg-white dark:bg-[#3b3b3b] px-4 pb-4 pt-5 sm:p-6 sm:pb-4 gap-4 dark:text-white">
                    <div className="flex">
                      <label className="grow text-lg font-semibold">
                        HKBU GenAI Platform API Service
                      </label>
                    </div>
                    <div className="flex gap-3 flex-col text-sm">
                      {/* Specification link (commented out for now) */}
                      <p>
                        This page contains information about the REST API
                        endpoint for the HKBU GenAI Platform API Service. To
                        make use of the service, you will require your key and
                        endpoint.{' '}
                        <span className="font-semibold">
                          The key is a unique identifier and provides access to
                          your account, please DO NOT share it with others.{' '}
                        </span>
                        The API usage will be monitored, and the API will share
                        the same monthly quota as the HKBU GenAI Platform
                        Service.
                        <span className="font-semibold">
                          {' '}
                          Rate limit has also imposed to optimize the resource
                          usage.
                        </span>
                      </p>
                      <p>
                        You can consult the following supported versions of
                        swagger specification for more guidance.
                        <br />
                      </p>
                      <div className="border border-gray-200 rounded-md overflow-hidden">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50 dark:bg-[#4a4a4a]">
                            <tr>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Model
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Specification
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white dark:bg-[#3b3b3b] divide-y divide-gray-200">
                            {/* GPT Specs */}
                            <tr>
                              <td className="px-6 py-4 whitespace-nowrap">
                                GPT
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <a
                                  className="cursor-pointer"
                                  target="_blank"
                                  href={`${basePath}/general/specification/2024-02-01`}
                                >
                                  2024-02-01
                                </a>
                                <br />
                                <a
                                  className="cursor-pointer"
                                  target="_blank"
                                  href={`${basePath}/general/specification/2024-05-01-preview`}
                                >
                                  2024-05-01-preview
                                </a>
                                <br />
                                <a
                                  className="cursor-pointer"
                                  target="_blank"
                                  href={`${basePath}/general/specification/2024-10-21`}
                                >
                                  2024-10-21
                                </a>
                                <br />
                                <a
                                  className="cursor-pointer"
                                  target="_blank"
                                  href={`${basePath}/general/specification/2024-12-01-preview`}
                                >
                                  2024-12-01-preview
                                </a>
                              </td>
                            </tr>
                            {/* Claude Specs */}
                            <tr>
                              <td className="px-6 py-4 whitespace-nowrap">
                                Claude
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <a
                                  className="cursor-pointer"
                                  target="_blank"
                                  href={`${basePath}/general/specification/claude-swagger`}
                                >
                                  20240620
                                </a>
                              </td>
                            </tr>
                            {/* Gemini Specs */}
                            <tr>
                              <td className="px-6 py-4 whitespace-nowrap">
                                Gemini
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <a
                                  className="cursor-pointer"
                                  target="_blank"
                                  href={`${basePath}/general/specification/gemini-swagger`}
                                >
                                  20240924
                                </a>
                              </td>
                            </tr>
                            {/* Deepseek Specs */}
                            <tr>
                              <td className="px-6 py-4 whitespace-nowrap">
                                Deepseek
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <a
                                  className="cursor-pointer"
                                  target="_blank"
                                  href={`${basePath}/general/specification/deepseek-swagger`}
                                >
                                  2024-05-01-preview
                                </a>
                              </td>
                            </tr>
                            {/* Qwen Specs */}
                            <tr>
                              <td className="px-6 py-4 whitespace-nowrap">
                                Qwen
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <a
                                  className="cursor-pointer"
                                  target="_blank"
                                  href={`${basePath}/general/specification/qwen-swagger`}
                                >
                                  v1
                                </a>
                              </td>
                            </tr>
                            {/* Llama Specs */}
                            <tr>
                              <td className="px-6 py-4 whitespace-nowrap">
                                Llama
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <a
                                  className="cursor-pointer"
                                  target="_blank"
                                  href={`${basePath}/general/specification/llama-swagger`}
                                >
                                  20240723
                                </a>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>

                      <p>
                        To obtain your own key, click on the <b>Generate</b>{' '}
                        button below. Keep in mind that the new key will only be
                        displayed once, and the previous key will become invalid
                        if it exists.
                      </p>
                    </div>
                    <div className="flex flex-row w-full items-center gap-2 overflow-hidden">
                      <p className="shrink-0">Key:</p>

                      <div
                        className={`overflow-hidden ${
                          apiKey !== undefined &&
                          apiKey !== dummyApiKey &&
                          'cursor-pointer'
                        }`}
                        onClick={onClickApiKey}
                      >
                        <p
                          className={`h-[26px] w-[380px] max-w-full rounded border border-black px-2 bg-gray-50 dark:bg-[#4a4a4a] whitespace-nowrap flex items-center`}
                        >
                          {/* Show loading spinner if API call is in progress */}
                          {isGeneratingApiKey ? (
                            <div
                              className="h-5 w-5 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite] text-slate-300"
                              role="status"
                            >
                              <span className="!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]"></span>
                            </div>
                          ) : (
                            apiKey
                          )}
                        </p>
                      </div>
                      <div
                        className={`shrink-0 rounded flex h-full aspect-square p-1 ${
                          apiKey !== undefined && apiKey !== dummyApiKey
                            ? 'cursor-pointer bg-emerald-700'
                            : 'bg-gray-500'
                        }`}
                        onClick={onClickApiKey}
                      >
                        <svg
                          fill="#FFF"
                          xmlns="http://www.w3.org/2000/svg"
                          width="18"
                          height="18"
                          viewBox="0 -960 960 960"
                        >
                          <path d="M360-240q-33 0-56.5-23.5T280-320v-480q0-33 23.5-56.5T360-880h360q33 0 56.5 23.5T800-800v480q0 33-23.5 56.5T720-240H360Zm0-80h360v-480H360v480ZM200-80q-33 0-56.5-23.5T120-160v-560h80v560h440v80H200Zm160-240v-480 480Z" />
                        </svg>
                      </div>
                    </div>
                  </div>
                  <div className="bg-gray-50 dark:bg-[#4a4a4a] px-4 py-3 justify-between gap-2 flex flex-col sm:flex-row sm:px-6">
                    <div className="flex flex-col-reverse sm:flex-row items-center gap-2">
                      <button
                        type="button"
                        className={`inline-flex w-full justify-center rounded-2xl px-3 py-2 text-sm font-semibold shadow-sm ring-1 ring-inset ring-gray-300 sm:w-auto ${isGeneratingApiKey ? 'bg-gray-400 cursor-not-allowed' : 'bg-emerald-700 text-gray-50 hover:bg-emerald-800'}`}
                        onClick={onClickRegenerate}
                        disabled={isGeneratingApiKey} // Disable button while loading
                      >
                        {isGeneratingApiKey ? 'Generating...' : 'Generate'}
                      </button>
                      {/* Info message */}
                    </div>
                    <button
                      type="button"
                      className="inline-flex w-full justify-center rounded-2xl bg-white dark:bg-[#3b3b3b] px-3 py-2 text-sm font-semibold text-gray-900 dark:text-white shadow-sm ring-1 ring-inset ring-gray-300 sm:hover:bg-gray-50 dark:hover:bg-[#4a4a4a] sm:mt-0 sm:w-auto"
                      onClick={onClickClose} // Use Redux-based close handler
                      disabled={isGeneratingApiKey} // Disable close while generating
                    >
                      Close
                    </button>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition.Root>
      <Snackbar
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        open={showCopied}
        autoHideDuration={2000} // Auto hide after 2 seconds
        onClose={() => setShowCopied(false)} // Close handler for click away or timeout
      >
        <Alert severity="success" sx={{ width: '100%' }}>
          Copied
        </Alert>
      </Snackbar>
    </>
  );
};

export default ApiKeyModal;
