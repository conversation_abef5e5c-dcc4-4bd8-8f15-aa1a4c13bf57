import { GptModel } from '@/lib/types/common';

// Interface matching the one used in HealthCheckModal.tsx
export interface ModelHealthCheck {
  modelName: string;
  displayName: string;
  status: 'active' | 'unstable' | 'down' | 'checking' | 'pending';
  responseTime?: number;
  error?: string;
  isChecking?: boolean;
}

// Placeholder function for checkModelHealth
// This function now immediately returns a "pending" status.
// The actual API call logic needs to be implemented later,
// likely using RTK Query and new backend endpoints.
export async function checkModelHealth(
  model: GptModel,
  _username: string = 'health_check', // Prefixed unused username
  _signal?: AbortSignal, // Prefixed unused signal
): Promise<ModelHealthCheck> {
  console.warn(
    `[Health Check Placeholder] checkModelHealth called for ${model.display_name}, returning pending.`,
  ); // Corrected property
  // Simulate a short delay before returning pending
  await new Promise((resolve) => setTimeout(resolve, 50));

  // Return a pending status immediately
  return {
    modelName: model.model_name,
    displayName: model.display_name, // Correct property access
    status: 'pending',
    isChecking: false, // Ensure this is false as we aren't actively checking
  };
}

// Removed unused checkO1ModelHealth function
