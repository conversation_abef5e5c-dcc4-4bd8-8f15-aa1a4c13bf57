# File Upload Feature Implementation Plan

This plan outlines the steps to implement file upload functionality for the chat interface, allowing users to upload various file types (PDF, DOCX, images) to be processed and included in the context sent to the LLM.

**Assumptions:**
*   OCR logic exists at the POST `/ocr` endpoint in `hkbu-genai-platform/apps/api/src/general/general.controller.ts`.
*   Files will be sent from the frontend to the backend as base64 encoded strings within the JSON payload.
*   Frontend file size limit is 10MB.
*   A new database column `supports_vision` will be added to identify vision-capable models.

**1. Database Schema Modification**

*   **Goal:** Add a way to identify models that support vision input.
*   **Action:** Add a new boolean column `supports_vision` to the `model_list` table in the database.
*   **Steps:**
    *   Edit `hkbu-genai-platform/packages/database/prisma/schema.prisma`: Add `supports_vision Boolean @default(false)` to the `model_list` model definition.
    *   Generate and apply a new Prisma migration (`npx prisma migrate dev --name add_supports_vision_flag`).
    *   Manually update the `model_list` table in the database, setting `supports_vision` to `true` for models that can process images (e.g., GPT-4 Vision, Gemini Vision models).

**2. Backend API Implementation (`hkbu-genai-platform/apps/api`)**

*   **Goal:** Modify the API to accept files (as base64), process them (OCR or pass-through for vision), and include their content in the LLM prompt.
*   **Steps:**
    *   **Update DTO:**
        *   Modify `hkbu-genai-platform/apps/api/src/general/chat-completion/dto/create-chat-completion.dto.ts`.
        *   Add an optional `files` array property to `CreateChatCompletionDto`. Each element should be: `{ filename: string; mimeType: string; content: string; }` (where `content` is base64).
    *   **Examine OCR Endpoint:**
        *   Review the POST `/ocr` endpoint logic within `hkbu-genai-platform/apps/api/src/general/general.controller.ts` and its associated service (if any).
    *   **Refactor OCR (Recommended):**
        *   Create a new service: `OcrService` (`hkbu-genai-platform/apps/api/src/utils/ocr.service.ts`).
        *   Move the core OCR processing logic into `OcrService`. It should accept file content (Buffer from base64) and return text.
        *   Update `GeneralController` to use `OcrService`.
        *   Inject `OcrService` into `ChatCompletionService`.
    *   **Update `ChatCompletionService` (`chat-completion.service.ts`):**
        *   Modify the `prisma.model_list.findFirst` query to select the new `supports_vision` column.
        *   In the `createCompletion` method:
            *   Receive the `files` array from the DTO.
            *   Initialize a variable/array for processed file content.
            *   Loop through `files`:
                *   Decode base64 `content` to a Buffer.
                *   Check `mimeType` and `supports_vision` flag:
                    *   **Image + Vision Model:** Format image data for Langchain multimodal input (e.g., `{ type: "image_url", image_url: { url: \`data:${mimeType};base64,${file.content}\` } }`). Add to `HumanMessage` content array.
                    *   **Document:** Call `OcrService`. Append returned text to the main prompt (e.g., `\n\n--- Content from ${filename} ---\n${ocrText}`).
                    *   **Unsupported:** Log warning, skip.
            *   Construct `HumanMessage` (content will be string or array).
            *   Pass potentially multimodal `llmMessages` to Langchain.

**3. Frontend Implementation (`hkbu-genai-platform/apps/web`)**

*   **Goal:** Allow users to select files, validate (type, size < 10MB), display, encode (base64), and send them.
*   **Steps:**
    *   **Update `ChatInputArea.tsx`:**
        *   Add an invisible file input: `<input type="file" multiple hidden ref={fileInputRef} onChange={handleFileSelection} accept={supportedFileList} />`.
        *   Modify `AttachFileIcon` IconButton: Add `onClick` to trigger the hidden input.
        *   Implement `handleFileSelection`:
            *   Access selected files (`event.target.files`).
            *   Validate type (`supportedFileList`) and size (`uploadFileSizeLimit`).
            *   Add valid `File` objects to `tempFileList` state via `setTempFileList`.
            *   Show errors via `setChatErrorMessage`.
            *   Clear file input value (`event.target.value = ''`).
        *   Display selected files (e.g., list/chips below input) with removal option.
    *   **Update Chat Page Components (`[chatId]/page.tsx`, `new/page.tsx`):**
        *   Modify `handleSubmit`:
            *   If `tempFileList` has files:
                *   Use `FileReader.readAsDataURL()` asynchronously for each file.
                *   Format results: `{ filename: file.name, mimeType: file.type, content: base64String.split(',')[1] }`.
                *   Add this array as the `files` property to the `requestBody`.
        *   Ensure `tempFileList` is cleared after submission.
    *   **Update Redux State/Selectors:**
        *   Ensure the model list fetch in `apiSlice.ts` includes the `supports_vision` field.
        *   Update relevant types/interfaces.

**Flow Diagram (Simplified Sequence):**

```mermaid
sequenceDiagram
    participant User
    participant ChatInputArea
    participant ChatPage
    participant apiSlice (RTK Query)
    participant ChatCompletionAPI
    participant OcrService
    participant LLM

    User->>ChatInputArea: Clicks Attach Icon -> Triggers file input
    User->>ChatInputArea: Selects file(s)
    ChatInputArea->>ChatInputArea: handleFileSelection() -> Validate -> Update state
    ChatInputArea->>ChatPage: setTempFileList(validFiles)
    ChatInputArea->>User: Displays selected files

    User->>ChatInputArea: Types message & Clicks Send
    ChatInputArea->>ChatPage: handleSubmit()
    ChatPage->>ChatPage: Read files as base64
    ChatPage->>apiSlice: triggerChatCompletion({ prompt, files: [...] })
    apiSlice->>ChatCompletionAPI: POST /chat-completion

    ChatCompletionAPI->>ChatCompletionAPI: Decode files, Check model vision support
    alt Image + Vision Model
        ChatCompletionAPI->>ChatCompletionAPI: Format image for prompt
    else Document
        ChatCompletionAPI->>OcrService: processFile()
        OcrService-->>ChatCompletionAPI: extractedText
        ChatCompletionAPI->>ChatCompletionAPI: Append text to prompt
    end
    ChatCompletionAPI->>LLM: Invoke model
    LLM-->>ChatCompletionAPI: Response
    ChatCompletionAPI-->>apiSlice: Response
    apiSlice-->>ChatPage: Update state
    ChatPage-->>User: Display response