@import "katex/dist/katex.min.css";
@import "tailwindcss";

/* :root {
  --background: #ffffff;
  --foreground: #171717;
} */

/* @theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
} */

/* @media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
} */

/* body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
} */
/* Custom scrollbar for WebKit-based browsers */
::-webkit-scrollbar {
  width: 4px;
}

::-webkit-scrollbar-track {
  background-color: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: #9ca3af;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #6b7280;
}
