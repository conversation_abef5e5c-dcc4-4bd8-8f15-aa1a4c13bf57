import {
  Controller,
  Get,
  UseGuards,
  Req,
  Logger,
  HttpException,
  HttpStatus,
  HttpCode,
  Query,
  DefaultValuePipe,
  ParseIntPipe,
} from '@nestjs/common';
import { ApiKeyAuthGuard } from '../../auth/guards/api-key-auth.guard';
import { GeneralRateLimitGuard } from '../../common/guards/general-rate-limit.guard';
import { GeneralService } from '../general.service';
import {
  GetRecentModelsResponseDto,
  RecentModelDto,
} from './dto/recent-models-response.dto';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiHeader,
  ApiQuery,
} from '@nestjs/swagger';
import { AuthenticatedUser } from '../../auth/user.interface';
import { Throttle } from '@nestjs/throttler';

// Interface for the request object with authenticated user
interface AuthenticatedRequestWithUser extends Request {
  user: AuthenticatedUser;
}

@ApiTags('User Recent Models (REST API)')
@Controller('rest/recent-models')
@UseGuards(ApiKeyAuthGuard, GeneralRateLimitGuard)
@ApiHeader({
  name: 'api-key',
  description: 'Your API Key for authentication.',
  required: true,
})
export class RecentModelsRestController {
  private readonly logger = new Logger(RecentModelsRestController.name);

  constructor(private readonly generalService: GeneralService) {}

  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get recently used models for the authenticated user',
    description:
      'Retrieves a list of models that the authenticated user has recently used, ordered by last usage time.',
  })
  @ApiQuery({
    name: 'limit',
    description: 'Maximum number of models to return',
    required: false,
    type: Number,
    example: 10,
    schema: { minimum: 1, maximum: 50, default: 10 },
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved recent models.',
    type: GetRecentModelsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request (e.g., invalid limit parameter).',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized (Invalid or missing API Key).',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden (User not authorized for this service).',
  })
  @ApiResponse({
    status: 429,
    description: 'Too Many Requests (Rate limit exceeded).',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error.',
  })
  @Throttle({ default: { limit: 100, ttl: 60000 } }) // 100 requests per minute
  async getRecentModels(
    @Req() req: AuthenticatedRequestWithUser,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
  ): Promise<GetRecentModelsResponseDto> {
    this.logger.log(
      `GET /rest/recent-models - Request from user "${req.user.userId}" with limit ${limit}`,
    );

    // Validate limit parameter
    if (limit < 1 || limit > 50) {
      this.logger.warn(
        `Invalid limit parameter: ${limit}. Must be between 1 and 50.`,
      );
      throw new HttpException(
        'Limit must be between 1 and 50.',
        HttpStatus.BAD_REQUEST,
      );
    }

    try {
      // Get recent models with timestamps from the service
      const models = await this.generalService.getRecentModelsWithTimestamp(
        req.user.userId,
        limit,
      );

      // Transform models to match the DTO structure
      const modelsWithTimestamp: RecentModelDto[] = models.map((model) => ({
        id: model.id,
        display_name: model.display_name,
        model_name: model.model_name,
        deployment_name: model.deployment_name,
        category: model.category,
        last_used_at: model.lastUsedAt.toISOString(),
      }));

      const response: GetRecentModelsResponseDto = {
        models: modelsWithTimestamp,
        count: modelsWithTimestamp.length,
        limit,
      };

      this.logger.log(
        `Successfully returned ${response.count} recent models for user "${req.user.userId}"`,
      );

      return response;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;

      this.logger.error(
        `Failed to get recent models for user "${req.user.userId}": ${errorMessage}`,
        errorStack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        'Failed to retrieve recent models.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
