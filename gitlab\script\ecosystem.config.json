{"apps": [{"name": "genai-api-app", "cwd": ".", "script": "dist/main.js", "instances": 2, "exec_mode": "cluster", "autorestart": false, "max_memory_restart": "1G", "env": {"NODE_ENV": "production", "PORT": "3005"}}, {"name": "genai-web-app", "cwd": ".", "script": "node_modules/next/dist/bin/next", "args": "start -p 3006", "instances": 2, "exec_mode": "cluster", "autorestart": false, "max_memory_restart": "1G", "env": {"NODE_ENV": "production"}}]}