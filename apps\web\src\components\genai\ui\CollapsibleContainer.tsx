import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline';
import { useState } from 'react';

export interface CollapsibleContainerProps {
  title: string;
  children: React.ReactNode;
  className?: string;
  subtextCollapsed?: string;
  subtextExpanded?: string;
}

const CollapsibleContainer: React.FC<CollapsibleContainerProps> = ({
  title,
  children,
  className = '',
  subtextCollapsed,
  subtextExpanded,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className={`w-full mt-1 ${className || ''} `}>
      <div
        className={`cursor-pointer p-4 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 transition-colors duration-500 ${isOpen ? 'rounded-t-lg' : 'rounded-lg'}`}
        onClick={() => setIsOpen(!isOpen)}
      >
        <div className="flex justify-between items-center">
          <p className="font-semibold text-gray-800 dark:text-gray-200">{title}</p>
          <span className="flex-shrink-0">
            {!isOpen ? (
              <ChevronDownIcon
                style={{ width: '16px', height: '16px' }}
                className="inline-block align-middle text-gray-600 dark:text-gray-300"
              />
            ) : (
              <ChevronUpIcon
                style={{ width: '16px', height: '16px' }}
                className="inline-block align-middle text-gray-600 dark:text-gray-300"
              />
            )}
          </span>
        </div>
        {!isOpen && subtextCollapsed && (
          <p className="text-xs text-gray-500 dark:text-gray-300 mt-1">
            {subtextCollapsed}
          </p>
        )}
        {isOpen && subtextExpanded && (
          <p className="text-xs text-gray-500 dark:text-gray-300 mt-1">
            {subtextExpanded}
          </p>
        )}
      </div>

      {isOpen && (
        <div className="p-4 border border-t-0 border-gray-200 dark:border-gray-600 rounded-b-lg">
          {children}
        </div>
      )}
    </div>
  );
};

export default CollapsibleContainer;
