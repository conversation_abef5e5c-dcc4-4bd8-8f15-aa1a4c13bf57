# RPM Rate Limit Testing

This test script verifies that the RPM (requests per minute) rate limiting is still working after removing the rate limit UI and API endpoints.

## Files

- `test_rpm_rate_limit.py` - Main test script
- `README_rpm_test.md` - This file

## Setup

The test script automatically reads configuration from:
- `.env` file for API key and base URL
- `config/models.json` for available models

No additional setup required if the existing configuration is correct.

## Running the Test

```bash
cd api_test
python test_rpm_rate_limit.py
```

## What the script tests

1. **Basic RPM Rate Limiting:**
   - **Phase 1**: Tests `/rest/models` endpoint with 65 rapid requests
   - **Phase 2**: Tests `/general/rest/deployments/{model}/chat/completions` with 65 requests
   - Should get rate limited after approximately 60 requests within a minute
   - Expects HTTP 429 responses when rate limited

2. **Model-specific Rate Limiting:**
   - Tests chat completions for multiple models separately
   - Each model uses the correct deployment name and API version
   - Verifies if rate limits apply per model or globally

3. **Correct Endpoint Structure:**
   - Uses proper REST API endpoints as defined in the backend
   - Chat completions: `/general/rest/deployments/{deployment_name}/chat/completions?api-version={version}`
   - Models: `/rest/models`

## Expected Results

SUCCESS (Rate limiting working):
- You should see some requests return HTTP 429 (Rate Limited)
- The script will show "RATE LIMITING IS WORKING!"

WARNING (Rate limiting not working):
- All requests return HTTP 200 (Success)
- The script will show "NO RATE LIMITING DETECTED"

## Sample Output

```
RPM Rate Limit Test Script
============================================================
Testing rate limit for: /rest/chat/completions
Sending 65 requests with 0.8s delay...
============================================================
[10:30:15.123] Request  1: SUCCESS (200)
[10:30:16.001] Request  2: SUCCESS (200)
...
[10:31:45.234] Request 61: RATE LIMITED (429)
[10:31:46.112] Request 62: RATE LIMITED (429)
...
============================================================
SUMMARY for /rest/chat/completions
============================================================
Total requests sent: 65
Successful requests: 60
Rate limited requests: 5
Other errors: 0
RATE LIMITING IS WORKING! Got 5 rate limit responses
```

## Configuration

The script automatically uses:
- **Port**: 3003 (from .env BASE_URL)
- **API Key**: From .env API_KEY
- **Models**: From config/models.json (excludes embedding models)

## Troubleshooting

1. **"Connection refused" errors:**
   - Make sure your API server is running on port 3003
   - Check the BASE_URL in .env file

2. **Authentication errors:**
   - Verify the API_KEY in .env file is correct
   - Make sure the API key has proper permissions

3. **All requests succeed (no rate limiting):**
   - Check if rate limiting service is running
   - Verify rate limits are configured correctly in your database
   - Check Redis is running (if using Redis for rate limiting)

## Clean Up

After testing, you can delete this file:
- `test_rpm_rate_limit.py`
- `README_rpm_test.md`