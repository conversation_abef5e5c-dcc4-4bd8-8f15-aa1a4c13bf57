import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SecretClient } from '@azure/keyvault-secrets';
import { DefaultAzureCredential } from '@azure/identity';

@Injectable()
export class KeyVaultService {
  private readonly logger = new Logger(KeyVaultService.name);
  private secrets: Map<string, string> = new Map();
  private isProductionOrUat: boolean;

  constructor(private configService: ConfigService) {
    this.isProductionOrUat = ['production', 'uat'].includes(
      this.configService.get<string>('NODE_ENV') || '',
    );
  }

  async init() {
    this.logger.log('KeyVaultService initializing...');
    const keyVaultName = this.configService.get<string>('KEY_VAULT_NAME');

    if (!this.isProductionOrUat || !keyVaultName) {
      this.logger.warn(
        'Not in production/UAT or KEY_VAULT_NAME is not defined. Skipping secret fetching.',
      );
      return;
    }

    try {
      const credential = new DefaultAzureCredential();
      const url = `https://${keyVaultName}.vault.azure.net`;
      const client = new SecretClient(url, credential);
      this.logger.log(`Fetching secrets from Key Vault: ${url}`);

      const secretNames = [
        'DB_ENCRYPTION_KEY_NAME',
        'DB_DECRYPTION_CERT_NAME',
        'DATABASE_URL',
        'AZURE_OPENAI_KEY',
        'PINECONE_API_KEY',
        'PINECONESSSP_API_KEY',
        'SQL_SERVER_DB_NAME',
        'SQL_SERVER_HOST',
        'SQL_SERVER_PASSWORD',
        'SQL_SERVER_PASSWORD_SCRIPTS',
        'SQL_SERVER_USER',
        'SSSP_API_KEY',
        'VISION_KEY',
        'GENERAL_AZURE_OPENAI_API_KEY_JP',
        'GENERAL_AZURE_OPENAI_API_KEY_AU',
        'GENERAL_AZURE_OPENAI_API_KEY_USNORTH',
        'GENERAL_AZURE_OPENAI_API_KEY_CA',
        'GENERAL_AZURE_OPENAI_API_KEY_UK',
        'GENERAL_AZURE_OPENAI_API_KEY_FR',
        'GENERAL_AZURE_OPENAI_API_KEY_USEAST2',
        'GENERAL_AZURE_OPENAI_API_KEY_USWEST',
        'GENERAL_AZURE_OPENAI_API_KEY_USWEST3',
        'GENERAL_SCECIE_AZURE_OPENAI_API_KEY_SWC',
        'GENERAL_SCECIE_AZURE_OPENAI_API_KEY_SWN',
        'GENERAL_SCECIE_AZURE_OPENAI_API_KEY_USEAST',
        'GENERAL_SCECIE_AZURE_OPENAI_API_KEY_USSOUTH',
        'REDIS_PASSWORD_1',
        'REDIS_PASSWORD_2',
        'REDIS_PASSWORD_3',
        'REDIS_PASSWORD_4',
        'REDIS_PASSWORD_5',
        'REDIS_PASSWORD_6',
        'TOKEN_COUNTER_HOST_KEY',
        'VISION_SCECIE_KEY',
        'DALLE_KEY',
        'SCERESTAPI_KEY',
        'VERTEX_AI_KEY',
        'GENERAL_AZURE_OPENAI_API_KEY_USEAST',
        'SQL-SERVER-MESSAGE-ENCRYPTION-CERT',
        'SQL-SERVER-MESSAGE-ENCRYPTION-KEY',
        'GENERAL_AZURE_AI_SERVICE_KEY',
        'ALIBABA_MODELSTUDIO_API_KEY',
      ];

      for (const name of secretNames) {
        try {
          const secretName = name.replaceAll('_', '-');
          const secret = await client.getSecret(secretName);
          if (secret.value) {
            this.secrets.set(name, secret.value);
            this.logger.log(`Successfully loaded secret: ${name}`);
          }
        } catch (e: unknown) {
          const error = e as Error;
          this.logger.error(`Failed to load secret: ${name}`, error.stack);
        }
      }
      this.logger.log('Finished fetching secrets from Key Vault.');
    } catch (e: unknown) {
      const error = e as Error;
      this.logger.error('Failed to initialize Key Vault client', error.stack);
    }
  }

  get(key: string): string {
    let value: string | undefined;
    if (this.isProductionOrUat) {
      value = this.secrets.get(key);
    }

    if (!value) {
      value = this.configService.get<string>(key);
    }

    if (!value) {
      throw new Error(`Configuration key "${key}" not found.`);
    }

    return value;
  }

  getAllSecrets(): Map<string, string> {
    return this.secrets;
  }
}
