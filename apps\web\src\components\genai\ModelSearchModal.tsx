'use client';

import React, { useState, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import Box from '@mui/material/Box';
import Modal from '@mui/material/Modal';
import Dialog from '@mui/material/Dialog'; // Using Dialog for better styling/structure
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import IconButton from '@mui/material/IconButton';
import TextField from '@mui/material/TextField';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Typography from '@mui/material/Typography';
import InputAdornment from '@mui/material/InputAdornment';
import CircularProgress from '@mui/material/CircularProgress';
import Alert from '@mui/material/Alert';
import CloseIcon from '@mui/icons-material/Close';
import SearchIcon from '@mui/icons-material/Search';
import ChatBubbleOutlineIcon from '@mui/icons-material/ChatBubbleOutline'; // Default icon

import { useGetModelsQuery } from '@/lib/store/apiSlice';
import { GptModel } from '@/lib/types/common';
import { modelInfo } from '@/components/genai/model/ModelInfo'; // Assuming this path is correct

interface ModelSearchModalProps {
  open: boolean;
  onClose: () => void;
}

// Helper to normalize model names for icon lookup
const normalize = (str: string = '') => str.replace(/[-_]/g, '').toLowerCase();

export default function ModelSearchModal({
  open,
  onClose,
}: ModelSearchModalProps) {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');

  const { data: allModels = [], isLoading, error } = useGetModelsQuery();

  const filteredModels = useMemo(() => {
    if (!searchTerm) return allModels;
    return allModels.filter(
      (model: GptModel) =>
        model.display_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        model.model_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        model.category?.toLowerCase().includes(searchTerm.toLowerCase()),
    );
  }, [allModels, searchTerm]);

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  const handleSelectModel = (model: GptModel) => {
    if (
      model &&
      typeof model.model_name === 'string' &&
      model.model_name.trim() !== ''
    ) {
      console.log(
        `Selected model from modal: ${model.display_name} (${model.model_name}). Navigating...`,
      );
      router.push(`/chat/new?model=${encodeURIComponent(model.model_name)}`);
      onClose(); // Close modal on selection
    } else {
      console.error(
        `Cannot navigate: Model "${model?.display_name}" has missing or invalid model_name:`,
        model?.model_name,
      );
      // Optionally show a toast error here
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{ sx: { borderRadius: 3 } }}
    >
      <DialogTitle sx={{ m: 0, p: 2, pb: 1 }}>
        <Typography variant="h6" component="div" sx={{ fontWeight: 'bold' }}>
          Search Models
        </Typography>
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: (theme) => theme.palette.grey[500],
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent dividers sx={{ p: 2 }}>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="Search all models..."
          value={searchTerm}
          onChange={handleSearchChange}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon color="action" />
              </InputAdornment>
            ),
            sx: { borderRadius: '25px', mb: 2 }, // Rounded input
          }}
          autoFocus // Focus input when modal opens
        />
        <Box sx={{ maxHeight: '60vh', overflowY: 'auto' }}>
          {' '}
          {/* Limit height and enable scroll */}
          {isLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', my: 3 }}>
              <CircularProgress />
            </Box>
          ) : error ? (
            <Alert severity="error" sx={{ mt: 2 }}>
              Failed to load models.
            </Alert>
          ) : (
            <List dense>
              {filteredModels.length > 0 ? (
                filteredModels.map((model: GptModel) => {
                  const modelKey = Object.keys(modelInfo).find(
                    (k) => normalize(k) === normalize(model.model_name),
                  ) as keyof typeof modelInfo | undefined; // Find the key and cast type

                  // Safely access the svg using the typed key
                  const modelIconSvg = modelKey
                    ? modelInfo[modelKey]?.svg
                    : null;
                  const modelIcon = modelIconSvg ? (
                    React.cloneElement(modelIconSvg, {
                      style: { width: 24, height: 24 },
                    })
                  ) : (
                    <ChatBubbleOutlineIcon
                      sx={{ fontSize: 24, color: 'text.secondary' }}
                    />
                  );

                  return (
                    <ListItem key={model.model_name} disablePadding>
                      <ListItemButton
                        onClick={() => handleSelectModel(model)}
                        sx={{ borderRadius: 2, mb: 0.5 }}
                      >
                        <ListItemIcon sx={{ minWidth: 'auto', mr: 1.5 }}>
                          {modelIcon}
                        </ListItemIcon>
                        <ListItemText
                          primary={model.display_name}
                          secondary={model.category || 'General'} // Show category or default
                          primaryTypographyProps={{ fontWeight: 'medium' }}
                          secondaryTypographyProps={{ fontSize: '0.75rem' }}
                        />
                      </ListItemButton>
                    </ListItem>
                  );
                })
              ) : (
                <Typography
                  variant="body2"
                  align="center"
                  sx={{ color: 'text.secondary', mt: 2 }}
                >
                  No models found matching "{searchTerm}".
                </Typography>
              )}
            </List>
          )}
        </Box>
      </DialogContent>
    </Dialog>
  );
}
