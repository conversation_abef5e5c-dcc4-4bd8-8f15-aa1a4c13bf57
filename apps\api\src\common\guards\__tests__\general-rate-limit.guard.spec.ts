import { GeneralRateLimitGuard } from '../general-rate-limit.guard';
import { ExecutionContext, HttpException, HttpStatus } from '@nestjs/common';

// Simple in-memory fake Redis implementation for testing
class FakeRedis {
  private store: Map<string, number> = new Map();
  private expiries: Map<string, number> = new Map();

  async get(key: string): Promise<string | null> {
    return this.store.has(key) ? this.store.get(key)!.toString() : null;
  }

  async incr(key: string): Promise<number> {
    const value = (this.store.get(key) || 0) + 1;
    this.store.set(key, value);
    return value;
  }

  async expire(key: string, ttlSeconds: number): Promise<number> {
    // Save expiry timestamp (ms) but we don't simulate passage of time in this test
    this.expiries.set(key, Date.now() + ttlSeconds * 1000);
    return 1;
  }

  async ttl(key: string): Promise<number> {
    return 60; // Fixed TTL for test simplicity
  }
}

describe('GeneralRateLimitGuard', () => {
  const fakeRedis = new FakeRedis() as any;
  const guard = new GeneralRateLimitGuard(fakeRedis);

  const makeContext = (): ExecutionContext => {
    const request: any = {
      originalUrl: '/general/model_list',
      user: { userId: 'test-user' },
      ip: '127.0.0.1',
    };

    const responseHeaders: Record<string, any> = {};
    const response: any = {
      setHeader: (name: string, value: string | number) => {
        responseHeaders[name] = value;
      },
    };

    return {
      switchToHttp: () => ({
        getRequest: () => request,
        getResponse: () => response,
      }),
    } as unknown as ExecutionContext;
  };

  it('allows first 60 requests and blocks the 61st', async () => {
    const ctx = makeContext();
    // First 60 should succeed
    for (let i = 0; i < 60; i++) {
      await expect(guard.canActivate(ctx)).resolves.toBe(true);
    }

    // 61st should throw
    await expect(guard.canActivate(ctx)).rejects.toThrow(HttpException);

    try {
      await guard.canActivate(ctx);
    } catch (e) {
      if (e instanceof HttpException) {
        expect(e.getStatus()).toBe(HttpStatus.TOO_MANY_REQUESTS);
      } else {
        throw e;
      }
    }
  });
});
