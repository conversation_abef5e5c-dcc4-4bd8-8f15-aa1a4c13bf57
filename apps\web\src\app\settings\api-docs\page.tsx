'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Typography from '@mui/material/Typography';
import Link from '@mui/material/Link'; // Using MUI Link for consistency
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
import Alert from '@mui/material/Alert';
import Paper from '@mui/material/Paper';
import IconButton from '@mui/material/IconButton';
import Snackbar from '@mui/material/Snackbar';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import { useSession, getSession } from 'next-auth/react';
import Container from '@mui/material/Container';
import { event } from '@/components/genai/GoogleAnalytics';
import { useMediaQuery, Toolbar, useTheme } from '@mui/material';

interface ApiKeyResponse {
  apiKey: string;
}

interface ModelVersionInfo {
  modelName: string;
  apiVersions: string[];
  provider: string;
  modelType?: string;
}

const ApiDocsPage: React.FC = () => {
  const theme = useTheme(); // Access theme
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const basePath = process.env.NEXT_PUBLIC_BASE_PATH || '';
  const { data: session, status } = useSession();

  // API Key Management State
  const [apiKey, setApiKey] = useState<string | null>(null);
  const [generatingKey, setGeneratingKey] = useState(false);
  const [keyError, setKeyError] = useState<string | null>(null);
  const [showCopiedSnackbar, setShowCopiedSnackbar] = useState(false);

  // Models Data State
  const [modelsData, setModelsData] = useState<ModelVersionInfo[]>([]);
  const [loadingModels, setLoadingModels] = useState(true);
  const [errorModels, setErrorModels] = useState<string | null>(null);

  // Placeholder for checking if user is authorized for API service
  const isUserApiAuthorized = session?.user?.rest === true; // Matching old project's session check

  const fetchModelsAndVersions = useCallback(async () => {
    setLoadingModels(true);
    setErrorModels(null);
    try {
      const session = await getSession();
      if (!session?.accessToken) {
        throw new Error('Not authenticated. Please log in again.');
      }

      const apiBaseUrl =
        process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3003/api/v0';
      const response = await fetch(`${apiBaseUrl}/rest/llm/models-versions`, {
        headers: {
          Authorization: `Bearer ${session.accessToken}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message ||
            `Failed to fetch models and versions: ${response.statusText}`,
        );
      }

      const data: ModelVersionInfo[] = await response.json();
      setModelsData(data);
    } catch (err) {
      setErrorModels(err instanceof Error ? err.message : String(err));
      setModelsData([]); // Clear data on error
    } finally {
      setLoadingModels(false);
    }
  }, []);

  const handleGenerateApiKey = async () => {
    setGeneratingKey(true);
    setKeyError(null);
    setApiKey(null);
    try {
      const session = await getSession();
      if (!session?.accessToken) {
        throw new Error('Not authenticated. Please log in again.');
      }

      const apiBaseUrl =
        process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3003/api/v0';
      const response = await fetch(`${apiBaseUrl}/rest/auth/api-key`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${session.accessToken}`,
        },
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message ||
            `Failed to generate API key: ${response.statusText}`,
        );
      }
      const data: ApiKeyResponse = await response.json();
      setApiKey(data.apiKey);
      event({
        action: 'click',
        category: 'api_docs',
        label: 'generate_api_key',
        value: 1,
      });
    } catch (err) {
      setKeyError(err instanceof Error ? err.message : String(err));
    } finally {
      setGeneratingKey(false);
    }
  };

  const handleCopyToClipboard = () => {
    if (apiKey) {
      navigator.clipboard.writeText(apiKey);
      setShowCopiedSnackbar(true);
      event({
        action: 'click',
        category: 'api_docs',
        label: 'copy_api_key',
        value: 1,
      });
    }
  };

  useEffect(() => {
    if (status === 'authenticated') {
      fetchModelsAndVersions();
    }
  }, [status, fetchModelsAndVersions]);

  // Group models by provider
  const groupedModels = modelsData.reduce(
    (acc, model) => {
      if (!acc[model.provider]) {
        acc[model.provider] = new Set<string>();
      }
      model.apiVersions.forEach((version) => acc[model.provider].add(version));
      return acc;
    },
    {} as Record<string, Set<string>>,
  );

  // Map provider names to display names
  const providerDisplayNames: Record<string, string> = {
    azure: 'GPT',
    anthropic: 'Claude',
    'gcp-vertexai-gemini': 'Gemini',
    'deepseek-aisvc': 'Deepseek',
    qwen: 'Qwen',
    'gcp-vertexai-llama': 'Llama',
  };

  return (
    <Box
      sx={{
        height: '100svh',
        display: 'flex',
        flexDirection: 'column',
        p: 0,
      }}
    >
      {isMobile ? (
        <Toolbar
          variant="dense"
          disableGutters
          sx={{
            justifyContent: 'space-between',
            position: 'relative',
            my: 0.5,
          }}
        >
          <Box sx={{ width: 34, display: { xs: undefined, md: 'none' } }} />
          <Typography
            variant="h6"
            noWrap
            component="div"
            sx={{
              flex: 1,
              textAlign: 'center',
              fontWeight: 'medium',
              opacity: 0.7,
              cursor: 'pointer',
              fontSize: '1rem',
            }}
          >
          API Service
          </Typography>
          <Box sx={{ width: 34, display: { xs: undefined, md: 'none' } }} />
        </Toolbar>
      ) : (
        <Box sx={{ mt: 3, mb: { xs: 2, md: 4 }, maxWidth: '72rem', width:'100%', alignSelf: 'center', px: 4  }}>
          <Typography
            variant="h4"
            component="h1"
            gutterBottom
            fontWeight={{ xs: 600, md: 400 }}
            fontSize={{ xs: 25, md: '2.125rem' }}
            textAlign={{ xs: 'center', md: 'start' }}
          >
          API Service
          </Typography>
        </Box>
      )}
      <Box display={'flex'} flexDirection={'column'} flex={1} overflow={'auto'} pb={2}>
        <Box className="text-sm max-w-6xl w-full self-center px-[32px]" >
          <Typography variant="body2" paragraph>
            This section contains information about the REST API endpoint for the
            HKBU GenAI Platform API Service. To make use of the service, you will
            require your key and endpoint.{' '}
            <Typography component="span" fontWeight="fontWeightSemibold">
              The key is a unique identifier and provides access to your account,
              please DO NOT share it with others.{' '}
            </Typography>
            The API usage will be monitored, and the API will share the same monthly
            quota as the HKBU GenAI Platform Service.
            <Typography component="span" fontWeight="fontWeightSemibold">
              {' '}
              Rate limit has also imposed to optimize the resource usage.
            </Typography>
          </Typography>

          {/* API Key Management Section */}
          <Paper sx={{ mb: 3, p: 2 }}>
            <Typography variant="h6" gutterBottom>
              API Key Management
            </Typography>
            {apiKey && (
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                  mb: 1,
                  p: 1,
                  border: '1px solid grey',
                  borderRadius: 1,
                  background: '#f5f5f5',
                }}
              >
                <Typography
                  variant="body2"
                  sx={{ flexGrow: 1, fontFamily: 'monospace', overflowX: 'auto' }}
                >
                  {apiKey}
                </Typography>
                <IconButton onClick={handleCopyToClipboard} size="small">
                  <ContentCopyIcon />
                </IconButton>
              </Box>
            )}
            <Button
              variant="contained"
              onClick={handleGenerateApiKey}
              disabled={generatingKey}
              sx={{ mb: 1 }}
            >
              {generatingKey ? (
                <CircularProgress size={24} />
              ) : apiKey ? (
                'Regenerate API Key'
              ) : (
                'Generate API Key'
              )}
            </Button>
            {keyError && (
              <Alert severity="error" sx={{ mt: 2 }}>
                {keyError}
              </Alert>
            )}
            {!apiKey && !generatingKey && (
              <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                Click to generate your unique API key. The key will only be
                displayed once.
              </Typography>
            )}
            {apiKey && (
              <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                Your new API key is displayed above. Please save it securely.
                Regenerating will invalidate the old key.
              </Typography>
            )}
          </Paper>
          <Typography variant="body2" paragraph>
            You can consult the following supported versions of swagger
            specification for more guidance.
          </Typography>
          {loadingModels && (
            <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>
              <CircularProgress size={24} />
            </Box>
          )}
          {errorModels && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {errorModels}
            </Alert>
          )}
          {!loadingModels && !errorModels && modelsData.length === 0 && (
            <Typography>No models or API versions currently available.</Typography>
          )}
          {!loadingModels && !errorModels && modelsData.length > 0 && (
            <Box className="border border-gray-200 dark:border-gray-700 rounded-md overflow-hidden mb-3">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700 text-xs">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Model
                    </th>
                    <th className="px-6 py-3 text-left font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Specification
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {Object.entries(groupedModels)
                    .filter(([provider]) => providerDisplayNames[provider]) // Only show providers with display names
                    .map(([provider, versions]) => {
                    const displayName = providerDisplayNames[provider];
                    const sortedVersions = Array.from(versions).sort();

                    // Map provider to swagger path
                    const swaggerPathMap: Record<string, string> = {
                      azure: 'gpt-swagger',
                      anthropic: 'claude-swagger',
                      'gcp-vertexai-gemini': 'gemini-swagger',
                      'deepseek-aisvc': 'deepseek-swagger',
                      qwen: 'qwen-swagger',
                      'gcp-vertexai-llama': 'llama-swagger',
                    };

                    return (
                      <tr key={provider}>
                        <td className="px-6 py-4 whitespace-nowrap dark:text-white">
                          {displayName}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {sortedVersions.map((version, index) => {
                            // Use the provider's swagger path consistently with fullscreen mode
                            const href = `${basePath}/general/specification/${swaggerPathMap[provider] || provider.toLowerCase()}?fullscreen=true`;

                            return (
                              <React.Fragment key={version}>
                                <Link
                                  href={href}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  underline="hover"
                                  onClick={() => {
                                    event({
                                      action: 'click',
                                      category: 'api_docs',
                                      label: `view_swagger_${provider}_${version}`,
                                      value: 1,
                                    });
                                  }}
                                >
                                  {version === 'default' ? 'Latest' : version}
                                </Link>
                                {index < sortedVersions.length - 1 && <br />}
                              </React.Fragment>
                            );
                          })}
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </Box>
          )}
          <Typography variant="body2" paragraph>
            To obtain your own key, click on the{' '}
            <Typography component="span" fontWeight="fontWeightBold">
              Generate API Key
            </Typography>{' '}
            button above. Keep in mind that the new key will only be displayed once,
            and the previous key will become invalid if it exists.
          </Typography>

          <Snackbar
            open={showCopiedSnackbar}
            autoHideDuration={2000}
            onClose={() => setShowCopiedSnackbar(false)}
            message="API Key copied to clipboard!"
          />
        </Box>
      </Box>
    </Box>
  );
};

export default ApiDocsPage;