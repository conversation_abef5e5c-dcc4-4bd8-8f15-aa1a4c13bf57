'use client';

import React from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import {
  Box,
  Typography,
  Paper,
  Button,
  Breadcrumbs,
  Link,
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';

const ModelProviderSpecificationPage = () => {
  const params = useParams();
  const router = useRouter();
  const modelProvider = params.model_provider as string;

  const handleBackClick = () => {
    router.push('/settings/api-service');
  };

  // Decode URL parameter for display
  const decodedProvider = decodeURIComponent(modelProvider);

  return (
    <Box sx={{ p: 3 }}>
      {/* Breadcrumb Navigation */}
      <Breadcrumbs
        separator={<NavigateNextIcon fontSize="small" />}
        aria-label="breadcrumb"
        sx={{ mb: 3 }}
      >
        <Link
          underline="hover"
          color="inherit"
          href="/settings"
          onClick={(e) => {
            e.preventDefault();
            router.push('/settings');
          }}
        >
          Settings
        </Link>
        <Link
          underline="hover"
          color="inherit"
          href="/settings/api-service"
          onClick={(e) => {
            e.preventDefault();
            router.push('/settings/api-service');
          }}
        >
          API Service
        </Link>
        <Typography color="text.primary">
          {decodedProvider} Specification
        </Typography>
      </Breadcrumbs>

      {/* Back Button */}
      <Button
        variant="outlined"
        startIcon={<ArrowBackIcon />}
        onClick={handleBackClick}
        sx={{ mb: 3 }}
      >
        Back to API Service
      </Button>

      {/* Page Header */}
      <Typography variant="h4" gutterBottom>
        {decodedProvider} API Specification
      </Typography>

      {/* Content Paper */}
      <Paper sx={{ p: 3, mt: 2 }}>
        <Typography variant="h6" gutterBottom>
          Model Provider: {decodedProvider}
        </Typography>
        <Typography variant="body1" color="text.secondary">
          This page will contain the detailed API specification for the{' '}
          {decodedProvider} model provider. The content will be implemented
          later.
        </Typography>

        {/* Placeholder content */}
        <Box sx={{ mt: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
          <Typography variant="body2" color="text.secondary">
            Coming soon: API documentation, endpoints, request/response
            examples, and integration guidelines for {decodedProvider}.
          </Typography>
        </Box>
      </Paper>
    </Box>
  );
};

export default ModelProviderSpecificationPage;
