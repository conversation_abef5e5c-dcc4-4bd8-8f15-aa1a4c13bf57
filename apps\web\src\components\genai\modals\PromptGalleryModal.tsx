'use client';

import {
  Drawer,
  SwipeableDrawer,
  useTheme,
  useMediaQuery,
  Modal,
  Box,
  Typography,
  TextField,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  Tabs,
  Tab,
  IconButton,
  InputAdornment,
} from '@mui/material';
import { useState, useEffect } from 'react';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import {
  useGetPromptGalleryQuery,
  useCreatePromptGalleryMutation,
  useUpdatePromptGalleryMutation,
  useDeletePromptGalleryMutation,
  useGetTasksQuery,
} from '@/lib/store/apiSlice';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation'; // Import useRouter
import CloseIcon from '@mui/icons-material/Close';
import SearchIcon from '@mui/icons-material/Search';

const style = {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: '80%',
  maxWidth: '1200px',
  height: '80vh',
  bgcolor: 'background.paper',
  boxShadow: 24,
  p: 0,
  display: 'flex',
  flexDirection: 'column',
  borderRadius: '16px', // Apply border-radius to the modal itself
  overflow: 'hidden', // Ensure content respects the border-radius
};

interface PromptGalleryModalProps {
  open: boolean;
  handleClose: () => void;
  handlePromptSelect: (prompt: any) => void;
  isExistingChat: boolean; // New prop to indicate if it's an existing chat page
}

export default function PromptGalleryModal({
  open,
  handleClose,
  handlePromptSelect,
  isExistingChat, // Destructure new prop
}: PromptGalleryModalProps) {
  const { data: prompts, isLoading, isError } = useGetPromptGalleryQuery();
  const { data: tasks } = useGetTasksQuery();
  const [createPrompt] = useCreatePromptGalleryMutation();
  const [updatePrompt] = useUpdatePromptGalleryMutation();
  const [deletePrompt] = useDeletePromptGalleryMutation();
  const { data: session } = useSession();

  const [tab, setTab] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  const [currentPrompt, setCurrentPrompt] = useState<any>(null);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const router = useRouter(); // Initialize useRouter
  const LAST_USED_MODEL_LS_KEY = 'chatApp_lastUsedModelName'; // Define LS key

  const handleSelectAndClose = (prompt: any) => {
    if (isExistingChat || window.location.pathname.includes('/chat/new')) {
      // If inside chat/{chat_id} page or on a new chat page, populate input area
      handlePromptSelect(prompt.prompt_content);
      handleClose();
    } else {
      // If outside chat page, open chat/new and populate
      const recentUseModel = localStorage.getItem(LAST_USED_MODEL_LS_KEY) || '';
      // Use sessionStorage to pass data to avoid URL length limits
      sessionStorage.setItem('promptContent', prompt.prompt_content);
      sessionStorage.setItem('systemInstruction', prompt.system_instruction);
      let newChatUrl = '/chat/new';
      if (recentUseModel) {
        newChatUrl += `?model=${recentUseModel}`;
      }
      router.push(newChatUrl);
      handleClose();
    }
  };

  const filteredPrompts = (promptsToFilter: any[]) =>
    promptsToFilter?.filter(
      (p) =>
        p.prompt_content.toLowerCase().includes(searchTerm.toLowerCase()) ||
        p.system_instruction.toLowerCase().includes(searchTerm.toLowerCase()),
    );

  const defaultPrompts = prompts?.filter((p: any) => p.is_default);
  const userPrompts = prompts?.filter(
    (p: any) => !p.is_default && p.user_id === session?.user?.name,
  );

  const galleryContent = (
    <Box
      sx={
        isMobile
          ? {
              height: '80vh',
              display: 'flex',
              flexDirection: 'column',
              overflow: 'hidden',
            }
          : style
      }
    >
      {/* Header with Search and Tabs */}
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          p: { xs: 2, sm: 3 },
          borderBottom: 1,
          borderColor: 'divider',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 2,
          }}
        >
          <TextField
            fullWidth
            placeholder="Search prompts..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
              disableUnderline: true, // Remove underline
              sx: {
                borderRadius: '12px', // Apply border-radius to the search input
                '&.MuiInputBase-root': {
                  backgroundColor: 'transparent', // Ensure no background
                },
                '& .MuiOutlinedInput-notchedOutline': {
                  border: 'none', // Remove border
                },
              },
            }}
            variant="standard" // Use standard variant for borderless
          />
        </Box>

        <Box
          sx={{
            display: 'flex',
            flexDirection: isMobile ? 'column' : 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            mt: 2,
            gap: isMobile ? 2 : 0,
          }}
        >
          {/* Tabs for Default and Your Prompts */}
          {!isEditing && (
            <Tabs
              value={tab}
              onChange={(e, newValue) => setTab(newValue)}
              sx={{
                order: isMobile ? 2 : 1,
                bgcolor: 'action.hover', // Background for the segmented control
                borderRadius: '12px', // Rounded corners for the entire tab container
                p: 0.5, // Padding inside the container
                minHeight: 'unset', // Allow height to shrink
                '& .MuiTabs-indicator': {
                  display: 'none', // Hide the default indicator
                },
                '& .MuiTab-root': {
                  minHeight: 'unset', // Allow height to shrink
                  py: 1, // Vertical padding for tab content
                  borderRadius: '10px', // Slightly smaller border-radius for individual tabs
                  textTransform: 'none', // Prevent uppercase
                  fontWeight: 'bold',
                  color: 'text.secondary', // Default text color
                  '&.Mui-selected': {
                    bgcolor: 'background.paper', // Background for selected tab
                    color: 'text.primary', // Text color for selected tab
                    boxShadow: 1, // Subtle shadow for selected tab
                  },
                },
              }}
            >
              <Tab label="Default Prompts" />
              <Tab label="Your Prompts" />
            </Tabs>
          )}
          {!isEditing && (
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              sx={{
                order: isMobile ? 1 : 2,
                textTransform: 'none',
                borderRadius: '12px',
                bgcolor: 'primary.main',
                '&:hover': {
                  bgcolor: 'primary.dark',
                },
              }}
              onClick={() => {
                setCurrentPrompt(null);
                setIsEditing(true);
              }}
            >
              Create Prompt
            </Button>
          )}
        </Box>
      </Box>

      {/* Main Content Area */}
      <Box sx={{ flexGrow: 1, p: { xs: 2, sm: 3 }, overflowY: 'auto' }}>
        {isEditing ? (
          <Box>
            <TextField
              label="System Instruction"
              fullWidth
              multiline
              rows={4}
              defaultValue={currentPrompt?.system_instruction || ''}
              onChange={(e) =>
                setCurrentPrompt({
                  ...currentPrompt,
                  system_instruction: e.target.value,
                })
              }
              sx={{ mb: 2 }}
            />
            <TextField
              label="Prompt Content"
              fullWidth
              multiline
              rows={4}
              defaultValue={currentPrompt?.prompt_content || ''}
              onChange={(e) =>
                setCurrentPrompt({
                  ...currentPrompt,
                  prompt_content: e.target.value,
                })
              }
              sx={{ mb: 2 }}
            />
            <Button
              variant="contained"
              onClick={() => {
                if (currentPrompt?.id) {
                  updatePrompt(currentPrompt);
                } else {
                  createPrompt(currentPrompt);
                }
                setIsEditing(false);
              }}
            >
              Save
            </Button>
            <Button onClick={() => setIsEditing(false)}>Cancel</Button>
          </Box>
        ) : (
          <Grid container spacing={3}>
            {(tab === 0
              ? filteredPrompts(defaultPrompts ?? [])
              : filteredPrompts(userPrompts ?? [])
            ).map((prompt: any) => (
              <Grid size={{ xs: 12, sm: 6, md: 3 }} key={prompt.id}>
                {' '}
                {/* Use size prop for Grid v2 */}
                <Card
                  sx={{
                    cursor: 'pointer',
                    height: '100%',
                    borderRadius: '12px',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'space-between',
                    width: '100%', // Ensure card takes full width of the grid item
                  }}
                >
                  <CardContent onClick={() => handleSelectAndClose(prompt)}>
                    <Typography
                      variant="h6"
                      color="text.secondary"
                      sx={{
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        display: '-webkit-box',
                        WebkitLineClamp: '2',
                        WebkitBoxOrient: 'vertical',
                      }}
                    >
                      {prompt.system_instruction}
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        display: '-webkit-box',
                        WebkitLineClamp: '5',
                        WebkitBoxOrient: 'vertical',
                      }}
                    >
                      {prompt.prompt_content}
                    </Typography>
                  </CardContent>
                  {tab === 1 && (
                    <CardActions sx={{ justifyContent: 'flex-end' }}>
                      <IconButton
                        onClick={(e) => {
                          e.stopPropagation();
                          setCurrentPrompt(prompt);
                          setIsEditing(true);
                        }}
                      >
                        <EditIcon />
                      </IconButton>
                      <IconButton
                        onClick={(e) => {
                          e.stopPropagation();
                          if (
                            window.confirm(
                              'Are you sure you want to delete this prompt?',
                            )
                          ) {
                            deletePrompt(prompt.id);
                          }
                        }}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </CardActions>
                  )}
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
      </Box>
    </Box>
  );

  if (isMobile) {
    return (
      <SwipeableDrawer
        anchor="bottom"
        open={open}
        onClose={handleClose}
        onOpen={() => {}} // Required prop for SwipeableDrawer
        PaperProps={{
          sx: {
            borderTopLeftRadius: 16, // Apply border-radius to top-left corner
            borderTopRightRadius: 16, // Apply border-radius to top-right corner
            backdropFilter: 'blur(10px)', // Add blur effect
            backgroundColor: 'background.paper', // Ensure solid background
          },
        }}
        disableSwipeToOpen={false}
        ModalProps={{
          keepMounted: true,
        }}
      >
        <Box
          onClick={handleClose} // Add onClick to close the drawer
          sx={{
            display: 'flex',
            justifyContent: 'center',
            pt: 1, // Padding top for the handle
            pb: 0.5, // Padding bottom for the handle
            cursor: 'pointer', // Indicate it's clickable
          }}
        >
          <Box
            sx={{
              width: 40,
              height: 4,
              borderRadius: 2,
              bgcolor: 'text.secondary', // Color of the handle
            }}
          />
        </Box>
        {galleryContent}
      </SwipeableDrawer>
    );
  }

  return (
    <Modal
      open={open}
      onClose={handleClose}
      disableRestoreFocus // Add this prop
      BackdropProps={{
        sx: {
          backdropFilter: 'blur(10px)',
        },
      }}
    >
      {galleryContent}
    </Modal>
  );
}
