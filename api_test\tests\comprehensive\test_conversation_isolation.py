#!/usr/bin/env python3
"""
Test script to verify that REST API calls don't create conversations.
This will make multiple API calls and then you can check the WebUI to ensure
no new conversations were created.
"""

import json
import os
import sys
import time
import requests
from dotenv import load_dotenv
from typing import Dict, Any
from datetime import datetime


def load_config() -> Dict[str, str]:
    """Load configuration from .env file."""
    load_dotenv()
    
    api_key = os.getenv("API_KEY")
    base_url = os.getenv("BASE_URL")
    
    if not api_key:
        raise ValueError("API_KEY not found in .env file")
    if not base_url:
        raise ValueError("BASE_URL not found in .env file")
    
    return {
        "api_key": api_key,
        "base_url": base_url
    }


def make_api_call(config: Dict[str, str], message: str, test_number: int) -> bool:
    """Make a single API call and return success status."""
    
    url = f"{config['base_url']}/rest/deployments/gpt-4.1/chat/completions"
    
    params = {
        "api-version": "2024-02-01"
    }
    
    headers = {
        "Content-Type": "application/json",
        "api-key": config["api_key"]
    }
    
    # Unique message with timestamp to identify if it appears in WebUI
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    unique_message = f"[API TEST {test_number}] {message} - {timestamp}"
    
    payload = {
        "messages": [
            {
                "role": "user",
                "content": unique_message
            }
        ],
        "temperature": 0.7,
        "stream": False
    }
    
    try:
        response = requests.post(
            url,
            params=params,
            headers=headers,
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            response_data = response.json()
            assistant_response = response_data.get("choices", [{}])[0].get("message", {}).get("content", "No response")
            print(f"✅ Test {test_number}: Success")
            print(f"   User: {unique_message}")
            print(f"   Assistant: {assistant_response[:100]}...")
            
            # Check that response does NOT contain conversation_uuid
            if "conversation_uuid" in response_data:
                print(f"   ⚠️  WARNING: Response contains conversation_uuid: {response_data['conversation_uuid']}")
                print("      This suggests conversations are being created!")
                return False
            else:
                print("   ✓ No conversation_uuid in response (expected)")
            
            return True
        else:
            print(f"❌ Test {test_number}: Failed with status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Test {test_number}: Error - {e}")
        return False


def main():
    """Run multiple API calls to test conversation isolation."""
    try:
        config = load_config()
        
        print("🔍 Testing Conversation Isolation for REST API")
        print("=" * 60)
        print("This test will make 5 API calls with unique messages.")
        print("After the test, check the WebUI to ensure NO new conversations were created.")
        print("=" * 60)
        print()
        
        test_messages = [
            "What is the capital of France?",
            "Tell me a joke about programming",
            "Explain quantum computing in simple terms",
            "What's the weather like today?",
            "How do I make a perfect cup of coffee?"
        ]
        
        success_count = 0
        
        for i, message in enumerate(test_messages, 1):
            if make_api_call(config, message, i):
                success_count += 1
            
            # Small delay between calls
            if i < len(test_messages):
                time.sleep(1)
            
            print()
        
        print("=" * 60)
        print(f"📊 Test Summary: {success_count}/{len(test_messages)} API calls successful")
        print()
        print("🔍 IMPORTANT: Now check the WebUI:")
        print("   1. Login to the WebUI at http://localhost:3000")
        print("   2. Check your conversation history")
        print("   3. Verify that NONE of these test messages appear")
        print("   4. If you see any messages starting with '[API TEST', there's a problem!")
        print()
        print("✅ If no test messages appear in WebUI, the isolation is working correctly!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()