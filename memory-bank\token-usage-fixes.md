# Token Usage Tracking Fixes - Memory Bank

## Overview
Fixed multiple issues with token usage tracking across different LLM providers that were causing duplicate rows, inconsistent model names, and missing token usage records in the rate limit table.

## Issues Fixed

### 1. Duplicate Rows in Token Usage Table
**Problem**: Same models appearing multiple times in the rate limit token usage table (e.g., GPT-4.1 showing twice with different usage)

**Root Cause**: `getAllMonthlyTokenUsage()` in `TokenUsageService` was grouping by exact model names instead of canonical names, so `gpt-4.1` and `chatgpt-4.1` were treated as separate models.

**Solution**: 
- Added `getCanonicalModelName()` method to `ModelMappingService` that returns the alphabetically first variant
- Updated `getAllMonthlyTokenUsage()` to use canonical model names for grouping, aggregating all variants together

**Files Modified**:
- `apps/api/src/common/services/model-mapping.service.ts` - Added `getCanonicalModelName()` method
- `apps/api/src/common/services/token-usage.service.ts` - Updated aggregation logic

### 2. Inconsistent Model Name Recording
**Problem**: Token usage records were inconsistently using `deployment_name` vs `model_name`, creating separate rows for the same logical model.

**Root Cause**: Code prioritized `deployment_name` over `model_name` in token usage recording:
```typescript
// Old (causes duplicates):
validModel.deployment_name ?? validModel.model_name ?? requestedModel
```

**Solution**: Changed priority order to prefer `model_name` for consistency:
```typescript  
// New (consistent):
validModel.model_name ?? validModel.deployment_name ?? requestedModel
```

**Files Modified**:
- `apps/api/src/general/chat/chat-completion/chat-completion.service.ts` - Fixed 7 locations

### 3. Missing Token Usage for DeepSeek Models
**Problem**: DeepSeek r1 and v3 streaming requests weren't updating token usage in the database.

**Root Cause**: `processDirectStream()` method didn't track token usage, unlike other streaming providers.

**Solution**: Enhanced `processDirectStream()` to:
- Accept additional parameters for token tracking
- Accumulate content and extract token usage from streams
- Call `tokenUsageService.updateTokenUsage()` on stream completion

**Files Modified**:
- `apps/api/src/general/chat/chat-completion/chat-completion.service.ts` - Enhanced `processDirectStream()` method

### 4. Missing Token Usage for Vertex Models
**Problem**: Vertex Gemini and Llama models weren't updating token usage despite having the tracking code.

**Root Cause**: `requestedModelName` parameter wasn't being passed in the options, causing the condition `if (user?.userId && requestedModelName)` to fail.

**Solution**: Added `requestedModelName: requestedModel` to all Vertex service options.

**Files Modified**:
- `apps/api/src/general/chat/chat-completion/chat-completion.service.ts` - Added missing parameter to 4 Vertex option objects

### 5. UI Terminology Update
**Problem**: Token usage table showed "prompt / completion" instead of preferred "input / output" terminology.

**Solution**: Updated display labels in the frontend component.

**Files Modified**:
- `apps/web/src/components/genai/settings/sections/RateLimitSection.tsx` - Updated label text

## Testing Verification
- ✅ Token usage properly tracked for all LLM providers (Azure OpenAI, Vertex Gemini, Vertex Llama, DeepSeek, Qwen)
- ✅ No duplicate rows in token usage table
- ✅ Consistent model names used across all records
- ✅ UI shows "input / output" terminology
- ✅ All existing data properly aggregated through canonical model name grouping

## Key Learnings
1. **Model Name Consistency**: Always use `model_name` as the primary identifier for token tracking
2. **Parameter Propagation**: Ensure all required parameters are passed through the entire call chain
3. **Stream Handling**: Different streaming implementations need consistent token usage tracking
4. **Aggregation Strategy**: Use canonical names to handle model variants consistently

### 6. Token Usage Record Duplication and Timezone Inconsistency
**Problem**: Token usage was creating duplicate records instead of updating existing daily records, and timezone inconsistency between conversation table and token usage table.

**Root Cause**: Multiple issues:
- Token usage calculation used custom Hong Kong timezone logic that didn't align with database timestamps
- Conversation `create_dt` used UTC time (Node.js `new Date()`) while `update_dt` used Hong Kong time (SQL Server `GETDATE()`)
- `token_date` was incorrectly showing previous day due to timezone calculation errors

**Solution**: Standardized all timestamps to Hong Kong timezone:
- Created shared `timezone.util.ts` with `getHongKongTime()` and `getHongKongDateOnly()` utilities
- Updated all Node.js database operations to use Hong Kong time consistently
- Fixed conversation-aware counting using actual conversation table queries
- Ensured `token_date` aligns with actual conversation creation dates

**Files Modified**:
- `apps/api/src/common/utils/timezone.util.ts` - New shared timezone utilities
- `apps/api/src/common/services/token-usage.service.ts` - Hong Kong timezone for all timestamps
- `apps/api/src/general/chat/chat-completion/chat-completion.service.ts` - Conversation creation with Hong Kong time
- `apps/api/src/auth/next-auth.controller.ts` - User details with Hong Kong time
- `apps/api/src/common/services/rate-limit.service.ts` - Rate limit logging with Hong Kong time

**Key Technical Changes**:
- **Before**: Mixed UTC (Node.js) and Hong Kong time (SQL Server)
- **After**: Consistent Hong Kong timezone across all database operations
- **Conversation Counting**: Uses database queries with `distinct` to count unique conversations per day
- **Daily Grouping**: All records now use Hong Kong date for proper daily aggregation

## Deployment Notes
- No database schema changes required
- Existing duplicate data will be automatically aggregated
- New records will use consistent model names and Hong Kong timezone
- All changes are backward compatible
- SQL Server stored procedures continue using `GETDATE()` (Hong Kong time) as before