import {
  Injectable,
  CanActivate,
  ExecutionContext,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { InjectRedis } from '@nestjs-modules/ioredis';
import { Redis } from 'ioredis';
import { AuthenticatedUser } from '../../auth/user.interface';

@Injectable()
export class ModelRateLimitGuard implements CanActivate {
  private readonly logger = new Logger(ModelRateLimitGuard.name);
  private readonly RATE_LIMIT_PER_MODEL = 60; // 60 requests per minute
  private readonly TTL_SECONDS = 60; // 1 minute

  constructor(@InjectRedis() private readonly redis: Redis) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user: AuthenticatedUser = request.user;

    if (!user || !user.userId) {
      throw new HttpException('Unauthorized', HttpStatus.UNAUTHORIZED);
    }

    // Extract model name from different possible sources
    const modelName = this.extractModelName(request);

    if (!modelName) {
      // If no model name found, allow the request (non-model endpoint)
      return true;
    }

    const key = `rate_limit:${modelName}:${user.userId}`;

    try {
      // First, get current count without incrementing
      const currentCount = await this.redis.get(key);
      const count = currentCount ? parseInt(currentCount, 10) : 0;

      // Get TTL for calculating reset time
      const ttl = await this.redis.ttl(key);

      // Check if limit would be exceeded before incrementing
      if (count >= this.RATE_LIMIT_PER_MODEL) {
        const resetAt = new Date(
          Date.now() + (ttl > 0 ? ttl * 1000 : this.TTL_SECONDS * 1000),
        );

        this.logger.warn(
          `Rate limit exceeded for user ${user.userId} on model ${modelName}. Current count: ${count}`,
        );

        throw new HttpException(
          {
            statusCode: HttpStatus.TOO_MANY_REQUESTS,
            message: `Rate limit exceeded for model ${modelName}`,
            error: 'Too Many Requests',
            retryAfter: ttl > 0 ? ttl : this.TTL_SECONDS,
            limit: this.RATE_LIMIT_PER_MODEL,
            remaining: 0,
            reset: resetAt.toISOString(),
          },
          HttpStatus.TOO_MANY_REQUESTS,
        );
      }

      // Only increment if within limit
      const newCount = await this.redis.incr(key);

      // Set TTL on first request
      if (newCount === 1) {
        await this.redis.expire(key, this.TTL_SECONDS);
      }

      // Get updated TTL after potential expiry set
      const updatedTtl = await this.redis.ttl(key);

      // Add rate limit headers to response
      const response = context.switchToHttp().getResponse();

      // Handle both Express and Fastify response objects
      const setHeader = (name: string, value: string | number) => {
        if (typeof response.header === 'function') {
          // Fastify
          response.header(name, value.toString());
        } else if (typeof response.setHeader === 'function') {
          // Express
          response.setHeader(name, value.toString());
        } else {
          this.logger.warn(
            'Unable to set response header - unknown response type',
          );
        }
      };

      setHeader('X-RateLimit-Limit', this.RATE_LIMIT_PER_MODEL);
      setHeader(
        'X-RateLimit-Remaining',
        Math.max(0, this.RATE_LIMIT_PER_MODEL - newCount),
      );
      setHeader(
        'X-RateLimit-Reset',
        new Date(
          Date.now() +
            (updatedTtl > 0 ? updatedTtl * 1000 : this.TTL_SECONDS * 1000),
        ).toISOString(),
      );

      return true;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Redis error in rate limiting: ${errorMessage}`);
      // In case of Redis error, allow the request
      return true;
    }
  }

  private extractModelName(request: any): string | null {
    // Check for model deployment name in params (REST API style)
    if (request.params?.modelDeploymentName) {
      return request.params.modelDeploymentName;
    }

    // Check for model in body (for web UI endpoints)
    if (request.body?.model) {
      return request.body.model;
    }

    // Check for deployment_name in body
    if (request.body?.deployment_name) {
      return request.body.deployment_name;
    }

    return null;
  }
}
