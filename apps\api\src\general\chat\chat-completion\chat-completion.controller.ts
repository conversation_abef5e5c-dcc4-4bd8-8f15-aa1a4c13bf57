import {
  Controller,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  Req,
  Logger,
  HttpException,
  HttpStatus,
  All,
  HttpCode,
} from '@nestjs/common';
import { ApiKeyAuthGuard } from '../../../auth/guards/api-key-auth.guard';
import { ModelRateLimitGuard } from '../../../common/guards/model-rate-limit.guard';
import { ChatCompletionService } from './chat-completion.service';
import { CreateChatCompletionDto } from './dto/create-chat-completion.dto'; // Import DTO from file
import {
  CreateChatCompletionRestDto,
  MessageContent,
} from './dto/create-chat-completion-rest.dto'; // Import REST-specific DTO
import { ChatCompletionResponseDto } from './dto/chat-completion-response.dto'; // Import response DTO
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiHeader,
  ApiBody,
} from '@nestjs/swagger';
import { ApiUserPayload } from '../../../auth/api-key.strategy';
import { AuthenticatedUser } from '../../../auth/user.interface';

// Interface for the request object, ensuring 'user' property from the guard
interface AuthenticatedRequestWithUser extends Request {
  // Express Request if not using Fastify
  user: AuthenticatedUser;
}

@ApiTags('LLM - Chat Completions (REST API)')
@Controller('rest/deployments/:modelDeploymentName/chat/completions')
@UseGuards(ApiKeyAuthGuard, ModelRateLimitGuard) // Protect all routes with auth and rate limiting
@ApiHeader({
  name: 'api-key',
  description: 'Your API Key for authentication.',
  required: true,
})
export class ChatCompletionsController {
  private readonly logger = new Logger(ChatCompletionsController.name);

  constructor(private readonly chatCompletionService: ChatCompletionService) {}

  @Post()
  @HttpCode(HttpStatus.OK) // Typically POST is 201, but OpenAI uses 200 for chat completions
  @ApiOperation({ summary: 'Creates a completion for the chat message.' })
  @ApiQuery({
    name: 'api-version',
    description: 'API Version (e.g., 2024-02-01) - optional for some models',
    required: false,
  })
  @ApiParam({
    name: 'modelDeploymentName',
    description:
      'The deployment name of the model (e.g., gpt-4, claude-3-opus)',
    required: true,
  })
  @ApiBody({
    type: CreateChatCompletionRestDto,
    description: 'Request body for chat completion.',
  })
  @ApiResponse({
    status: 200,
    description: 'Chat completion successful.',
    type: ChatCompletionResponseDto,
    content: {
      'application/json': {
        example: {
          id: 'chatcmpl-7QyqpwdfhqwajicIEznoc6Q47XAyW',
          object: 'chat.completion',
          created: 1677652288,
          model: 'gpt-4',
          conversation_uuid: 'a1b2c3d4-e5f6-7890-1234-567890abcdef',
          choices: [
            {
              index: 0,
              message: {
                role: 'assistant',
                content: 'Hello! I can help you write a Python function to calculate factorial. Here\'s a simple implementation:\n\n```python\ndef factorial(n):\n    if n == 0 or n == 1:\n        return 1\n    else:\n        return n * factorial(n - 1)\n```\n\nThis function uses recursion to calculate the factorial of a number. You can also implement it iteratively if you prefer!'
              },
              finish_reason: 'stop'
            }
          ],
          usage: {
            prompt_tokens: 25,
            completion_tokens: 85,
            total_tokens: 110
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request (e.g., invalid parameters).',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized (Invalid API Key).' })
  @ApiResponse({
    status: 403,
    description: 'Forbidden (User not authorized for this service/model).',
  })
  @ApiResponse({ status: 500, description: 'Internal server error.' })
  async createChatCompletion(
    @Query('api-version') apiVersion: string,
    @Param('modelDeploymentName') modelDeploymentName: string,
    @Body() incomingDto: CreateChatCompletionRestDto, // Use the REST-specific DTO
    @Req() req: AuthenticatedRequestWithUser,
  ) {
    this.logger.log(
      `Chat completion request for model [${modelDeploymentName}], API version [${apiVersion}] by user [${req.user.userId}]`,
    );

    // Validate API version - allow empty/undefined for models that don't require it
    // Only throw error if apiVersion is explicitly null (not undefined or empty string)
    if (apiVersion === null) {
      throw new HttpException(
        {
          error: {
            code: '404',
            message: 'Resource not found',
          },
        },
        HttpStatus.BAD_REQUEST,
      );
    }

    // Validate messages array
    if (!incomingDto.messages || incomingDto.messages.length === 0) {
      throw new HttpException(
        {
          error: {
            message: "[] is too short - 'messages'",
            type: 'invalid_request_error',
            param: null,
            code: null,
          },
        },
        HttpStatus.BAD_REQUEST,
      );
    }

    // Convert messages array to a single prompt string for the service
    // Check if any message contains vision content (array format)
    const hasVisionContent = incomingDto.messages.some(
      (msg) =>
        Array.isArray(msg.content) &&
        msg.content.some((item) => item.type === 'image_url'),
    );

    // Prepare the DTO for the service
    let serviceDto: CreateChatCompletionDto;

    if (hasVisionContent) {
      // For vision prompts, pass the full message structure
      this.logger.log(
        `Vision prompt detected for model ${modelDeploymentName}. Passing full message structure.`,
      );

      serviceDto = {
        messages: incomingDto.messages.map((msg) => ({
          role: msg.role,
          content: msg.content,
        })),
        model: modelDeploymentName,
        chat_session_id: incomingDto.chat_session_id,
        temperature: incomingDto.temperature,
        stream: incomingDto.stream,
        useGoogle: incomingDto.useGoogle,
        files: incomingDto.files,
        prompt: '', // Will be ignored when messages are provided
      };
    } else {
      // For text-only prompts, use the original logic
      let prompt = '';

      // Find the last user message as the primary prompt
      const userMessages = incomingDto.messages.filter(
        (msg) => msg.role === 'user',
      );

      if (userMessages.length > 0) {
        prompt = userMessages[userMessages.length - 1].content as string;
      } else {
        // If no user message, use the last message regardless of role
        prompt = incomingDto.messages[incomingDto.messages.length - 1]
          .content as string;
      }

      // Validate that we have a non-empty prompt
      if (!prompt || prompt.trim() === '') {
        throw new HttpException(
          {
            error: {
              message: 'No valid text content found in messages',
              type: 'invalid_request_error',
              param: null,
              code: null,
            },
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      serviceDto = {
        prompt: prompt,
        model: modelDeploymentName,
        chat_session_id: incomingDto.chat_session_id,
        temperature: incomingDto.temperature,
        stream: incomingDto.stream,
        useGoogle: incomingDto.useGoogle,
        files: incomingDto.files,
      };
    }

    // Service DTO is now prepared above based on content type

    try {
      // Call the REST API-specific service method that doesn't create conversations
      const result = await this.chatCompletionService.createRestApiCompletion(
        serviceDto,
        req.user,
      );
      return result;
    } catch (error) {
      let errorMessage =
        'An unknown error occurred while processing the chat completion request.';
      let errorStack: string | undefined = undefined; // Corrected type
      if (error instanceof Error) {
        errorMessage = error.message;
        errorStack = error.stack;
      }
      this.logger.error(
        `Error in createChatCompletion for ${modelDeploymentName} by ${req.user.userId}: ${errorMessage}`,
        errorStack,
      );
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        'Failed to process chat completion request.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
