import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { GoogleCustomSearch } from '@langchain/community/tools/google_custom_search';

// Define Source interface for search results
export interface Source {
  title: string;
  link: string;
  snippet: string;
}

// Result interface for search context with sources
export interface SearchResult {
  context: string;
  sources: Source[];
}

@Injectable()
export class SearchContextService {
  private readonly logger = new Logger(SearchContextService.name);
  private googleSearchTool: GoogleCustomSearch | null = null;

  constructor(private readonly configService: ConfigService) {
    // Initialize Google Search Tool
    const googleApiKey = this.configService.get<string>('GOOGLE_CSE_API_KEY');
    const googleCseId = this.configService.get<string>('GOOGLE_CSE_ID');

    if (googleApiKey && googleCseId) {
      this.googleSearchTool = new GoogleCustomSearch({
        apiKey: googleApi<PERSON>ey,
        googleCSEId: googleCseId,
      });
      this.logger.log(
        'Google Custom Search tool initialized in SearchContextService.',
      );
    } else {
      this.logger.warn(
        'Google CSE API Key or ID not found. Search functionality disabled.',
      );
    }
  }

  /**
   * Performs multiple searches using extracted keywords and formats results as context
   * Searches each keyword separately and takes top 3 results from each
   * @param keywords - Array of search keywords
   * @returns Object with formatted search context string and source objects
   */
  async createSearchContext(keywords: string[]): Promise<SearchResult> {
    if (!this.googleSearchTool) {
      this.logger.warn(
        '🚫 Google Search tool not available, returning empty context',
      );
      return { context: '', sources: [] };
    }

    // Limit to a maximum of 3 keywords
    const keywordsToSearch = keywords.slice(0, 3);

    if (keywordsToSearch.length === 0) {
      this.logger.warn('🚫 No keywords provided for search');
      return { context: '', sources: [] };
    }

    try {
      this.logger.log(`🔍 MULTI-QUERY GOOGLE SEARCH STARTED`);
      this.logger.log(`🏷️ Search Terms: [${keywordsToSearch.join(', ')}]`);
      this.logger.log(
        `📊 Will perform ${keywordsToSearch.length} separate searches, taking top 3 results from each`,
      );

      const allSources: Source[] = [];
      const seenUrls = new Set<string>(); // For deduplication

      // Search each keyword separately
      for (let i = 0; i < keywordsToSearch.length; i++) {
        const originalKeyword = keywordsToSearch[i];
        // Strip quotes from search terms to avoid double-quoting issues
        const cleanedKeyword = originalKeyword
          .replace(/^["']|["']$/g, '')
          .trim();
        this.logger.log(
          `🔎 Search ${i + 1}/${keywordsToSearch.length}: "${cleanedKeyword}" (original: "${originalKeyword}")`,
        );

        try {
          // Perform individual search with cleaned keyword
          const searchResults =
            await this.googleSearchTool.call(cleanedKeyword);
          this.logger.log(
            `📨 Search ${i + 1} received ${searchResults.length} characters`,
          );

          // Parse results for this keyword
          const keywordSources = this.parseGoogleSearchResults(searchResults);

          if (keywordSources.length === 0) {
            this.logger.warn(
              `⚠️ No results for search term: "${cleanedKeyword}"`,
            );
            continue;
          }

          // Take top 3 results from this search, avoiding duplicates
          let addedFromThisSearch = 0;
          for (const source of keywordSources) {
            if (addedFromThisSearch >= 3) break; // Limit to top 3 per search

            if (!seenUrls.has(source.link)) {
              allSources.push(source);
              seenUrls.add(source.link);
              addedFromThisSearch++;
              this.logger.log(
                `📌 Added result ${addedFromThisSearch} from search ${i + 1}: "${source.title}"`,
              );
            } else {
              this.logger.debug(`🔄 Skipped duplicate URL: ${source.link}`);
            }
          }

          this.logger.log(
            `✅ Search ${i + 1} contributed ${addedFromThisSearch} unique results`,
          );
        } catch (searchError) {
          const errorMessage =
            searchError instanceof Error
              ? searchError.message
              : String(searchError);
          this.logger.warn(
            `⚠️ Failed to search "${cleanedKeyword}": ${errorMessage}`,
          );
          continue; // Continue with other searches
        }
      }

      if (allSources.length === 0) {
        this.logger.warn(
          `⚠️ No search results found across all ${keywordsToSearch.length} search terms`,
        );
        return { context: '', sources: [] };
      }

      this.logger.log(
        `📄 COMBINED RESULTS: ${allSources.length} unique sources from ${keywordsToSearch.length} searches`,
      );
      allSources.forEach((source, index) => {
        this.logger.log(
          `📌 Final Result ${index + 1}: "${source.title}" - ${source.link}`,
        );
      });

      // Format combined search results as context
      const combinedQuery = keywordsToSearch.join(', ');
      const searchContext = this.formatSearchContext(allSources, combinedQuery);

      this.logger.log(
        `✅ MULTI-SEARCH CONTEXT CREATED: ${searchContext.length} characters`,
      );
      this.logger.log(
        `📋 Returning ${allSources.length} sources for frontend display`,
      );

      return {
        context: searchContext,
        sources: allSources,
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error during multi-search: ${errorMessage}`,
        errorStack,
      );
      return { context: '', sources: [] };
    }
  }

  /**
   * Parses Google Search results string into structured Source objects
   * @param searchResults - Raw search results string from Google Custom Search
   * @returns Array of Source objects
   */
  private parseGoogleSearchResults(searchResults: string): Source[] {
    const sources: Source[] = [];

    this.logger.debug(`🔍 Starting to parse search results...`);
    this.logger.debug(`📄 Search results type: ${typeof searchResults}`);
    this.logger.debug(`📏 Search results length: ${searchResults.length}`);

    try {
      // Try to parse as JSON first
      this.logger.debug(`🧩 Attempting JSON parsing...`);
      const parsed = JSON.parse(searchResults);
      this.logger.debug(`✅ JSON parsing successful`);
      this.logger.debug(
        `🔑 Parsed data type: ${Array.isArray(parsed) ? 'Array' : 'Object'}`,
      );

      // Handle both direct array format and object with items array
      let itemsToProcess: any[] = [];
      if (Array.isArray(parsed)) {
        this.logger.debug(
          `📋 Direct array format detected with ${parsed.length} items`,
        );
        itemsToProcess = parsed;
      } else if (parsed.items && Array.isArray(parsed.items)) {
        this.logger.debug(
          `📋 Object format detected with ${parsed.items.length} items in 'items' array`,
        );
        itemsToProcess = parsed.items;
      } else {
        this.logger.warn(
          `⚠️ Unexpected response format. Keys: [${Object.keys(parsed).join(', ')}]`,
        );
        this.logger.debug(`🔍 Full parsed structure:`, parsed);
      }

      if (itemsToProcess.length > 0) {
        for (const item of itemsToProcess.slice(0, 8)) {
          // Limit to top 8 results
          this.logger.debug(
            `📌 Processing item: title=${!!item.title}, link=${!!item.link}, snippet=${!!item.snippet}`,
          );
          if (item.title && item.link && item.snippet) {
            sources.push({
              title: item.title,
              link: item.link,
              snippet: item.snippet,
            });
            this.logger.debug(
              `✅ Added source: "${item.title.substring(0, 50)}..."`,
            );
          } else {
            this.logger.debug(`❌ Skipped item due to missing fields`);
          }
        }
      }
    } catch (parseError) {
      const errorMessage =
        parseError instanceof Error ? parseError.message : String(parseError);
      this.logger.warn(
        `❌ Failed to parse search results as JSON: ${errorMessage}`,
      );
      this.logger.debug(`🔍 Attempting text parsing as fallback...`);

      // Fallback: try to extract from text format
      const lines = searchResults.split('\n');
      let currentSource: Partial<Source> = {};

      for (const line of lines) {
        if (line.startsWith('Title: ')) {
          if (
            currentSource.title &&
            currentSource.link &&
            currentSource.snippet
          ) {
            sources.push(currentSource as Source);
          }
          currentSource = { title: line.substring(7) };
        } else if (line.startsWith('Link: ')) {
          currentSource.link = line.substring(6);
        } else if (line.startsWith('Snippet: ')) {
          currentSource.snippet = line.substring(9);
        }
      }

      // Add the last source if complete
      if (currentSource.title && currentSource.link && currentSource.snippet) {
        sources.push(currentSource as Source);
      }

      this.logger.debug(
        `📝 Text parsing completed, found ${sources.length} sources`,
      );
    }

    this.logger.debug(
      `🎯 Final result: Parsed ${sources.length} sources from search results`,
    );
    return sources;
  }

  /**
   * Formats search results into a context string for LLM consumption
   * @param sources - Array of search result sources
   * @param searchQuery - Original search query
   * @returns Formatted context string
   */
  private formatSearchContext(sources: Source[], searchQuery: string): string {
    if (sources.length === 0) {
      return '';
    }

    let context = `SEARCH RESULTS for "${searchQuery}":\n\n`;

    sources.forEach((source, index) => {
      const sourceNumber = index + 1;
      context += `[${sourceNumber}] ${source.title}\n`;
      context += `${source.snippet}\n`;
      context += `Source: ${source.link}\n\n`;
    });

    context += `Please use the search results above to inform your response. When referencing information from the search results, cite the source using the bracketed numbers (e.g., [1], [2], etc.).\n\n`;

    return context;
  }

  /**
   * Checks if search functionality is available
   * @returns True if Google Search is configured and available
   */
  isSearchAvailable(): boolean {
    return this.googleSearchTool !== null;
  }
}
