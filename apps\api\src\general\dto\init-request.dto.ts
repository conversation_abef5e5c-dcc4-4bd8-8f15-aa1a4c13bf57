import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>U<PERSON>D,
  IsN<PERSON>ber,
  IsBoolean,
  IsArray,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer'; // Required for nested validation

// Placeholder for the structure within totalResquest array
class ConversationTurnDto {
  @IsString()
  role!: 'user' | 'model'; // Added '!'

  @IsString()
  content!: string; // Added '!'
}

export class InitRequestDto {
  @IsString()
  @IsOptional() // Make it optional as per original route logic
  currentModel?: string; // Seems unused in original init logic, but keep for potential validation

  @IsString()
  message!: string; // Added '!' The user's input message

  @IsArray()
  @ValidateNested({ each: true }) // Validate each item in the array
  @Type(() => ConversationTurnDto) // Specify the type of items in the array
  @IsOptional() // Make it optional as per original init logic
  totalResquest?: ConversationTurnDto[]; // Renamed from `history`? Represents chat history

  @IsUUID()
  @IsOptional()
  conversationId?: string; // Existing conversation ID

  // Based on `sp_cvst_InsertConversation` parameters
  @IsString()
  deploymentName!: string; // Added '!' Model deployment name (e.g., 'gpt-35-turbo')

  @IsString()
  @IsOptional()
  conversation_name?: string; // Optional conversation name

  @IsNumber()
  @IsOptional()
  temperature?: number;

  @IsNumber()
  @IsOptional()
  max_tokens?: number;

  @IsBoolean()
  @IsOptional()
  stream?: boolean;

  @IsString()
  @IsOptional()
  system_prompt?: string;

  // Add any other necessary fields based on sp_cvst_InsertConversation or Azure request body
}
