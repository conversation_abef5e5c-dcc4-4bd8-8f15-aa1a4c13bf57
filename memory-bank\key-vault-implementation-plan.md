# Azure Key Vault Implementation Plan

## 1. Current Secret Management Understanding:
*   Both frontend and backend currently use `.env` files for managing environment variables and secrets.
*   The goal is to use Azure Key Vault for sensitive variables (e.g., database access, encryption keys, API keys) specifically in UAT and Production environments, while retaining `.env` for local development.

## 2. Backend (apps/api) Azure Key Vault Integration Design:

*   **Conditional Loading:** Secrets will be loaded from Azure Key Vault only when the application is running in UAT or Production environments (determined by an environment variable like `NODE_ENV`). For local development, the existing `.env` file mechanism will be used.
*   **Azure SDK Integration:** The backend will utilize the `@azure/keyvault-secrets` and `@azure/identity` SDKs to securely connect to and retrieve secrets from Azure Key Vault.
*   **Dedicated Key Vault Service:** A new service (e.g., `KeyVaultService`) will be created within the `apps/api` project. This service will encapsulate all Key Vault interactions.
    *   It will initialize an `SecretClient` using `DefaultAzureCredential` for authentication.
    *   It will fetch the specified list of sensitive secrets from Key Vault.
    *   It will implement an in-memory caching mechanism with a configurable Time-To-Live (TTL) to minimize calls to Azure Key Vault and improve performance.
    *   It will provide a unified interface for other backend services to retrieve secrets, abstracting whether they come from Key Vault or `.env` files.
*   **Integration Point:** This `KeyVaultService` will be integrated into the application's configuration loading process, likely as a module or provider, to ensure secrets are available before other services start.
*   **Robust Error Handling:** Comprehensive error handling will be implemented to gracefully manage scenarios where Key Vault access fails (e.g., network issues, permission problems), potentially falling back to `.env` values if appropriate for non-sensitive defaults.

## 3. Frontend (apps/web) Secure Secret Exposure Design:

*   The frontend will **not** directly access Azure Key Vault. Direct access would expose sensitive credentials and is a significant security risk.
*   **Backend as Proxy:** The backend will act as a secure proxy for frontend configuration.
*   **Dedicated API Endpoint:** A new, secure API endpoint (e.g., `/api/config`) will be created in the backend.
*   **Filtered Configuration:** This endpoint will be responsible for retrieving necessary configuration values (which may originate from Key Vault or `.env` files in the backend) and exposing *only* the non-sensitive values required by the frontend. Sensitive secrets (like database credentials) will never be exposed through this endpoint.
*   **Frontend Caching:** The frontend will cache these configuration values in memory to reduce repeated API calls to the backend.

## 4. Caching Strategy:

*   **Backend:** In-memory cache for Key Vault secrets with a configurable TTL. This will significantly reduce latency and cost associated with repeated Key Vault calls.
*   **Frontend:** In-memory cache for configuration values fetched from the backend API.

## 5. Proposed Secret Management Flow Diagram:

```mermaid
graph TD
    subgraph Local Development
        A[Developer] --> B[.env files]
        B --> C[Backend App]
        B --> D[Frontend App]
    end

    subgraph UAT/Production Environment
        E[Backend App] --> F{Is NODE_ENV UAT/Prod?}
        F -- Yes --> G[Azure Key Vault]
        G --> H[Key Vault Service]
        H --> I[In-memory Cache]
        I --> E
        F -- No --> J[.env files]
        J --> E
        E --> K[Secure API Endpoint]
        K --> L[Frontend App]
        L --> M[In-memory Cache]
        M --> L
    end

    style A fill:#f9f,stroke:#333,stroke-width:2px
    style E fill:#f9f,stroke:#333,stroke-width:2px
    style L fill:#f9f,stroke:#333,stroke-width:2px
    style G fill:#ccf,stroke:#333,stroke-width:2px
    style I fill:#ccf,stroke:#333,stroke-width:2px
    style M fill:#ccf,stroke:#333,stroke-width:2px