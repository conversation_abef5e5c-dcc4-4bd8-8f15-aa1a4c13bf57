import React, { ReactElement, ReactHTMLElement, ReactSVGElement } from 'react';
import <PERSON><PERSON><PERSON> from '@/icons/ClaudeLogo';
import DeepSeekLogo from '@/icons/DeepSeekLogo';
import <PERSON><PERSON>ogo from '@/icons/GeminiLogo';
import <PERSON><PERSON><PERSON>ogo from '@/icons/OpenAILogo';
import <PERSON>wen<PERSON>ogo from '@/icons/QwenLogo';
import AdobeExpressLogo from '@/icons/AdobeExpressLogo';
import Adobe<PERSON>ireflyLogo from '@/icons/AdobeFireflyLogo';
import LlamaLogo from '@/icons/LlamaLogo'; // Added Llama logo import
import SvgIcon from '@mui/material/SvgIcon'; // For placeholder

// Simple Placeholder Icon for missing logos
const PlaceholderSvg = () => (
  <SvgIcon>
    <circle cx="12" cy="12" r="10" fill="grey" />
  </SvgIcon>
);

export const modelInfo: {
  [_: string]: { svg: React.JSX.Element; info: string };
} = {
  'claude-3-haiku': {
    svg: <ClaudeLogo />,
    info: 'Fastest model, good for simple tasks.', // Rewritten
  },
  'claude-3-sonnet': {
    svg: <ClaudeLogo />,
    info: 'Balanced intelligence and speed.', // Rewritten
  },
  'claude-3-5-sonnet': {
    svg: <ClaudeLogo />,
    info: 'Most intelligent model, excels complex tasks.', // Rewritten
  },
  // Wait until it releases - 2024-12-13
  // "claude-3-5-sonnet-v2": {
  //   svg: (
  //     <svg
  //       className="w-10 h-10"
  //       xmlns="http://www.w3.org/2000/svg"
  //       viewBox="0 0 512 512"
  //     >
  //       <rect
  //         fill="#CC9B7A"
  //         width="512"
  //         height="512"
  //         rx="104.187"
  //         ry="105.042"
  //       />
  //       <path
  //         fill="#1F1F1E"
  //         fillRule="nonzero"
  //         d="M318.663 149.787h-43.368l78.952 212.423 43.368.004-78.952-212.427zm-125.326 0l-78.952 212.427h44.255l15.932-44.608 82.846-.004 16.107 44.612h44.255l-79.126-212.427h-45.317zm-4.251 128.341l26.91-74.701 27.083 74.701h-53.993z"
  //       />
  //     </svg>
  //   ),
  //   info: "fast and versatile",
  // },
  'gemini-1.0-pro': {
    svg: <GeminiLogo />,
    info: 'Balanced capability for various tasks.', // Rewritten
  },
  'gemini-1.5-pro': {
    svg: <GeminiLogo />,
    info: 'Highly capable multimodal model.', // Rewritten
  },
  'gemini-1.5-flash': {
    svg: <GeminiLogo />,
    info: 'Fast, efficient multimodal model.', // Rewritten
  },
  'gemini-1.5-pro-grounding': {
    svg: <GeminiLogo />,
    info: 'Gemini 1.5 Pro with grounding.', // Rewritten
  },
  'gemini-1.5-flash-grounding': {
    svg: <GeminiLogo />,
    info: 'Gemini 1.5 Flash with grounding.', // Rewritten
  },
  'gemini-2.0-flash': {
    svg: <GeminiLogo />,
    info: 'Latest fast, efficient multimodal model.', // Rewritten
  },
  'gemini-2.0-flash-thinking': {
    svg: <GeminiLogo />,
    info: 'Gemini 2.0 Flash for reasoning.', // Rewritten
  },
  'gemini-2.0-flash-grounding': {
    svg: <GeminiLogo />,
    info: 'Gemini 2.0 Flash with grounding.', // Rewritten
  },
  'gpt-35-turbo': {
    svg: <OpenAILogo />,
    info: 'Fast, optimized for dialogue.', // Rewritten
  },
  'gpt-4-turbo': {
    svg: <OpenAILogo />,
    info: 'High-performance model with large context.', // Rewritten
  },
  'gpt-4-o': {
    svg: <OpenAILogo />,
    info: 'Fastest, most capable flagship model.', // Rewritten
  },
  'gpt-4.1': {
    // Added entry for gpt-4.1
    svg: <OpenAILogo />,
    info: 'Advanced OpenAI model.', // Placeholder info
  },
  'deepseek-r1': {
    svg: <DeepSeekLogo />,
    info: 'Specialized reasoning model.', // Rewritten
  },
  'DeepSeek-R1': {
    svg: <DeepSeekLogo />,
    info: 'Specialized reasoning model.', // Rewritten
  },
  'deepseek-v3': {
    svg: <DeepSeekLogo />,
    info: 'Mixture-of-Experts model.', // Rewritten
  },
  'DeepSeek-V3': {
    svg: <DeepSeekLogo />,
    info: 'Mixture-of-Experts model.', // Rewritten
  },
  'qwen-plus': {
    svg: <QwenLogo />,
    info: 'Advanced large language model.', // Rewritten
  },
  'Qwen-Plus': {
    svg: <QwenLogo />,
    info: 'Advanced large language model.', // Rewritten
  },
  'qwen-max': {
    svg: <QwenLogo />,
    info: 'Flagship large language model.', // Rewritten
  },
  'qwen2.5-max': {
    svg: <QwenLogo />,
    info: 'Latest flagship large language model.', // Rewritten
  },
  'Qwen2.5-Max': {
    svg: <QwenLogo />,
    info: 'Latest flagship large language model.', // Rewritten
  },
  llama3_1: {
    svg: <LlamaLogo />, // Use LlamaLogo component
    info: "Meta's latest open model.", // Rewritten
  },
  'gpt-4-o-mini': {
    svg: <OpenAILogo />,
    info: 'Efficient, compact, versatile GPT-4o variant.', // Rewritten
  },
  o1: {
    // Added entry for o1
    svg: <OpenAILogo />,
    info: "OpenAI's advanced reasoning model.", // Placeholder info
  },
  'o1-preview': {
    svg: <OpenAILogo />,
    info: "Preview of OpenAI's reasoning model.", // Rewritten
  },
  'o1-mini': {
    svg: <OpenAILogo />,
    info: "Compact version of OpenAI's reasoning model.", // Rewritten
  },
  'o3-mini': {
    svg: <OpenAILogo />,
    info: 'Future compact reasoning model.', // Rewritten
  },
  'o3-Mini': {
    svg: <OpenAILogo />,
    info: 'Future compact reasoning model.', // Rewritten
  },
  'Adobe Express': {
    svg: <AdobeExpressLogo />,
    info: 'Generate images via Adobe Express.', // Rewritten
  },
  'Adobe Firefly': {
    svg: <AdobeFireflyLogo />,
    info: 'Generate images via Adobe Firefly.', // Rewritten
  },
  // --- Mappings for History API Models ---
  'chatgpt4o-standard': {
    // Map to gpt-4-o
    svg: <OpenAILogo />,
    info: 'Fastest, most capable flagship model.',
  },
  gpt4o: {
    // Map to gpt-4-o
    svg: <OpenAILogo />,
    info: 'Fastest, most capable flagship model.',
  },
  'gemini-1.5-pro-groun': {
    // Map to gemini-1.5-pro-grounding
    svg: <GeminiLogo />,
    info: 'Gemini 1.5 Pro with grounding.',
  },
  'gemini-1.5-flash-gro': {
    // Map to gemini-1.5-flash-grounding
    svg: <GeminiLogo />,
    info: 'Gemini 1.5 Flash with grounding.',
  },
  'SenseChat-5-Cantones': {
    svg: <PlaceholderSvg />, // Use placeholder
    info: 'SenseTime model (Cantonese).',
  },
  'SenseChat-5': {
    svg: <PlaceholderSvg />, // Use placeholder
    info: 'SenseTime model.',
  },
  'claude-3-5-sonnet@20': {
    // Map to claude-3-5-sonnet
    svg: <ClaudeLogo />,
    info: 'Most intelligent model, excels complex tasks.',
  },
  // Add other mappings here if needed
  'GPT-4.1': {
    svg: <OpenAILogo />,
    info: '',
  },
  'GPT-4.1 mini': {
    svg: <OpenAILogo />,
    info: '',
  },
  'Gemini-2.5 Flash': {
    svg: <GeminiLogo />,
    info: '',
  },
  'Gemini-2.5 Pro': {
    svg: <GeminiLogo />,
    info: '',
  },
  'LLama 4 Maverick': {
    svg: <LlamaLogo />,
    info: '',
  },
};
