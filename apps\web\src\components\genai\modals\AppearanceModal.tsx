'use client';

import React from 'react';
import {
  Modal,
  SwipeableDrawer,
  useTheme,
  useMediaQuery,
  Box,
  Typography,
  Button,
  RadioGroup,
  FormControlLabel,
  Radio,
} from '@mui/material';
import DialogActions from '@mui/material/DialogActions';
import { useAppDispatch, useAppSelector } from '@/lib/store/hooks';
import { setTheme, selectTheme, ThemeMode } from '@/lib/store/themeSlice';

const style = {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: '80%',
  maxWidth: '600px', // Reduced max width for desktop
  height: '80vh',
  bgcolor: 'background.paper',
  boxShadow: 24,
  p: 0,
  display: 'flex',
  flexDirection: 'column',
  borderRadius: '16px', // Apply border-radius to the modal itself
  overflow: 'hidden', // Ensure content respects the border-radius
};

interface AppearanceModalProps {
  open: boolean;
  handleClose: () => void;
}

const AppearanceModal: React.FC<AppearanceModalProps> = ({
  open,
  handleClose,
}) => {
  const dispatch = useAppDispatch();
  const currentTheme = useAppSelector(selectTheme);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const handleOptionChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newThemeMode = event.target.value as ThemeMode;
    dispatch(setTheme(newThemeMode));
  };

  const modalContent = (
    <Box sx={isMobile ? { height: '80vh', display: 'flex', flexDirection: 'column', overflow: 'hidden' } : style}>
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant="h6" component="h2">Appearance Settings</Typography>
      </Box>
      <Box sx={{ flexGrow: 1, p: 2, overflowY: 'auto' }}>
        <Typography variant="body1" gutterBottom>
          Choose your preferred theme:
        </Typography>
        <Box sx={{ mt: 2 }}>
          <RadioGroup
            aria-label="appearance-theme"
            name="appearance-theme-group"
            value={currentTheme}
            onChange={handleOptionChange}
          >
            <FormControlLabel
              value="light"
              control={<Radio />}
              label="Light Theme"
            />
            <FormControlLabel
              value="dark"
              control={<Radio />}
              label="Dark Theme"
            />
            <FormControlLabel
              value="system"
              control={<Radio />}
              label="System Default"
            />
          </RadioGroup>
        </Box>
      </Box>
      <DialogActions sx={{ p: 2, borderTop: 1, borderColor: 'divider', justifyContent: 'flex-end' }}>
        <Button onClick={handleClose} color="primary" variant="contained">
          Close
        </Button>
      </DialogActions>
    </Box>
  );

  if (isMobile) {
    return (
      <SwipeableDrawer
        anchor="bottom"
        open={open}
        onClose={handleClose}
        onOpen={() => {}} // Required prop for SwipeableDrawer
        PaperProps={{
          sx: {
            borderTopLeftRadius: 16, // Apply border-radius to top-left corner
            borderTopRightRadius: 16, // Apply border-radius to top-right corner
            backdropFilter: 'blur(10px)', // Add blur effect
            backgroundColor: 'background.paper', // Ensure solid background
          },
        }}
        disableSwipeToOpen={false}
        ModalProps={{
          keepMounted: true,
        }}
      >
        <Box
          onClick={handleClose} // Add onClick to close the drawer
          sx={{
            display: 'flex',
            justifyContent: 'center',
            pt: 1, // Padding top for the handle
            pb: 0.5, // Padding bottom for the handle
            cursor: 'pointer', // Indicate it's clickable
          }}
        >
          <Box
            sx={{
              width: 40,
              height: 4,
              borderRadius: 2,
              bgcolor: 'text.secondary', // Color of the handle
            }}
          />
        </Box>
        {modalContent}
      </SwipeableDrawer>
    );
  }

  return (
    <Modal
      open={open}
      onClose={handleClose}
      disableRestoreFocus // Add this prop
      BackdropProps={{
        sx: {
          backdropFilter: 'blur(10px)',
        },
      }}
    >
      {modalContent}
    </Modal>
  );
};

export default AppearanceModal;