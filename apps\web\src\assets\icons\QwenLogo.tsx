import React from 'react';

const QwenLogo: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg
    width="20"
    height="20"
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 300 300"
    {...props} // Pass any additional props
  >
    <path
      fill="#5f62ac"
      fillRule="evenodd"
      d="M191.4,122.7c.6,0,.7.3.4.7l-10.1,17.7-31.5,55.4c0,.3-.3.3-.6.3s-.4,0-.6-.3l-41.7-72.7c-.3-.4,0-.6.3-.6h2.7l81.2-.3h-.1ZM115.5,25.9c-.3,0-.4,0-.6.3l-34.6,60.6c-.3.6-.9.9-1.6.9h-34.6c-.7,0-.9.3-.4.9l70.1,122.6c.3.4.1.7-.4.7h-33.7c-1,0-1.9.6-2.4,1.6l-15.9,27.9c-.6.9-.3,1.5.9,1.5h69c.6,0,1,.3,1.2.9l16.9,29.7c.6,1,1,1,1.6,0l60.4-105.7,9.5-16.6c0-.3.3-.3.6-.3s.4,0,.6.3l17.2,30.6c.3.4.7.7,1.3.7l33.4-.3c.1,0,.3,0,.4-.3v-.4l-35-61.5c-.3-.4-.3-.9,0-1.3l3.6-6.1,13.5-23.9c.3-.4,0-.7-.4-.7H116.1c-.7,0-.9-.3-.4-.9l17.4-30.3c.3-.4.3-.9,0-1.3l-16.5-28.9c0-.3-.3-.4-.6-.4h-.4ZM156.9,21.2c4.8,8.3,9.5,16.6,14.1,25.1.4.7,1,1,1.9,1h67c2.1,0,3.9,1.3,5.3,4l17.5,31c2.2,4,3,5.8.3,10.1-3.1,5.2-6.2,10.4-9.2,15.7l-4.5,8c-1.3,2.4-2.7,3.4-.4,6.2l32.1,56c2.1,3.6,1.3,5.9-.4,9.4-5.3,9.5-10.7,18.9-16.2,28.2-1.9,3.3-4.3,4.5-8.2,4.5-9.4-.1-18.7,0-28.1.1-.4,0-.7.3-1,.6-10.8,19.2-21.7,38.2-32.7,57.2-2.1,3.6-4.6,4.5-8.8,4.5h-36.4c-2.5,0-4.3-1-5.6-3.3l-16.2-28.1c-.1-.4-.6-.6-1-.6h-61.8c-3.4.3-6.7,0-9.6-1l-19.3-33.4c-1.2-2.2-1.2-4.3,0-6.5l14.5-25.5c.4-.7.4-1.6,0-2.4-7.6-13.2-15.1-26.3-22.6-39.5l-9.5-16.8c-1.9-3.7-2.1-5.9,1.2-11.6,5.6-9.8,11.1-19.6,16.8-29.4,1.6-2.8,3.7-4,7-4h31.3c.6,0,1-.3,1.3-.7l33.8-59.1c1.2-1.9,2.8-3,5-3h19.2l12.3-.3c4.2,0,8.8.4,10.8,4.2v-.6Z"
    />
  </svg>
);

export default QwenLogo;
