import { ChatMessageDto } from '../general/dto/stream-chat.dto'; // Assuming DTO is in this path
import { Logger } from '@nestjs/common';
import * as _ from 'lodash'; // Import lodash

// Define types for clarity (can be refined or moved)
// MultiModelContentPart is no longer needed as convertMultiModelPrompt is removed

interface GeminiContentPart {
  text?: string;
  inlineData?: {
    mimeType: string;
    data: string; // Base64 encoded data
  };
}

interface GeminiMessage {
  role: string;
  parts: GeminiContentPart[];
}

const logger = new Logger('PromptProcessingUtil'); // Create a logger instance

/**
 * Determines the type of model based on its deployment name.
 * @param deploymentName The deployment name of the model.
 * @returns The model type ('azure', 'gemini', 'qwen', 'aiservice', 'unknown').
 */
export function getModelType(deploymentName: string): string {
  if (!deploymentName) return 'unknown';
  const lowerCaseName = deploymentName.toLowerCase();

  // Order checks from more specific to less specific if names overlap
  if (lowerCaseName.includes('gpt-4') || lowerCaseName.includes('gpt-35')) {
    return 'azure'; // Assuming all GPT models are via Azure OpenAI
  } else if (lowerCaseName.includes('gemini')) {
    return 'gemini'; // Vertex AI Gemini
  }
  // Removed Claude/Anthropic checks
  else if (lowerCaseName.includes('qwen') || lowerCaseName.includes('tongyi')) {
    return 'qwen'; // Alibaba Tongyi Qwen
  } else if (lowerCaseName.includes('deepseek')) {
    // Assuming DeepSeek deployment names include 'deepseek'
    return 'aiservice'; // DeepSeek via Azure AI Service
  }
  // Add more rules if needed for other models/providers

  logger.warn(
    `Could not determine model type for deployment name: ${deploymentName}. Returning 'unknown'.`,
  );
  return 'unknown';
}

/**
 * Removes elements from an array by their indexes.
 * @param array The array to modify.
 * @param indexesToRemove An array of indexes to remove.
 * @returns A new array with elements removed.
 */
export function removeByIndexes<T>(array: T[], indexesToRemove: number[]): T[] {
  // Create a Set for efficient lookup
  const indexesSet = new Set(indexesToRemove);
  // Filter the array, keeping elements whose index is not in the Set
  return array.filter((_, index) => !indexesSet.has(index));
}

// Removed convertMultiModelPrompt function

/**
 * Converts a standard message history to the format expected by Google Gemini models.
 * Handles mapping roles and converting image attachments to inlineData.
 *
 * @param messages The input array of ChatMessageDto.
 * @param deploymentName The deployment name (used for logging).
 * @param attachments Optional array of attachments (e.g., { type: 'image', base64: '...', mimeType: '...' }). Adjust type as needed.
 * @returns An array of GeminiMessage objects.
 */
export function convertGeminiPrompt(
  messages: ChatMessageDto[],
  deploymentName: string,
  attachments?: any[], // TODO: Define a proper Attachment DTO type
): GeminiMessage[] {
  logger.debug(`Converting to Gemini prompt for ${deploymentName}`);
  const geminiMessages: GeminiMessage[] = [];

  messages.forEach((message, index) => {
    // Gemini uses 'user' and 'model' roles.
    const role =
      message.role === 'assistant'
        ? 'model'
        : message.role === 'system'
          ? 'user'
          : message.role;

    // Gemini expects 'parts' array even for simple text.
    const parts: GeminiContentPart[] = [];

    // Handle string content
    if (typeof message.content === 'string') {
      // Add system prompt instructions directly into the user message if it's the first one
      if (role === 'user' && message.role === 'system' && index === 0) {
        parts.push({ text: `System Instruction: ${message.content}\n---\n` });
        // If the next message is also a user message, combine it. Otherwise, create a new message.
        const nextMessage = messages[index + 1];
        if (nextMessage && nextMessage.role === 'user') {
          // Append the actual user message content here if combining
          if (typeof nextMessage.content === 'string') {
            parts[0].text += nextMessage.content;
            messages.splice(index + 1, 1); // Remove the next message as it's combined
          } else {
            logger.warn(
              'Cannot combine system and next user message as next message content is not a string.',
            );
            parts.push({ text: message.content }); // Add system prompt as plain text
          }
        } else {
          parts.push({ text: message.content }); // Add system prompt as plain text
        }
      } else {
        parts.push({ text: message.content });
      }
    } else {
      logger.warn(
        `Non-string content found for role ${role}, skipping content for Gemini prompt.`,
      );
      // Optionally stringify or handle complex content if Gemini API supports it in a specific way
      // parts.push({ text: JSON.stringify(message.content) });
    }

    // Add attachments only to the *last* user message
    if (
      role === 'user' &&
      index === messages.length - 1 &&
      attachments &&
      attachments.length > 0
    ) {
      attachments.forEach((att) => {
        if (att.type === 'image' && att.base64 && att.mimeType) {
          parts.push({
            inlineData: {
              mimeType: att.mimeType,
              data: att.base64,
            },
          });
        } else {
          logger.warn(
            `Skipping unsupported attachment type or format for Gemini: ${JSON.stringify(att)}`,
          );
        }
      });
    }

    // Ensure a message is only added if it has valid parts
    if (parts.length > 0) {
      geminiMessages.push({ role, parts });
    }
  });

  // Gemini requires alternating user/model roles. Fix if needed.
  const validatedMessages: GeminiMessage[] = [];
  let lastRole = '';
  geminiMessages.forEach((msg) => {
    if (msg.role === lastRole) {
      const prevMsg = validatedMessages[validatedMessages.length - 1];
      if (prevMsg && prevMsg.role === msg.role) {
        prevMsg.parts.push(...msg.parts);
        logger.warn(`Combining consecutive ${msg.role} messages for Gemini.`);
      } else {
        logger.error(
          `Consecutive ${msg.role} roles detected, cannot combine. Skipping message.`,
        );
      }
    } else if (msg.role === 'user' || msg.role === 'model') {
      // Only allow valid roles
      validatedMessages.push(msg);
      lastRole = msg.role;
    } else {
      logger.error(
        `Invalid role detected for Gemini: ${msg.role}. Skipping message.`,
      );
    }
  });

  // Ensure the conversation starts with a 'user' role if possible
  if (validatedMessages.length > 0 && validatedMessages[0].role !== 'user') {
    logger.warn(
      'Gemini conversation does not start with "user" role. Consider prepending one.',
    );
    // validatedMessages.unshift({ role: 'user', parts: [{ text: '' }] });
  }

  return validatedMessages; // Return potentially modified message list
}
