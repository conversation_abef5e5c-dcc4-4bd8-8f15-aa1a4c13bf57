import { DefaultSession, DefaultUser } from 'next-auth';
import { JWT as NextAuthJWT } from 'next-auth/jwt'; // Renamed import JWT

// Define the custom properties expected from BUAM/profile
interface BUAMUserExtensions {
  id?: string; // Maps to ssoid
  dept_unit_code?: string;
  type?: 'STUDENT' | 'STAFF'; // After mapping 'OTHER'
  rest?: boolean;
  accessToken?: string;
}

// Extend the built-in session types
declare module 'next-auth' {
  /**
   * Returned by `useSession`, `getSession` and received as a prop on the `SessionProvider` React Context
   */
  interface Session {
    accessToken?: string; // Re-add this property
    user: DefaultSession['user'] & BUAMUserExtensions; // Merge default user type with custom properties
  }

  // Extend the built-in User type returned by the 'profile' callback
  interface User extends DefaultUser, BUAMUserExtensions {
    // Ensure id is always present as returned by profile
    id: string;
    // Include properties from BUAMProfile used in profile()
    employee_type?: 'STUDENT' | 'STAFF' | 'OTHER'; // Keep original type if needed
  }
}

// Extend the built-in JWT types
declare module 'next-auth/jwt' {
  /** Returned by the `jwt` callback and `getToken`, when using JWT sessions */
  interface JWT extends NextAuthJWT, BUAMUserExtensions {
    // `id` is inherited via BUAMUserExtensions
    // Ensure custom properties from jwt callback are included
  }
}
