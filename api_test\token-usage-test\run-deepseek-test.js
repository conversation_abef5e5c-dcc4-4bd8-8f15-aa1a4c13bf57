#!/usr/bin/env node

/**
 * Simple DeepSeek Token Test Runner with File Logging
 */

import dotenv from 'dotenv';
import fs from 'fs';

// Load environment variables
dotenv.config({ path: '../apps/api/.env' });

// Setup logging
const logFile = 'deepseek-test-results.log';
const logStream = fs.createWriteStream(logFile, { flags: 'w' });

function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}`;
  console.log(logMessage);
  logStream.write(logMessage + '\n');
}

// Test configuration for DeepSeek
const deepseekEndpoint = process.env.AZURE_AI_SERVICE_DEEPSEEK_ENDPOINT || process.env.AZURE_AI_SERVICE_ENDPOINT;
const deepseekApiKey = process.env.AZURE_AI_SERVICE_DEEPSEEK_KEY || process.env.AZURE_AI_SERVICE_KEY;
const apiVersion = '2024-05-01-preview'; // Use the correct API version from the code
const deploymentName = 'deepseek-v3-hkbu'; // Correct deployment name from database
const instanceName = 'eastus'; // Use eastus instance

log('🧪 Starting DeepSeek Token Test');
log(`Endpoint: ${deepseekEndpoint}`);
log(`API Key Available: ${deepseekApiKey ? 'YES' : 'NO'}`);
log(`API Version: ${apiVersion}`);

if (!deepseekApiKey || !deepseekEndpoint) {
  log('❌ Missing required environment variables');
  logStream.end();
  process.exit(1);
}

async function testDeepSeekNonStreaming() {
  try {
    log('\n📝 Testing DeepSeek Non-Streaming');
    
    // Ensure endpoint ends with proper API path
    let endpoint = deepseekEndpoint;
    if (!endpoint.endsWith('/chat/completions')) {
      endpoint = endpoint.replace(/\/+$/, '') + '/chat/completions';
    }
    endpoint = `${endpoint}?api-version=${apiVersion}`;
    
    const payload = {
      messages: [{ role: 'user', content: 'Hello! Please respond briefly.' }],
      temperature: 0.7,
      stream: false,
      model: `${deploymentName}-${instanceName}`, // Use the correct model format
    };
    
    log(`Request URL: ${endpoint}`);
    log(`Payload: ${JSON.stringify(payload, null, 2)}`);
    
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'api-key': deepseekApiKey,
      },
      body: JSON.stringify(payload),
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      log(`❌ Non-streaming request failed: ${response.status} ${errorText}`);
      return;
    }
    
    const responseData = await response.json();
    log(`✅ Non-streaming response: ${JSON.stringify(responseData, null, 2)}`);
    
    if (responseData.usage) {
      log(`🎯 NON-STREAMING TOKENS FOUND: ${JSON.stringify(responseData.usage)}`);
    } else {
      log(`❌ No tokens in non-streaming response`);
    }
    
  } catch (error) {
    log(`❌ Non-streaming test failed: ${error.message}`);
  }
}

async function testDeepSeekStreaming() {
  try {
    log('\n📡 Testing DeepSeek Streaming with Token Usage');
    
    // Ensure endpoint ends with proper API path
    let endpoint = deepseekEndpoint;
    if (!endpoint.endsWith('/chat/completions')) {
      endpoint = endpoint.replace(/\/+$/, '') + '/chat/completions';
    }
    endpoint = `${endpoint}?api-version=${apiVersion}`;
    
    const payload = {
      messages: [{ role: 'user', content: 'Hello! Please respond briefly.' }],
      temperature: 0.7,
      stream: true,
      stream_options: { include_usage: true },
      model: `${deploymentName}-${instanceName}`, // Use the correct model format 
    };
    
    log(`Request URL: ${endpoint}`);
    log(`Payload: ${JSON.stringify(payload, null, 2)}`);
    
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'api-key': deepseekApiKey,
      },
      body: JSON.stringify(payload),
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      log(`❌ Streaming request failed: ${response.status} ${errorText}`);
      return;
    }
    
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let chunkCount = 0;
    let usageFound = false;
    let content = '';
    
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      
      const chunk = decoder.decode(value, { stream: true });
      const lines = chunk.split('\n').filter(line => line.trim());
      
      for (const line of lines) {
        if (!line.startsWith('data:')) continue;
        
        const data = line.slice(5).trim();
        if (data === '[DONE]') continue;
        
        try {
          const parsed = JSON.parse(data);
          chunkCount++;
          
          log(`\n--- DeepSeek Stream Chunk ${chunkCount} ---`);
          log(`Chunk: ${JSON.stringify(parsed, null, 2)}`);
          
          const deltaContent = parsed.choices?.[0]?.delta?.content;
          if (deltaContent) {
            content += deltaContent;
          }
          
          if (parsed.usage) {
            log(`🎯 STREAMING TOKENS FOUND: ${JSON.stringify(parsed.usage)}`);
            usageFound = true;
          }
          
          const finishReason = parsed.choices?.[0]?.finish_reason;
          if (finishReason) {
            log(`Finish reason: ${finishReason}`);
          }
          
        } catch (parseError) {
          log(`Parse error: ${parseError.message} for data: ${data}`);
        }
      }
    }
    
    log(`\n✅ Streaming completed`);
    log(`Total chunks: ${chunkCount}`);
    log(`Content: "${content}"`);
    log(`Usage found: ${usageFound ? 'YES' : 'NO'}`);
    
  } catch (error) {
    log(`❌ Streaming test failed: ${error.message}`);
  }
}

// Run tests
async function runAllTests() {
  await testDeepSeekNonStreaming();
  await testDeepSeekStreaming();
  
  log('\n🏁 All DeepSeek tests completed');
  log(`Results saved to: ${logFile}`);
  logStream.end();
}

runAllTests().catch((error) => {
  log(`💥 Test suite failed: ${error.message}`);
  logStream.end();
  process.exit(1);
});