{"name": "@hkbu-genai-platform/database", "version": "0.1.0", "private": true, "exports": {".": null, "./generated/client": "./prisma/generated/client/index.js"}, "files": ["prisma/generated"], "typesVersions": {"*": {"generated/client": ["./prisma/generated/client/index.d.ts"]}}, "scripts": {"build": "prisma generate", "db:generate": "prisma generate", "db:migrate:dev": "prisma migrate dev", "db:studio": "prisma studio", "db:push": "prisma db push --skip-generate", "db:seed": "prisma db seed"}, "dependencies": {"@nestjs/mapped-types": "^2.1.0", "@prisma/client": "^6.5.0"}, "devDependencies": {"@hkbu-genai-platform/tsconfig": "workspace:*", "@types/node": "^20.14.9", "prisma": "^6.5.0", "tsx": "^4.16.2", "typescript": "^5.x.x"}, "prisma": {"seed": "tsx prisma/seed.ts"}}