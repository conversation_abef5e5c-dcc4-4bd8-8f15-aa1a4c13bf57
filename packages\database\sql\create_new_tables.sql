-- Create table for API request prompts
CREATE TABLE api_request_prompt (
    id INT IDENTITY(1,1) PRIMARY KEY,
    prompt_data VARBINARY(MAX) NULL,
    prompt_order INT NOT NULL,
    api_footprint_id INT NOT NULL,
    CONSTRAINT FK_api_request_prompt_api_footprint FOREIGN KEY (api_footprint_id) REFERENCES api_footprint(id)
);

-- Create index for api_request_prompt
CREATE INDEX IX_api_request_prompt_api_footprint_id ON api_request_prompt(api_footprint_id);

-- Create table for API response prompts
CREATE TABLE api_response_prompt (
    id INT IDENTITY(1,1) PRIMARY KEY,
    prompt_data VARBINARY(MAX) NULL,
    prompt_order INT NOT NULL,
    api_footprint_id INT NOT NULL,
    CONSTRAINT FK_api_response_prompt_api_footprint FOREIGN KEY (api_footprint_id) REFERENCES api_footprint(id)
);

-- Create index for api_response_prompt
CREATE INDEX IX_api_response_prompt_api_footprint_id ON api_response_prompt(api_footprint_id);

-- Create table for message prompts
CREATE TABLE prompt (
    id INT IDENTITY(1,1) PRIMARY KEY,
    message_id INT NOT NULL,
    prompt_data VARBINARY(MAX) NULL,
    prompt_order INT NOT NULL,
    CONSTRAINT FK_prompt_message FOREIGN KEY (message_id) REFERENCES message(message_id)
);

-- Create index for prompt
CREATE INDEX IX_prompt_message_id ON prompt(message_id);
