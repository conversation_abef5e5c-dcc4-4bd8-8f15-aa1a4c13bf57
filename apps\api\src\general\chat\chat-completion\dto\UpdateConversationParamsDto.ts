import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsUUID,
  IsInt,
  <PERSON>N<PERSON><PERSON>,
  <PERSON>,
  Max,
} from 'class-validator';

export class UpdateConversationParamsDto {
  @ApiProperty({
    description: 'UUID of the chat session to update.',
    format: 'uuid',
  })
  @IsUUID()
  chat_session_id!: string;

  @ApiPropertyOptional({
    description: 'Custom instructions or system prompt for the model.',
    type: String,
    maxLength: 8000, // Example max length, adjust as needed
  })
  @IsString()
  @IsOptional()
  instructions?: string;

  @ApiPropertyOptional({
    description:
      'Number of past messages to include in the context (0-20). Default: 10.',
    type: Number,
    minimum: 0,
    maximum: 20, // New max
  })
  @IsInt()
  @Min(0)
  @Max(20) // New max
  @IsOptional()
  pastMessagesCount?: number;

  @ApiPropertyOptional({
    description:
      'Maximum number of tokens to generate in the response (e.g., 1-4096). Default: 2000.',
    type: Number,
    minimum: 1,
    maximum: 4096, // Add new max
  })
  @IsInt()
  @Min(1)
  @Max(4096) // Add new max
  @IsOptional()
  maxResponseTokens?: number;

  @ApiPropertyOptional({
    description:
      'Sampling temperature (0.0 to 1.0). Higher values make output more random. Default: 0.6.',
    type: Number,
    minimum: 0.0,
    maximum: 1.0, // New max
  })
  @IsNumber()
  @Min(0.0)
  @Max(1.0) // New max
  @IsOptional()
  temperature?: number;

  @ApiPropertyOptional({
    description:
      'Top-p sampling (0.0 to 1.0). Nucleus sampling parameter. Default: 0.6.',
    type: Number,
    minimum: 0.0,
    maximum: 1.0, // Max is already 1.0
  })
  @IsNumber()
  @Min(0.0)
  @Max(1.0) // Max is already 1.0
  @IsOptional()
  topP?: number;
}
