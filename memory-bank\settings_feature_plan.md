# Settings Feature Implementation Plan

## Objective
To implement a unified settings page accessible from the sidebar, consolidating various informational and action items into expandable sections.

## Core Changes

1.  **Sidebar Modification**:
    *   The "Settings" icon in `hkbu-genai-platform/apps/web/src/components/genai/ChatHistorySidebar.tsx` will be converted into a direct link navigating to a new `/settings` page.
    *   The existing settings `Drawer` component (which slides from the right) and the "Sign out" button currently within that drawer will be removed from the sidebar.

2.  **Unified Settings Page (`/settings/page.tsx`)**:
    *   This page will be the central hub for all settings-related content and actions.
    *   It will display user account information (e.g., "Signed in as...").
    *   The following items will each be presented as an expandable/collapsible section (e.g., using an Accordion component):
        *   FAQ
        *   API
        *   Prompt Engineering & Temperature
        *   Terms & Conditions
        *   Privacy Policy
        *   Feedback
        *   Request Quote
        *   Contact Us
        *   LLM Health Check
    *   Clicking a section header will expand it to show its content directly on the `/settings` page.
    *   Initially, all content within these sections will be placeholder text.
    *   A "Sign out" button will be prominently placed on this `/settings` page.

3.  **No New Individual Sub-Pages**:
    *   Separate pages like `/faq`, `/api-docs`, etc., will *not* be created. All content is consolidated onto the `/settings` page within their respective expandable sections.

## Visual Plan (Mermaid Diagram)

```mermaid
graph TD
    A[ChatHistorySidebar] --> B{Sidebar Expanded State (Global)};

    A --> C[Logo & Title/Icon];
    A --> D[History Section (if expanded)];
    A --> E[Settings Icon (Link to /settings page)];
    %% Sign out button removed from sidebar

    subgraph Settings Page (/settings)
        G[Main Settings Page Layout] --> H_Header[Page Title: Settings];
        G --> AccountInfo[Account Info: Signed in as...];
        G --> Section1[Expandable Section: FAQ];
        Section1 -- Expand --> Content1[FAQ Content (Placeholder)];
        G --> Section2[Expandable Section: API];
        Section2 -- Expand --> Content2[API Content (Placeholder)];
        G --> Section3[Expandable Section: Prompt Engineering];
        Section3 -- Expand --> Content3[Prompt Engineering Content (Placeholder)];
        G --> Section4[Expandable Section: Terms & Conditions];
        Section4 -- Expand --> Content4[Terms & Conditions Content (Placeholder)];
        G --> Section5[Expandable Section: Privacy Policy];
        Section5 -- Expand --> Content5[Privacy Policy Content (Placeholder)];
        G --> Section6[Expandable Section: Feedback];
        Section6 -- Expand --> Content6[Feedback Content (Placeholder)];
        G --> Section7[Expandable Section: Request Quote];
        Section7 -- Expand --> Content7[Request Quote Content (Placeholder)];
        G --> Section8[Expandable Section: Contact Us];
        Section8 -- Expand --> Content8[Contact Us Content (Placeholder)];
        G --> Section9[Expandable Section: LLM Health Check];
        Section9 -- Expand --> Content9[LLM Health Check Content (Placeholder)];
        G --> SignOutButton[Button: Sign Out];
    end
```

## Key Files Involved
*   `hkbu-genai-platform/apps/web/src/components/genai/ChatHistorySidebar.tsx` (for sidebar link modification)
*   `hkbu-genai-platform/apps/web/src/app/settings/page.tsx` (for the main settings page implementation)