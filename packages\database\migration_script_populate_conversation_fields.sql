-- ======================================================================
-- Migration Script: Standardize conversation_title encryption as NVARCHAR
--
-- Description: For each conversation, decrypt message.last_prompt (assumed
--              to be encrypted as VARCHAR), convert to NVARCHAR, truncate to 512 chars,
--              re-encrypt as NVARCHAR, and update conversation.conversation_title.
--              Also updates conversation.model_name from the first message.
--
-- WARNING: This script performs an UPDATE operation.
--          It defaults to ROLLBACK. Review the changes carefully
--          before changing to COMMIT. Backup your database first!
-- ======================================================================

BEGIN TRANSACTION;

PRINT 'Starting migration to standardize conversation_title encryption as NVARCHAR...';
DECLARE @StartTime DATETIME = GETDATE();

-- Open the symmetric key for decryption/encryption
-- Replace with your actual key and cert names if needed
DECLARE @encryption_key_name NVARCHAR(128) = N'chatgpt_db_symmetric_key';
DECLARE @decryption_cert_name NVARCHAR(128) = N'chatgpt_db_certification';

DECLARE @OpenKeySQL NVARCHAR(MAX) = N'OPEN SYMMETRIC KEY ' + QUOTENAME(@encryption_key_name) + N' DECRYPTION BY CERTIFICATE ' + QUOTENAME(@decryption_cert_name) + N';';
EXEC sp_executesql @OpenKeySQL;

-- Use a CTE to identify the first message (by create_dt) for each conversation
;WITH FirstMessage AS (
    SELECT
        m.conversation_id,
        m.last_prompt, -- Encrypted VARBINARY
        m.model_name,
        ROW_NUMBER() OVER(PARTITION BY m.conversation_id ORDER BY m.create_dt ASC) as rn
    FROM
        dbo.message m
)
-- Update the conversation table, joining with the CTE result
UPDATE c
SET
    c.conversation_title =
        -- Re-encrypt as NVARCHAR: decrypt VARBINARY, convert directly to NVARCHAR, truncate, then re-encrypt
        EncryptByKey(
            Key_GUID(@encryption_key_name),
            LEFT(CONVERT(NVARCHAR(MAX), DecryptByKey(fm.last_prompt)), 512)
        ),
    c.model_name = fm.model_name
FROM
    dbo.conversation c
INNER JOIN
    FirstMessage fm ON c.conversation_id = fm.conversation_id
WHERE
    fm.rn = 1 and c.conversation_title is null and c.create_dt < '2025-04-20';

-- Get the count of updated rows
DECLARE @UpdatedRowCount INT = @@ROWCOUNT;
DECLARE @EndTime DATETIME = GETDATE();
PRINT 'Finished update. Updated ' + CAST(@UpdatedRowCount AS VARCHAR) + ' rows in the conversation table.';
PRINT 'Duration: ' + CAST(DATEDIFF(MILLISECOND, @StartTime, @EndTime) AS VARCHAR) + ' ms.';

-- Close the symmetric key
DECLARE @CloseKeySQL NVARCHAR(MAX) = N'CLOSE SYMMETRIC KEY ' + QUOTENAME(@encryption_key_name) + N';';
EXEC sp_executesql @CloseKeySQL;

PRINT 'Migration update executed. Please review the changes before committing.';
PRINT 'Review the changes carefully before committing.';

COMMIT TRANSACTION; -- Uncomment this line to make changes permanent
-- ROLLBACK TRANSACTION; -- Comment this line out if you uncomment COMMIT

PRINT 'Transaction committed. Changes are permanent.';