/**
 * Utility functions for normalizing model names to ensure consistency
 * between different selection methods and API calls
 */

/**
 * Mapping of model names that need normalization
 * Maps from the normalized name to the full database name
 */
const MODEL_NAME_MAPPING: Record<string, string[]> = {
  // DeepSeek models
  'deepseek-v3': ['deepseek-v3', 'deepseek-v3-hkbu'],

  // ChatGPT models - normalize to chatgpt prefix for consistency
  'GPT-4.1': ['chatgpt-4.1', 'gpt-4.1', 'GPT-4.1'],
  'GPT-4.1-mini': ['chatgpt-4.1-mini', 'gpt-4.1-mini', 'GPT-4.1-mini'],

  // O1 models - keep as simple o1 format
  o1: ['o1', 'chatgpt-o1', 'gpt-o1'],

  // O3 models - keep as simple o3 format
  'o3-mini': ['o3-mini', 'chatgpt-o3-mini', 'gpt-o3-mini'],

  // Add other model name mappings here as needed
  // This ensures all models have consistent display names across frontend and backend
};

/**
 * Normalizes a model name by removing institution-specific suffixes
 *
 * @param modelName - The model name to normalize
 * @returns The normalized model name
 */
export function normalizeModelName(
  modelName: string | null | undefined,
): string | null {
  if (!modelName) {
    return null;
  }

  // Check if we already have a normalized form
  const normalizedVersion = findNormalizedName(modelName);
  if (normalizedVersion) {
    return normalizedVersion;
  }

  // Generic normalization: remove common institution suffixes
  const suffixesToRemove = ['-hkbu', '-HKBU'];

  for (const suffix of suffixesToRemove) {
    if (modelName.endsWith(suffix)) {
      return modelName.slice(0, -suffix.length);
    }
  }

  // Return original name if no normalization needed
  return modelName;
}

/**
 * Finds the normalized name for a given model name
 *
 * @param modelName - The model name to find normalized version for
 * @returns The normalized model name if found, null otherwise
 */
function findNormalizedName(modelName: string): string | null {
  for (const [normalized, variants] of Object.entries(MODEL_NAME_MAPPING)) {
    if (variants.includes(modelName)) {
      return normalized;
    }
  }
  return null;
}

/**
 * Gets all possible variants of a model name (including the original)
 * This is useful for database lookups when we need to check multiple possible names
 *
 * @param modelName - The model name to get variants for
 * @returns Array of possible model name variants
 */
export function getModelNameVariants(
  modelName: string | null | undefined,
): string[] {
  if (!modelName) {
    return [];
  }

  const normalized = normalizeModelName(modelName);
  if (!normalized) {
    return [modelName];
  }

  // Get all variants for the normalized name
  const variants = MODEL_NAME_MAPPING[normalized] || [];

  // Always include the original input and normalized version
  const allVariants = new Set([modelName, normalized, ...variants]);

  return Array.from(allVariants);
}

/**
 * Checks if a model name needs normalization
 *
 * @param modelName - The model name to check
 * @returns True if the model name needs normalization
 */
export function needsNormalization(
  modelName: string | null | undefined,
): boolean {
  if (!modelName) {
    return false;
  }

  return normalizeModelName(modelName) !== modelName;
}
