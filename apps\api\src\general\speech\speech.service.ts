import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config'; // Import ConfigService
import { SpeechClient } from '@google-cloud/speech';
import { google } from '@google-cloud/speech/build/protos/protos'; // Import specific types
import { TokenUsageService } from '../../common/services/token-usage.service';
import { ModelMappingService } from '../../common/services/model-mapping.service';
import { AuthenticatedUser } from '../../auth/user.interface';

@Injectable()
export class SpeechService {
  private readonly logger = new Logger(SpeechService.name);
  private speechClient: SpeechClient;

  constructor(
    private configService: ConfigService,
    private readonly tokenUsageService: TokenUsageService,
    private readonly modelMappingService: ModelMappingService,
  ) {
    // Inject ConfigService
    const projectId = this.configService.get<string>(
      'GOOGLE_VERTEXAI_PROJECT_ID',
    );
    if (!projectId) {
      this.logger.error(
        'GOOGLE_VERTEXAI_PROJECT_ID is not set in environment variables.',
      );
      // Handle error appropriately - maybe throw or prevent client creation
    }
    this.logger.log(
      `Initializing Google SpeechClient for project ID: ${projectId}`,
    );
    // Instantiate the client, providing projectId for quota/billing.
    // Credentials should still be picked up from GOOGLE_APPLICATION_CREDENTIALS.
    this.speechClient = new SpeechClient({ projectId });
    this.logger.log('Google SpeechClient initialized.');
  }

  /**
   * Transcribes audio using Azure OpenAI Whisper model (GPT-4o Mini).
   * @param audioBuffer The audio data as a Buffer.
   * @returns The transcribed text, or an empty string if transcription fails.
   */
  async transcribeWithAzureOpenAI(audioBuffer: Buffer): Promise<string> {
    this.logger.log('Received audio buffer for Azure OpenAI transcription.');

    try {
      // Get Azure OpenAI configuration
      const endpoint =
        this.configService.get<string>('AZURE_OPENAI_SPEECH_ENDPOINT') ||
        'https://hkbu-chatgpt-us-east2.openai.azure.com/openai/deployments/chatgpt-4o-mini-transcribe/audio/transcriptions';
      const apiKey =
        this.configService.get<string>('AZURE_OPENAI_SPEECH_API_KEY') ||
        this.configService.get<string>('GENERAL_AZURE_OPENAI_API_KEY_USEAST2');
      const apiVersion =
        this.configService.get<string>('AZURE_OPENAI_SPEECH_API_VERSION') ||
        '2025-03-01-preview';

      if (!apiKey) {
        this.logger.error('Azure OpenAI API key not configured.');
        throw new Error('Azure OpenAI API key not configured');
      }

      // Build the endpoint URL with api-version
      const fullEndpoint = `${endpoint}?api-version=${apiVersion}`;

      this.logger.log(`Sending request to Azure OpenAI: ${fullEndpoint}`);

      // Dynamically import node-fetch for the request
      const { default: fetch } = await import('node-fetch');

      // Create multipart form data manually using Buffer concatenation
      const boundary = `----formdata-${Date.now()}`;
      const formParts: Buffer[] = [];

      // Add file field
      formParts.push(Buffer.from(`--${boundary}\r\n`));
      formParts.push(
        Buffer.from(
          `Content-Disposition: form-data; name="file"; filename="audio.webm"\r\n`,
        ),
      );
      formParts.push(Buffer.from(`Content-Type: audio/webm\r\n\r\n`));
      formParts.push(audioBuffer);
      formParts.push(Buffer.from(`\r\n`));

      // Add model field
      formParts.push(Buffer.from(`--${boundary}\r\n`));
      formParts.push(
        Buffer.from(`Content-Disposition: form-data; name="model"\r\n\r\n`),
      );
      formParts.push(Buffer.from(`chatgpt-4o-mini-transcribe`));
      formParts.push(Buffer.from(`\r\n`));

      // Add response_format field for JSON (verbose_json not supported by chatgpt-4o-mini-transcribe)
      formParts.push(Buffer.from(`--${boundary}\r\n`));
      formParts.push(
        Buffer.from(
          `Content-Disposition: form-data; name="response_format"\r\n\r\n`,
        ),
      );
      formParts.push(Buffer.from(`json`));
      formParts.push(Buffer.from(`\r\n`));

      // End boundary
      formParts.push(Buffer.from(`--${boundary}--\r\n`));

      const formData = Buffer.concat(formParts);

      const response = await fetch(fullEndpoint, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${apiKey}`,
          'Content-Type': `multipart/form-data; boundary=${boundary}`,
          'Content-Length': formData.length.toString(),
        },
        body: formData,
      });

      if (!response.ok) {
        let errorMessage = `Azure OpenAI transcription request failed with status ${response.status}`;
        try {
          const errorData = await response.json();
          errorMessage = errorData.error?.message || errorMessage;
        } catch {
          // If parsing error response fails, use default message
        }
        this.logger.error(errorMessage);
        throw new Error(errorMessage);
      }

      const result = await response.json();
      this.logger.log('Azure OpenAI transcription successful.');
      this.logger.debug(`Azure OpenAI API Response: ${JSON.stringify(result)}`);

      if (result.text) {
        this.logger.log(`Transcription successful: "${result.text}"`);
        return result.text;
      } else {
        this.logger.warn(
          'No transcription text received from Azure OpenAI API.',
        );
        return '';
      }
    } catch (error) {
      this.logger.error('Error during Azure OpenAI transcription:', error);
      throw error;
    }
  }

  /**
   * Transcribes audio using Azure OpenAI as primary method with Google Cloud as fallback.
   * @param audioBuffer The audio data as a Buffer.
   * @param user The authenticated user for token tracking.
   * @param recordingDuration Duration of the recording in seconds (for token calculation).
   * @returns The transcribed text, or an empty string if transcription fails.
   */
  async transcribeAudio(
    audioBuffer: Buffer,
    user: AuthenticatedUser,
    recordingDuration: number = 0,
  ): Promise<string> {
    this.logger.log(
      `Received audio buffer for transcription from user ${user.userId}, duration: ${recordingDuration}s`,
    );

    // STT model name - using Azure OpenAI Whisper model name
    const modelName = 'chatgpt-4o-mini-transcribe';
    const canonicalModelName =
      this.modelMappingService.getCanonicalModelName(modelName);

    // Check token limits before processing
    const tokenLimitCheck = await this.tokenUsageService.checkTokenLimit(
      user,
      canonicalModelName,
    );
    if (!tokenLimitCheck.allowed) {
      throw new Error(
        tokenLimitCheck.message || 'Speech transcription token limit exceeded',
      );
    }

    let transcriptionResult = '';
    let inputTokens = 0;
    let outputTokens = 0;
    let totalTokens = 0;
    let tokenSource = 'fallback';

    // Try Azure OpenAI first
    const useAzureOpenAI =
      this.configService.get<string>('USE_AZURE_OPENAI_SPEECH') !== 'false';

    if (useAzureOpenAI) {
      try {
        this.logger.log('Attempting transcription with Azure OpenAI...');
        const azureResult = await this.transcribeWithAzureOpenAI(audioBuffer);
        if (azureResult && azureResult.trim()) {
          this.logger.log(
            'Azure OpenAI transcription successful, returning result.',
          );
          transcriptionResult = azureResult;

          // Use duration-based token calculation (1 second = 1 token)
          inputTokens = Math.ceil(recordingDuration); // Round up to ensure at least 1 token for any recording
          outputTokens = 0; // Transcription is input-only, no output tokens
          totalTokens = inputTokens;
          tokenSource = 'azure_openai_duration';

          this.logger.log(
            `Azure OpenAI duration-based token calculation: ${recordingDuration}s = ${inputTokens} tokens`,
          );
        } else {
          this.logger.warn(
            'Azure OpenAI returned empty result, falling back to Google Cloud.',
          );
        }
      } catch (azureError) {
        this.logger.error(
          'Azure OpenAI transcription failed, falling back to Google Cloud:',
          azureError,
        );
      }
    }

    // Fallback to Google Cloud Speech-to-Text if Azure didn't work
    if (!transcriptionResult) {
      this.logger.log('Using Google Cloud Speech-to-Text for transcription.');
      transcriptionResult = await this.transcribeWithGoogleCloud(audioBuffer);

      // Use duration-based token calculation for Google Cloud as well
      if (transcriptionResult && transcriptionResult.trim()) {
        inputTokens = Math.ceil(recordingDuration); // Same 1 second = 1 token approach
        outputTokens = 0; // Transcription is input-only
        totalTokens = inputTokens;
        tokenSource = 'google_cloud_duration';

        this.logger.log(
          `Google Cloud duration-based token calculation: ${recordingDuration}s = ${inputTokens} tokens`,
        );
      } else {
        // No successful transcription from either provider
        inputTokens = 0;
        outputTokens = 0;
        totalTokens = 0;
        tokenSource = 'no_transcription_fallback';
      }
    }

    this.logger.log(
      `STT completed (${tokenSource}): ${inputTokens} input + ${outputTokens} output = ${totalTokens} total tokens`,
    );

    // Record token usage using real model name
    await this.tokenUsageService.updateTokenUsage({
      username: user.userId,
      modelName: canonicalModelName,
      tokenDate: new Date(),
      promptTokens: inputTokens,
      completionTokens: outputTokens,
      totalTokens: totalTokens,
      isApi: false, // This is web UI
    });

    // Check if user exceeded limit after this usage
    const postUsageCheck =
      await this.tokenUsageService.checkTokenLimitPostUsage(
        user,
        canonicalModelName,
      );
    if (postUsageCheck.exceeded) {
      this.logger.warn(postUsageCheck.message);
    }

    return transcriptionResult;
  }

  /**
   * Transcribes audio using Google Cloud Speech-to-Text with auto language detection.
   * @param audioBuffer The audio data as a Buffer.
   * @returns The transcribed text, or an empty string if transcription fails.
   */
  async transcribeWithGoogleCloud(audioBuffer: Buffer): Promise<string> {
    this.logger.log('Received audio buffer for Google Cloud transcription.');

    // Configure the request
    // TODO: Make encoding and sampleRateHertz potentially configurable or detect from input if possible
    const recognitionConfig: google.cloud.speech.v1.IRecognitionConfig = {
      // Match the format likely recorded by the browser (MediaRecorder default)
      encoding:
        google.cloud.speech.v1.RecognitionConfig.AudioEncoding.WEBM_OPUS,
      // sampleRateHertz: 48000, // Remove this - GCP detects it from WEBM_OPUS header
      languageCode: 'en-US', // Restore primary language hint
      alternativeLanguageCodes: ['en-US', 'zh-CN', 'yue-Hant-HK'], // Keep alternative languages for now
      enableAutomaticPunctuation: true,
      // model: 'telephony', // Revert model change, let Google use default based on encoding/settings
      enableWordConfidence: true,
      // useEnhanced: false,
    };

    const recognitionAudio: google.cloud.speech.v1.IRecognitionAudio = {
      content: audioBuffer.toString('base64'),
    };

    const request: google.cloud.speech.v1.IRecognizeRequest = {
      config: recognitionConfig,
      audio: recognitionAudio,
    };

    try {
      this.logger.log(
        `Sending request to Google Cloud Speech-to-Text with buffer size: ${audioBuffer.length}...`,
      ); // Log buffer size
      const [response] = await this.speechClient.recognize(request);
      this.logger.log(`Received response from Google Cloud Speech-to-Text.`); // Keep simpler log
      this.logger.debug(`Google API Response: ${JSON.stringify(response)}`); // Log the full response for debugging

      if (response.results && response.results.length > 0) {
        const transcription = response.results
          .map((result) => result.alternatives?.[0]?.transcript || '')
          .join('\n');
        this.logger.log(`Transcription successful: "${transcription}"`);
        return transcription;
      } else {
        this.logger.warn('No transcription results received from Google API.');
        return '';
      }
    } catch (error) {
      this.logger.error(
        'Error during Google Cloud Speech-to-Text API call:',
        error,
      );
      // Consider throwing a specific exception or returning an error indicator
      return ''; // Return empty string on error for now
    }
  }
}
