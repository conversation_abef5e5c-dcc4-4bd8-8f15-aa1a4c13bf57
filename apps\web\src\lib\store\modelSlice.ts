import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from './store';
import { apiSlice } from './apiSlice'; // Import apiSlice to handle its actions
import _ from 'lodash'; // Import lodash
import { GptModel } from '../types/common'; // Import the shared GptModel type

// Remove the local GptModel interface definition

const LOCAL_STORAGE_MODEL_KEY = 'selectedGptModelName'; // Use model_name

interface ModelState {
  availableModels: GptModel[];
  currentGptModel: GptModel | null;
  shouldUpdateGptModelList: boolean;
  shouldUpdateUsage: boolean;
}

// Helper function to load initial state from localStorage (Not used for currentGptModel anymore)
// const loadState = <T>(key: string, defaultValue: T): T => { ... };

const initialState: ModelState = {
  availableModels: [],
  currentGptModel: null, // Initialize as null, will be set after fetching models
  shouldUpdateGptModelList: true,
  shouldUpdateUsage: true,
};

const modelSlice = createSlice({
  name: 'model',
  initialState,
  reducers: {
    // setAvailableModels is handled by extraReducer now
    setModel: (state, action: PayloadAction<GptModel | null>) => {
      state.currentGptModel = action.payload;
      if (typeof window !== 'undefined') {
        if (action.payload) {
          // Store model_name when a model is explicitly selected
          localStorage.setItem(
            LOCAL_STORAGE_MODEL_KEY,
            action.payload.model_name,
          );
        } else {
          localStorage.removeItem(LOCAL_STORAGE_MODEL_KEY);
        }
      }
    },
    triggerUpdateModelList: (state) => {
      state.shouldUpdateGptModelList = true;
    },
    triggerUsageUpdate: (state) => {
      state.shouldUpdateUsage = true;
    },
    resetUsageUpdateTrigger: (state) => {
      state.shouldUpdateUsage = false;
    },
  },
  // Correct placement for extraReducers
  extraReducers: (builder) => {
    // Handle successful fetching of models from the API
    builder.addMatcher(
      apiSlice.endpoints.getModels.matchFulfilled,
      (state: ModelState, { payload }: PayloadAction<GptModel[]>) => {
        // Add explicit types
        console.log('Fetched models:', payload);
        state.availableModels = payload;
        state.shouldUpdateGptModelList = false;

        // Check if a model is already selected (e.g., by layout reading URL param)
        if (state.currentGptModel) {
          console.log(
            '[ModelSlice] Model already set, skipping default selection logic.',
          );
          return; // Don't override the already selected model
        }

        // Determine initial/default model selection logic
        let modelToSelect: GptModel | null = null;
        let storedModelName: string | null = null;

        // 1. Try loading from local storage
        if (typeof window !== 'undefined') {
          storedModelName = localStorage.getItem(LOCAL_STORAGE_MODEL_KEY);
          if (storedModelName) {
            const storedModel = payload.find(
              (m: GptModel) =>
                m.model_name === storedModelName &&
                m.availability_status === 'A',
            ); // Add type for m
            if (storedModel) {
              modelToSelect = storedModel;
              console.log(
                'Loaded available model from localStorage:',
                JSON.stringify(modelToSelect),
              );
            } else {
              console.log(
                `Stored model "${storedModelName}" not found or unavailable in fetched list. Removing from localStorage.`,
              );
              localStorage.removeItem(LOCAL_STORAGE_MODEL_KEY);
            }
          }
        }

        // 2. If no valid stored model, find the first available model in the fetched list
        if (!modelToSelect && payload.length > 0) {
          // Sort by seq before finding the first available
          const sortedPayload = [...payload].sort(
            (a, b) => (a.seq ?? Infinity) - (b.seq ?? Infinity),
          );
          modelToSelect =
            sortedPayload.find(
              (m: GptModel) => m.availability_status === 'A',
            ) || null; // Add type for m
          if (modelToSelect) {
            console.log(
              'Setting first available model as default:',
              JSON.stringify(modelToSelect),
            );
          }
        }

        // 3. If still no model (e.g., none are available), fall back to the absolute first model (sorted by seq) or null
        if (!modelToSelect && payload.length > 0) {
          const sortedPayload = [...payload].sort(
            (a, b) => (a.seq ?? Infinity) - (b.seq ?? Infinity),
          );
          modelToSelect = sortedPayload[0]; // Use the first model after sorting
          console.warn(
            "No available ('A') models found. Falling back to first model in list:",
            JSON.stringify(modelToSelect),
          );
        }

        // 4. Update state and potentially local storage
        // Only update if the selected model is actually different from the current state
        if (!_.isEqual(state.currentGptModel, modelToSelect)) {
          state.currentGptModel = modelToSelect;
          if (typeof window !== 'undefined') {
            if (modelToSelect) {
              localStorage.setItem(
                LOCAL_STORAGE_MODEL_KEY,
                modelToSelect.model_name,
              );
            } else {
              localStorage.removeItem(LOCAL_STORAGE_MODEL_KEY);
            }
          }
        } else {
          console.log(
            'Current model selection remains unchanged:',
            JSON.stringify(state.currentGptModel),
          );
        }
      },
    );
  },
});

export const {
  // setAvailableModels is handled by extraReducer now
  setModel,
  triggerUpdateModelList,
  triggerUsageUpdate,
  resetUsageUpdateTrigger,
} = modelSlice.actions;

// Selectors
export const selectAvailableModels = (state: RootState): GptModel[] =>
  state.model.availableModels;
export const selectCurrentGptModel = (state: RootState): GptModel | null =>
  state.model.currentGptModel;
export const selectShouldUpdateModelList = (state: RootState): boolean =>
  state.model.shouldUpdateGptModelList;
export const selectShouldUpdateUsage = (state: RootState): boolean =>
  state.model.shouldUpdateUsage;

export default modelSlice.reducer;
