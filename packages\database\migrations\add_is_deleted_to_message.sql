-- Migration: Add is_deleted field to message table for soft delete functionality
-- This field will be used to mark messages as deleted during regeneration

-- Add is_deleted column to message table
ALTER TABLE [dbo].[message]
ADD [is_deleted] BIT NOT NULL DEFAULT 0;

-- Add index on is_deleted for better query performance
CREATE NONCLUSTERED INDEX [IX_message_is_deleted]
ON [dbo].[message] ([is_deleted])
INCLUDE ([conversation_id], [sender], [create_dt]);

-- Update stored procedure to filter out deleted messages
-- NOTE: The stored procedure sp_cvst_GetDecryptedMessagesByConversationUUID needs to be updated to:
-- 1. Include is_deleted in the SELECT statement
-- 2. Add WHERE is_deleted = 0 to filter out soft-deleted messages

-- Example update for the stored procedure (exact implementation depends on current SP):
/*
ALTER PROCEDURE [dbo].[sp_cvst_GetDecryptedMessagesByConversationUUID]
    @conversation_uuid UNIQUEIDENTIFIER,
    @encryption_key_name <PERSON><PERSON><PERSON><PERSON><PERSON>(128),
    @decryption_cert_name NVA<PERSON>HA<PERSON>(128)
AS
BEGIN
    -- Existing SP logic...
    -- Add is_deleted to SELECT and WHERE clause:
    
    SELECT 
        message_uuid,
        sender,
        create_dt,
        model_name,
        reaction,
        token_spent,
        prompt_order,
        content,
        sources_json,
        is_deleted  -- Add this field
    FROM [message_decrypted_view] -- or whatever the source is
    WHERE conversation_uuid = @conversation_uuid
        AND is_deleted = 0  -- Filter out soft-deleted messages
    ORDER BY create_dt;
END
*/