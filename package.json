{"name": "hkbu-genai-platform", "private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev --parallel", "lint": "turbo run lint", "test": "turbo run test", "clean": "turbo run clean && rd /s /q node_modules", "format": "prettier --write \"**/*.{ts,tsx,md,json}\"", "db:generate": "turbo run db:generate --filter=@my-chat-app/db", "db:migrate:dev": "turbo run db:migrate:dev --filter=@my-chat-app/db", "db:studio": "turbo run db:studio --filter=@my-chat-app/db", "prepare": "husky install"}, "devDependencies": {"@hkbu-genai-platform/eslint-config": "workspace:*", "@hkbu-genai-platform/tsconfig": "workspace:*", "eslint": "^8.x.x", "prettier": "^3.x.x", "turbo": "^1.x.x", "typescript": "^5.x.x", "husky": "^9.1.4", "lint-staged": "^15.2.7"}, "lint-staged": {"apps/web/**/*.{ts,tsx}": ["pnpm --filter web lint", "pnpm --dir apps/web exec bash -c tsc --noEmit"], "apps/api/**/*.{ts,tsx}": "pnpm --filter api lint"}, "packageManager": "pnpm@10.7.1", "dependencies": {"@types/react-syntax-highlighter": "^15.5.13", "@types/uuid": "^10.0.0", "next-auth": "^4.24.11", "react-icons": "^5.5.0", "react-toastify": "^11.0.5", "uuid": "^11.1.0"}}