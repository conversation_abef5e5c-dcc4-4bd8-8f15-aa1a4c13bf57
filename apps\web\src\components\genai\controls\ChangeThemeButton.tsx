import { useAppDispatch, useAppSelector } from '../../../lib/store/hooks'; // Adjust path as needed
import { Theme, selectTheme, setTheme } from '../../../lib/store/uiSlice'; // Import from uiSlice

export function ChangeThemeButton() {
  // Use Redux hooks
  const dispatch = useAppDispatch();
  const currentTheme = useAppSelector(selectTheme);

  const onClick = () => {
    // Dispatch the setTheme action, uiSlice reducer handles localStorage and classList
    dispatch(setTheme(currentTheme === Theme.dark ? Theme.light : Theme.dark));
  };

  // Keep the environment variable check
  return process.env.NEXT_PUBLIC_CHANGE_THEME_ENABLE === 'true' ? (
    <div className="relative inline-block text-left">
      <button
        className="inline-flex w-full items-center justify-center gap-x-1.5 rounded-3xl px-3 py-2 text-sm font-semibold text-gray-900 dark:text-white"
        onClick={onClick}
      >
        {currentTheme === Theme.dark ? ( // Use currentTheme from Redux state
          // Dark theme icon (Original SVG)
          <svg
            className="h-6 w-6 lg:h-8 lg:w-8 stroke-current fill-current"
            viewBox="0 0 48 48"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M41.916 30.6501C42.324 29.6781 41.158 28.8501 40.18 29.2821C38.2248 30.1393 36.1128 30.5807 33.978 30.5781C25.608 30.5781 18.824 23.9301 18.824 15.7281C18.8231 12.9591 19.6097 10.247 21.092 7.90812C21.66 7.01212 20.978 5.77212 19.938 6.03612C11.92 8.08212 6 15.2261 6 23.7241C6 33.8181 14.35 42.0001 24.652 42.0001C32.452 42.0001 39.132 37.3101 41.916 30.6501Z"
              // fill="black"
            />
            <path
              d="M31.222 6.20559C30.162 5.49759 28.898 6.76159 29.604 7.82159L30.864 9.71159C31.3757 10.4777 31.6487 11.3783 31.6487 12.2996C31.6487 13.2209 31.3757 14.1215 30.864 14.8876L29.604 16.7776C28.898 17.8376 30.164 19.1016 31.224 18.3936L33.112 17.1336C33.8782 16.6219 34.7788 16.3489 35.7 16.3489C36.6213 16.3489 37.5219 16.6219 38.288 17.1336L40.178 18.3936C41.238 19.1016 42.502 17.8376 41.794 16.7776L40.534 14.8876C40.0224 14.1215 39.7493 13.2209 39.7493 12.2996C39.7493 11.3783 40.0224 10.4777 40.534 9.71159L41.794 7.82159C42.502 6.76159 41.238 5.49759 40.176 6.20559L38.288 7.46559C37.5219 7.97723 36.6213 8.2503 35.7 8.2503C34.7788 8.2503 33.8782 7.97723 33.112 7.46559L31.222 6.20559Z"
              // fill="black"
            />
          </svg>
        ) : (
          // Light theme icon
          <svg
            className="h-6 w-6 lg:h-8 lg:w-8"
            viewBox="0 0 33 33"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M16.5002 25.8002C21.6364 25.8002 25.8002 21.6364 25.8002 16.5002C25.8002 11.3639 21.6364 7.2002 16.5002 7.2002C11.3639 7.2002 7.2002 11.3639 7.2002 16.5002C7.2002 21.6364 11.3639 25.8002 16.5002 25.8002Z"
              fill="black"
              stroke="black"
              strokeWidth="1.5"
            />
            <path
              d="M16.5 1V2.55ZM16.5 30.45V32ZM32 16.5H30.45ZM2.55 16.5H1ZM27.4585 5.5415L26.8509 6.15065ZM6.1491 26.8509L5.53995 27.46ZM27.4585 27.4585L26.8509 26.8493ZM6.1491 6.1491L5.53995 5.53995Z"
              fill="black"
            />
            <path
              d="M16.5 1V2.55M16.5 30.45V32M32 16.5H30.45M2.55 16.5H1M27.4585 5.5415L26.8509 6.15065M6.1491 26.8509L5.53995 27.46M27.4585 27.4585L26.8509 26.8493M6.1491 6.1491L5.53995 5.53995"
              stroke="black"
              strokeWidth="1.5"
              strokeLinecap="round"
            />
          </svg>
        )}
      </button>
    </div>
  ) : (
    <></>
  );
}
