import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty } from 'class-validator';

export class RewritePromptDto {
  @ApiProperty({
    description: 'The prompt to be rewritten.',
    example: 'what is the capital of france',
  })
  @IsString()
  @IsNotEmpty()
  prompt!: string;
}

export class GeneratePromptDto {
  @ApiProperty({
    description: 'The idea to generate a prompt from.',
    example: 'a function that calculates the factorial of a number',
  })
  @IsString()
  @IsNotEmpty()
  idea!: string;
}

export class PromptRewriteResponseDto {
  @ApiProperty({
    description: 'The rewritten or generated prompt.',
    example: 'What is the capital city of France?',
  })
  @IsString()
  @IsNotEmpty()
  prompt!: string;
}
