#!/usr/bin/env python3
"""
Test script to verify RPM (requests per minute) rate limiting is still working
after removing the rate limit UI and API endpoints.
"""

import requests
import time
import json
import os
import asyncio
import aiohttp
from datetime import datetime
from dotenv import load_dotenv
from concurrent.futures import ThreadPoolExecutor, as_completed

# Load configuration
load_dotenv()

def load_config():
    """Load configuration from files"""
    # Load models from config/models.json
    models_path = os.path.join(os.path.dirname(__file__), 'config', 'models.json')
    with open(models_path, 'r') as f:
        models_config = json.load(f)
    
    # Extract chat models (exclude embedding models)
    chat_models = [
        model for model in models_config['models'] 
        if model.get('model_type') != 'embedding'
    ]
    
    config = {
        'base_url': os.getenv('BASE_URL', 'http://localhost:3003/api/v0'),
        'api_key': os.getenv('API_KEY', ''),
        'models': chat_models,
        'test_requests_count': 65,
        'request_delay': 0.05,  # Very fast for rate limit testing
        'timeout': 15,  # Longer timeout for LLM responses
        'fast_test_requests': 65  # Same as full test since we need to exceed 60 to test rate limiting
    }
    
    return config

def create_payload_for_model(model):
    """Create appropriate payload based on model capabilities"""
    # Base payload
    payload = {
        "messages": [{"role": "user", "content": "Hi"}],
        "stream": False
    }
    
    # Add parameters based on model support
    if model.get('supports_temperature', True):
        payload["temperature"] = 0.1
    
    # Always add max_tokens for faster responses, but some models might not support it
    # We'll handle errors gracefully in the request
    payload["max_tokens"] = 5
    
    return payload

def make_single_request(url, headers, request_id, timeout=15, model_name=None, model_config=None):
    """Make a single request and return the result"""
    try:
        start_time = time.time()
        if '/chat/completions' in url:
            # POST request for chat completions
            if model_config:
                payload = create_payload_for_model(model_config)
            else:
                # Fallback minimal payload
                payload = {
                    "messages": [{"role": "user", "content": "Hi"}],
                    "stream": False
                }
            response = requests.post(url, headers=headers, json=payload, timeout=timeout)
        else:
            # GET request for models
            response = requests.get(url, headers=headers, timeout=timeout)
        end_time = time.time()
        
        return {
            'request_id': request_id,
            'model_name': model_name,
            'status_code': response.status_code,
            'response_time': end_time - start_time,
            'timestamp': datetime.now().strftime("%H:%M:%S.%f")[:-3],
            'error': None,
            'response_text': response.text[:500] if response.status_code != 200 else None
        }
    except Exception as e:
        return {
            'request_id': request_id,
            'model_name': model_name,
            'status_code': 0,
            'response_time': 0,
            'timestamp': datetime.now().strftime("%H:%M:%S.%f")[:-3],
            'error': str(e)[:300],
            'response_text': None
        }

def test_rate_limit_concurrent(config, max_requests=70, max_workers=20):
    """Test rate limiting by sending concurrent requests"""
    endpoint = "/rest/models"
    url = f"{config['base_url']}{endpoint}"
    headers = {"api-key": config['api_key']}
    
    print(f"\n{'='*60}")
    print(f"Testing rate limit for: {endpoint}")
    print(f"Sending {max_requests} concurrent requests with {max_workers} workers...")
    print(f"{'='*60}")
    
    successful_requests = 0
    rate_limited_requests = 0
    errors = 0
    results = []
    
    start_time = time.time()
    
    # Use ThreadPoolExecutor for concurrent requests
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all requests
        future_to_id = {
            executor.submit(make_single_request, url, headers, i+1, config['timeout']): i+1 
            for i in range(max_requests)
        }
        
        # Collect results as they complete
        for future in as_completed(future_to_id):
            result = future.result()
            results.append(result)
            
            if result['error']:
                errors += 1
                print(f"[{result['timestamp']}] Request {result['request_id']:2d}: EXCEPTION - {result['error']}")
            elif result['status_code'] == 200:
                successful_requests += 1
                print(f"[{result['timestamp']}] Request {result['request_id']:2d}: SUCCESS (200) - {result['response_time']:.2f}s")
            elif result['status_code'] == 429:
                rate_limited_requests += 1
                print(f"[{result['timestamp']}] Request {result['request_id']:2d}: RATE LIMITED (429) - {result['response_time']:.2f}s")
            else:
                errors += 1
                print(f"[{result['timestamp']}] Request {result['request_id']:2d}: ERROR ({result['status_code']}) - {result['response_time']:.2f}s")
                if result['response_text']:
                    print(f"                     Error details: {result['response_text']}")
    
    end_time = time.time()
    duration = end_time - start_time
    
    # Sort results by request_id for summary
    results.sort(key=lambda x: x['request_id'])
    
    # Summary
    print(f"\n{'='*60}")
    print(f"SUMMARY for {endpoint} (Concurrent)")
    print(f"{'='*60}")
    print(f"Total requests sent: {max_requests}")
    print(f"Successful requests: {successful_requests}")
    print(f"Rate limited requests: {rate_limited_requests}")
    print(f"Errors: {errors}")
    print(f"Total duration: {duration:.2f} seconds")
    print(f"Average rate: {max_requests/duration:.2f} requests/second")
    
    if rate_limited_requests > 0:
        print(f"RATE LIMITING IS WORKING! Got {rate_limited_requests} rate limit responses")
        
        # Show when rate limiting started
        rate_limited_results = [r for r in results if r['status_code'] == 429]
        if rate_limited_results:
            first_rate_limit = min(rate_limited_results, key=lambda x: x['request_id'])
            print(f"First rate limit at request {first_rate_limit['request_id']}")
    else:
        print(f"NO RATE LIMITING DETECTED - All requests succeeded")
    
    return {
        'endpoint': endpoint,
        'total': max_requests,
        'successful': successful_requests,
        'rate_limited': rate_limited_requests,
        'errors': errors,
        'duration': duration,
        'test_type': 'concurrent'
    }

def test_all_models_concurrent(config, requests_per_model=70, max_workers=25):
    """Test rate limiting by sending concurrent requests to all available models (per-model rate limiting)"""
    print(f"\n{'='*60}")
    print("Testing PER-MODEL rate limiting across ALL MODELS concurrently")
    print(f"Models: {len(config['models'])}, Requests per model: {requests_per_model}")
    print(f"Total requests: {len(config['models']) * requests_per_model}")
    print("Note: Rate limit is 60 requests/minute PER MODEL")
    print(f"{'='*60}")
    
    headers = {
        "Content-Type": "application/json",
        "api-key": config['api_key']
    }
    
    successful_requests = 0
    rate_limited_requests = 0
    errors = 0
    results = []
    request_tasks = []
    
    # Create request tasks for all models
    request_id = 1
    for model in config['models']:
        # Construct URL for this model
        endpoint = f"/rest/deployments/{model['deployment_name']}/chat/completions"
        url = f"{config['base_url']}{endpoint}"
        # API version parameter is required (can be empty string)
        api_version = model.get('api_version', '')
        url += f"?api-version={api_version}"
        
        # Create multiple requests for this model
        for i in range(requests_per_model):
            request_tasks.append({
                'url': url,
                'headers': headers,
                'request_id': request_id,
                'model_name': model['name'],
                'model_config': model,
                'timeout': config['timeout']
            })
            request_id += 1
    
    start_time = time.time()
    
    # Execute all requests concurrently
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all requests
        future_to_task = {
            executor.submit(
                make_single_request, 
                task['url'], 
                task['headers'], 
                task['request_id'], 
                task['timeout'],
                task['model_name'],
                task['model_config']
            ): task 
            for task in request_tasks
        }
        
        # Collect results as they complete
        for future in as_completed(future_to_task):
            result = future.result()
            results.append(result)
            
            model_display = f"{result['model_name']}" if result['model_name'] else "unknown"
            
            if result['error']:
                errors += 1
                print(f"[{result['timestamp']}] Request {result['request_id']:2d} ({model_display}): EXCEPTION - {result['error']}")
            elif result['status_code'] == 200:
                successful_requests += 1
                print(f"[{result['timestamp']}] Request {result['request_id']:2d} ({model_display}): SUCCESS (200) - {result['response_time']:.2f}s")
            elif result['status_code'] == 429:
                rate_limited_requests += 1
                print(f"[{result['timestamp']}] Request {result['request_id']:2d} ({model_display}): RATE LIMITED (429) - {result['response_time']:.2f}s")
            else:
                errors += 1
                print(f"[{result['timestamp']}] Request {result['request_id']:2d} ({model_display}): ERROR ({result['status_code']}) - {result['response_time']:.2f}s")
                if result['response_text']:
                    print(f"                     Error details: {result['response_text']}")
    
    end_time = time.time()
    duration = end_time - start_time
    total_requests = len(request_tasks)
    
    # Sort results by request_id for analysis
    results.sort(key=lambda x: x['request_id'])
    
    # Analyze by model
    model_stats = {}
    for result in results:
        model_name = result['model_name'] or 'unknown'
        if model_name not in model_stats:
            model_stats[model_name] = {'success': 0, 'rate_limited': 0, 'errors': 0}
        
        if result['error']:
            model_stats[model_name]['errors'] += 1
        elif result['status_code'] == 200:
            model_stats[model_name]['success'] += 1
        elif result['status_code'] == 429:
            model_stats[model_name]['rate_limited'] += 1
        else:
            model_stats[model_name]['errors'] += 1
    
    # Summary
    print(f"\n{'='*60}")
    print(f"SUMMARY for ALL MODELS (Concurrent)")
    print(f"{'='*60}")
    print(f"Total requests sent: {total_requests}")
    print(f"Successful requests: {successful_requests}")
    print(f"Rate limited requests: {rate_limited_requests}")
    print(f"Errors: {errors}")
    print(f"Total duration: {duration:.2f} seconds")
    print(f"Average rate: {total_requests/duration:.2f} requests/second")
    
    if rate_limited_requests > 0:
        print(f"RATE LIMITING IS WORKING! Got {rate_limited_requests} rate limit responses")
        
        # Show when rate limiting started
        rate_limited_results = [r for r in results if r['status_code'] == 429]
        if rate_limited_results:
            first_rate_limit = min(rate_limited_results, key=lambda x: x['request_id'])
            print(f"First rate limit at request {first_rate_limit['request_id']} ({first_rate_limit['model_name']})")
    else:
        print(f"NO RATE LIMITING DETECTED - All requests succeeded")
    
    # Per-model breakdown
    print(f"\nPer-model breakdown:")
    for model_name, stats in model_stats.items():
        total_model_requests = sum(stats.values())
        print(f"  {model_name}: {stats['success']} success, {stats['rate_limited']} rate limited, {stats['errors']} errors (total: {total_model_requests})")
    
    return {
        'endpoint': 'all_models_chat_completions',
        'total': total_requests,
        'successful': successful_requests,
        'rate_limited': rate_limited_requests,
        'errors': errors,
        'duration': duration,
        'test_type': 'all_models_concurrent',
        'model_stats': model_stats
    }

def test_single_model_intensive(config, model, requests=70, max_workers=25):
    """Test rate limiting intensively on a single model"""
    endpoint = f"/rest/deployments/{model['deployment_name']}/chat/completions"
    url = f"{config['base_url']}{endpoint}"
    # API version parameter is required (can be empty string)
    api_version = model.get('api_version', '')
    url += f"?api-version={api_version}"
    
    print(f"\n{'='*60}")
    print(f"Testing INTENSIVE rate limiting for: {model['name']}")
    print(f"Model: {model['deployment_name']}")
    print(f"Sending {requests} concurrent requests")
    print("Note: Rate limit is 60 requests/minute per model")
    print(f"{'='*60}")
    
    headers = {
        "Content-Type": "application/json",
        "api-key": config['api_key']
    }
    
    successful_requests = 0
    rate_limited_requests = 0
    errors = 0
    results = []
    
    start_time = time.time()
    
    # Create request tasks for this model
    request_tasks = []
    for i in range(requests):
        request_tasks.append({
            'url': url,
            'headers': headers,
            'request_id': i + 1,
            'model_name': model['name'],
            'model_config': model,
            'timeout': config['timeout']
        })
    
    # Execute all requests concurrently
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all requests
        future_to_task = {
            executor.submit(
                make_single_request, 
                task['url'], 
                task['headers'], 
                task['request_id'], 
                task['timeout'],
                task['model_name'],
                task['model_config']
            ): task 
            for task in request_tasks
        }
        
        # Collect results as they complete
        for future in as_completed(future_to_task):
            result = future.result()
            results.append(result)
            
            if result['error']:
                errors += 1
                print(f"[{result['timestamp']}] Request {result['request_id']:2d}: EXCEPTION - {result['error']}")
            elif result['status_code'] == 200:
                successful_requests += 1
                print(f"[{result['timestamp']}] Request {result['request_id']:2d}: SUCCESS (200) - {result['response_time']:.2f}s")
            elif result['status_code'] == 429:
                rate_limited_requests += 1
                print(f"[{result['timestamp']}] Request {result['request_id']:2d}: RATE LIMITED (429) - {result['response_time']:.2f}s")
            else:
                errors += 1
                print(f"[{result['timestamp']}] Request {result['request_id']:2d}: ERROR ({result['status_code']}) - {result['response_time']:.2f}s")
                if result['response_text']:
                    print(f"                     Error details: {result['response_text']}")
    
    end_time = time.time()
    duration = end_time - start_time
    
    # Sort results by request_id for analysis
    results.sort(key=lambda x: x['request_id'])
    
    # Summary
    print(f"\n{'='*60}")
    print(f"SUMMARY for {model['name']} (Intensive Test)")
    print(f"{'='*60}")
    print(f"Total requests sent: {requests}")
    print(f"Successful requests: {successful_requests}")
    print(f"Rate limited requests: {rate_limited_requests}")
    print(f"Errors: {errors}")
    print(f"Total duration: {duration:.2f} seconds")
    print(f"Average rate: {requests/duration:.2f} requests/second")
    
    if rate_limited_requests > 0:
        print(f"RATE LIMITING IS WORKING! Got {rate_limited_requests} rate limit responses")
        
        # Show when rate limiting started
        rate_limited_results = [r for r in results if r['status_code'] == 429]
        if rate_limited_results:
            first_rate_limit = min(rate_limited_results, key=lambda x: x['request_id'])
            print(f"First rate limit at request {first_rate_limit['request_id']}")
    else:
        print(f"NO RATE LIMITING DETECTED - All requests succeeded")
    
    return {
        'endpoint': f"{model['name']}_intensive",
        'model': model['name'],
        'total': requests,
        'successful': successful_requests,
        'rate_limited': rate_limited_requests,
        'errors': errors,
        'duration': duration,
        'test_type': 'single_model_intensive'
    }

def test_rate_limit_models(config, max_requests=None, delay=None):
    """Test rate limiting on the models endpoint"""
    if max_requests is None:
        max_requests = config['test_requests_count']
    if delay is None:
        delay = config['request_delay']
        
    endpoint = "/rest/models"
    print(f"\n{'='*60}")
    print(f"Testing rate limit for: {endpoint}")
    print(f"Sending {max_requests} requests with {delay}s delay...")
    print(f"{'='*60}")
    
    headers = {
        "api-key": config['api_key']
    }
    
    successful_requests = 0
    rate_limited_requests = 0
    errors = []
    
    start_time = time.time()
    
    for i in range(max_requests):
        try:
            response = requests.get(
                f"{config['base_url']}{endpoint}", 
                headers=headers,
                timeout=config['timeout']
            )
            
            current_time = datetime.now().strftime("%H:%M:%S.%f")[:-3]
            
            if response.status_code == 200:
                successful_requests += 1
                print(f"[{current_time}] Request {i+1:2d}: SUCCESS (200)")
            elif response.status_code == 429:
                rate_limited_requests += 1
                print(f"[{current_time}] Request {i+1:2d}: RATE LIMITED (429)")
                try:
                    error_data = response.json()
                    print(f"                     Rate limit message: {error_data.get('message', 'No message')}")
                except:
                    print(f"                     Raw response: {response.text[:100]}")
            else:
                errors.append({
                    'request': i+1,
                    'status': response.status_code,
                    'response': response.text[:200]
                })
                print(f"[{current_time}] Request {i+1:2d}: ERROR ({response.status_code})")
                print(f"                     Error: {response.text[:100]}")
            
        except requests.exceptions.RequestException as e:
            errors.append({
                'request': i+1,
                'error': str(e)
            })
            print(f"[{datetime.now().strftime('%H:%M:%S.%f')[:-3]}] Request {i+1:2d}: EXCEPTION - {str(e)[:100]}")
        
        if delay > 0:
            time.sleep(delay)
    
    end_time = time.time()
    duration = end_time - start_time
    
    # Summary
    print(f"\n{'='*60}")
    print(f"SUMMARY for {endpoint}")
    print(f"{'='*60}")
    print(f"Total requests sent: {max_requests}")
    print(f"Successful requests: {successful_requests}")
    print(f"Rate limited requests: {rate_limited_requests}")
    print(f"Other errors: {len(errors)}")
    print(f"Total duration: {duration:.2f} seconds")
    print(f"Average rate: {max_requests/duration:.2f} requests/second")
    
    if rate_limited_requests > 0:
        print(f"RATE LIMITING IS WORKING! Got {rate_limited_requests} rate limit responses")
    else:
        print(f"NO RATE LIMITING DETECTED - All requests succeeded")
    
    if errors:
        print(f"\nOther errors encountered:")
        for error in errors[:5]:  # Show first 5 errors
            print(f"  - Request {error['request']}: {error}")
    
    return {
        'endpoint': endpoint,
        'total': max_requests,
        'successful': successful_requests,
        'rate_limited': rate_limited_requests,
        'errors': len(errors),
        'duration': duration
    }

def test_rate_limit_chat_completions(config, model, max_requests=None, delay=None):
    """Test rate limiting for chat completions with a specific model"""
    if max_requests is None:
        max_requests = config['test_requests_count']
    if delay is None:
        delay = config['request_delay']
        
    # Construct the endpoint with model deployment name
    endpoint = f"/rest/deployments/{model['deployment_name']}/chat/completions"
    print(f"\n{'='*60}")
    print(f"Testing rate limit for: {endpoint}")
    print(f"Model: {model['name']} ({model['deployment_name']})")
    print(f"Sending {max_requests} requests with {delay}s delay...")
    print(f"{'='*60}")
    
    headers = {
        "Content-Type": "application/json",
        "api-key": config['api_key']
    }
    
    # Create payload based on model capabilities
    payload = create_payload_for_model(model)
    
    successful_requests = 0
    rate_limited_requests = 0
    errors = []
    
    start_time = time.time()
    
    for i in range(max_requests):
        try:
            # Add api-version query parameter (required, can be empty)
            url = f"{config['base_url']}{endpoint}"
            api_version = model.get('api_version', '')
            url += f"?api-version={api_version}"
            
            response = requests.post(
                url,
                headers=headers, 
                json=payload,
                timeout=config['timeout']
            )
            
            current_time = datetime.now().strftime("%H:%M:%S.%f")[:-3]
            
            if response.status_code == 200:
                successful_requests += 1
                print(f"[{current_time}] Request {i+1:2d}: SUCCESS (200)")
            elif response.status_code == 429:
                rate_limited_requests += 1
                print(f"[{current_time}] Request {i+1:2d}: RATE LIMITED (429)")
                try:
                    error_data = response.json()
                    print(f"                     Rate limit message: {error_data.get('message', 'No message')}")
                except:
                    print(f"                     Raw response: {response.text[:100]}")
            else:
                errors.append({
                    'request': i+1,
                    'status': response.status_code,
                    'response': response.text[:200]
                })
                print(f"[{current_time}] Request {i+1:2d}: ERROR ({response.status_code})")
                print(f"                     Error: {response.text[:100]}")
            
        except requests.exceptions.RequestException as e:
            errors.append({
                'request': i+1,
                'error': str(e)
            })
            print(f"[{datetime.now().strftime('%H:%M:%S.%f')[:-3]}] Request {i+1:2d}: EXCEPTION - {str(e)[:100]}")
        
        if delay > 0:
            time.sleep(delay)
    
    end_time = time.time()
    duration = end_time - start_time
    
    # Summary
    print(f"\n{'='*60}")
    print(f"SUMMARY for {model['name']} chat completions")
    print(f"{'='*60}")
    print(f"Total requests sent: {max_requests}")
    print(f"Successful requests: {successful_requests}")
    print(f"Rate limited requests: {rate_limited_requests}")
    print(f"Other errors: {len(errors)}")
    print(f"Total duration: {duration:.2f} seconds")
    print(f"Average rate: {max_requests/duration:.2f} requests/second")
    
    if rate_limited_requests > 0:
        print(f"RATE LIMITING IS WORKING! Got {rate_limited_requests} rate limit responses")
    else:
        print(f"NO RATE LIMITING DETECTED - All requests succeeded")
    
    if errors:
        print(f"\nOther errors encountered:")
        for error in errors[:5]:  # Show first 5 errors
            print(f"  - Request {error['request']}: {error}")
    
    return {
        'endpoint': endpoint,
        'model': model['name'],
        'total': max_requests,
        'successful': successful_requests,
        'rate_limited': rate_limited_requests,
        'errors': len(errors),
        'duration': duration
    }

def test_specific_model_rate_limit(config):
    """Test rate limiting for specific models with fewer requests"""
    print(f"\n{'='*60}")
    print("Testing model-specific rate limiting...")
    print(f"{'='*60}")
    
    # Test with first 3 available models, but with fewer requests
    test_models = config['models'][:3]
    results = []
    
    for model in test_models:
        print(f"\nTesting model: {model['name']}")
        result = test_rate_limit_chat_completions(config, model, max_requests=5, delay=0.1)
        results.append(result)
        time.sleep(1)  # Wait between model tests
    
    return results

def main():
    print("RPM Rate Limit Test Script")
    print("=" * 60)
    print("This script will test if rate limiting is still working")
    print("after removing the rate limit UI and API endpoints.")
    print("=" * 60)
    
    # Ask user for test mode
    print("\nSelect test mode:")
    print("1. Fast test (/rest/models only - 70 concurrent requests, ~5-10 seconds)")
    print("2. Single model intensive (70 requests to ONE model - tests per-model rate limiting, ~10-15 seconds)")
    print("3. All models test (70 requests to EACH model - tests per-model rate limiting across all models)")
    print("4. Standard test (sequential requests to models + one chat model)")
    print("5. Full test (sequential requests to models + multiple chat models)")
    
    while True:
        try:
            choice = input("\nEnter choice (1-5) or press Enter for fast test: ").strip()
            if choice == "" or choice == "1":
                test_mode = "fast"
                break
            elif choice == "2":
                test_mode = "single_intensive"
                break
            elif choice == "3":
                test_mode = "all_models"
                break
            elif choice == "4":
                test_mode = "standard"
                break
            elif choice == "5":
                test_mode = "full"
                break
            else:
                print("Invalid choice. Please enter 1, 2, 3, 4, or 5.")
        except KeyboardInterrupt:
            print("\nTest cancelled.")
            return
    
    # Load configuration
    try:
        config = load_config()
    except Exception as e:
        print(f"ERROR: Failed to load configuration: {e}")
        return
    
    # Check if API key is set
    if not config['api_key']:
        print("ERROR: API key not found in .env file")
        print("   Make sure API_KEY is set in api_test/.env")
        return
    
    print(f"Using API endpoint: {config['base_url']}")
    print(f"Testing with {len(config['models'])} available models")
    print(f"Test mode: {test_mode}")
    
    results = []
    
    if test_mode == "single_intensive":
        # Test one model intensively to check per-model rate limiting
        if config['models']:
            first_model = config['models'][8]
            print(f"Testing first available model: {first_model['name']}")
            models_result = test_single_model_intensive(config, first_model, requests=70, max_workers=25)
            results.append(models_result)
        else:
            print("ERROR: No models available for testing")
            return
    elif test_mode == "all_models":
        # Test all models concurrently - most comprehensive
        models_result = test_all_models_concurrent(config, requests_per_model=70, max_workers=25)
        results.append(models_result)
    else:
        # Test models endpoint first (should have general rate limiting)
        print("\n" + "="*60)
        print("PHASE 1: Testing /rest/models endpoint")
        print("="*60)
        
        if test_mode == "fast":
            # Use concurrent requests for maximum speed
            models_result = test_rate_limit_concurrent(config, max_requests=70, max_workers=25)
        elif test_mode == "standard":
            models_result = test_rate_limit_models(config, max_requests=65, delay=0.05)
        else:  # full
            models_result = test_rate_limit_models(config, max_requests=65, delay=0.05)
        
        results.append(models_result)
        
        # Only test chat completions in standard and full modes
        if test_mode != "fast":
            time.sleep(2)  # Wait between test phases
            
            # Test chat completions endpoint (should have model-specific rate limiting)
            print("\n" + "="*60)
            print("PHASE 2: Testing chat completions endpoints")
            print("="*60)
            
            # Test with the first model
            if config['models']:
                first_model = config['models'][0]
                print(f"Testing primary model: {first_model['name']}")
                
                if test_mode == "standard":
                    chat_requests = 65  # Need 65+ to test rate limiting
                else:  # full
                    chat_requests = 65
                    
                chat_result = test_rate_limit_chat_completions(config, first_model, max_requests=chat_requests, delay=0.05)
                results.append(chat_result)
                
                # Only test multiple models in full mode
                if test_mode == "full":
                    time.sleep(2)
                    model_results = test_specific_model_rate_limit(config)
                    results.extend(model_results)
    
    # Overall summary
    print(f"\n{'='*60}")
    print("OVERALL TEST SUMMARY")
    print(f"{'='*60}")
    
    total_rate_limited = sum(r['rate_limited'] for r in results)
    
    if total_rate_limited > 0:
        print("SUCCESS: Rate limiting is still working!")
        print(f"   Total rate limited responses: {total_rate_limited}")
    else:
        print("WARNING: No rate limiting detected")
        print("   This could mean:")
        print("   1. Rate limits are set very high")
        print("   2. Rate limiting service is not working")
        print("   3. API key doesn't have rate limits applied")
    
    for result in results:
        endpoint_name = result.get('model', result['endpoint'])
        print(f"\n{endpoint_name}:")
        print(f"  Success: {result['successful']}, Rate Limited: {result['rate_limited']}, Errors: {result['errors']}")

if __name__ == "__main__":
    main()