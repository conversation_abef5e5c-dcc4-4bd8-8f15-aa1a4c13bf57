import { ApiProperty } from '@nestjs/swagger';

export class ConversationHistoryItemDto {
  @ApiProperty({ description: 'The unique identifier of the conversation.' })
  id!: string;

  @ApiProperty({ description: 'The title of the conversation.' })
  title!: string;

  @ApiProperty({
    description: 'The model used for the conversation.',
    nullable: true,
  })
  model!: string | null;

  @ApiProperty({
    description:
      'The timestamp of the last update or creation of the conversation.',
  })
  updated_at!: string; // ISO Date string
}

export class PaginatedConversationHistoryResponseDto {
  @ApiProperty({
    type: [ConversationHistoryItemDto],
    description: 'The list of conversation history items for the current page.',
  })
  items!: ConversationHistoryItemDto[];

  @ApiProperty({
    description: 'The total number of conversation items available.',
  })
  totalItems!: number;

  @ApiProperty({ description: 'The total number of pages available.' })
  totalPages!: number;

  @ApiProperty({ description: 'The current page number.' })
  currentPage!: number;
}
