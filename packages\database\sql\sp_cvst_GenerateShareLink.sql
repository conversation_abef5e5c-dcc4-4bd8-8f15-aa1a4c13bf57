SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

-- =============================================
-- Author:      System
-- Create date: 2025-01-03
-- Description: Generate or retrieve share link for a conversation
-- =============================================
CREATE OR ALTER PROCEDURE [dbo].[sp_cvst_GenerateShareLink]
    @ConversationUuid UNIQUEIDENTIFIER,
    @UserId VARCHAR(30),
    @ShareId UNIQUEIDENTIFIER OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Check if user owns the conversation
        IF NOT EXISTS (
            SELECT 1 
            FROM [dbo].[conversation] 
            WHERE conversation_uuid = @ConversationUuid 
                AND ssoid = @UserId
        )
        BEGIN
            RAISERROR('User does not own this conversation', 16, 1);
            RETURN;
        END
        
        -- Check if share link already exists
        SELECT @ShareId = share_id
        FROM [dbo].[conversation]
        WHERE conversation_uuid = @ConversationUuid;
        
        -- If no share link exists, create one
        IF @ShareId IS NULL
        BEGIN
            SET @ShareId = NEWID();
            
            UPDATE [dbo].[conversation]
            SET share_id = @ShareId,
                is_shared = 1,
                update_by = @UserId,
                update_dt = GETDATE()
            WHERE conversation_uuid = @ConversationUuid;
        END
        
    END TRY
    BEGIN CATCH
        -- Log error
        INSERT INTO [dbo].[sys_sql_error] (
            sp_name,
            error_num,
            error_line,
            error_msg,
            error_severity,
            error_state,
            create_dt
        )
        VALUES (
            'sp_cvst_GenerateShareLink',
            ERROR_NUMBER(),
            ERROR_LINE(),
            ERROR_MESSAGE(),
            ERROR_SEVERITY(),
            ERROR_STATE(),
            GETDATE()
        );
        
        THROW;
    END CATCH
END
GO