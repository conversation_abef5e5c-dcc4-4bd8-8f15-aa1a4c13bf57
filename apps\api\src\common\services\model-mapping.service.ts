import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';

@Injectable()
export class ModelMappingService {
  private readonly logger = new Logger(ModelMappingService.name);
  private modelMappings: Map<string, Set<string>> = new Map();
  private canonicalNames: Map<string, string> = new Map(); // Maps any name to its canonical model_name

  constructor(private readonly prisma: PrismaService) {
    // Initialize model mappings on service start
    this.loadModelMappings().catch((error) => {
      this.logger.error(`Failed to load model mappings: ${error.message}`);
    });
  }

  /**
   * Load model name mappings from database
   * Creates bidirectional mapping between model_name and deployment_name
   */
  private async loadModelMappings(): Promise<void> {
    try {
      const models = await this.prisma.model_list.findMany({
        where: {
          rec_status: 'A', // Active
        },
        select: {
          model_name: true,
          deployment_name: true,
        },
      });

      // Clear existing mappings
      this.modelMappings.clear();
      this.canonicalNames.clear();

      // Build bidirectional mapping
      models.forEach((model) => {
        if (model.model_name) {
          // Always add model_name to itself
          this.addMapping(model.model_name, model.model_name);
          this.canonicalNames.set(model.model_name, model.model_name);

          if (
            model.deployment_name &&
            model.deployment_name !== model.model_name
          ) {
            // If deployment_name exists and is different, create bidirectional mapping
            this.addMapping(model.deployment_name, model.deployment_name);
            this.addMapping(model.deployment_name, model.model_name);
            this.addMapping(model.model_name, model.deployment_name);

            // deployment_name maps to model_name as canonical
            this.canonicalNames.set(model.deployment_name, model.model_name);
          }
        }
      });

      this.logger.log(`Loaded model mappings for ${models.length} models`);
      this.logger.debug(
        `Canonical mappings: ${this.canonicalNames.size} entries`,
      );

      // Log some example mappings for debugging
      const examples = [
        'gpt-4.1',
        'chatgpt-4.1',
        'gpt-4.1-mini',
        'chatgpt-4.1-mini',
      ];
      examples.forEach((name) => {
        if (this.canonicalNames.has(name)) {
          this.logger.debug(
            `Canonical: ${name} -> ${this.canonicalNames.get(name)}`,
          );
        }
      });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Error loading model mappings: ${errorMessage}`);
      // Fall back to hardcoded mappings if database fails
      this.addMapping('chatgpt-4.1', 'gpt-4.1');
      this.addMapping('gpt-4.1', 'chatgpt-4.1');
      this.addMapping('chatgpt-4.1-mini', 'gpt-4.1-mini');
      this.addMapping('gpt-4.1-mini', 'chatgpt-4.1-mini');

      // Set canonical names for fallback mappings
      this.canonicalNames.set('gpt-4.1', 'gpt-4.1');
      this.canonicalNames.set('chatgpt-4.1', 'gpt-4.1');
      this.canonicalNames.set('gpt-4.1-mini', 'gpt-4.1-mini');
      this.canonicalNames.set('chatgpt-4.1-mini', 'gpt-4.1-mini');
    }
  }

  /**
   * Add a mapping between model names
   */
  private addMapping(key: string, value: string): void {
    if (!this.modelMappings.has(key)) {
      this.modelMappings.set(key, new Set([key]));
    }
    this.modelMappings.get(key)!.add(value);
  }

  /**
   * Get all name variants for a model
   */
  getModelNameVariants(modelName: string): string[] {
    const variants = this.modelMappings.get(modelName);
    return variants ? Array.from(variants) : [modelName];
  }

  /**
   * Refresh model mappings from database
   * Can be called to update mappings when models are added/changed
   */
  async refreshModelMappings(): Promise<void> {
    await this.loadModelMappings();
  }

  /**
   * Check if a model name has known variants
   */
  hasVariants(modelName: string): boolean {
    const variants = this.modelMappings.get(modelName);
    return variants ? variants.size > 1 : false;
  }

  /**
   * Get the canonical (normalized) model name for grouping purposes
   * Returns the actual model_name from the database (not deployment_name)
   */
  getCanonicalModelName(modelName: string): string {
    // First check if we have a direct canonical mapping
    const canonical = this.canonicalNames.get(modelName);
    if (canonical) {
      return canonical;
    }

    // If not found in canonical names, fall back to alphabetically first variant
    // This handles cases where the model might not be in the database yet
    const variants = this.getModelNameVariants(modelName);
    const fallback = variants.sort()[0];
    return fallback;
  }
}
