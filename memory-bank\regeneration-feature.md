# Regeneration Feature Implementation

## Overview
This document describes the implementation of the message regeneration feature that allows users to regenerate the last assistant response in a conversation without creating duplicate messages in the history.

## Problem Statement
Previously, when users clicked the regenerate button:
1. The UI would remove the last assistant message
2. A new request would be sent to the backend
3. The backend would create a NEW message in the database
4. When history was refetched, both the original and regenerated messages would appear
5. Additionally, the user message was being duplicated on each regeneration

## Solution
1. Implement a soft-delete mechanism to mark the original assistant message as deleted when regenerating
2. Reuse the existing user message instead of creating a duplicate during regeneration

## Implementation Details

### 1. Database Changes
- Added `is_deleted` BIT field to the `message` table with default value 0
- Created index on `is_deleted` for query performance

### 2. Backend Changes

#### API Changes
- Added `isRegeneration` boolean field to `CreateChatCompletionDto`
- Modified `ChatCompletionService.executeCompletion` to handle regeneration:
  - When `isRegeneration = true`:
    - Finds the last assistant message and soft-deletes it by setting `is_deleted = 1`
    - Reuses the existing user message instead of creating a new one
    - Creates the new regenerated assistant message

#### History Retrieval
- Updated `ChatService.getHistoryMessages` to filter out messages where `is_deleted = 1`
- Added temporary application-level filtering until stored procedure is updated

### 3. Frontend Changes
- Added `isRegeneration` field to `ChatCompletionRequest` interface
- Updated `handleRegenerate` function to send `isRegeneration: true` flag

## Database Migration Required

1. Run the SQL migration script:
```sql
ALTER TABLE [dbo].[message]
ADD [is_deleted] BIT NOT NULL DEFAULT 0;
```

2. Update the stored procedure `sp_cvst_GetDecryptedMessagesByConversationUUID` to:
   - Include `is_deleted` in the SELECT statement
   - Add `WHERE is_deleted = 0` to filter out soft-deleted messages

## Testing
1. Start a conversation and get an assistant response
2. Click the regenerate button
3. Verify that:
   - The old response is replaced in the UI
   - After page refresh, only the regenerated response appears
   - No duplicate messages in the conversation history

## Future Enhancements
- Consider adding a "regeneration history" feature to view previous attempts
- Add ability to regenerate any message in the conversation, not just the last one
- Implement undo functionality for regenerations