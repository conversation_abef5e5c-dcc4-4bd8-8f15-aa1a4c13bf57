import { Module, Global } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { RedisModule as IoRedisModule } from '@nestjs-modules/ioredis';

@Global()
@Module({
  imports: [
    IoRedisModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        type: 'cluster',
        nodes: [
          {
            host: configService.get('REDIS_HOST_1') || '*************',
            port: Number(configService.get('REDIS_PORT_1')) || 6379,
          },
          {
            host: configService.get('REDIS_HOST_2') || '*************',
            port: Number(configService.get('REDIS_PORT_2')) || 6379,
          },
          {
            host: configService.get('REDIS_HOST_3') || '*************',
            port: Number(configService.get('REDIS_PORT_3')) || 6379,
          },
          {
            host: configService.get('REDIS_HOST_4') || '*************',
            port: Number(configService.get('REDIS_PORT_4')) || 6379,
          },
          {
            host: configService.get('REDIS_HOST_5') || '*************',
            port: Number(configService.get('REDIS_PORT_5')) || 6379,
          },
          {
            host: configService.get('REDIS_HOST_6') || '*************',
            port: Number(configService.get('REDIS_PORT_6')) || 6379,
          },
        ],
        options: {
          redisOptions: {
            password: configService.get('REDIS_PASSWORD_1') || 'maredis',
            db: parseInt(configService.get('REDIS_DB_1') || '1', 10),
          },
          enableReadyCheck: true,
          lazyConnect: false,
          enableOfflineQueue: true,
          showFriendlyErrorStack:
            configService.get('REDIS_SHOW_FRIENDLY_ERROR_STACK_1') === 'true',
        },
      }),
      inject: [ConfigService],
    }),
  ],
  exports: [IoRedisModule],
})
export class RedisModule {}
