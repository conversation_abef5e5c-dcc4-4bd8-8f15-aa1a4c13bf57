'use client';

import React, { useState } from 'react';
import { useSession, signOut } from 'next-auth/react';
import Typography from '@mui/material/Typography';
import Container from '@mui/material/Container';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import LogoutIcon from '@mui/icons-material/Logout';
import CircularProgress from '@mui/material/CircularProgress';
import SignOutConfirmationModal from '../../components/genai/modals/SignOutConfirmationModal';
import { event } from '@/components/genai/GoogleAnalytics';

// Settings items moved to Profile & Settings menu in sidebar
// This page now serves as a simple container for sign-out functionality

export default function SettingsPage() {
  const { data: session, status } = useSession();
  const [isSignOutConfirmModalOpen, setIsSignOutConfirmModalOpen] = useState(false);
  const [isSigningOut, setIsSigningOut] = useState(false);

  const handleSignOut = async () => {
    setIsSigningOut(true);
    const basePath = process.env.NEXT_PUBLIC_BASE_PATH || '';
    try {
      await signOut({ callbackUrl: `${window.location.origin}${basePath}/` });
    } catch (error) {
      console.error('Sign out error:', error);
      setIsSigningOut(false);
    }
  };

  if (status === 'loading') {
    return (
      <Container
        sx={{
          py: 4,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '80vh',
        }}
      >
        <CircularProgress />
      </Container>
    );
  }

  return (
    <Container
      sx={{
        height: '100svh',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <Box sx={{ mt: 3, mb: { xs: 2, md: 4 } }}>
        <Typography
          variant="h4"
          component="h1"
          gutterBottom
          fontWeight={{ xs: 600, md: 400 }}
          fontSize={{ xs: 25, md: '2.125rem' }}
          textAlign={{ xs: 'center', md: 'start' }}
        >
          Settings
        </Typography>
        {session?.user?.email && (
          <Typography
            variant="subtitle1"
            color="text.secondary"
            textAlign={{ xs: 'center', md: 'start' }}
          >
            Signed in as: {session.user.email}
          </Typography>
        )}
      </Box>
      <Box display={'flex'} flexDirection={'column'} flex={1} overflow={'auto'}>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
          Settings have been moved to the <strong>Profile &amp; Settings</strong> menu in the sidebar for easier access.
        </Typography>

        <Box sx={{ my: 4, display: 'flex', justifyContent: 'flex-start' }}>
          <Button
            variant="contained"
            color="error"
            startIcon={<LogoutIcon />}
            onClick={() => {
              setIsSignOutConfirmModalOpen(true);
              event({
                action: 'click',
                category: 'settings',
                label: 'sign_out',
                value: 1,
              });
            }}
            disabled={isSigningOut || status === 'unauthenticated'}
          >
            {isSigningOut ? 'Signing Out...' : 'Sign Out'}
          </Button>
        </Box>
      </Box>

      {/* Modal */}
      <SignOutConfirmationModal
        open={isSignOutConfirmModalOpen}
        handleClose={() => setIsSignOutConfirmModalOpen(false)}
        handleConfirm={handleSignOut}
        isSigningOut={isSigningOut}
      />
    </Container>
  );
}