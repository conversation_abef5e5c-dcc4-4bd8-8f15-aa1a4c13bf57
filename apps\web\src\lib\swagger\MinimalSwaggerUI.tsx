'use client';

import { useEffect, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { useSearchParams } from 'next/navigation';
import { Box, Typography, Alert, CircularProgress } from '@mui/material';
import ThemedSwaggerUI from './ThemedSwaggerUI';

type Props = {
  spec: Record<string, any>;
};

function MinimalSwaggerUI({ spec }: Props) {
  const { data: session, status } = useSession();
  const searchParams = useSearchParams();
  const isFullscreen = searchParams.get('fullscreen') === 'true';
  const originalConsoleError = useRef(console.error);

  useEffect(() => {
    // Suppress specific React lifecycle warnings from swagger-ui-react
    // These are known issues with swagger-ui-react v5.x and React 18+
    // The library uses deprecated lifecycle methods that trigger warnings in strict mode
    // Functionality is not affected, but we suppress to keep console clean
    // TODO: Remove this when swagger-ui-react updates to modern React patterns
    const suppressedWarnings = [
      'UNSAFE_componentWillReceiveProps',
      'ModelCollapse',
      'OperationContainer',
      'ContentType',
      'Use the `defaultValue` or `value` props on <select> instead of setting `selected` on <option>',
      'selected',
      'option',
      'validateOptionProps',
    ];

    console.error = (...args) => {
      const message = args[0]?.toString() || '';
      const shouldSuppress = suppressedWarnings.some((warning) =>
        message.includes(warning),
      );

      if (!shouldSuppress) {
        originalConsoleError.current(...args);
      }
    };

    // Also suppress React DevTools warnings for swagger-ui-react
    const originalConsoleWarn = console.warn;
    console.warn = (...args) => {
      const message = args[0]?.toString() || '';
      const shouldSuppress = suppressedWarnings.some((warning) =>
        message.includes(warning),
      );

      if (!shouldSuppress) {
        originalConsoleWarn(...args);
      }
    };

    // Restore original console methods on cleanup
    const savedConsoleError = originalConsoleError.current;
    return () => {
      console.error = savedConsoleError;
      console.warn = originalConsoleWarn;
    };
  }, []);

  if (status === 'loading') {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: isFullscreen ? '100svh' : '400px',
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  if (!session) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          <Typography>Access Denied</Typography>
          <Typography variant="body2">
            You must be signed in to view this page.
          </Typography>
        </Alert>
      </Box>
    );
  }

  const containerStyle = isFullscreen
    ? {
        width: '100vw',
        height: '100svh',
        overflow: 'auto',
        '& .swagger-ui': {
          maxWidth: 'none',
        },
      }
    : {
        '& .swagger-ui': {
          maxWidth: '100%',
        },
      };

  return (
    <Box sx={containerStyle}>
      <ThemedSwaggerUI spec={spec} />
    </Box>
  );
}

export default MinimalSwaggerUI;
