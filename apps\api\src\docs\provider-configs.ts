import { ProviderConfig } from './swagger-generator.service';

export const providerConfigs: Record<string, ProviderConfig> = {
  gpt: {
    name: 'gpt',
    displayName: 'GPT',
    title: 'HKBU GenAI Platform API Service – GPT Models Swagger Specification',
    description:
      'The HKBU GenAI Platform API Service provides access to various GPT models through Azure OpenAI integration.',
    modelFilter: (model) =>
      model.model_name &&
      (model.model_name.toLowerCase().includes('gpt') ||
        model.model_name === 'o1' ||
        model.model_name === 'o1-mini' ||
        model.model_name === 'o3-mini'),
    examples: {
      standard: {
        summary: 'Example for GPT-4, GPT-4.1, and other standard models',
        value: {
          messages: [
            {
              role: 'system',
              content:
                'You are a helpful programming assistant. Provide clear, concise code examples and explanations.',
            },
            {
              role: 'user',
              content:
                'Hello! Can you help me write a Python function to calculate the factorial of a number?',
            },
          ],
          temperature: 0.7,
          max_tokens: 150,
          top_p: 1,
          stream: false,
        },
      },
      special: {
        summary: 'Example for o1 and o3-mini models (limited parameters)',
        value: {
          messages: [
            {
              role: 'user',
              content:
                'Hello! Can you help me write a Python function to calculate the factorial of a number?',
            },
          ],
          max_tokens: 150,
          stream: false,
        },
      },
      vision: {
        summary: 'Vision prompt example for GPT models with image support',
        value: {
          messages: [
            {
              role: 'system',
              content: 'You are a helpful assistant.',
            },
            {
              role: 'user',
              content: [
                {
                  type: 'text',
                  text: 'Describe this picture:',
                },
                {
                  type: 'image_url',
                  image_url: {
                    url: 'https://learn.microsoft.com/azure/ai-services/computer-vision/media/quickstarts/presentation.png',
                    detail: 'low',
                  },
                },
              ],
            },
          ],
          temperature: 0,
        },
      },
    },
    specialModels: ['o1', 'o3-mini'],
    chipLabels: {
      provider: 'Azure OpenAI',
      version: '2024-12-01-preview',
    },
    referenceUrl:
      'https://learn.microsoft.com/en-us/azure/ai-services/openai/reference',
  },

  qwen: {
    name: 'qwen',
    displayName: 'Qwen',
    title:
      'HKBU GenAI Platform API Service – Qwen Models Swagger Specification',
    description:
      'The HKBU GenAI Platform API Service provides access to various Qwen models through Alibaba Cloud integration.',
    modelFilter: (model) =>
      model.model_name && model.model_name.toLowerCase().includes('qwen'),
    examples: {
      standard: {
        summary: 'Example for Qwen models',
        value: {
          messages: [
            {
              role: 'system',
              content:
                'You are a helpful AI assistant. Provide clear, accurate, and helpful responses.',
            },
            {
              role: 'user',
              content:
                'Hello! Can you help me write a Python function to calculate the factorial of a number?',
            },
          ],
          temperature: 0.7,
          max_tokens: 150,
          top_p: 1,
          stream: false,
        },
      },
    },
    chipLabels: {
      provider: 'Alibaba Cloud',
      version: 'v1',
    },
    referenceUrl:
      'https://help.aliyun.com/zh/dashscope/developer-reference/api-details',
  },

  gemini: {
    name: 'gemini',
    displayName: 'Gemini',
    title:
      'HKBU GenAI Platform API Service – Gemini Models Swagger Specification',
    description:
      'The HKBU GenAI Platform API Service provides access to various Gemini models through Google Vertex AI integration.',
    modelFilter: (model) =>
      model.model_name && model.model_name.toLowerCase().includes('gemini'),
    examples: {
      standard: {
        summary: 'Example for Gemini models',
        value: {
          messages: [
            {
              role: 'system',
              content:
                'You are a helpful AI assistant powered by Google Gemini. Provide clear, accurate, and helpful responses.',
            },
            {
              role: 'user',
              content:
                'Hello! Can you help me write a Python function to calculate the factorial of a number?',
            },
          ],
          temperature: 0.7,
          max_tokens: 150,
          top_p: 1,
          stream: false,
        },
      },
    },
    chipLabels: {
      provider: 'Google Vertex AI',
      version: 'Latest',
    },
    referenceUrl:
      'https://cloud.google.com/vertex-ai/docs/generative-ai/model-reference/gemini',
  },

  llama: {
    name: 'llama',
    displayName: 'Llama',
    title:
      'HKBU GenAI Platform API Service – Llama Models Swagger Specification',
    description:
      'The HKBU GenAI Platform API Service provides access to various Llama models through Google Vertex AI integration.',
    modelFilter: (model) =>
      model.model_name && model.model_name.toLowerCase().includes('llama'),
    examples: {
      standard: {
        summary: 'Example for Llama models',
        value: {
          messages: [
            {
              role: 'system',
              content:
                'You are a helpful AI assistant powered by Llama. Provide clear, accurate, and helpful responses.',
            },
            {
              role: 'user',
              content:
                'Hello! Can you help me write a Python function to calculate the factorial of a number?',
            },
          ],
          temperature: 0.7,
          max_tokens: 150,
          top_p: 1,
          stream: false,
        },
      },
    },
    chipLabels: {
      provider: 'Google Vertex AI',
      version: '20240723',
    },
    referenceUrl:
      'https://cloud.google.com/vertex-ai/docs/generative-ai/model-reference/llama',
  },

  deepseek: {
    name: 'deepseek',
    displayName: 'DeepSeek',
    title:
      'HKBU GenAI Platform API Service – DeepSeek Models Swagger Specification',
    description:
      'The HKBU GenAI Platform API Service provides access to various DeepSeek models through DeepSeek AI integration.',
    modelFilter: (model) => {
      const modelName = model.model_name || '';
      const lowerModelName = modelName.toLowerCase();
      const includesDeepseek = lowerModelName.includes('deepseek');
      console.log(
        `[DEBUG] DeepSeek filter check: model_name="${modelName}", lowercase="${lowerModelName}", includes('deepseek')=${includesDeepseek}`,
      );
      return model.model_name && includesDeepseek;
    },
    examples: {
      standard: {
        summary: 'Example for DeepSeek models',
        value: {
          messages: [
            {
              role: 'system',
              content:
                'You are a helpful AI assistant powered by DeepSeek. Provide clear, accurate, and helpful responses.',
            },
            {
              role: 'user',
              content:
                'Hello! Can you help me write a Python function to calculate the factorial of a number?',
            },
          ],
          temperature: 0.7,
          max_tokens: 150,
          top_p: 1,
          stream: false,
        },
      },
    },
    chipLabels: {
      provider: 'DeepSeek AI',
      version: '2024-05-01-preview',
    },
    referenceUrl: 'https://api-docs.deepseek.com/',
  },
};
