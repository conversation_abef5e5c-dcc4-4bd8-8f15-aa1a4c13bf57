import { Fragment, useEffect, useRef, useState } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import DropdownPicker, {
  Option,
} from '@/components/genai/controls/DropdownPicker'; // Use path alias
// Removed: import getModelList from "../utils/GetModelList";
import { useGetModelsQuery } from '@/lib/store/apiSlice'; // Path alias is correct
import { GptModel } from '@/lib/types/common'; // Import GptModel type
import moment from 'moment'; // Assuming 'moment' is installed
import { humanFileSize } from '@/lib/utils/HumanFileSize'; // Path alias is correct

// Define interface for the data submitted
interface QuoteFormData {
  model: string;
  quote: string;
  duration: string;
  reason: string;
}

// Define props interface
interface RequestQuoteModalProps {
  show: boolean;
  isLoading?: boolean;
  onClose: () => void;
  closeFn: () => void;
  submitFn: (data: QuoteFormData) => void; // Use defined interface
}

const RequestQuoteModal: React.FC<RequestQuoteModalProps> = (props) => {
  // Use defined props interface
  const { show, isLoading, onClose, closeFn, submitFn } = props;
  const [shouldDisplayError, setShouldDisplayError] = useState(false);

  // Use RTK Query hook to fetch models
  const {
    data: availableModels = [],
    isLoading: isLoadingModels,
    isError: isErrorModels,
  } = useGetModelsQuery(undefined, {
    skip: !show, // Only fetch when the modal is shown
  });

  // State derived from the hook's data
  const [modelOptionList, setModelOptionList] = useState<Option<string>[]>([]);
  const [quoteOptionList, setQuoteOptionList] = useState<Option<string>[]>([]);
  const [durationOptionList, setDurationOptionList] = useState<
    Option<string>[]
  >([]);

  const [selectedModel, setSelectedModel] = useState<string | undefined>(
    undefined,
  ); // Explicit type
  const [selectedQuote, setSelectedQuote] = useState<string | undefined>(
    undefined,
  ); // Explicit type
  const [selectedDuration, setSelectedDuration] = useState<string | undefined>(
    undefined,
  ); // Explicit type
  const [reason, setReason] = useState<string>('');

  const messageInput = useRef<HTMLTextAreaElement | null>(null);

  // Removed getModelOptionList function

  const getQuoteOptionList = () => {
    if (process.env.NEXT_PUBLIC_REQUEST_QUOTE_QUOTE_OPTION) {
      setQuoteOptionList(
        process.env.NEXT_PUBLIC_REQUEST_QUOTE_QUOTE_OPTION.split(',').map(
          (quote) => ({
            value: quote,
            displayName: humanFileSize(parseInt(quote, 10), true, 0, ''), // Parse quote string to number
          }),
        ),
      );
    }
  };

  const getDurationOptionList = () => {
    if (process.env.NEXT_PUBLIC_REQUEST_QUOTE_DURATION_OPTION) {
      setDurationOptionList(
        process.env.NEXT_PUBLIC_REQUEST_QUOTE_DURATION_OPTION.split(',').map(
          (duration) => ({
            value: duration,
            displayName: moment.duration(`P${duration}`).humanize(),
          }),
        ),
      );
    }
  };

  const onClickSubmit = () => {
    // Add null check for messageInput.current
    if (
      selectedModel == undefined ||
      selectedQuote == undefined ||
      selectedDuration == undefined ||
      !messageInput.current || // Check if ref exists
      messageInput.current.value == ''
    ) {
      setShouldDisplayError(true);
    } else {
      // Ensure all required fields are strings before submitting
      if (selectedModel && selectedQuote && selectedDuration && reason) {
        submitFn({
          model: selectedModel,
          quote: selectedQuote,
          duration: selectedDuration,
          reason: reason,
        });
      } else {
        // This case should ideally be prevented by the error check, but added for safety
        console.error('Attempted to submit quote with missing data');
        setShouldDisplayError(true);
      }
    }
  };

  const textareaChange = () => {
    // Ensure a string is passed using nullish coalescing
    setReason(messageInput.current?.value ?? '');
  };

  // Effect to update modelOptionList when availableModels changes
  useEffect(() => {
    if (availableModels.length > 0) {
      setModelOptionList(
        availableModels.map((item: GptModel) => ({
          // Type annotation is correct
          value: item.deployment_name,
          displayName: item.display_name,
        })),
      );
    } else {
      setModelOptionList([]); // Clear list if no models
    }
  }, [availableModels]);

  // Effect to run remaining initializations when modal shows
  useEffect(() => {
    if (show) {
      setShouldDisplayError(false);
      // getModelOptionList(); // Removed, handled by the hook and the effect above
      getQuoteOptionList();
      getDurationOptionList();
    }
  }, [show]);

  return (
    <Transition.Root show={show}>
      <Dialog as="div" className="relative z-30" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="grow relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-4xl">
                <div className="bg-white px-4 pb-4 pt-5 sm:p-6 sm:pb-4 flex flex-col gap-4">
                  <div className="flex mb-3">
                    <label className="grow text-lg font-semibold">
                      {process.env.NEXT_PUBLIC_REQUEST_QUOTE_TITLE ??
                        "Please complete the form below and click 'Submit' to request increase quote."}
                    </label>
                  </div>
                  {process.env.NEXT_PUBLIC_REQUEST_QUOTE_MESSAGE && (
                    <div className="flex mb-3">
                      <label className="grow font-medium">
                        {process.env.NEXT_PUBLIC_REQUEST_QUOTE_MESSAGE}
                      </label>
                    </div>
                  )}
                  <DropdownPicker
                    title="model:"
                    placeholder={
                      isLoadingModels
                        ? 'Loading models...'
                        : isErrorModels
                          ? 'Error loading models'
                          : 'Select model...'
                    }
                    option={modelOptionList}
                    onPick={setSelectedModel}
                    isError={shouldDisplayError && !selectedModel} // Only error if nothing selected
                    // Removed unsupported 'disabled' prop
                  />
                  <DropdownPicker
                    title="quote:"
                    placeholder="Select quote..."
                    option={quoteOptionList}
                    onPick={setSelectedQuote}
                    isError={shouldDisplayError}
                    inputable
                  />
                  <DropdownPicker
                    title="duration:"
                    placeholder="Select duration..."
                    option={durationOptionList}
                    onPick={setSelectedDuration}
                    isError={shouldDisplayError}
                    inputable
                  />
                  <div className="flex flex-col gap-2">
                    <p>reason:</p>
                    <div className="flex">
                      <textarea
                        autoFocus={true}
                        maxLength={1000}
                        readOnly={isLoading}
                        className="grow resize-none bg-transparent outline-none py-2 px-2 border-2 border-emerald-700 h-48"
                        name="Message"
                        ref={messageInput}
                        onChange={textareaChange}
                      />
                    </div>
                    {shouldDisplayError && reason === '' && (
                      <div className="text-red-500">
                        Please fill in the text box
                      </div>
                    )}
                  </div>
                </div>

                <div className="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                  <button
                    disabled={isLoading}
                    type="button"
                    className="mt-3 inline-flex w-full justify-center rounded-2xl bg-emerald-700 px-3 py-2 text-sm font-semibold text-gray-50 shadow-sm hover:bg-emerald-800 sm:mt-0 sm:w-auto"
                    onClick={onClickSubmit}
                  >
                    {!isLoading ? (
                      'Submit'
                    ) : (
                      <div
                        className="h-5 w-5 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite] text-slate-300"
                        role="status"
                      >
                        <span className="!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]"></span>
                      </div>
                    )}
                  </button>
                  <button
                    type="button"
                    className="mr-3 mt-3 inline-flex w-full justify-center rounded-2xl bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto"
                    onClick={() => {
                      closeFn();
                    }}
                  >
                    Close
                  </button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
};

export default RequestQuoteModal;
