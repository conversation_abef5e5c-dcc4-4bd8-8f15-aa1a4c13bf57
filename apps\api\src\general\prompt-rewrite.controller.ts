import {
  Controller,
  Post,
  Body,
  UseGuards,
  Req,
  ForbiddenException,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import {
  RewritePromptDto,
  GeneratePromptDto,
  PromptRewriteResponseDto,
} from './dto/prompt-rewrite.dto';
import { PromptRewriteService } from './prompt-rewrite.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { PromptSpeechRateLimitGuard } from '../common/guards/prompt-speech-rate-limit.guard';
import { AuthenticatedUser } from '../auth/user.interface';
import { Request } from 'express';

@ApiTags('Prompt Rewrite')
@Controller('general/prompt-rewrite')
@UseGuards(JwtAuthGuard, PromptSpeechRateLimitGuard)
export class PromptRewriteController {
  constructor(private readonly promptRewriteService: PromptRewriteService) {}

  @Post('rewrite')
  @ApiOperation({ summary: 'Rewrite a prompt' })
  @ApiResponse({
    status: 200,
    description: 'The rewritten prompt.',
    type: PromptRewriteResponseDto,
  })
  async rewrite(
    @Body() rewritePromptDto: RewritePromptDto,
    @Req() req: Request,
  ): Promise<PromptRewriteResponseDto> {
    const user = req.user as AuthenticatedUser;
    if (!user?.userId) {
      throw new ForbiddenException('User information not available.');
    }
    return this.promptRewriteService.rewrite(rewritePromptDto, user);
  }

  @Post('generate')
  @ApiOperation({ summary: 'Generate a prompt from an idea' })
  @ApiResponse({
    status: 200,
    description: 'The generated prompt.',
    type: PromptRewriteResponseDto,
  })
  async generate(
    @Body() generatePromptDto: GeneratePromptDto,
    @Req() req: Request,
  ): Promise<PromptRewriteResponseDto> {
    const user = req.user as AuthenticatedUser;
    if (!user?.userId) {
      throw new ForbiddenException('User information not available.');
    }
    return this.promptRewriteService.generate(generatePromptDto, user);
  }
}
