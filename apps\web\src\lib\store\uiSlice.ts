import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from './store'; // Assuming store exports RootState type

// Types copied from gpt/components/chatgpt/AlertModal.tsx
export type ButtonProps = {
  text: string;
  style?: 'primary' | 'secondary';
  onClick?: () => void; // Consider if storing functions in state is ideal, might need refactoring later
  disableAutoCloseModal?: boolean;
  // Removed otherProps as it contained complex, non-serializable types unsuitable for Redux state
};

export type AlertModalProps =
  | {
      message?: string;
      buttons?: ButtonProps[];
      onClose?: () => void; // Retain onClose callback if needed for side effects
    }
  | undefined; // Allow undefined as per original AppContext

export enum Theme {
  dark = 'dark',
  light = 'light',
}

interface UiState {
  showAccessDeniedModal: boolean;
  accessDeniedModalMessage: string;
  showTncModal: boolean;
  showFaqModal: boolean;
  showPromptEngModal: boolean;
  showImgGenModal: boolean;
  showDesignatedHkbuStaffModal: boolean;
  showContactUsModal: boolean;
  showFeedbackModal: boolean;
  tncModalShowAgree: boolean;
  showFeedbackSuccessModal: boolean;
  showHealthCheckModal: boolean;
  alertModal: AlertModalProps;
  showApiKeyModal: boolean;
  showRequestQuoteModal: boolean;
  showRequestQuoteSuccessModal: boolean;
  bannerNotified: boolean;
  showBannerModal: boolean;
  showModelSideBar: boolean; // Keep this for model menu visibility
  theme: Theme;
  isHome: boolean;
  isLoading: boolean; // Generic loading state
}

// Function to safely get theme from localStorage
const getInitialTheme = (): Theme => {
  if (typeof window !== 'undefined') {
    const storedTheme = localStorage.getItem('theme');
    if (storedTheme === Theme.dark) {
      // Ensure body class matches on initial load after localStorage check
      document.documentElement.classList.add('dark');
      return Theme.dark;
    }
    // Default to light if no theme stored or stored value is invalid/light
    document.documentElement.classList.remove('dark');
  }
  return Theme.light; // Default theme
};

const initialState: UiState = {
  showAccessDeniedModal: false,
  accessDeniedModalMessage: '',
  showTncModal: false,
  tncModalShowAgree: false,
  showFaqModal: false,
  showPromptEngModal: false,
  showImgGenModal: false,
  showDesignatedHkbuStaffModal: false,
  showContactUsModal: false,
  showFeedbackModal: false,
  showFeedbackSuccessModal: false,
  showHealthCheckModal: false,
  alertModal: undefined,
  showApiKeyModal: false,
  showRequestQuoteModal: false,
  showRequestQuoteSuccessModal: false,
  // Default bannerNotified based on localStorage or default to true?
  bannerNotified:
    typeof window !== 'undefined'
      ? localStorage.getItem('bannerNotified') === 'true'
      : true,
  showBannerModal: true, // Or false, depending on desired initial state
  showModelSideBar: false, // Initially hidden
  theme: getInitialTheme(),
  isHome: true,
  isLoading: false, // Initialize loading to false
};

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    openAccessDeniedModal: (state) => {
      state.showAccessDeniedModal = true;
    },
    closeAccessDeniedModal: (state) => {
      state.showAccessDeniedModal = false;
    },
    setAccessDeniedModalMessage: (state, action: PayloadAction<string>) => {
      state.accessDeniedModalMessage = action.payload;
    },
    openTncModal: (state) => {
      state.showTncModal = true;
    },
    closeTncModal: (state) => {
      state.showTncModal = false;
    },
    setTncModalShowAgree: (state, action: PayloadAction<boolean>) => {
      state.tncModalShowAgree = action.payload;
    },
    openFaqModal: (state) => {
      state.showFaqModal = true;
    },
    closeFaqModal: (state) => {
      state.showFaqModal = false;
    },
    openPromptEngModal: (state) => {
      state.showPromptEngModal = true;
    },
    closePromptEngModal: (state) => {
      state.showPromptEngModal = false;
    },
    openImgGenModal: (state) => {
      state.showImgGenModal = true;
    },
    closeImgGenModal: (state) => {
      state.showImgGenModal = false;
    },
    openDesignatedHkbuStaffModal: (state) => {
      state.showDesignatedHkbuStaffModal = true;
    },
    closeDesignatedHkbuStaffModal: (state) => {
      state.showDesignatedHkbuStaffModal = false;
    },
    openContactUsModal: (state) => {
      state.showContactUsModal = true;
    },
    closeContactUsModal: (state) => {
      state.showContactUsModal = false;
    },
    openFeedbackModal: (state) => {
      state.showFeedbackModal = true;
    },
    closeFeedbackModal: (state) => {
      state.showFeedbackModal = false;
    },
    openFeedbackSuccessModal: (state) => {
      state.showFeedbackSuccessModal = true;
    },
    closeFeedbackSuccessModal: (state) => {
      state.showFeedbackSuccessModal = false;
    },
    openRequestQuoteModal: (state) => {
      state.showRequestQuoteModal = true;
    },
    closeRequestQuoteModal: (state) => {
      state.showRequestQuoteModal = false;
    },
    openRequestQuoteSuccessModal: (state) => {
      state.showRequestQuoteSuccessModal = true;
    },
    closeRequestQuoteSuccessModal: (state) => {
      state.showRequestQuoteSuccessModal = false;
    },
    setAlertModal: (state, action: PayloadAction<AlertModalProps>) => {
      state.alertModal = action.payload;
    },
    openApiKeyModal: (state) => {
      state.showApiKeyModal = true;
    },
    closeApiKeyModal: (state) => {
      state.showApiKeyModal = false;
    },
    setBannerNotified: (state, action: PayloadAction<boolean>) => {
      state.bannerNotified = action.payload;
      if (typeof window !== 'undefined') {
        localStorage.setItem('bannerNotified', action.payload.toString());
      }
    },
    openBannerModal: (state) => {
      state.showBannerModal = true;
    },
    closeBannerModal: (state) => {
      state.showBannerModal = false;
    },
    openModelSideBar: (state) => {
      // Action to open sidebar
      state.showModelSideBar = true;
    },
    closeModelSideBar: (state) => {
      // Action to close sidebar
      state.showModelSideBar = false;
    },
    toggleModelSideBar: (state) => {
      // Optional: Action to toggle sidebar
      state.showModelSideBar = !state.showModelSideBar;
    },
    setTheme: (state, action: PayloadAction<Theme>) => {
      state.theme = action.payload;
      if (typeof window !== 'undefined') {
        localStorage.setItem('theme', action.payload);
        if (action.payload === Theme.dark) {
          document.documentElement.classList.add('dark');
        } else {
          document.documentElement.classList.remove('dark');
        }
      }
    },
    setIsHome: (state, action: PayloadAction<boolean>) => {
      state.isHome = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      // Renamed from setIsLoading for consistency
      state.isLoading = action.payload;
    },
    openHealthCheckModal: (state) => {
      state.showHealthCheckModal = true;
    },
    closeHealthCheckModal: (state) => {
      state.showHealthCheckModal = false;
    },
  },
});

export const {
  openAccessDeniedModal,
  closeAccessDeniedModal,
  setAccessDeniedModalMessage,
  openTncModal,
  closeTncModal,
  setTncModalShowAgree,
  openFaqModal,
  closeFaqModal,
  openPromptEngModal,
  closePromptEngModal,
  openImgGenModal,
  closeImgGenModal,
  openDesignatedHkbuStaffModal,
  closeDesignatedHkbuStaffModal,
  openContactUsModal,
  closeContactUsModal,
  openFeedbackModal,
  closeFeedbackModal,
  openFeedbackSuccessModal,
  closeFeedbackSuccessModal,
  openRequestQuoteModal,
  closeRequestQuoteModal,
  openRequestQuoteSuccessModal,
  closeRequestQuoteSuccessModal,
  setAlertModal,
  openApiKeyModal,
  closeApiKeyModal,
  setBannerNotified,
  openBannerModal,
  closeBannerModal,
  openModelSideBar, // Export the action
  closeModelSideBar, // Export the action
  toggleModelSideBar, // Export the action if needed
  setTheme,
  setIsHome,
  setLoading, // Export the action (consider renaming if causing conflicts)
  openHealthCheckModal,
  closeHealthCheckModal,
} = uiSlice.actions;

// Selectors
export const selectUiState = (state: RootState) => state.ui;
export const selectIsLoading = (state: RootState) => state.ui.isLoading; // Keep selector name consistent?
export const selectTheme = (state: RootState) => state.ui.theme;
export const selectShowModelSideBar = (state: RootState) =>
  state.ui.showModelSideBar; // Export the selector
// Add other specific selectors as needed for better component isolation
export const selectShowTncModal = (state: RootState) => state.ui.showTncModal;
export const selectTncModalShowAgree = (state: RootState) =>
  state.ui.tncModalShowAgree;
export const selectAlertModalProps = (state: RootState) => state.ui.alertModal;

export default uiSlice.reducer;
