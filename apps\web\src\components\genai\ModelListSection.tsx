'use client';

import React, { useState, useRef, useEffect } from 'react'; // Import useRef
import { useRouter } from 'next/navigation';
import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import Link from '@mui/material/Link'; // Import Link for "See all"
import IconButton from '@mui/material/IconButton'; // For arrow buttons
import ArrowBackIosNewIcon from '@mui/icons-material/ArrowBackIosNew';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
import CircularProgress from '@mui/material/CircularProgress';
import Alert from '@mui/material/Alert';
import { SerializedError } from '@reduxjs/toolkit';
import { FetchBaseQueryError } from '@reduxjs/toolkit/query';
import { GptModel } from '@/lib/types/common';
import ModelCard, { ModelCardWidth } from './ModelCard';
import ArrowButton, { Direction } from './ArrowButton';

const ModelCardGap = 8;

// Define props type
type ModelListSectionProps = {
  title: string;
  models: GptModel[] | undefined;
  onSelectModel: (model: GptModel) => void;
  isLoading?: boolean;
  error?: SerializedError | FetchBaseQueryError | undefined;
  categorySlug?: string; // For "See all" link navigation
};

// Helper to check if error is FetchBaseQueryError
function isFetchBaseQueryError(error: unknown): error is FetchBaseQueryError {
  return typeof error === 'object' && error != null && 'status' in error;
}

// Helper to check if error is SerializedError
function isSerializedError(error: unknown): error is SerializedError {
  return typeof error === 'object' && error != null && 'message' in error;
}

function ModelListSection({
  title,
  models,
  onSelectModel,
  isLoading,
  error,
  categorySlug,
}: ModelListSectionProps) {
  const router = useRouter();
  const scrollContainerRef = useRef<HTMLDivElement>(null); // Ref for the scrollable container
  // Store the element on which the scroll listener is attached so we can
  // reliably remove it in the cleanup function even if `scrollContainerRef.current`
  // changes between renders.
  const scrollListenerTargetRef = useRef<HTMLDivElement | null>(null);

  const [scrollInfo, setScrollInfo] = useState<{
    scrollable: boolean;
    position: number;
    isAtStart: boolean;
    isAtEnd: boolean;
  }>({
    scrollable: false,
    position: 0,
    isAtStart: false,
    isAtEnd: false,
  });

  useEffect(() => {
    const updateScrollInfo = () => {
      if (scrollContainerRef.current) {
        const { scrollLeft, scrollWidth, clientWidth } =
          scrollContainerRef.current;
        const maxScroll = scrollWidth - clientWidth;
        const roundedScrollLeft = Math.round(scrollLeft);

        setScrollInfo({
          scrollable: maxScroll > 0,
          position: roundedScrollLeft,
          isAtStart: roundedScrollLeft === 0,
          isAtEnd: maxScroll > 0 && roundedScrollLeft >= maxScroll - 1,
        });
      }
    };

    // Check scroll position on mount
    updateScrollInfo();

    // Add scroll event listener to the current container element
    const currentElement = scrollContainerRef.current;
    if (currentElement) {
      currentElement.addEventListener('scroll', updateScrollInfo);
      // Keep track of the element so we can remove the listener later, even if
      // `scrollContainerRef.current` changes.
      scrollListenerTargetRef.current = currentElement;
    }

    return () => {
      if (scrollListenerTargetRef.current) {
        scrollListenerTargetRef.current.removeEventListener(
          'scroll',
          updateScrollInfo,
        );
      }
    };
  }, []);

  const handleScroll = (direction: Direction) => {
    if (scrollContainerRef.current) {
      const { scrollLeft, clientWidth } = scrollContainerRef.current;
      const scrollAmount =
        Math.floor(clientWidth / ModelCardWidth) *
        (ModelCardWidth + ModelCardGap);
      const currentScroll = scrollLeft;
      const newScroll =
        direction === Direction.LEFT
          ? currentScroll - scrollAmount
          : currentScroll + scrollAmount;
      scrollContainerRef.current.scrollTo({
        left: newScroll,
        behavior: 'smooth',
      });
    }
  };

  // Removed slicing logic. Always render all models in the Grid.
  // Removed showAll state logic.

  // Function to render the error message safely
  const renderErrorMessage = () => {
    if (!error) return 'Unknown error';
    if (isFetchBaseQueryError(error)) {
      return ` ${error.status} ${JSON.stringify(error.data)}`;
    }
    if (isSerializedError(error)) {
      return error.message;
    }
    // position: 'relative', // This was incorrectly placed here
    return 'Unknown error structure';
  };

  return (
    <Box
      sx={{ background: 'transparent', borderRadius: '0px', boxShadow: 'none' }}
    >
      {' '}
      {/* Simplified section styling */}
      {/* Header with Title and "See all" link */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 2.5,
        }}
      >
        <Typography
          variant="h5" // Adjusted variant for new design
          sx={{
            fontWeight: 600, // Adjusted weight
            color: 'rgba(101, 101, 101, 1)', // Darker text color
            fontSize: { xs: '1.25rem', md: '1.5rem' }, // Adjusted font size
          }}
        >
          {title}
        </Typography>
        {/* "See all" link removed */}
      </Box>
      {/* Content Area (Loading, Error, or Grid) */}
      <Box sx={{ position: 'relative' }}>
        {' '}
        {/* Removed py:2 from here, ModelCard has padding */}
        {isLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Alert severity="error">
            Error loading models: {renderErrorMessage()}
          </Alert>
        ) : models && models.length > 0 ? (
          <Box
            ref={scrollContainerRef}
            display={'flex'}
            flexDirection={'row'}
            width={'100%'}
            overflow={'auto'}
            gap={`${ModelCardGap}px`}
            sx={{ scrollbarWidth: 'none' }}
          >
            {models.map((model) => (
              <ModelCard
                key={model.deployment_name || model.model_name}
                model={model}
                onSelectModel={onSelectModel}
              />
            ))}
            <ArrowButton
              disabled={!scrollInfo.scrollable || scrollInfo.isAtStart}
              direction={Direction.LEFT}
              onClick={() => handleScroll(Direction.LEFT)}
            />
            <ArrowButton
              disabled={!scrollInfo.scrollable || scrollInfo.isAtEnd}
              direction={Direction.RIGHT}
              onClick={() => handleScroll(Direction.RIGHT)}
            />
          </Box>
        ) : (
          <Typography variant="body2" color="text.secondary">
            No models available in this category.
          </Typography>
        )}
      </Box>
    </Box>
  );
}

export default ModelListSection;
