#!/usr/bin/env python3
"""
Test Azure OpenAI API Token Usage Response

This script tests the raw Azure OpenAI API to check if and how it returns token usage information.
It tests both streaming and non-streaming modes with different configurations.

Key test scenarios:
1. Non-streaming request - check if usage appears in response body
2. Streaming request without stream_options - check default behavior
3. Streaming request with stream_options.include_usage=true - check if usage appears in stream
4. Different API versions to see if behavior varies

The script will output the complete raw responses to help understand the exact format.
"""

import os
import json
import requests
import sys
from datetime import datetime
from dotenv import load_dotenv
import time

# Load environment variables
load_dotenv()

# Color codes for output
GREEN = '\033[92m'
RED = '\033[91m'
BLUE = '\033[94m'
YELLOW = '\033[93m'
RESET = '\033[0m'
BOLD = '\033[1m'

class AzureOpenAITokenTester:
    def __init__(self):
        # Azure OpenAI configuration - you'll need to set these in .env
        self.instance_name = os.getenv('AZURE_OPENAI_INSTANCE', 'hkbu-chatgpt-us-east2')
        self.api_key = os.getenv('AZURE_OPENAI_API_KEY')
        self.deployment_name = os.getenv('AZURE_OPENAI_DEPLOYMENT', 'chatgpt-4.1')
        self.api_version = os.getenv('AZURE_OPENAI_API_VERSION', '2024-12-01-preview')
        
        if not self.api_key:
            print(f"{RED}❌ Error: AZURE_OPENAI_API_KEY not found in .env file{RESET}")
            print(f"{YELLOW}Please add the following to your .env file:{RESET}")
            print(f"AZURE_OPENAI_API_KEY=your-api-key-here")
            print(f"AZURE_OPENAI_INSTANCE=hkbu-chatgpt-us-east2  # optional")
            print(f"AZURE_OPENAI_DEPLOYMENT=chatgpt-4.1  # optional")
            sys.exit(1)
        
        # Construct the base URL
        self.base_url = f"https://{self.instance_name}.openai.azure.com/openai/deployments/{self.deployment_name}"
        
    def print_section(self, title):
        """Print a formatted section header"""
        print(f"\n{BOLD}{BLUE}{'='*80}{RESET}")
        print(f"{BOLD}{BLUE}{title}{RESET}")
        print(f"{BOLD}{BLUE}{'='*80}{RESET}\n")
        
    def test_non_streaming(self):
        """Test non-streaming API call"""
        self.print_section("TEST 1: Non-Streaming Request")
        
        url = f"{self.base_url}/chat/completions?api-version={self.api_version}"
        
        headers = {
            "Content-Type": "application/json",
            "api-key": self.api_key
        }
        
        payload = {
            "messages": [
                {
                    "role": "system",
                    "content": "You are a helpful assistant."
                },
                {
                    "role": "user",
                    "content": "Say 'Hello, I am testing token usage' and nothing else."
                }
            ],
            "temperature": 0.7,
            "stream": False
        }
        
        print(f"{YELLOW}Request URL:{RESET} {url}")
        print(f"{YELLOW}Request Body:{RESET}")
        print(json.dumps(payload, indent=2))
        
        try:
            response = requests.post(url, headers=headers, json=payload, timeout=30)
            
            print(f"\n{YELLOW}Response Status:{RESET} {response.status_code}")
            print(f"{YELLOW}Response Headers:{RESET}")
            for key, value in response.headers.items():
                if key.lower() in ['content-type', 'x-ms-region', 'x-ratelimit-remaining-tokens']:
                    print(f"  {key}: {value}")
            
            if response.status_code == 200:
                response_json = response.json()
                print(f"\n{YELLOW}Response Body:{RESET}")
                print(json.dumps(response_json, indent=2))
                
                # Check for usage information
                if 'usage' in response_json:
                    print(f"\n{GREEN}✅ Token usage found in response!{RESET}")
                    usage = response_json['usage']
                    print(f"  Prompt tokens: {usage.get('prompt_tokens', 'N/A')}")
                    print(f"  Completion tokens: {usage.get('completion_tokens', 'N/A')}")
                    print(f"  Total tokens: {usage.get('total_tokens', 'N/A')}")
                else:
                    print(f"\n{RED}❌ No 'usage' field found in response{RESET}")
            else:
                print(f"\n{RED}Error Response:{RESET}")
                print(response.text)
                
        except Exception as e:
            print(f"\n{RED}Exception occurred:{RESET} {str(e)}")
            
    def test_streaming_without_options(self):
        """Test streaming API call without stream_options"""
        self.print_section("TEST 2: Streaming Request (without stream_options)")
        
        url = f"{self.base_url}/chat/completions?api-version={self.api_version}"
        
        headers = {
            "Content-Type": "application/json",
            "api-key": self.api_key
        }
        
        payload = {
            "messages": [
                {
                    "role": "system",
                    "content": "You are a helpful assistant."
                },
                {
                    "role": "user",
                    "content": "Say 'Hello, I am testing streaming' and nothing else."
                }
            ],
            "temperature": 0.7,
            "stream": True
        }
        
        print(f"{YELLOW}Request URL:{RESET} {url}")
        print(f"{YELLOW}Request Body:{RESET}")
        print(json.dumps(payload, indent=2))
        
        try:
            response = requests.post(url, headers=headers, json=payload, stream=True, timeout=30)
            
            print(f"\n{YELLOW}Response Status:{RESET} {response.status_code}")
            
            if response.status_code == 200:
                print(f"\n{YELLOW}Stream chunks:{RESET}")
                chunks = []
                
                for line in response.iter_lines():
                    if line:
                        line_str = line.decode('utf-8')
                        chunks.append(line_str)
                        
                        # Print first few chunks and last few chunks
                        if len(chunks) <= 3 or len(chunks) > 20:
                            print(f"  Chunk {len(chunks)}: {line_str[:200]}{'...' if len(line_str) > 200 else ''}")
                        elif len(chunks) == 4:
                            print(f"  ... (showing first 3 and last chunks only)")
                
                # Check if any chunk contains usage
                usage_found = False
                for i, chunk in enumerate(chunks):
                    if 'usage' in chunk:
                        print(f"\n{GREEN}✅ Token usage found in chunk {i+1}!{RESET}")
                        print(f"  {chunk}")
                        usage_found = True
                        
                if not usage_found:
                    print(f"\n{RED}❌ No 'usage' field found in any streaming chunk{RESET}")
                    
            else:
                print(f"\n{RED}Error Response:{RESET}")
                print(response.text)
                
        except Exception as e:
            print(f"\n{RED}Exception occurred:{RESET} {str(e)}")
            
    def test_streaming_with_options(self):
        """Test streaming API call with stream_options.include_usage=true"""
        self.print_section("TEST 3: Streaming Request (with stream_options.include_usage=true)")
        
        url = f"{self.base_url}/chat/completions?api-version={self.api_version}"
        
        headers = {
            "Content-Type": "application/json",
            "api-key": self.api_key
        }
        
        payload = {
            "messages": [
                {
                    "role": "system",
                    "content": "You are a helpful assistant."
                },
                {
                    "role": "user",
                    "content": "Say 'Hello, testing with stream options' and nothing else."
                }
            ],
            "temperature": 0.7,
            "stream": True,
            "stream_options": {
                "include_usage": True
            }
        }
        
        print(f"{YELLOW}Request URL:{RESET} {url}")
        print(f"{YELLOW}Request Body:{RESET}")
        print(json.dumps(payload, indent=2))
        
        try:
            response = requests.post(url, headers=headers, json=payload, stream=True, timeout=30)
            
            print(f"\n{YELLOW}Response Status:{RESET} {response.status_code}")
            
            if response.status_code == 200:
                print(f"\n{YELLOW}Stream chunks:{RESET}")
                chunks = []
                all_chunks_raw = []
                
                for line in response.iter_lines():
                    if line:
                        line_str = line.decode('utf-8')
                        all_chunks_raw.append(line_str)
                        
                        # Remove "data: " prefix if present
                        if line_str.startswith("data: "):
                            data_str = line_str[6:]
                            
                            # Skip [DONE] marker
                            if data_str.strip() == "[DONE]":
                                chunks.append("[DONE]")
                                print(f"  Chunk {len(chunks)}: [DONE]")
                                continue
                                
                            try:
                                chunk_json = json.loads(data_str)
                                chunks.append(chunk_json)
                                
                                # Print first few chunks and last few chunks
                                if len(chunks) <= 3:
                                    print(f"  Chunk {len(chunks)}: {json.dumps(chunk_json, indent=2)}")
                                elif len(chunks) == 4:
                                    print(f"  ... (showing detailed view of first 3 chunks and any chunk with usage)")
                                    
                                # Always print chunks with usage
                                if 'usage' in chunk_json:
                                    print(f"\n  {GREEN}Chunk {len(chunks)} (with usage):{RESET}")
                                    print(f"  {json.dumps(chunk_json, indent=2)}")
                                    
                            except json.JSONDecodeError:
                                print(f"  Chunk {len(chunks)}: Failed to parse JSON: {data_str[:100]}...")
                
                # Analyze results
                usage_found = False
                for i, chunk in enumerate(chunks):
                    if isinstance(chunk, dict) and 'usage' in chunk:
                        print(f"\n{GREEN}✅ Token usage found in chunk {i+1}!{RESET}")
                        usage = chunk['usage']
                        print(f"  Prompt tokens: {usage.get('prompt_tokens', 'N/A')}")
                        print(f"  Completion tokens: {usage.get('completion_tokens', 'N/A')}")
                        print(f"  Total tokens: {usage.get('total_tokens', 'N/A')}")
                        usage_found = True
                        
                if not usage_found:
                    print(f"\n{RED}❌ No 'usage' field found in any streaming chunk{RESET}")
                    print(f"\n{YELLOW}Last 5 raw chunks for debugging:{RESET}")
                    for chunk in all_chunks_raw[-5:]:
                        print(f"  {chunk}")
                    
            else:
                print(f"\n{RED}Error Response:{RESET}")
                print(response.text)
                
        except Exception as e:
            print(f"\n{RED}Exception occurred:{RESET} {str(e)}")
            
    def test_different_api_versions(self):
        """Test with different API versions"""
        self.print_section("TEST 4: Different API Versions (Non-Streaming)")
        
        api_versions = [
            '2023-03-15-preview',
            '2024-02-01',
            '2024-05-01-preview',
            '2024-12-01-preview'
        ]
        
        for version in api_versions:
            print(f"\n{YELLOW}Testing API Version: {version}{RESET}")
            
            url = f"{self.base_url}/chat/completions?api-version={version}"
            
            headers = {
                "Content-Type": "application/json",
                "api-key": self.api_key
            }
            
            payload = {
                "messages": [
                    {
                        "role": "user",
                        "content": "Say 'Hi' and nothing else."
                    }
                ],
                "temperature": 0.7,
                "stream": False
            }
            
            try:
                response = requests.post(url, headers=headers, json=payload, timeout=30)
                
                if response.status_code == 200:
                    response_json = response.json()
                    if 'usage' in response_json:
                        usage = response_json['usage']
                        print(f"  {GREEN}✅ Usage found - Tokens: {usage.get('total_tokens', 'N/A')}{RESET}")
                    else:
                        print(f"  {RED}❌ No usage field found{RESET}")
                else:
                    print(f"  {RED}❌ Error: {response.status_code}{RESET}")
                    
            except Exception as e:
                print(f"  {RED}❌ Exception: {str(e)}{RESET}")
                
            time.sleep(1)  # Rate limiting
            
    def run_all_tests(self):
        """Run all tests"""
        print(f"{BOLD}{GREEN}Azure OpenAI Token Usage Testing{RESET}")
        print(f"{YELLOW}Configuration:{RESET}")
        print(f"  Instance: {self.instance_name}")
        print(f"  Deployment: {self.deployment_name}")
        print(f"  API Version: {self.api_version}")
        print(f"  Base URL: {self.base_url}")
        
        self.test_non_streaming()
        time.sleep(2)  # Rate limiting
        
        self.test_streaming_without_options()
        time.sleep(2)  # Rate limiting
        
        self.test_streaming_with_options()
        time.sleep(2)  # Rate limiting
        
        self.test_different_api_versions()
        
        self.print_section("SUMMARY")
        print(f"{YELLOW}Key Findings:{RESET}")
        print("1. Check if non-streaming requests include usage in response body")
        print("2. Check if streaming requests include usage in any chunk")
        print("3. Check if stream_options.include_usage=true affects the response")
        print("4. Check if different API versions have different behaviors")
        print("\nReview the output above to understand Azure OpenAI's token usage reporting.")


if __name__ == "__main__":
    tester = AzureOpenAITokenTester()
    tester.run_all_tests()