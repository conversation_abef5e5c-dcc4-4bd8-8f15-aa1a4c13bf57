import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from './store';
// Import GptModel from common types
import type { GptModel } from '../types/common';

// Define Mode Enum
export enum Mode {
  chatgpt = 'chatgpt',
  chatgpt_multibot = 'chatgpt_multibot',
  image_generation = 'image_generation',
  text_embedding = 'text_embedding',
  chatgpt_web = 'chatgpt_web',
  file_chat = 'file_chat',
}

interface AppState {
  modelList: GptModel[]; // Now managed here based on selector usage in HomeScreen
  visionModelList: Array<string>; // Now managed here
  shouldUpdateGptModelList: boolean;
  shouldUpdateUsage: boolean;
  supportedFileList: string;
  multiBotModelList: Array<string>;

  // Modal Visibility States
  showFaqModal: boolean;
  showImgGenModal: boolean;
  showApiKeyModal: boolean;
  showTncModal: boolean;
  tncModalShowAgree: boolean;
  showFeedbackModal: boolean;
  showFeedbackSuccessModal: boolean;
  showContactUsModal: boolean;
  showHealthCheckModal: boolean;
  showPromptEngModal: boolean; // Added
  showRequestQuoteModal: boolean; // Added
  showBannerModal: boolean; // Added

  // Other UI States
  bannerNotified: boolean; // Added
  isHome: boolean; // Added
  mode: Mode; // Added
}

const initialState: AppState = {
  modelList: [], // Initial empty list
  visionModelList: [
    // Moved from modelSlice/initial context
    'claude-3-haiku',
    // 'claude-3-sonnet',
    'claude-3-5-sonnet',
    // 'gpt-4-vision',
    // 'gemini-1.0-pro-vision',
    'gemini-1.5-pro',
    'gemini-1.5-flash',
    'gpt-4-o',
    // 'llama3_1',
    'o1-mini',
    'o1-preview',
  ],
  shouldUpdateGptModelList: false,
  shouldUpdateUsage: true,
  supportedFileList:
    '.jpeg, .jpg, .png, .bmp, .tiff, .xlsx, .xls, .xlt, .xla, .xlsm, .xlsb, .xltx, .xltm, .xlam, .pptx, .txt, .csv, .pdf, .docx',
  multiBotModelList: ['gpt-4-turbo', 'gpt-4-o', 'gpt-4-o-mini'],

  // Initial Modal States
  showFaqModal: false,
  showImgGenModal: false,
  showApiKeyModal: false,
  showTncModal: false,
  tncModalShowAgree: true,
  showFeedbackModal: false,
  showFeedbackSuccessModal: false,
  showContactUsModal: false,
  showHealthCheckModal: false,
  showPromptEngModal: false, // Added
  showRequestQuoteModal: false, // Added
  showBannerModal: true, // Assume banner shows initially
  bannerNotified: false, // User has not dismissed banner yet
  isHome: true, // Start at home screen
  mode: Mode.chatgpt, // Default mode
};

const appSlice = createSlice({
  name: 'app',
  initialState,
  reducers: {
    // Existing reducers...
    setModelList: (state, action: PayloadAction<GptModel[]>) => {
      state.modelList = action.payload;
    },
    triggerUpdateGptModelList: (state) => {
      state.shouldUpdateGptModelList = true;
    },
    gptModelListUpdated: (state) => {
      state.shouldUpdateGptModelList = false;
    },
    triggerUpdateUsage: (state) => {
      state.shouldUpdateUsage = true;
    },
    usageUpdated: (state) => {
      state.shouldUpdateUsage = false;
    },
    setVisionModelFileList: (state) => {
      state.supportedFileList = '.jpeg, .jpg, .png, .bmp, .webp';
    },
    setTextModelFileList: (state) => {
      state.supportedFileList = initialState.supportedFileList;
    },

    // Existing Modal Actions...
    openFaqModal: (state) => {
      state.showFaqModal = true;
    },
    closeFaqModal: (state) => {
      state.showFaqModal = false;
    },
    openImgGenModal: (state) => {
      state.showImgGenModal = true;
    },
    closeImgGenModal: (state) => {
      state.showImgGenModal = false;
    },
    openApiKeyModal: (state) => {
      state.showApiKeyModal = true;
    },
    closeApiKeyModal: (state) => {
      state.showApiKeyModal = false;
    },
    openTncModal: (state) => {
      state.showTncModal = true;
    },
    closeTncModal: (state) => {
      state.showTncModal = false;
    },
    setTncModalShowAgree: (state, action: PayloadAction<boolean>) => {
      state.tncModalShowAgree = action.payload;
    },
    openFeedbackModal: (state) => {
      state.showFeedbackModal = true;
    },
    closeFeedbackModal: (state) => {
      state.showFeedbackModal = false;
    },
    openFeedbackSuccessModal: (state) => {
      state.showFeedbackSuccessModal = true;
    },
    closeFeedbackSuccessModal: (state) => {
      state.showFeedbackSuccessModal = false;
    },
    openContactUsModal: (state) => {
      state.showContactUsModal = true;
    },
    closeContactUsModal: (state) => {
      state.showContactUsModal = false;
    },
    openHealthCheckModal: (state) => {
      state.showHealthCheckModal = true;
    },
    closeHealthCheckModal: (state) => {
      state.showHealthCheckModal = false;
    },

    // Added Actions based on HomeScreen usage
    openPromptEngModal: (state) => {
      state.showPromptEngModal = true;
    },
    closePromptEngModal: (state) => {
      state.showPromptEngModal = false;
    },
    openRequestQuoteModal: (state) => {
      state.showRequestQuoteModal = true;
    },
    closeRequestQuoteModal: (state) => {
      state.showRequestQuoteModal = false;
    },
    closeBannerModal: (state) => {
      state.showBannerModal = false;
    },
    setBannerNotified: (state, action: PayloadAction<boolean>) => {
      state.bannerNotified = action.payload;
    },
    setIsNotHome: (state) => {
      state.isHome = false;
    },
    setIsHome: (state) => {
      state.isHome = true;
    }, // Added action to potentially reset home state
    // Assuming CHANGE_TO_CHATGPT action logic
    changeToChatgpt: (state) => {
      state.mode = Mode.chatgpt;
    },
    // Action to set the mode generally
    setMode: (state, action: PayloadAction<Mode>) => {
      state.mode = action.payload;
    },
  },
  // Consider adding extraReducers for handling async thunks fetching model lists later
});

export const {
  setModelList,
  triggerUpdateGptModelList,
  gptModelListUpdated,
  triggerUpdateUsage,
  usageUpdated,
  setVisionModelFileList,
  setTextModelFileList,
  // Export Existing Modal Actions
  openFaqModal,
  closeFaqModal,
  openImgGenModal,
  closeImgGenModal,
  openApiKeyModal,
  closeApiKeyModal,
  openTncModal,
  closeTncModal,
  setTncModalShowAgree,
  openFeedbackModal,
  closeFeedbackModal,
  openFeedbackSuccessModal,
  closeFeedbackSuccessModal,
  openContactUsModal,
  closeContactUsModal,
  openHealthCheckModal,
  closeHealthCheckModal,
  // Export Added Actions
  openPromptEngModal,
  closePromptEngModal,
  openRequestQuoteModal,
  closeRequestQuoteModal,
  closeBannerModal,
  setBannerNotified,
  setIsNotHome,
  setIsHome, // Export added action
  changeToChatgpt,
  setMode,
} = appSlice.actions;

// Selectors
export const selectAppState = (state: RootState) => state.app;
export const selectModelList = (state: RootState) => state.app.modelList; // Moved from modelSlice
export const selectVisionModelList = (state: RootState) =>
  state.app.visionModelList; // Moved from modelSlice
export const selectShouldUpdateUsage = (state: RootState) =>
  state.app.shouldUpdateUsage;
export const selectSupportedFileList = (state: RootState) =>
  state.app.supportedFileList;
export const selectMultiBotModelList = (state: RootState) =>
  state.app.multiBotModelList;
// Add selectors for added state
export const selectShowPromptEngModal = (state: RootState) =>
  state.app.showPromptEngModal;
export const selectShowRequestQuoteModal = (state: RootState) =>
  state.app.showRequestQuoteModal;
export const selectShowBannerModal = (state: RootState) =>
  state.app.showBannerModal;
export const selectBannerNotified = (state: RootState) =>
  state.app.bannerNotified;
export const selectIsHome = (state: RootState) => state.app.isHome;
export const selectMode = (state: RootState) => state.app.mode;

export default appSlice.reducer;
