'use client';

import React from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import LaunchIcon from '@mui/icons-material/Launch';
import { GptModel } from '@/lib/types/common';
import { modelInfo } from './model/ModelInfo';
// Removed model name normalization - using original database names for consistency
interface ModelCardProps {
  model: GptModel;
  onSelectModel: (model: GptModel) => void;
}

const scale = 0.7;

export const ModelCardWidth = 334 * scale;
export const ModelCardHeight = 141 * scale;

// Check if model opens external link
const isExternalModel = (model: GptModel): boolean => {
  return model.model_name === 'adobe-express' || model.model_name === 'adobe-firefly';
};

const ModelCard = ({ model, onSelectModel }: ModelCardProps) => {
  const iconComponent =
    modelInfo[model.model_name as keyof typeof modelInfo]?.svg ||
    modelInfo[model.display_name as keyof typeof modelInfo]?.svg ||
    null;

  const isUnavailable = model.availability_status === 'U';
  const isExternal = isExternalModel(model);

  return (
    <Box
      display={'flex'}
      flexDirection={{ xs: 'column', sm: 'row' }}
      alignItems={'center'}
      justifyContent={{ xs: 'center', sm: 'flex-start' }}
      width={{ xs: '100%', sm: ModelCardWidth }}
      height={{ xs: 'auto', sm: ModelCardHeight }}
      flexShrink={0}
      bgcolor={'background.paper'}
      border={'1px solid'}
      borderColor={'divider'}
      borderRadius={'15px'}
      onClick={() => {
        if (!isUnavailable) {
          // Use original model data as-is
          onSelectModel(model);
        }
      }}
      p={{ xs: 1, sm: '18px' }}
      gap={{ xs: 1, sm: '8px' }}
      boxShadow={'0 1px 3px 0 rgba(0,0,0,0.05)'}
      sx={{
        aspectRatio: { xs: '1 / 1', sm: 'auto' },
        maxHeight: { xs: 120, sm: 'none' },
        cursor: isUnavailable ? 'not-allowed' : 'pointer',
        opacity: isUnavailable ? 0.6 : 1,
        transition: 'box-shadow 0.2s, border-color 0.2s, background-color 0.2s',
        position: 'relative',
        '&:hover': {
          borderColor: isUnavailable ? 'divider' : 'primary.main',
          boxShadow: isUnavailable
            ? '0 1px 3px 0 rgba(0,0,0,0.05)'
            : '0 2px 8px 0 rgba(26,127,100,0.08)',
          bgcolor: isUnavailable ? 'background.paper' : 'action.hover',
        },
      }}
    >
      <Box
        width={{ xs: 40, sm: 78 * scale }}
        height={{ xs: 40, sm: 78 * scale }}
        flexShrink={0}
      >
        {iconComponent &&
          React.cloneElement(iconComponent, { width: '100%', height: '100%' })}
      </Box>

      <Box display={'flex'} flexDirection={'column'} height={'100%'} gap={1}>
        <Typography
          fontWeight={700}
          color={'text.primary'}
          fontSize={{ xs: 16, sm: 24 * scale }}
          lineHeight={1}
          textAlign={{ xs: 'center', sm: 'left' }}
        >
          {model.display_name}
        </Typography>

        <Typography
          fontWeight={'500'}
          color={'text.secondary'}
          fontSize={{ xs: 12, sm: 12 }}
          lineHeight={1.1}
          textOverflow={'ellipsis'}
          overflow={'hidden'}
          textAlign={{ xs: 'center', sm: 'left' }}
          sx={{
            display: '-webkit-box',
            WebkitLineClamp: { xs: 2, sm: 3 },
            WebkitBoxOrient: 'vertical',
          }}
        >
          {modelInfo[model.model_name]?.info}
        </Typography>
      </Box>

      {/* External Link Indicator */}
      {isExternal && (
        <Box
          sx={{
            position: 'absolute',
            top: 8,
            right: 8,
            backgroundColor: 'background.paper',
            borderRadius: '50%',
            width: 20,
            height: 20,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            boxShadow: '0 1px 3px 0 rgba(0,0,0,0.1)',
            border: '1px solid',
            borderColor: 'divider',
          }}
        >
          <LaunchIcon
            sx={{
              fontSize: 12,
              color: 'text.secondary',
            }}
          />
        </Box>
      )}
    </Box>
  );
};

export default ModelCard;
