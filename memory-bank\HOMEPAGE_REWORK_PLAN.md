# Homepage Rework Plan: `hkbu-genai-platform`

**Goal:** Transform the current homepage ([`hkbu-genai-platform/apps/web/src/app/page.tsx`](hkbu-genai-platform/apps/web/src/app/page.tsx:1)) to match the new UI design, ensuring responsiveness and implementing the specified behaviors.

**I. Core Layout Changes (in [`hkbu-genai-platform/apps/web/src/app/page.tsx`](hkbu-genai-platform/apps/web/src/app/page.tsx:1)):**

1.  **Remove Chat History Sidebar:**
    *   The current [`ChatHistorySidebar`](hkbu-genai-platform/apps/web/src/components/genai/ChatHistorySidebar.tsx:1) component and its associated logic (like `expanded` state and `toggleSidebarExpand` function) will be removed from the main page layout.
    *   The main content `Box`'s `marginLeft` styling, which adjusts based on the sidebar's state, will be removed or set to a static value (likely `0` or a small default padding if needed).
2.  **New Top Section:**
    *   **Title:** "What do you want to know?" - This seems to be already present ([`Typography variant="h4"`](hkbu-genai-platform/apps/web/src/app/page.tsx:235)). We'll ensure its styling matches the design.
    *   **Featured Model Chips:**
        *   Display "GPT-4.O" and "DeepSeek-R1" as clickable chips/buttons.
        *   These will be static. We'll need to ensure these models are correctly identified from the `allModels` data or hardcoded if they have specific identifiers not matching the display names.
        *   Clicking a chip will set it as the `selectedModel` for the main query input.
        *   Visually indicate the selected chip (e.g., different background color, border).
    *   **Model Search Icon:**
        *   Place a search icon next to the featured model chips.
        *   Clicking this icon will trigger the existing [`ModelSearchModal`](hkbu-genai-platform/apps/web/src/components/genai/ModelSearchModal.tsx:1) to open. The current `handleOpenSearchModal` logic can be reused.
3.  **Main Query Input:**
    *   The existing [`ChatInputArea`](hkbu-genai-platform/apps/web/src/components/genai/chat/ChatInputArea.tsx:1) component will be the central focus.
    *   Its styling will be updated to match the larger, more prominent input field in the design.
    *   The "Type your query" placeholder text should be used.
    *   The microphone and "A" (presumably for attachments or advanced options) icons need to be implemented or styled if they are part of `ChatInputArea`'s capabilities. If not, we'll need to assess adding them.
    *   Submission logic: When the user types a query and submits (e.g., presses Enter or a submit button within `ChatInputArea`), if a featured model chip was selected, the query should be submitted with that model. If no chip was selected, it might prompt the user to select a model or default to one (e.g., the first featured model).
4.  **Model Category Sections:**
    *   The existing [`ModelListSection`](hkbu-genai-platform/apps/web/src/components/genai/ModelListSection.tsx:1) component will be reused for "General Model," "Thinking Model," and "Image Generation."
    *   **"See all" Links:**
        *   Each section will have a "See all" link.
        *   Clicking this link will navigate to a new, dedicated page for that category (e.g., `/models/general`, `/models/thinking`, `/models/image-generation`). This will require creating new route handlers and pages for these category views.
    *   **Model Cards:** The styling of the model cards within [`ModelListSection`](hkbu-genai-platform/apps/web/src/components/genai/ModelListSection.tsx:1) (likely rendered by [`ModelCard`](hkbu-genai-platform/apps/web/src/components/genai/ModelCard.tsx:1) or similar) will need to be updated to match the design in the image (icon on the left, title, description).
    *   The horizontal scrolling/carousel for model cards within each section needs to be implemented.
5.  **Bottom Settings Icon:**
    *   A new settings icon will be added to the bottom-left corner of the page.
    *   Clicking this icon will navigate to a dedicated settings page (e.g., `/settings`). This will require creating a new route handler and page for settings.

**II. Component Modifications & New Components:**

1.  **[`hkbu-genai-platform/apps/web/src/app/page.tsx`](hkbu-genai-platform/apps/web/src/app/page.tsx:1):**
    *   Update state management for `selectedModel` to handle the new featured model chips.
    *   Adjust logic for `handleSubmitForHomepage` to use the `selectedModel` from the chips.
    *   Remove logic related to `selectedRecentModel` and the old recent model pills, as these are replaced by the featured model chips.
2.  **[`ChatInputArea`](hkbu-genai-platform/apps/web/src/components/genai/chat/ChatInputArea.tsx:1) (Potential modifications):**
    *   Review props and internal styling options to match the new design's input field.
    *   Ensure it can visually integrate the microphone and "A" icon if these are new additions.
3.  **[`ModelListSection`](hkbu-genai-platform/apps/web/src/components/genai/ModelListSection.tsx:1) / [`ModelCard`](hkbu-genai-platform/apps/web/src/components/genai/ModelCard.tsx:1):**
    *   Update styling to match the new card design.
    *   Implement horizontal scrolling/carousel for the model cards.
    *   Add the "See all" link and its navigation functionality.
4.  **New Category Pages (e.g., `app/models/[category]/page.tsx`):**
    *   Create dynamic route and page component.
    *   Fetch and display all models for the given category.
    *   Likely reuse [`ModelListSection`](hkbu-genai-platform/apps/web/src/components/genai/ModelListSection.tsx:1) or individual [`ModelCard`](hkbu-genai-platform/apps/web/src/components/genai/ModelCard.tsx:1)s in a grid or list layout.
5.  **New Settings Page (e.g., `app/settings/page.tsx`):**
    *   Create route and page component.
    *   This page will eventually house settings options and the access point for Chat History.
6.  **Featured Model Chip Component (New or adapted Chip):**
    *   A simple component or styled MUI `Chip`/`Button` to represent "GPT-4.O" and "DeepSeek-R1".
    *   Handles click events to update the selected model state on the main page.

**III. Styling and Responsiveness:**

*   All new and modified components will be styled using Tailwind CSS (as per [`tailwind.config.ts`](hkbu-genai-platform/apps/web/tailwind.config.ts:1)) and MUI, ensuring they match the provided UI design.
*   Flexbox and Grid will be used extensively for layout.
*   Media queries will be applied to ensure the page is responsive across various screen sizes (desktop, tablet, mobile). Particular attention will be paid to how the model sections and the main input area adapt.

**IV. State Management (Redux Toolkit - as per [`hkbu-genai-platform/apps/web/src/lib/store/`](hkbu-genai-platform/apps/web/src/lib/store/)):**

*   The selection of the featured model on the homepage will likely be managed by local component state in [`page.tsx`](hkbu-genai-platform/apps/web/src/app/page.tsx:1).
*   Navigation to new chat: The existing logic in `handleSelectModel` and `handleSubmitForHomepage` that dispatches `triggerClearMessage` and pushes to `/chat/new` with model and query parameters will be adapted.
*   Data fetching for models (`useGetModelsQuery`, `useGetRecentModelsQuery`) will continue to be handled by RTK Query. The `useGetRecentModelsQuery` might become less relevant for the homepage display if featured models are static.

**V. Workflow Diagram (Mermaid):**

```mermaid
graph TD
    A[User visits Homepage] --> B{Load Page Data};
    B -- Models API --> C[Fetch All Models];
    B -- Featured Models --> D[Display Featured Model Chips (GPT-4.O, DeepSeek-R1)];
    B -- UI --> E[Display Main Query Input];
    B -- UI --> F[Display Model Sections (General, Thinking, Image)];

    D -- Click Chip --> G[Set Selected Model for Query];
    E -- Type Query & Submit --> H{Query Submitted};
    G --> H;

    H -- Selected Model & Query --> I[Navigate to /chat/new?model=...&query=...];

    J[Click Model Search Icon (top)] --> K[Open ModelSearchModal];
    K -- Select Model in Modal --> L[Navigate to /chat/new?model=...];

    F -- Click "See all" in Section --> M[Navigate to /models/[category]];
    F -- Click Model Card in Section --> L;

    N[Click Settings Icon (bottom-left)] --> O[Navigate to /settings];

    subgraph Homepage UI Elements
        direction LR
        D;
        E;
        F;
        J;
        N;
    end

    subgraph Model Data Flow
        direction TB
        C --> D;
        C --> F;
        C --> K;
    end
```

**Key Implementation Steps:**

1.  **Skeleton Layout:** Modify [`page.tsx`](hkbu-genai-platform/apps/web/src/app/page.tsx:1) to remove the sidebar and set up the basic new structure (top section, query input, model sections container, settings icon placeholder).
2.  **Featured Models & Top Search:** Implement the static featured model chips and the top search icon functionality.
3.  **Main Query Input Styling:** Restyle [`ChatInputArea`](hkbu-genai-platform/apps/web/src/components/genai/chat/ChatInputArea.tsx:1).
4.  **Model Sections Rework:**
    *   Update [`ModelListSection`](hkbu-genai-platform/apps/web/src/components/genai/ModelListSection.tsx:1) and/or [`ModelCard`](hkbu-genai-platform/apps/web/src/components/genai/ModelCard.tsx:1) styling.
    *   Implement horizontal scrolling for cards.
    *   Add "See all" links (initially as placeholders).
5.  **Navigation & New Pages:**
    *   Create the new category pages (`/models/[category]`).
    *   Create the new settings page (`/settings`).
    *   Implement navigation logic for "See all" and the settings icon.
6.  **Responsiveness:** Apply responsive styling throughout the process.
7.  **Testing:** Thoroughly test all functionalities and responsive breakpoints.