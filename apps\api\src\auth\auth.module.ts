import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config'; // Import ConfigService
import { PrismaModule } from '../prisma/prisma.module';
import { ApiKeyService } from './api-key.service';
import { <PERSON><PERSON><PERSON><PERSON>Controller } from './api-key.controller';
import { NextAuthController } from './next-auth.controller';
import { ApiKeyStrategy } from './api-key.strategy';
import { AuthService } from './auth.service'; // Import AuthService
import { ThrottlerModule } from '@nestjs/throttler';
// If you have a JwtStrategy or other auth strategies, import them here
import { JwtStrategy } from './jwt.strategy';
import { PassportModule } from '@nestjs/passport';
import { JwtModule } from '@nestjs/jwt';

@Module({
  imports: [
    ConfigModule,
    PrismaModule,
    ThrottlerModule.forRoot([
      {
        // Default throttler config, can be overridden per-endpoint
        ttl: 60000, // 1 minute in milliseconds
        limit: 10, // 10 requests per minute
      },
    ]),
    PassportModule.register({ defaultStrategy: 'jwt' }), // If using Passport JWT
    JwtModule.registerAsync({
      // If using JWT
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        // Ensure this secret aligns with what JwtStrategy and NextAuth use
        secret: configService.get<string>('BUAM_NEXTAUTH_JWT_SECRET'),
        signOptions: { expiresIn: '1h' }, // Example: Token expiration, adjust as needed
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [
    ApiKeyService,
    ApiKeyStrategy,
    AuthService, // Add AuthService as a provider
    JwtStrategy, // If using JwtStrategy
  ],
  controllers: [ApiKeyController, NextAuthController],
  exports: [ApiKeyService, AuthService], // Export services
})
export class AuthModule {}
