'use client';

import React, { useEffect, Suspense } from 'react';
import GoogleAnalytics from '@/components/genai/GoogleAnalytics';
import ThemeRegistry from '@/components/ThemeRegistry/ThemeRegistry';
import StoreProvider from '@/lib/store/StoreProvider';
import './globals.css';
import AuthProvider from '@/app/providers/AuthProvider';
import { useSession } from 'next-auth/react';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import Box from '@mui/material/Box';
import IconButton from '@mui/material/IconButton';
import MenuIcon from '@mui/icons-material/Menu';
import ChatHistorySidebar, {
  SIDEBAR_WIDTH_COLLAPSED,
  SIDEBAR_WIDTH_EXPANDED,
} from '@/components/genai/ChatHistorySidebar';
import {
  ChatLayoutProvider,
  useChatLayout,
} from '@/contexts/ChatLayoutContext';
import { useTheme } from '@mui/material';
import { ConfigProvider } from '@/contexts/ConfigContext';
import { useTncCheck } from '@/hooks/useTncCheck';
import TncModal from '@/components/genai/modals/TncModal';
import { useAppSelector, useAppDispatch } from '@/lib/store/hooks';
import { selectShowTncModal, selectTncModalShowAgree, closeTncModal } from '@/lib/store/uiSlice';
import { useAcceptTncMutation } from '@/lib/store/apiSlice';

// Separate component for handling search params that needs Suspense
function FullscreenHandler({ children }: { children: React.ReactNode }) {
  const searchParams = useSearchParams();
  const isFullscreen = searchParams.get('fullscreen') === 'true';

  if (isFullscreen) {
    return (
      <Box
        sx={{
          width: '100vw',
          minHeight: '100svh',
          overflow: 'auto',
        }}
      >
        {children}
      </Box>
    );
  }

  return <>{children}</>;
}

// Component that handles authenticated layout with fullscreen support
function AuthenticatedLayout({ 
  children, 
  isNotFound, 
  isTncBlocking = false 
}: { 
  children: React.ReactNode; 
  isNotFound: boolean;
  isTncBlocking?: boolean;
}) {
  const { isSidebarExpanded, toggleSidebar, setIsSidebarExpanded } = useChatLayout();
  const theme = useTheme();
  const searchParams = useSearchParams();
  const isFullscreen = searchParams.get('fullscreen') === 'true';

  const handleContentClick = () => {
    if (isSidebarExpanded && window.innerWidth < theme.breakpoints.values.md) {
      setIsSidebarExpanded(false);
    }
  };

  // In fullscreen mode, render children directly without sidebar
  if (isFullscreen) {
    return <>{children}</>;
  }

  // Normal authenticated layout with sidebar
  return (
    <Box
      sx={{
        display: 'flex',
        minHeight: '100svh',
        maxWidth: '100vw',
        overflow: 'hidden',
      }}
    >
      {!isNotFound && <ChatHistorySidebar isBlocked={isTncBlocking} />}
      <Box
        component="main"
        onClick={handleContentClick}
        sx={{
          flexGrow: 1,
          transition: 'all 0.2s ease-out',
          width: isNotFound ? '100vw' : '100%',
          paddingLeft: isNotFound ? 0 : {
            xs: 0,
            md: isSidebarExpanded
              ? `${SIDEBAR_WIDTH_EXPANDED}px`
              : `${SIDEBAR_WIDTH_COLLAPSED}px`,
          },
        }}
      >
        {!isNotFound && (
          <IconButton
            onClick={toggleSidebar}
            size='small'
            sx={{
              display: { xs: 'block', md: 'none' },
              position: 'absolute',
              height: theme.mixins.toolbar?.minHeight,
              py: 0.5,
              top: 0,
              left: 16,
              zIndex: 1300, // Increased z-index to ensure visibility above sidebar
              color: 'text.secondary',
              opacity: isSidebarExpanded ? 0 : 1,
              pointerEvents: isSidebarExpanded ? 'none' : 'auto',
              transition: 'opacity 0.2s ease-in-out',
              backgroundColor: 'background.paper', // Add background for better visibility
              borderRadius: '50%',
              '&:hover': {
                backgroundColor: 'action.hover',
              },
            }}
          >
            <MenuIcon color='action' />
          </IconButton>
        )}
        
        {/* Emergency sidebar toggle for debugging - remove in production */}
        {process.env.NODE_ENV === 'development' && (
          <IconButton
            onClick={toggleSidebar}
            size='small'
            sx={{
              position: 'fixed',
              bottom: 20,
              right: 20,
              zIndex: 9999,
              backgroundColor: 'primary.main',
              color: 'primary.contrastText',
              '&:hover': {
                backgroundColor: 'primary.dark',
              },
            }}
            title="Emergency Sidebar Toggle (Dev Only)"
          >
            <MenuIcon />
          </IconButton>
        )}
        {children}
        {/* Overlay to block interaction when T&C acceptance is required */}
        {isTncBlocking && (
          <Box
            sx={{
              position: 'fixed',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              zIndex: 40,
              pointerEvents: 'all',
              cursor: 'not-allowed',
            }}
            onClick={(e) => e.preventDefault()}
          />
        )}
      </Box>
    </Box>
  );
}

// Component to handle T&C checking and modal display
function TncWrapper({ children }: { children: React.ReactNode }) {
  const { status } = useSession();
  const dispatch = useAppDispatch();
  const showTncModal = useAppSelector(selectShowTncModal);
  const tncModalShowAgree = useAppSelector(selectTncModalShowAgree);
  const [acceptTnc] = useAcceptTncMutation();

  // Initialize T&C checking for authenticated users
  useTncCheck();

  const handleAcceptTnc = async () => {
    try {
      await acceptTnc().unwrap();
      dispatch(closeTncModal());
      console.log('T&C accepted successfully');
    } catch (error) {
      console.error('Failed to accept T&C:', error);
    }
  };

  const handleCloseTncModal = () => {
    // Only allow closing if not required to agree
    if (!tncModalShowAgree) {
      dispatch(closeTncModal());
    }
  };

  const isTncBlocking = showTncModal && tncModalShowAgree;

  return (
    <>
      {React.Children.map(children, (child) => {
        if (React.isValidElement(child) && child.type === AuthenticatedLayout) {
          return React.cloneElement(child as React.ReactElement<any>, { isTncBlocking });
        }
        return child;
      })}
      {status === 'authenticated' && (
        <TncModal
          show={showTncModal}
          onClose={handleCloseTncModal}
          agree={tncModalShowAgree}
          agreeFn={handleAcceptTnc}
        />
      )}
    </>
  );
}

function AppContent({ children }: { children: React.ReactNode }) {
  const { status } = useSession();
  const router = useRouter();
  const pathname = usePathname();
  const { isNotFound, isSidebarExpanded } = useChatLayout();

  // Debug logging for layout state
  useEffect(() => {
    console.log('[AppContent] Layout state:', {
      pathname,
      isNotFound,
      isSidebarExpanded,
      status,
      timestamp: new Date().toISOString()
    });
  }, [pathname, isNotFound, isSidebarExpanded, status]);

  const initPath = '/';
  const backgroundImagePath = '/image.jpg';
  const signInPath = '/api/auth/signin';
  const errorPath = '/api/auth/error';
  const apiAuthPrefix = '/api/auth';

  const isPublicPath =
    pathname === backgroundImagePath ||
    pathname === initPath ||
    pathname === signInPath ||
    pathname === errorPath ||
    pathname.startsWith(apiAuthPrefix);

  useEffect(() => {
    if (status === 'loading') return;

    if (status === 'unauthenticated' && !isPublicPath) {
      router.push('/');
    }
  }, [status, router, pathname, isPublicPath]);

  if (status === 'loading') {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100svh',
        }}
      >
        Loading...
      </Box>
    );
  }

  if (status === 'unauthenticated' && isPublicPath) {
    return <>{children}</>;
  }

  if (status === 'authenticated') {
    // Wrap the authenticated layout in Suspense for search params
    return (
      <Suspense
        fallback={
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '100svh',
            }}
          >
            Loading...
          </Box>
        }
      >
        <FullscreenHandler>
          <TncWrapper>
            <AuthenticatedLayout isNotFound={isNotFound}>
              {children}
            </AuthenticatedLayout>
          </TncWrapper>
        </FullscreenHandler>
      </Suspense>
    );
  }

  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100svh',
      }}
    >
      Redirecting to sign-in...
    </Box>
  );
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <title>HKBU GenAI Platform</title>
        <meta name="description" content="HKBU Generative AI Platform" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0" />
      </head>
      <body id="__next" className="antialiased">
        <StoreProvider>
          <Suspense fallback={null}>
            <GoogleAnalytics />
          </Suspense>
          <ThemeRegistry>
            <AuthProvider>
              <ConfigProvider>
                <ChatLayoutProvider>
                  <AppContent>{children}</AppContent>
                </ChatLayoutProvider>
              </ConfigProvider>
            </AuthProvider>
          </ThemeRegistry>
        </StoreProvider>
      </body>
    </html>
  );
}
