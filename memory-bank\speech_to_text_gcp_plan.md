# Plan: Speech-to-Text with Google Cloud Auto Language Detection

**Goal:** Replace the browser's Web Speech API with Google Cloud Speech-to-Text, enabling automatic detection of English (`en-US`), Mandarin (`zh-CN`), and Cantonese (`yue-Hant-HK`).

**Architecture Overview:**

1.  **Frontend (React - `ChatInputArea.tsx`):** Capture audio using `MediaRecorder`, send audio blob to backend, display transcription.
2.  **Backend (NestJS - `apps/api`):** Create `/api/speech/transcribe` endpoint, use Google Cloud Speech SDK with auto language detection, return transcription.

**Sequence Diagram:**

```mermaid
sequenceDiagram
    participant User
    participant Frontend (ChatInputArea)
    participant Backend (API)
    participant GoogleCloudSpeech

    User->>+Frontend (ChatInputArea): Clicks <PERSON><PERSON> (Start)
    Frontend (ChatInputArea)->>Frontend (ChatInputArea): Request Mic Permission & Start MediaRecorder
    User->>+Frontend (ChatInputArea): Clicks <PERSON><PERSON> (Stop)
    Frontend (ChatInputArea)->>Frontend (ChatInputArea): Stop MediaRecorder & Create Audio Blob (ensure compatible type e.g., WAV)
    Frontend (ChatInputArea)->>+Backend (API): POST /api/speech/transcribe (Audio Blob)
    Backend (API)->>+GoogleCloudSpeech: Send Audio for Transcription (Auto Detect: en-US, zh-CN, yue-Hant-HK)
    GoogleCloudSpeech-->>-Backend (API): Return Transcription Result
    Backend (API)-->>-Frontend (ChatInputArea): Send Transcription Text
    Frontend (ChatInputArea)->>Frontend (ChatInputArea): Update Input Field
    Frontend (ChatInputArea)-->>-User: Display Transcription
```

**Detailed Plan:**

**Phase 1: Backend Implementation (NestJS API)**

1.  **Install Google Cloud SDK:** Add `@google-cloud/speech` to `apps/api`.
    ```bash
    # Using pnpm (adjust if using npm)
    pnpm --filter api add @google-cloud/speech
    ```
2.  **Configuration:**
    *   Obtain Google Cloud service account credentials (JSON key file).
    *   Set the `GOOGLE_APPLICATION_CREDENTIALS` environment variable to the path of the key file.
3.  **Create/Update Speech Module:**
    *   Ensure `SpeechModule`, `SpeechController`, `SpeechService` exist in `apps/api`.
4.  **Implement Controller (`speech.controller.ts`):**
    *   Define POST endpoint `/transcribe`.
    *   Use `@UseInterceptors(FileInterceptor('file'))` (assuming 'file' is the field name from frontend).
    *   Accept `@UploadedFile() file: Express.Multer.File`.
    *   Call `speechService.transcribeAudio(file.buffer)`.
    *   Return transcription result (e.g., `{ transcription: '...' }`).
5.  **Implement Service (`speech.service.ts`):**
    *   Import `SpeechClient` from `@google-cloud/speech`.
    *   Instantiate `const speechClient = new SpeechClient();`.
    *   Create `async transcribeAudio(audioBuffer: Buffer): Promise<string>` method.
    *   Inside method:
        *   Define `RecognitionConfig`:
            *   `encoding`: e.g., `LINEAR16` (match frontend format).
            *   `sampleRateHertz`: e.g., 16000 (match frontend format).
            *   `languageCode`: e.g., `en-US` (primary default).
            *   `alternativeLanguageCodes`: `['en-US', 'zh-CN', 'yue-Hant-HK']`.
            *   `enableAutomaticPunctuation`: `true`.
        *   Define `RecognitionAudio`:
            *   `content`: `audioBuffer.toString('base64')`.
        *   Prepare `request = { config: recognitionConfig, audio: recognitionAudio }`.
        *   Call `const [response] = await speechClient.recognize(request);`.
        *   Extract text from `response.results[0]?.alternatives[0]?.transcript`.
        *   Add error handling.
        *   Return transcription text.

**Phase 2: Frontend Implementation (React - `ChatInputArea.tsx`)**

1.  **Remove Web Speech API:** Delete `useEffect` hook for `window.SpeechRecognition`, related state/refs.
2.  **State Management:** Add `isProcessingAudio: boolean` state.
3.  **Audio Capture:**
    *   Use `navigator.mediaDevices.getUserMedia({ audio: true })`.
    *   Use `MediaRecorder`. Store chunks in `audioChunksRef`.
    *   On `stopRecording`:
        *   Stop recorder, set `isProcessingAudio = true`.
        *   On `onstop`: Create Blob (`new Blob(audioChunksRef.current, { type: 'audio/wav' })` - **ensure type matches backend config**). Call `sendAudioToBackend(audioBlob)`. Clear chunks.
4.  **API Call:**
    *   Implement `async sendAudioToBackend(audioBlob)`:
        *   Create `FormData`, append blob (`formData.append('file', audioBlob, 'recording.wav');`).
        *   POST FormData to `/api/speech/transcribe`.
        *   On success: Update input field, `isProcessingAudio = false`.
        *   On error: Show error message, `isProcessingAudio = false`.
5.  **UI Updates:**
    *   Update Mic button based on `isRecording`, `isProcessingAudio`.
    *   Disable relevant controls during processing.
    *   Handle mic permission errors.