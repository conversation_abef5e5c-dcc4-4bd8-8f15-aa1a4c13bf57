-- Migration: Add conversation sharing fields
-- Description: Add fields to support conversation sharing and duplication feature

-- Add sharing fields to conversation table
ALTER TABLE [dbo].[conversation]
ADD [share_id] UNIQUEIDENTIFIER NULL,
    [is_shared] BIT NOT NULL DEFAULT 0,
    [shared_from_uuid] UNIQUEIDENTIFIER NULL;

-- Add unique constraint for share_id
ALTER TABLE [dbo].[conversation]
ADD CONSTRAINT [UQ_conversation_share_id] UNIQUE ([share_id]);

-- Add index for shared_from_uuid to improve lookup performance
CREATE INDEX [IX_conversation_shared_from_uuid] 
ON [dbo].[conversation] ([shared_from_uuid])
WHERE [shared_from_uuid] IS NOT NULL;

-- Add index for is_shared to improve filtering performance
CREATE INDEX [IX_conversation_is_shared] 
ON [dbo].[conversation] ([is_shared])
WHERE [is_shared] = 1;

GO

-- Verify the changes
SELECT 
    c.COLUMN_NAME,
    c.DATA_TYPE,
    c.IS_NULLABLE,
    c.COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS c
WHERE c.TABLE_NAME = 'conversation'
    AND c.COLUMN_NAME IN ('share_id', 'is_shared', 'shared_from_uuid')
ORDER BY c.ORDINAL_POSITION;