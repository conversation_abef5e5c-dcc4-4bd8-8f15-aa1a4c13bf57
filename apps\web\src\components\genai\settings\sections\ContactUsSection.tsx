'use client';

import React from 'react';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Link from '@mui/material/Link'; // Using MUI Link

const ContactUsSection: React.FC = () => {
  const hyperLinkStyle = { color: 'primary.main' };

  return (
    <Box
      className="text-sm"
      sx={{ color: 'text.primary', textAlign: 'center' }}
    >
      <Typography variant="body2" paragraph>
        For technical assistance, please contact ITO Service Centre:
      </Typography>
      <Typography variant="body2">
        Phone:{' '}
        <Link sx={hyperLinkStyle} href="tel:34117899">
          3411 7899
        </Link>
      </Typography>
      <Typography variant="body2">
        Email:{' '}
        <Link
          sx={hyperLinkStyle}
          href="mailto:<EMAIL>"
          target="_blank"
          rel="noopener noreferrer"
        >
          <EMAIL>
        </Link>
      </Typography>
    </Box>
  );
};

export default ContactUsSection;
