'use client';

import React, { useEffect } from 'react';
import { useTheme } from '@mui/material/styles';
import SwaggerUI from 'swagger-ui-react';
import 'swagger-ui-react/swagger-ui.css';

type Props = {
  spec: Record<string, any>;
};

function ThemedSwaggerUI({ spec }: Props) {
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';

  useEffect(() => {
    
    // Create or update custom CSS for Swagger UI themes
    const styleId = 'swagger-theme-styles';
    let existingStyle = document.getElementById(styleId);
    
    // Always recreate the style element to ensure clean state
    if (existingStyle) {
      existingStyle.remove();
    }
    
    existingStyle = document.createElement('style');
    existingStyle.id = styleId;
    document.head.appendChild(existingStyle);

    // Generate CSS based on current theme
    const themeCss = isDarkMode ? `
      .swagger-ui {
        color: ${theme.palette.text.primary};
      }
      
      .swagger-ui .topbar {
        background-color: ${theme.palette.background.paper};
        border-bottom: 1px solid rgba(255, 255, 255, 0.12);
      }
      
      .swagger-ui .info {
        background-color: ${theme.palette.background.default};
      }
      
      .swagger-ui .info .title {
        color: ${theme.palette.text.primary};
      }
      
      .swagger-ui .info .description {
        color: ${theme.palette.text.primary} !important;
        opacity: 0.9 !important;
      }
      
      .swagger-ui .info .description p,
      .swagger-ui .info .description div,
      .swagger-ui .info .description span {
        color: ${theme.palette.text.primary} !important;
        opacity: 0.9 !important;
      }
      
      .swagger-ui .info .version {
        color: ${theme.palette.text.secondary};
      }
      
      .swagger-ui .scheme-container {
        background-color: ${theme.palette.background.paper};
        border: 1px solid rgba(255, 255, 255, 0.12);
      }
      
      .swagger-ui .opblock-tag {
        color: ${theme.palette.text.primary};
        border-bottom: 1px solid rgba(255, 255, 255, 0.12);
      }
      
      .swagger-ui .opblock {
        background-color: ${theme.palette.background.paper};
        border: 1px solid rgba(255, 255, 255, 0.12);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }
      
      .swagger-ui .opblock .opblock-summary {
        border-top: 1px solid rgba(255, 255, 255, 0.12);
        border-bottom: 1px solid rgba(255, 255, 255, 0.12);
      }
      
      .swagger-ui .opblock-summary-description,
      .swagger-ui .opblock-summary-path {
        color: ${theme.palette.text.primary};
      }
      
      .swagger-ui .opblock-description-wrapper,
      .swagger-ui .opblock-body {
        background-color: ${theme.palette.background.default};
      }
      
      .swagger-ui .parameter__name,
      .swagger-ui .parameter__type {
        color: ${theme.palette.text.primary};
      }
      
      .swagger-ui .parameter__description {
        color: ${theme.palette.text.secondary};
      }
      
      .swagger-ui table thead tr th,
      .swagger-ui table thead tr td {
        color: ${theme.palette.text.primary};
        border-bottom: 1px solid rgba(255, 255, 255, 0.12);
      }
      
      .swagger-ui table tbody tr td {
        color: ${theme.palette.text.secondary};
        border-bottom: 1px solid rgba(255, 255, 255, 0.12);
      }
      
      .swagger-ui .model-box {
        background-color: ${theme.palette.background.paper};
        border: 1px solid rgba(255, 255, 255, 0.12);
      }
      
      .swagger-ui .model .property {
        color: ${theme.palette.text.primary};
        border-top: 1px solid rgba(255, 255, 255, 0.12);
      }
      
      .swagger-ui .model .property .prop-type {
        color: ${theme.palette.primary.main};
      }
      
      .swagger-ui .model .property .prop-format {
        color: ${theme.palette.text.secondary};
      }
      
      .swagger-ui .response-col_status {
        color: ${theme.palette.text.primary};
      }
      
      .swagger-ui .response-col_description {
        color: ${theme.palette.text.secondary};
      }
      
      .swagger-ui .responses-inner h4,
      .swagger-ui .responses-inner h5 {
        color: ${theme.palette.text.primary};
      }
      
      .swagger-ui input[type=text],
      .swagger-ui input[type=password],
      .swagger-ui input[type=email],
      .swagger-ui textarea {
        background-color: ${theme.palette.background.paper};
        color: ${theme.palette.text.primary};
        border: 1px solid rgba(255, 255, 255, 0.12);
      }
      
      .swagger-ui input[type=text]:focus,
      .swagger-ui input[type=password]:focus,
      .swagger-ui input[type=email]:focus,
      .swagger-ui textarea:focus {
        border-color: ${theme.palette.primary.main};
        box-shadow: 0 0 0 1px ${theme.palette.primary.main};
      }
      
      .swagger-ui select {
        background-color: ${theme.palette.background.paper};
        color: ${theme.palette.text.primary};
        border: 1px solid rgba(255, 255, 255, 0.12);
      }
      
      .swagger-ui .btn {
        background-color: ${theme.palette.primary.main};
        color: ${theme.palette.primary.contrastText};
        border: 1px solid ${theme.palette.primary.main};
      }
      
      .swagger-ui .btn:hover {
        background-color: ${theme.palette.primary.dark};
        border-color: ${theme.palette.primary.dark};
      }
      
      .swagger-ui .btn.cancel {
        background-color: transparent;
        color: ${theme.palette.text.secondary};
        border-color: rgba(255, 255, 255, 0.12);
      }
      
      .swagger-ui .authorization__btn {
        background-color: transparent;
        border: 1px solid ${theme.palette.primary.main};
        color: ${theme.palette.primary.main};
      }
      
      .swagger-ui .authorization__btn:hover {
        background-color: ${theme.palette.primary.main};
        color: ${theme.palette.primary.contrastText};
      }
      
      .swagger-ui .highlight-code .microlight {
        background-color: ${theme.palette.background.paper};
        color: ${theme.palette.text.primary};
      }
      
      .swagger-ui .microlight .comment {
        color: ${theme.palette.text.secondary};
      }
      
      .swagger-ui .microlight .string {
        color: ${theme.palette.text.primary} !important;
        font-weight: normal !important;
      }
      
      .swagger-ui .microlight .number {
        color: ${theme.palette.text.primary} !important;
        font-weight: normal !important;
      }
      
      .swagger-ui .microlight .keyword {
        color: ${theme.palette.text.primary} !important;
        font-weight: normal !important;
      }
      
      .swagger-ui .dialog-ux .backdrop-ux {
        background-color: rgba(0, 0, 0, 0.7);
      }
      
      .swagger-ui .dialog-ux .modal-ux {
        background-color: ${theme.palette.background.paper};
        border: 1px solid rgba(255, 255, 255, 0.12);
      }
      
      .swagger-ui .dialog-ux .modal-ux-header {
        background-color: ${theme.palette.background.default};
        border-bottom: 1px solid rgba(255, 255, 255, 0.12);
        color: ${theme.palette.text.primary};
      }
      
      .swagger-ui .dialog-ux .modal-ux-content {
        color: ${theme.palette.text.primary};
      }
      
      .swagger-ui section.models {
        border: 1px solid rgba(255, 255, 255, 0.12);
      }
      
      .swagger-ui section.models .model-container {
        background-color: ${theme.palette.background.paper};
        border-bottom: 1px solid rgba(255, 255, 255, 0.12);
      }
      
      .swagger-ui section.models .model-container:hover {
        background-color: ${theme.palette.background.default};
      }
      
      .swagger-ui .model-title {
        color: ${theme.palette.text.primary};
      }
      
      .swagger-ui section.models .model-title:hover {
        color: ${theme.palette.primary.main};
      }
      
      .swagger-ui .tab li {
        color: ${theme.palette.text.secondary};
        border-bottom: 1px solid rgba(255, 255, 255, 0.12);
      }
      
      .swagger-ui .tab li.active {
        color: ${theme.palette.text.primary};
        border-bottom-color: ${theme.palette.primary.main};
      }
      
      .swagger-ui .tab li button {
        color: inherit;
        background: none;
        border: none;
      }
      
      .swagger-ui .opblock.is-open .opblock-summary {
        border-bottom: 1px solid rgba(255, 255, 255, 0.12);
      }
      
      .swagger-ui .parameter__extension,
      .swagger-ui .parameter__in {
        color: ${theme.palette.text.secondary};
      }
      
      .swagger-ui .response-col_links {
        color: ${theme.palette.text.secondary};
      }
      
      .swagger-ui code {
        background-color: ${theme.palette.background.paper};
        color: ${theme.palette.text.primary};
        border: 1px solid rgba(255, 255, 255, 0.12);
      }
      
      .swagger-ui pre {
        background-color: ${theme.palette.background.paper};
        color: ${theme.palette.text.primary};
        border: 1px solid rgba(255, 255, 255, 0.12);
      }
      
      /* HTTP Method Colors */
      .swagger-ui .opblock.opblock-get {
        border-color: #61affe;
        background-color: rgba(97, 175, 254, 0.1);
      }
      
      .swagger-ui .opblock.opblock-get .opblock-summary-method {
        background-color: #61affe;
      }
      
      .swagger-ui .opblock.opblock-post {
        border-color: #49cc90;
        background-color: rgba(73, 204, 144, 0.1);
      }
      
      .swagger-ui .opblock.opblock-post .opblock-summary-method {
        background-color: #49cc90;
      }
      
      .swagger-ui .opblock.opblock-put {
        border-color: #fca130;
        background-color: rgba(252, 161, 48, 0.1);
      }
      
      .swagger-ui .opblock.opblock-put .opblock-summary-method {
        background-color: #fca130;
      }
      
      .swagger-ui .opblock.opblock-delete {
        border-color: #f93e3e;
        background-color: rgba(249, 62, 62, 0.1);
      }
      
      .swagger-ui .opblock.opblock-delete .opblock-summary-method {
        background-color: #f93e3e;
      }
      
      .swagger-ui .opblock.opblock-patch {
        border-color: #50e3c2;
        background-color: rgba(80, 227, 194, 0.1);
      }
      
      .swagger-ui .opblock.opblock-patch .opblock-summary-method {
        background-color: #50e3c2;
      }
      
      /* Enhanced Dropdown Styling */
      .swagger-ui select:focus {
        border-color: ${theme.palette.primary.main};
        box-shadow: 0 0 0 2px rgba(42, 101, 253, 0.2);
        outline: none;
      }
      
      .swagger-ui select option {
        background-color: ${theme.palette.background.paper};
        color: ${theme.palette.text.primary};
      }
      
      /* Improved Syntax Highlighting */
      .swagger-ui .microlight .boolean {
        color: ${theme.palette.text.primary} !important;
        font-weight: normal !important;
      }
      
      .swagger-ui .microlight .null {
        color: ${theme.palette.text.secondary} !important;
        font-weight: normal !important;
      }
      
      .swagger-ui .microlight .property {
        color: ${theme.palette.text.primary} !important;
        font-weight: normal !important;
      }
      
      .swagger-ui .microlight .bracket {
        color: ${theme.palette.text.primary} !important;
        font-weight: normal !important;
      }
      
      /* Enhanced Focus States */
      .swagger-ui input[type=text]:focus,
      .swagger-ui input[type=password]:focus,
      .swagger-ui input[type=email]:focus,
      .swagger-ui textarea:focus {
        border-color: ${theme.palette.primary.main};
        box-shadow: 0 0 0 2px rgba(42, 101, 253, 0.2);
        outline: none;
      }
      
      .swagger-ui .btn:focus {
        box-shadow: 0 0 0 2px rgba(42, 101, 253, 0.2);
        outline: none;
      }
      
      /* Response Content Styling */
      .swagger-ui .response-content-type {
        background-color: ${theme.palette.background.paper};
        color: ${theme.palette.text.primary};
        border: 1px solid rgba(255, 255, 255, 0.12);
      }
      
      .swagger-ui .response-content-type.controls-accept-header select {
        background-color: ${theme.palette.background.paper};
        color: ${theme.palette.text.primary};
        border: 1px solid rgba(255, 255, 255, 0.12);
      }
      
      /* Fix OAS Version Badge - try all possible selectors */
      .swagger-ui .version-stamp,
      .swagger-ui .info .version-stamp,
      .swagger-ui .info hgroup .version-stamp,
      .swagger-ui hgroup .version-stamp,
      .swagger-ui .info hgroup.main .version-stamp,
      .swagger-ui .info .version small,
      .swagger-ui .info .version span,
      .swagger-ui .version-stamp pre,
      .swagger-ui .version-stamp code,
      .swagger-ui .info .version pre,
      .swagger-ui .info .version code {
        background-color: #4CAF50 !important;
        color: white !important;
        border: none !important;
        padding: 2px 6px !important;
        border-radius: 3px !important;
        font-size: 12px !important;
      }
      
      /* Target any element inside version that might be the badge */
      .swagger-ui .info .version * {
        background-color: #4CAF50 !important;
        color: white !important;
        border: none !important;
      }
      
      /* Authorization Modal Styling */
      .swagger-ui .auth-wrapper {
        background-color: ${theme.palette.background.paper} !important;
        border: 1px solid rgba(255, 255, 255, 0.12) !important;
        border-radius: 8px !important;
      }
      
      .swagger-ui .auth-container {
        background-color: ${theme.palette.background.paper} !important;
        color: ${theme.palette.text.primary} !important;
      }
      
      .swagger-ui .auth-container h4,
      .swagger-ui .auth-container h5,
      .swagger-ui .auth-container label,
      .swagger-ui .auth-container p {
        color: ${theme.palette.text.primary} !important;
      }
      
      .swagger-ui .auth-container .description {
        color: ${theme.palette.text.secondary} !important;
      }
      
      .swagger-ui .auth-container input[type="text"],
      .swagger-ui .auth-container input[type="password"] {
        background-color: ${theme.palette.background.default} !important;
        color: ${theme.palette.text.primary} !important;
        border: 1px solid rgba(255, 255, 255, 0.12) !important;
      }
      
      .swagger-ui .auth-container input[type="text"]:focus,
      .swagger-ui .auth-container input[type="password"]:focus {
        border-color: ${theme.palette.primary.main} !important;
        box-shadow: 0 0 0 2px rgba(42, 101, 253, 0.2) !important;
        outline: none !important;
      }
      
      .swagger-ui .auth-btn-wrapper .authorize {
        background-color: ${theme.palette.primary.main} !important;
        color: ${theme.palette.primary.contrastText} !important;
        border: none !important;
      }
      
      .swagger-ui .auth-btn-wrapper .authorize:hover {
        background-color: ${theme.palette.primary.dark} !important;
      }
      
      .swagger-ui .auth-btn-wrapper .close {
        background-color: transparent !important;
        color: ${theme.palette.text.secondary} !important;
        border: 1px solid rgba(255, 255, 255, 0.12) !important;
      }
      
      .swagger-ui .auth-btn-wrapper .close:hover {
        background-color: rgba(255, 255, 255, 0.05) !important;
        color: ${theme.palette.text.primary} !important;
      }
      
      /* Modal overlay and container */
      .swagger-ui .dialog-ux .backdrop-ux {
        background-color: rgba(0, 0, 0, 0.7) !important;
      }
      
      .swagger-ui .dialog-ux .modal-ux {
        background-color: ${theme.palette.background.paper} !important;
        border: 1px solid rgba(255, 255, 255, 0.12) !important;
        border-radius: 8px !important;
      }
      
      .swagger-ui .dialog-ux .modal-ux-header {
        background-color: ${theme.palette.background.paper} !important;
        border-bottom: 1px solid rgba(255, 255, 255, 0.12) !important;
        color: ${theme.palette.text.primary} !important;
      }
      
      .swagger-ui .dialog-ux .modal-ux-content {
        background-color: ${theme.palette.background.paper} !important;
        color: ${theme.palette.text.primary} !important;
      }
      
      /* Close button in modal header */
      .swagger-ui .dialog-ux .modal-ux-header .close-modal {
        color: ${theme.palette.text.secondary} !important;
        background: none !important;
        border: none !important;
      }
      
      .swagger-ui .dialog-ux .modal-ux-header .close-modal:hover {
        color: ${theme.palette.text.primary} !important;
      }
      
      /* Schema and Model Section Styling */
      .swagger-ui section.models h4,
      .swagger-ui section.models .model-title {
        color: ${theme.palette.text.primary} !important;
        font-weight: 600 !important;
      }
      
      .swagger-ui section.models .model-title:hover {
        color: ${theme.palette.primary.main} !important;
      }
      
      .swagger-ui .model-container {
        background-color: ${theme.palette.background.paper} !important;
        border: 1px solid rgba(255, 255, 255, 0.12) !important;
      }
      
      .swagger-ui .model-container:hover {
        background-color: ${theme.palette.background.default} !important;
      }
      
      .swagger-ui .models-control {
        color: ${theme.palette.text.primary} !important;
      }
      
      /* Schema property styling */
      .swagger-ui .model .property .prop-name {
        color: ${theme.palette.text.primary} !important;
      }
      
      .swagger-ui .model .property .prop-type {
        color: ${theme.palette.primary.main} !important;
      }
      
      .swagger-ui .model .property .prop-format {
        color: ${theme.palette.text.secondary} !important;
      }
      
      /* Expandable section headers */
      .swagger-ui .opblock-tag {
        color: ${theme.palette.text.primary} !important;
        border-bottom: 1px solid rgba(255, 255, 255, 0.12) !important;
      }
      
      .swagger-ui .opblock-tag small {
        color: ${theme.palette.text.secondary} !important;
      }
      
      /* Schema section header */
      .swagger-ui section.models h4 span {
        color: ${theme.palette.text.primary} !important;
      }
      
      /* Model toggle button */
      .swagger-ui .models-control button {
        color: ${theme.palette.text.primary} !important;
        background: none !important;
        border: none !important;
      }
      
      .swagger-ui .models-control button:hover {
        color: ${theme.palette.primary.main} !important;
      }
      
      /* Operation path text visibility */
      .swagger-ui .opblock-summary-path {
        color: ${theme.palette.text.primary} !important;
        font-weight: 500 !important;
      }
      
      .swagger-ui .opblock-summary-path__deprecated {
        color: ${theme.palette.text.secondary} !important;
      }
      
      /* Operation summary text */
      .swagger-ui .opblock-summary-description {
        color: ${theme.palette.text.primary} !important;
      }
      
      /* Operation summary container */
      .swagger-ui .opblock-summary {
        color: ${theme.palette.text.primary} !important;
      }
      
      /* Ensure all text in operation blocks is visible */
      .swagger-ui .opblock .opblock-summary .opblock-summary-path,
      .swagger-ui .opblock .opblock-summary .opblock-summary-description {
        color: ${theme.palette.text.primary} !important;
      }
      
      /* CRITICAL: Response Body Text Visibility Fixes */
      .swagger-ui .responses-wrapper {
        background-color: ${theme.palette.background.paper} !important;
        color: ${theme.palette.text.primary} !important;
      }
      
      .swagger-ui .live-responses-table {
        background-color: ${theme.palette.background.paper} !important;
        color: ${theme.palette.text.primary} !important;
      }
      
      .swagger-ui .response {
        color: ${theme.palette.text.primary} !important;
        background-color: ${theme.palette.background.paper} !important;
      }
      
      .swagger-ui .response .response-content {
        background-color: ${theme.palette.background.default} !important;
        color: ${theme.palette.text.primary} !important;
      }
      
      .swagger-ui .response pre {
        background-color: ${theme.palette.background.default} !important;
        color: ${theme.palette.text.primary} !important;
        border: 1px solid rgba(255, 255, 255, 0.12) !important;
      }
      
      .swagger-ui .response pre code {
        background-color: transparent !important;
        color: ${theme.palette.text.primary} !important;
      }
      
      .swagger-ui .response-content pre {
        background-color: ${theme.palette.background.default} !important;
        color: ${theme.palette.text.primary} !important;
        border: 1px solid rgba(255, 255, 255, 0.12) !important;
        padding: 12px !important;
      }
      
      .swagger-ui .response-content pre code {
        background-color: transparent !important;
        color: ${theme.palette.text.primary} !important;
      }
      
      /* Enhanced JSON Content Visibility */
      .swagger-ui .microlight {
        background-color: ${theme.palette.background.default} !important;
        color: ${theme.palette.text.primary} !important;
      }
      
      /* Force ALL microlight elements to use theme colors */
      .swagger-ui .microlight * {
        color: ${theme.palette.text.primary} !important;
      }
      
      /* Nuclear option: Override any remaining green text */
      .swagger-ui pre code span[style*="color"] {
        color: ${theme.palette.text.primary} !important;
      }
      
      .swagger-ui .highlight-code span {
        color: ${theme.palette.text.primary} !important;
      }
      
      /* JSON Response Table Styling */
      .swagger-ui .live-responses-table .response-content {
        background-color: ${theme.palette.background.default} !important;
      }
      
      .swagger-ui .live-responses-table tbody tr td {
        background-color: ${theme.palette.background.paper} !important;
        color: ${theme.palette.text.primary} !important;
        border: 1px solid rgba(255, 255, 255, 0.12) !important;
      }
      
      .swagger-ui .live-responses-table thead tr th {
        background-color: ${theme.palette.background.paper} !important;
        color: ${theme.palette.text.primary} !important;
        border: 1px solid rgba(255, 255, 255, 0.12) !important;
      }
      
      /* Response Details Container */
      .swagger-ui .responses-table {
        background-color: ${theme.palette.background.paper} !important;
        border: 1px solid rgba(255, 255, 255, 0.12) !important;
      }
      
      .swagger-ui .responses-table td {
        background-color: ${theme.palette.background.paper} !important;
        color: ${theme.palette.text.primary} !important;
        border: 1px solid rgba(255, 255, 255, 0.12) !important;
      }
      
      .swagger-ui .responses-table th {
        background-color: ${theme.palette.background.default} !important;
        color: ${theme.palette.text.primary} !important;
        border: 1px solid rgba(255, 255, 255, 0.12) !important;
      }
    ` : `
      /* Light Mode Styles */
      .swagger-ui {
        color: ${theme.palette.text.primary} !important;
        background-color: ${theme.palette.background.default} !important;
      }
      
      /* Override any dark mode styles that might persist */
      .swagger-ui * {
        color: inherit;
      }
      
      .swagger-ui .topbar {
        background-color: ${theme.palette.background.paper};
        border-bottom: 1px solid ${theme.palette.divider};
      }
      
      .swagger-ui .info {
        background-color: ${theme.palette.background.default};
      }
      
      .swagger-ui .info .title {
        color: ${theme.palette.text.primary};
      }
      
      .swagger-ui .info .description {
        color: ${theme.palette.text.primary} !important;
        opacity: 0.9 !important;
      }
      
      .swagger-ui .info .description p,
      .swagger-ui .info .description div,
      .swagger-ui .info .description span {
        color: ${theme.palette.text.primary} !important;
        opacity: 0.9 !important;
      }
      
      .swagger-ui .info .version {
        color: ${theme.palette.text.secondary};
      }
      
      .swagger-ui .scheme-container {
        background-color: ${theme.palette.background.paper};
        border: 1px solid ${theme.palette.divider};
      }
      
      .swagger-ui .opblock-tag {
        color: ${theme.palette.text.primary};
        border-bottom: 1px solid ${theme.palette.divider};
      }
      
      .swagger-ui .opblock {
        background-color: ${theme.palette.background.paper};
        border: 1px solid ${theme.palette.divider};
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      
      .swagger-ui .opblock .opblock-summary {
        border-top: 1px solid ${theme.palette.divider};
        border-bottom: 1px solid ${theme.palette.divider};
      }
      
      .swagger-ui .opblock-summary-description,
      .swagger-ui .opblock-summary-path {
        color: ${theme.palette.text.primary};
      }
      
      .swagger-ui .opblock-description-wrapper,
      .swagger-ui .opblock-body {
        background-color: ${theme.palette.background.default};
      }
      
      .swagger-ui .parameter__name,
      .swagger-ui .parameter__type {
        color: ${theme.palette.text.primary};
      }
      
      .swagger-ui .parameter__description {
        color: ${theme.palette.text.secondary};
      }
      
      .swagger-ui table thead tr th,
      .swagger-ui table thead tr td {
        color: ${theme.palette.text.primary};
        border-bottom: 1px solid ${theme.palette.divider};
        background-color: ${theme.palette.background.paper};
      }
      
      .swagger-ui table tbody tr td {
        color: ${theme.palette.text.secondary};
        border-bottom: 1px solid ${theme.palette.divider};
      }
      
      .swagger-ui .model-box {
        background-color: ${theme.palette.background.paper};
        border: 1px solid ${theme.palette.divider};
      }
      
      .swagger-ui .model .property {
        color: ${theme.palette.text.primary};
        border-top: 1px solid ${theme.palette.divider};
      }
      
      .swagger-ui .model .property .prop-type {
        color: ${theme.palette.primary.main};
      }
      
      .swagger-ui .model .property .prop-format {
        color: ${theme.palette.text.secondary};
      }
      
      .swagger-ui .response-col_status {
        color: ${theme.palette.text.primary};
      }
      
      .swagger-ui .response-col_description {
        color: ${theme.palette.text.secondary};
      }
      
      .swagger-ui .responses-inner h4,
      .swagger-ui .responses-inner h5 {
        color: ${theme.palette.text.primary};
      }
      
      .swagger-ui input[type=text],
      .swagger-ui input[type=password],
      .swagger-ui input[type=email],
      .swagger-ui textarea {
        background-color: ${theme.palette.background.paper};
        color: ${theme.palette.text.primary};
        border: 1px solid ${theme.palette.divider};
      }
      
      .swagger-ui input[type=text]:focus,
      .swagger-ui input[type=password]:focus,
      .swagger-ui input[type=email]:focus,
      .swagger-ui textarea:focus {
        border-color: ${theme.palette.primary.main};
        box-shadow: 0 0 0 1px ${theme.palette.primary.main};
      }
      
      .swagger-ui select {
        background-color: ${theme.palette.background.paper};
        color: ${theme.palette.text.primary};
        border: 1px solid ${theme.palette.divider};
      }
      
      .swagger-ui .btn {
        background-color: ${theme.palette.primary.main};
        color: ${theme.palette.primary.contrastText};
        border: 1px solid ${theme.palette.primary.main};
      }
      
      .swagger-ui .btn:hover {
        background-color: ${theme.palette.primary.dark};
        border-color: ${theme.palette.primary.dark};
      }
      
      .swagger-ui .btn.cancel {
        background-color: transparent;
        color: ${theme.palette.text.secondary};
        border-color: ${theme.palette.divider};
      }
      
      .swagger-ui .authorization__btn {
        background-color: transparent;
        border: 1px solid ${theme.palette.primary.main};
        color: ${theme.palette.primary.main};
      }
      
      .swagger-ui .authorization__btn:hover {
        background-color: ${theme.palette.primary.main};
        color: ${theme.palette.primary.contrastText};
      }
      
      .swagger-ui .highlight-code .microlight {
        background-color: ${theme.palette.background.paper};
        color: ${theme.palette.text.primary};
      }
      
      .swagger-ui .microlight .comment {
        color: ${theme.palette.text.secondary};
      }
      
      .swagger-ui .microlight .string {
        color: ${theme.palette.text.primary} !important;
        font-weight: normal !important;
      }
      
      .swagger-ui .microlight .number {
        color: ${theme.palette.text.primary} !important;
        font-weight: normal !important;
      }
      
      .swagger-ui .microlight .keyword {
        color: ${theme.palette.text.primary} !important;
        font-weight: normal !important;
      }
      
      .swagger-ui .dialog-ux .backdrop-ux {
        background-color: rgba(0, 0, 0, 0.5);
      }
      
      .swagger-ui .dialog-ux .modal-ux {
        background-color: ${theme.palette.background.paper};
        border: 1px solid ${theme.palette.divider};
      }
      
      .swagger-ui .dialog-ux .modal-ux-header {
        background-color: ${theme.palette.background.paper};
        border-bottom: 1px solid ${theme.palette.divider};
        color: ${theme.palette.text.primary};
      }
      
      .swagger-ui .dialog-ux .modal-ux-content {
        color: ${theme.palette.text.primary};
      }
      
      .swagger-ui section.models {
        border: 1px solid ${theme.palette.divider};
      }
      
      .swagger-ui section.models .model-container {
        background-color: ${theme.palette.background.paper};
        border-bottom: 1px solid ${theme.palette.divider};
      }
      
      .swagger-ui section.models .model-container:hover {
        background-color: ${theme.palette.action.hover};
      }
      
      .swagger-ui .model-title {
        color: ${theme.palette.text.primary};
      }
      
      .swagger-ui section.models .model-title:hover {
        color: ${theme.palette.primary.main};
      }
      
      .swagger-ui .tab li {
        color: ${theme.palette.text.secondary};
        border-bottom: 1px solid ${theme.palette.divider};
      }
      
      .swagger-ui .tab li.active {
        color: ${theme.palette.text.primary};
        border-bottom-color: ${theme.palette.primary.main};
      }
      
      .swagger-ui .tab li button {
        color: inherit;
        background: none;
        border: none;
      }
      
      .swagger-ui .opblock.is-open .opblock-summary {
        border-bottom: 1px solid ${theme.palette.divider};
      }
      
      .swagger-ui .parameter__extension,
      .swagger-ui .parameter__in {
        color: ${theme.palette.text.secondary};
      }
      
      .swagger-ui .response-col_links {
        color: ${theme.palette.text.secondary};
      }
      
      .swagger-ui code {
        background-color: ${theme.palette.background.paper};
        color: ${theme.palette.text.primary};
        border: 1px solid ${theme.palette.divider};
      }
      
      .swagger-ui pre {
        background-color: ${theme.palette.background.paper};
        color: ${theme.palette.text.primary};
        border: 1px solid ${theme.palette.divider};
      }
      
      /* HTTP Method Colors - same for both themes */
      .swagger-ui .opblock.opblock-get {
        border-color: #61affe;
        background-color: rgba(97, 175, 254, 0.1);
      }
      
      .swagger-ui .opblock.opblock-get .opblock-summary-method {
        background-color: #61affe;
      }
      
      .swagger-ui .opblock.opblock-post {
        border-color: #49cc90;
        background-color: rgba(73, 204, 144, 0.1);
      }
      
      .swagger-ui .opblock.opblock-post .opblock-summary-method {
        background-color: #49cc90;
      }
      
      .swagger-ui .opblock.opblock-put {
        border-color: #fca130;
        background-color: rgba(252, 161, 48, 0.1);
      }
      
      .swagger-ui .opblock.opblock-put .opblock-summary-method {
        background-color: #fca130;
      }
      
      .swagger-ui .opblock.opblock-delete {
        border-color: #f93e3e;
        background-color: rgba(249, 62, 62, 0.1);
      }
      
      .swagger-ui .opblock.opblock-delete .opblock-summary-method {
        background-color: #f93e3e;
      }
      
      .swagger-ui .opblock.opblock-patch {
        border-color: #50e3c2;
        background-color: rgba(80, 227, 194, 0.1);
      }
      
      .swagger-ui .opblock.opblock-patch .opblock-summary-method {
        background-color: #50e3c2;
      }
      
      /* Enhanced Dropdown Styling */
      .swagger-ui select:focus {
        border-color: ${theme.palette.primary.main};
        box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
        outline: none;
      }
      
      .swagger-ui select option {
        background-color: ${theme.palette.background.paper};
        color: ${theme.palette.text.primary};
      }
      
      /* Improved Syntax Highlighting */
      .swagger-ui .microlight .boolean {
        color: ${theme.palette.text.primary} !important;
        font-weight: normal !important;
      }
      
      .swagger-ui .microlight .null {
        color: ${theme.palette.text.secondary} !important;
        font-weight: normal !important;
      }
      
      .swagger-ui .microlight .property {
        color: ${theme.palette.text.primary} !important;
        font-weight: normal !important;
      }
      
      .swagger-ui .microlight .bracket {
        color: ${theme.palette.text.primary} !important;
        font-weight: normal !important;
      }
      
      /* Enhanced Focus States */
      .swagger-ui input[type=text]:focus,
      .swagger-ui input[type=password]:focus,
      .swagger-ui input[type=email]:focus,
      .swagger-ui textarea:focus {
        border-color: ${theme.palette.primary.main};
        box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
        outline: none;
      }
      
      .swagger-ui .btn:focus {
        box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
        outline: none;
      }
      
      /* Response Content Styling */
      .swagger-ui .response-content-type {
        background-color: ${theme.palette.background.paper};
        color: ${theme.palette.text.primary};
        border: 1px solid ${theme.palette.divider};
      }
      
      .swagger-ui .response-content-type.controls-accept-header select {
        background-color: ${theme.palette.background.paper};
        color: ${theme.palette.text.primary};
        border: 1px solid ${theme.palette.divider};
      }
      
      /* Fix OAS Version Badge */
      .swagger-ui .version-stamp,
      .swagger-ui .info .version-stamp,
      .swagger-ui .info hgroup .version-stamp,
      .swagger-ui hgroup .version-stamp,
      .swagger-ui .info hgroup.main .version-stamp,
      .swagger-ui .info .version small,
      .swagger-ui .info .version span,
      .swagger-ui .version-stamp pre,
      .swagger-ui .version-stamp code,
      .swagger-ui .info .version pre,
      .swagger-ui .info .version code {
        background-color: #4CAF50 !important;
        color: white !important;
        border: none !important;
        padding: 2px 6px !important;
        border-radius: 3px !important;
        font-size: 12px !important;
      }
      
      /* Target any element inside version that might be the badge */
      .swagger-ui .info .version * {
        background-color: #4CAF50 !important;
        color: white !important;
        border: none !important;
      }
      
      /* Authorization Modal Styling */
      .swagger-ui .auth-wrapper {
        background-color: ${theme.palette.background.paper} !important;
        border: 1px solid ${theme.palette.divider} !important;
        border-radius: 8px !important;
      }
      
      .swagger-ui .auth-container {
        background-color: ${theme.palette.background.paper} !important;
        color: ${theme.palette.text.primary} !important;
      }
      
      .swagger-ui .auth-container h4,
      .swagger-ui .auth-container h5,
      .swagger-ui .auth-container label,
      .swagger-ui .auth-container p {
        color: ${theme.palette.text.primary} !important;
      }
      
      .swagger-ui .auth-container .description {
        color: ${theme.palette.text.secondary} !important;
      }
      
      .swagger-ui .auth-container input[type="text"],
      .swagger-ui .auth-container input[type="password"] {
        background-color: ${theme.palette.background.default} !important;
        color: ${theme.palette.text.primary} !important;
        border: 1px solid ${theme.palette.divider} !important;
      }
      
      .swagger-ui .auth-container input[type="text"]:focus,
      .swagger-ui .auth-container input[type="password"]:focus {
        border-color: ${theme.palette.primary.main} !important;
        box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2) !important;
        outline: none !important;
      }
      
      .swagger-ui .auth-btn-wrapper .authorize {
        background-color: ${theme.palette.primary.main} !important;
        color: ${theme.palette.primary.contrastText} !important;
        border: none !important;
      }
      
      .swagger-ui .auth-btn-wrapper .authorize:hover {
        background-color: ${theme.palette.primary.dark} !important;
      }
      
      .swagger-ui .auth-btn-wrapper .close {
        background-color: transparent !important;
        color: ${theme.palette.text.secondary} !important;
        border: 1px solid ${theme.palette.divider} !important;
      }
      
      .swagger-ui .auth-btn-wrapper .close:hover {
        background-color: ${theme.palette.action.hover} !important;
        color: ${theme.palette.text.primary} !important;
      }
      
      /* Modal overlay and container */
      .swagger-ui .dialog-ux .backdrop-ux {
        background-color: rgba(0, 0, 0, 0.5) !important;
      }
      
      .swagger-ui .dialog-ux .modal-ux {
        background-color: ${theme.palette.background.paper} !important;
        border: 1px solid ${theme.palette.divider} !important;
        border-radius: 8px !important;
      }
      
      .swagger-ui .dialog-ux .modal-ux-header {
        background-color: ${theme.palette.background.paper} !important;
        border-bottom: 1px solid ${theme.palette.divider} !important;
        color: ${theme.palette.text.primary} !important;
      }
      
      .swagger-ui .dialog-ux .modal-ux-content {
        background-color: ${theme.palette.background.paper} !important;
        color: ${theme.palette.text.primary} !important;
      }
      
      /* Close button in modal header */
      .swagger-ui .dialog-ux .modal-ux-header .close-modal {
        color: ${theme.palette.text.secondary} !important;
        background: none !important;
        border: none !important;
      }
      
      .swagger-ui .dialog-ux .modal-ux-header .close-modal:hover {
        color: ${theme.palette.text.primary} !important;
      }
      
      /* Schema and Model Section Styling */
      .swagger-ui section.models h4,
      .swagger-ui section.models .model-title {
        color: ${theme.palette.text.primary} !important;
        font-weight: 600 !important;
      }
      
      .swagger-ui section.models .model-title:hover {
        color: ${theme.palette.primary.main} !important;
      }
      
      .swagger-ui .model-container {
        background-color: ${theme.palette.background.paper} !important;
        border: 1px solid ${theme.palette.divider} !important;
      }
      
      .swagger-ui .model-container:hover {
        background-color: ${theme.palette.action.hover} !important;
      }
      
      .swagger-ui .models-control {
        color: ${theme.palette.text.primary} !important;
      }
      
      /* Schema property styling */
      .swagger-ui .model .property .prop-name {
        color: ${theme.palette.text.primary} !important;
      }
      
      .swagger-ui .model .property .prop-type {
        color: ${theme.palette.primary.main} !important;
      }
      
      .swagger-ui .model .property .prop-format {
        color: ${theme.palette.text.secondary} !important;
      }
      
      /* Expandable section headers */
      .swagger-ui .opblock-tag {
        color: ${theme.palette.text.primary} !important;
        border-bottom: 1px solid ${theme.palette.divider} !important;
      }
      
      .swagger-ui .opblock-tag small {
        color: ${theme.palette.text.secondary} !important;
      }
      
      /* Schema section header */
      .swagger-ui section.models h4 span {
        color: ${theme.palette.text.primary} !important;
      }
      
      /* Model toggle button */
      .swagger-ui .models-control button {
        color: ${theme.palette.text.primary} !important;
        background: none !important;
        border: none !important;
      }
      
      .swagger-ui .models-control button:hover {
        color: ${theme.palette.primary.main} !important;
      }
      
      /* Operation path text visibility */
      .swagger-ui .opblock-summary-path {
        color: ${theme.palette.text.primary} !important;
        font-weight: 500 !important;
      }
      
      .swagger-ui .opblock-summary-path__deprecated {
        color: ${theme.palette.text.secondary} !important;
      }
      
      /* Operation summary text */
      .swagger-ui .opblock-summary-description {
        color: ${theme.palette.text.primary} !important;
      }
      
      /* Operation summary container */
      .swagger-ui .opblock-summary {
        color: ${theme.palette.text.primary} !important;
      }
      
      /* Ensure all text in operation blocks is visible */
      .swagger-ui .opblock .opblock-summary .opblock-summary-path,
      .swagger-ui .opblock .opblock-summary .opblock-summary-description {
        color: ${theme.palette.text.primary} !important;
      }
      
      /* CRITICAL: Response Body Text Visibility Fixes - Light Mode */
      .swagger-ui .responses-wrapper {
        background-color: ${theme.palette.background.paper} !important;
        color: ${theme.palette.text.primary} !important;
      }
      
      .swagger-ui .live-responses-table {
        background-color: ${theme.palette.background.paper} !important;
        color: ${theme.palette.text.primary} !important;
      }
      
      .swagger-ui .response {
        color: ${theme.palette.text.primary} !important;
        background-color: ${theme.palette.background.paper} !important;
      }
      
      .swagger-ui .response .response-content {
        background-color: ${theme.palette.background.default} !important;
        color: ${theme.palette.text.primary} !important;
      }
      
      .swagger-ui .response pre {
        background-color: ${theme.palette.background.default} !important;
        color: ${theme.palette.text.primary} !important;
        border: 1px solid ${theme.palette.divider} !important;
      }
      
      .swagger-ui .response pre code {
        background-color: transparent !important;
        color: ${theme.palette.text.primary} !important;
      }
      
      .swagger-ui .response-content pre {
        background-color: ${theme.palette.background.default} !important;
        color: ${theme.palette.text.primary} !important;
        border: 1px solid ${theme.palette.divider} !important;
        padding: 12px !important;
      }
      
      .swagger-ui .response-content pre code {
        background-color: transparent !important;
        color: ${theme.palette.text.primary} !important;
      }
      
      /* Enhanced JSON Content Visibility - Light Mode */
      .swagger-ui .microlight {
        background-color: ${theme.palette.background.default} !important;
        color: ${theme.palette.text.primary} !important;
      }
      
      /* Force ALL microlight elements to use theme colors - Light Mode */
      .swagger-ui .microlight * {
        color: ${theme.palette.text.primary} !important;
      }
      
      /* Nuclear option: Override any remaining green text - Light Mode */
      .swagger-ui pre code span[style*="color"] {
        color: ${theme.palette.text.primary} !important;
      }
      
      .swagger-ui .highlight-code span {
        color: ${theme.palette.text.primary} !important;
      }
      
      /* JSON Response Table Styling - Light Mode */
      .swagger-ui .live-responses-table .response-content {
        background-color: ${theme.palette.background.default} !important;
      }
      
      .swagger-ui .live-responses-table tbody tr td {
        background-color: ${theme.palette.background.paper} !important;
        color: ${theme.palette.text.primary} !important;
        border: 1px solid ${theme.palette.divider} !important;
      }
      
      .swagger-ui .live-responses-table thead tr th {
        background-color: ${theme.palette.background.paper} !important;
        color: ${theme.palette.text.primary} !important;
        border: 1px solid ${theme.palette.divider} !important;
      }
      
      /* Response Details Container - Light Mode */
      .swagger-ui .responses-table {
        background-color: ${theme.palette.background.paper} !important;
        border: 1px solid ${theme.palette.divider} !important;
      }
      
      .swagger-ui .responses-table td {
        background-color: ${theme.palette.background.paper} !important;
        color: ${theme.palette.text.primary} !important;
        border: 1px solid ${theme.palette.divider} !important;
      }
      
      .swagger-ui .responses-table th {
        background-color: ${theme.palette.background.default} !important;
        color: ${theme.palette.text.primary} !important;
        border: 1px solid ${theme.palette.divider} !important;
      }
    `;

    existingStyle.textContent = themeCss;
    
    // Cleanup function to remove style element when component unmounts
    return () => {
      const styleToRemove = document.getElementById(styleId);
      if (styleToRemove) {
        styleToRemove.remove();
      }
    };
  }, [isDarkMode, theme]);

  // Use key to force re-render when theme changes
  return <SwaggerUI key={theme.palette.mode} spec={spec} />;
}

export default ThemedSwaggerUI;