import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  ValidateNested,
} from 'class-validator';

// Define a class for individual messages within the history
export class ChatMessageDto {
  @ApiProperty({
    description: 'The role of the message sender.',
    enum: ['user', 'assistant', 'system'], // Allow system role if needed
    example: 'user',
  })
  @IsString()
  @IsNotEmpty()
  role!: 'user' | 'assistant' | 'system'; // Add definite assignment assertion

  @ApiProperty({
    description:
      'The content of the message. Can be a string or an array for multi-modal input.',
    example: 'Hello, how are you?',
  })
  @IsNotEmpty() // Content should not be empty, but can be string or array/object
  content!: string | object[]; // Add definite assignment assertion
}

// Define the main DTO for the streaming chat endpoint
export class StreamChatDto {
  @ApiProperty({
    description: 'The deployment name of the model to use.',
    example: 'chatgpt4-turbo-preview',
  })
  @IsString()
  @IsNotEmpty()
  deploymentName!: string; // Add definite assignment assertion

  @ApiProperty({
    description:
      'The conversation history, including the latest user message as the last element.',
    type: [ChatMessageDto],
  })
  @IsArray()
  @ValidateNested({ each: true }) // Validate each object in the array
  @Type(() => ChatMessageDto) // Ensure transformation applies to array elements
  @IsNotEmpty()
  history!: ChatMessageDto[]; // Add definite assignment assertion

  @ApiPropertyOptional({
    description: 'Optional UUID of the existing conversation.',
    example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef',
  })
  @IsOptional()
  @IsUUID()
  conversationId?: string;

  @ApiPropertyOptional({
    description:
      'Optional temperature setting for the model response (0.0 to 1.0).',
    example: 0.7,
    default: 0.6, // Or your preferred default
  })
  @IsOptional()
  @IsNumber()
  temperature?: number;

  @ApiPropertyOptional({
    description:
      'Optional field for attachment information (structure TBD based on implementation).',
    example: [{ fileId: 'xyz', type: 'image/png' }],
  })
  @IsOptional()
  // Add specific validation based on the final structure for attachments
  attachments?: any; // Use 'any' for now, refine later
}
