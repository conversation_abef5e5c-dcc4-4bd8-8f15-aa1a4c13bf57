BEGIN TRY

BEGIN TRAN;

-- AlterTable
ALTER TABLE [dbo].[model_list] ADD [category] VARCHAR(50);

-- CreateTable
CREATE TABLE [dbo].[user_recent_model] (
    [id] INT NOT NULL IDENTITY(1,1),
    [userId] VARCHAR(30) NOT NULL,
    [modelId] INT NOT NULL,
    [lastUsedAt] DATETIME NOT NULL CONSTRAINT [user_recent_model_lastUsedAt_df] DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT [user_recent_model_pkey] PRIMARY KEY CLUSTERED ([id]),
    CONSTRAINT [user_recent_model_userId_modelId_key] UNIQUE NONCLUSTERED ([userId],[modelId])
);

-- CreateIndex
CREATE NONCLUSTERED INDEX [user_recent_model_userId_lastUsedAt_idx] ON [dbo].[user_recent_model]([userId], [lastUsedAt]);

-- AddForeignKey
ALTER TABLE [dbo].[user_recent_model] ADD CONSTRAINT [user_recent_model_userId_fkey] FOREIGN KEY ([userId]) REFERENCES [dbo].[acl_user]([username]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[user_recent_model] ADD CONSTRAINT [user_recent_model_modelId_fkey] FOREIGN KEY ([modelId]) REFERENCES [dbo].[model_list]([id]) ON DELETE NO ACTION ON UPDATE CASCADE;

COMMIT TRAN;

END TRY
BEGIN CATCH

IF @@TRANCOUNT > 0
BEGIN
    ROLLBACK TRAN;
END;
THROW

END CATCH