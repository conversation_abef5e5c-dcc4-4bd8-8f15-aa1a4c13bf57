#!/usr/bin/env node

/**
 * Check DeepSeek Model Configuration in Database
 */

import dotenv from 'dotenv';
import { PrismaClient } from '@hkbu-genai-platform/database';
import fs from 'fs';

// Load environment variables
dotenv.config({ path: '../apps/api/.env' });

// Setup logging
const logFile = 'deepseek-models-check.log';
const logStream = fs.createWriteStream(logFile, { flags: 'w' });

function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}`;
  console.log(logMessage);
  logStream.write(logMessage + '\n');
}

const prisma = new PrismaClient();

async function checkDeepSeekModels() {
  try {
    log('🔍 Checking DeepSeek models in database...');
    
    // Query all models that might be DeepSeek
    const deepseekModels = await prisma.model_list.findMany({
      where: {
        OR: [
          { model_name: { contains: 'deepseek' } },
          { deployment_name: { contains: 'deepseek' } },
          { display_name: { contains: 'deepseek' } },
          { display_name: { contains: 'DeepSeek' } },
        ]
      },
      select: {
        display_name: true,
        deployment_name: true,
        model_name: true,
        model_type: true,
        api_version: true,
        api_status: true,
        rec_status: true,
        availability_status: true,
      }
    });
    
    if (deepseekModels.length === 0) {
      log('❌ No DeepSeek models found in database');
    } else {
      log(`✅ Found ${deepseekModels.length} DeepSeek models:`);
      deepseekModels.forEach((model, index) => {
        log(`\n--- DeepSeek Model ${index + 1} ---`);
        log(`Display Name: ${model.display_name}`);
        log(`Deployment Name: ${model.deployment_name}`);
        log(`Model Name: ${model.model_name}`);
        log(`Model Type: ${model.model_type}`);
        log(`API Version: ${model.api_version}`);
        log(`API Status: ${model.api_status}`);
        log(`Record Status: ${model.rec_status}`);
        log(`Availability Status: ${model.availability_status}`);
      });
    }
    
    // Also check if any models have deepseek-aisvc or similar patterns
    const possibleDeepSeekModels = await prisma.model_list.findMany({
      where: {
        OR: [
          { model_type: { contains: 'deepseek' } },
          { display_name: { contains: 'AI Service' } },
        ]
      },
      select: {
        display_name: true,
        deployment_name: true,
        model_name: true,
        model_type: true,
        api_version: true,
      }
    });
    
    if (possibleDeepSeekModels.length > 0) {
      log(`\n🔍 Other possible DeepSeek/AI Service models:`);
      possibleDeepSeekModels.forEach((model, index) => {
        log(`${index + 1}. ${model.display_name} (${model.deployment_name}) - ${model.model_name}`);
      });
    }
    
  } catch (error) {
    log(`❌ Database query failed: ${error.message}`);
    log(`Stack: ${error.stack}`);
  } finally {
    await prisma.$disconnect();
    logStream.end();
  }
}

checkDeepSeekModels();