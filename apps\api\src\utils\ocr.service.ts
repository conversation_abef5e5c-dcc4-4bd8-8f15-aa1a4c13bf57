import {
  Injectable,
  InternalServerErrorException,
  ServiceUnavailableException,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ComputerVisionClient } from '@azure/cognitiveservices-computervision';
import { ApiKeyCredentials } from '@azure/ms-rest-js';
import { HttpsProxyAgent } from 'https-proxy-agent'; // Changed import style
import { Readable } from 'stream';

// Helper function for sleep
const sleep = (ms: number): Promise<void> =>
  new Promise((resolve) => setTimeout(resolve, ms));

@Injectable()
export class OcrService {
  private readonly logger = new Logger(OcrService.name);

  constructor(private configService: ConfigService) {}

  /**
   * Performs OCR on a given file buffer using Azure Computer Vision.
   * @param fileBuffer The buffer containing the file content.
   * @param dept_unit_code The department code to determine Azure credentials.
   * @param originalFilename Optional: Filename for logging purposes.
   * @returns The extracted text content.
   * @throws ServiceUnavailableException | InternalServerErrorException
   */
  async extractTextFromFile(
    fileBuffer: Buffer,
    dept_unit_code: string,
    originalFilename?: string,
  ): Promise<string> {
    const logContext = originalFilename ? `file ${originalFilename}` : 'a file';
    this.logger.log(
      `Performing OCR for dept ${dept_unit_code} on ${logContext}`,
    );

    // --- 1. Get Azure Credentials ---
    let visionKey: string | undefined;
    let visionEndpoint: string | undefined;
    const proxyUrl = this.configService.get<string>('HTTPS_PROXY');

    // Determine credentials based on department using new AZURE_OCR_* names
    if (dept_unit_code === 'SCE' || dept_unit_code === 'CIE') {
      visionKey = this.configService.get<string>('AZURE_OCR_SCECIE_KEY'); // Renamed
      visionEndpoint = this.configService.get<string>(
        'AZURE_OCR_SCECIE_ENDPOINT',
      ); // Renamed
    } else {
      visionKey = this.configService.get<string>('AZURE_OCR_KEY'); // Renamed
      visionEndpoint = this.configService.get<string>('AZURE_OCR_ENDPOINT'); // Renamed
    }

    if (!visionKey || !visionEndpoint) {
      this.logger.error(
        `Azure OCR credentials not configured for dept_unit_code: ${dept_unit_code}`,
      ); // Updated log message
      throw new InternalServerErrorException(
        'OCR service configuration error.',
      );
    }

    // --- 2. Initialize ComputerVisionClient ---
    const credentials = new ApiKeyCredentials({
      inHeader: { 'Ocp-Apim-Subscription-Key': visionKey },
    });
    const clientOptions: any = {};
    if (proxyUrl) {
      // Instantiate directly using the named import
      clientOptions.requestOptions = {
        agent: new HttpsProxyAgent(proxyUrl),
      };
      this.logger.debug(`Using HTTPS Proxy: ${proxyUrl}`);
    }

    const computerVisionClient = new ComputerVisionClient(
      credentials,
      visionEndpoint,
      clientOptions,
    );

    // --- 3. Prepare File Stream ---
    const readStream = new Readable({
      read() {
        this.push(fileBuffer);
        this.push(null); // Signal end of stream
      },
    });

    // --- 4. Call Azure Read API ---
    let resultText = '';
    try {
      this.logger.debug(
        `Calling Azure readInStream for dept ${dept_unit_code}`,
      );
      const streamResponse = await computerVisionClient.readInStream(
        () => readStream,
      );

      const operationLocation = streamResponse.operationLocation;
      if (!operationLocation) {
        this.logger.error(
          'Failed to get operation location from Azure Vision API response.',
          streamResponse,
        );
        throw new ServiceUnavailableException(
          'Failed to initiate OCR process.',
        );
      }

      const operationId = operationLocation.substring(
        operationLocation.lastIndexOf('/') + 1,
      );
      this.logger.debug(
        `Azure OCR Operation ID: ${operationId} for dept ${dept_unit_code}`,
      );

      // --- 5. Poll for Results ---
      let pollCount = 0;
      const maxPolls = 60; // Poll for max 60 seconds (adjust as needed)

      while (pollCount < maxPolls) {
        const readOpResult =
          await computerVisionClient.getReadResult(operationId);
        this.logger.debug(
          `Polling OCR result status: ${readOpResult.status} (Poll ${pollCount + 1}/${maxPolls})`,
        );

        if (readOpResult.status === 'failed') {
          this.logger.error(
            `Azure OCR process failed for operation ID: ${operationId}`,
          );
          throw new ServiceUnavailableException('OCR processing failed.');
        }

        if (readOpResult.status === 'succeeded') {
          if (
            readOpResult.analyzeResult &&
            readOpResult.analyzeResult.readResults
          ) {
            resultText = readOpResult.analyzeResult.readResults
              .map((pageResult) =>
                pageResult.lines.map((line) => line.text).join('\n'),
              )
              .join('\n\n'); // Join pages with double newline
          } else {
            this.logger.warn(
              `OCR succeeded but analyzeResult or readResults were missing for operation ID: ${operationId}`,
            );
          }
          this.logger.log(
            `OCR process succeeded for operation ID: ${operationId}`,
          );
          break; // Exit loop on success
        }

        if (
          readOpResult.status !== 'running' &&
          readOpResult.status !== 'notStarted'
        ) {
          this.logger.warn(
            `OCR process ended with unexpected status: ${readOpResult.status} for operation ID: ${operationId}`,
          );
          throw new ServiceUnavailableException(
            `OCR processing ended unexpectedly with status: ${readOpResult.status}`,
          );
        }

        await sleep(1000); // Wait 1 second before next poll
        pollCount++;
      }

      if (pollCount >= maxPolls) {
        this.logger.error(
          `OCR process timed out after ${maxPolls} seconds for operation ID: ${operationId}`,
        );
        throw new ServiceUnavailableException('OCR processing timed out.');
      }
    } catch (error) {
      const message =
        error instanceof Error
          ? error.message
          : 'Unknown Azure Vision API error';
      const stack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Azure Vision API call failed for dept ${dept_unit_code}: ${message}`,
        stack,
      );

      if (
        error instanceof ServiceUnavailableException ||
        error instanceof InternalServerErrorException
      ) {
        throw error;
      }
      throw new ServiceUnavailableException(
        `Failed to communicate with OCR service: ${message}`,
      );
    }

    this.logger.log(
      `OCR completed successfully for dept ${dept_unit_code} on ${logContext}`,
    );
    return resultText;
  }
}
