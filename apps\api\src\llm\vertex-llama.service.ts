import {
  Injectable,
  Logger,
  InternalServerErrorException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { TokenUsageService } from '../common/services/token-usage.service';
import { getHongKongTime, getHongKongDateOnly } from '../common/utils/timezone.util';
import { Readable, PassThrough } from 'stream';
import { v4 as uuidv4 } from 'uuid';
import { GoogleAuth } from 'google-auth-library';
import { LlmStreamOptions } from './llm.service'; // Assuming this defines the input structure
import { TextDecoder, TextEncoder } from 'util';
import { Source } from '../utils/search-context.service';
import { Inject } from '@nestjs/common';
interface LlamaMessage {
  role: 'user' | 'assistant' | 'system'; // Llama uses 'assistant'
  content: string; // Llama expects simple string content
}

// Define the structure of the usage object in the stream
interface LlamaUsage {
  prompt_tokens: number;
  completion_tokens: number;
  total_tokens: number;
}

// Helper function for sleep
const sleep = (ms: number): Promise<void> =>
  new Promise((resolve) => setTimeout(resolve, ms));

@Injectable()
export class VertexLlamaService {
  private readonly logger = new Logger(VertexLlamaService.name);
  private readonly googleProjectId: string; // Use existing variable name
  // Remove vertexAIServiceAccountKeyBase64 as we'll use GOOGLE_APPLICATION_CREDENTIALS path
  private readonly encryptionKeyName: string;
  private readonly decryptionCertName: string;

  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
    private tokenUsageService: TokenUsageService,
  ) {
    // Use existing environment variable names
    this.googleProjectId = this.configService.getOrThrow<string>(
      'GOOGLE_VERTEXAI_PROJECT_ID',
    );
    // GOOGLE_APPLICATION_CREDENTIALS will be read automatically by GoogleAuth if set
    const encryptionKeyName = this.configService.get<string>(
      'DB_ENCRYPTION_KEY_NAME',
    );
    const decryptionCertName = this.configService.get<string>(
      'DB_DECRYPTION_CERT_NAME',
    );

    if (!encryptionKeyName || !decryptionCertName) {
      throw new InternalServerErrorException(
        'Database encryption configuration is missing.',
      );
    }

    this.encryptionKeyName = encryptionKeyName;
    this.decryptionCertName = decryptionCertName;
  }

  /**
   * Initializes Google Auth token using Application Default Credentials (ADC)
   * which typically reads the GOOGLE_APPLICATION_CREDENTIALS environment variable.
   */
  private async initializeToken(): Promise<string> {
    try {
      // GoogleAuth will automatically find credentials if GOOGLE_APPLICATION_CREDENTIALS is set
      const googleAuth = new GoogleAuth({
        scopes: 'https://www.googleapis.com/auth/cloud-platform',
      });
      const token = await googleAuth.getAccessToken();
      if (!token) {
        throw new Error(
          'Failed to obtain Google Auth token. Ensure GOOGLE_APPLICATION_CREDENTIALS is set correctly.',
        );
      }
      return token;
    } catch (error: any) {
      this.logger.error(
        `Failed to initialize Google Auth token: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        'Authentication failed for Vertex AI.',
      );
    }
  }

  /**
   * Transforms LlmMessage format to LlamaMessage format.
   * Ensures content is string and handles role mapping.
   */
  private transformMessagesToLlama(
    messages: LlmStreamOptions['messages'],
  ): LlamaMessage[] {
    const transformed: LlamaMessage[] = [];
    for (const msg of messages) {
      let role: LlamaMessage['role'];
      if (
        msg.role === 'user' ||
        msg.role === 'system' ||
        msg.role === 'assistant'
      ) {
        role = msg.role;
      } else {
        this.logger.warn(
          `Unsupported role '${msg.role}' for Llama, treating as 'user'.`,
        );
        role = 'user';
      }

      // Llama expects simple string content
      let content: string;
      if (typeof msg.content === 'string') {
        content = msg.content;
      } else if (Array.isArray(msg.content)) {
        // Attempt to extract text from array format (used by vision models)
        content = msg.content
          .filter(
            (part) => part.type === 'text' && typeof part.text === 'string',
          )
          .map((part) => part.text)
          .join('\n');
        if (!content) {
          this.logger.warn(
            `Could not extract string content from array message for Llama: ${JSON.stringify(msg)}`,
          );
          continue; // Skip message if no text content found
        }
      } else {
        this.logger.warn(
          `Unsupported message content format for Llama: ${JSON.stringify(msg.content)}`,
        );
        continue; // Skip message
      }

      if (!content || content.trim() === '') {
        this.logger.warn(
          `Skipping message with empty content for Llama: ${JSON.stringify(msg)}`,
        );
        continue;
      }

      transformed.push({ role, content });
    }
    // Llama conversation formatting often requires alternating user/assistant roles.
    // The old code had `alternativeConversation`, let's replicate basic filtering.
    const alternativeMsg: LlamaMessage[] = [];
    for (let i = 0; i < transformed.length; i++) {
      const current = transformed[i];
      if (current.role === 'assistant') {
        alternativeMsg.push(current);
      } else if (
        i === transformed.length - 1 ||
        transformed[i + 1].role !== 'user'
      ) {
        // Keep user/system message if it's the last one or the next one isn't also user/system
        alternativeMsg.push(current);
      } else {
        this.logger.warn(
          `Skipping consecutive user/system message for Llama: ${JSON.stringify(current)}`,
        );
      }
    }
    return alternativeMsg;
  }

  /**
   * Creates a Vertex AI Llama chat completion stream using the REST API.
   */
  async createChatCompletionStream(
    options: LlmStreamOptions,
  ): Promise<Readable> {
    const {
      modelConfig,
      messages,
      temperature,
      conversationId,
      dialogId,
      userMessageId,
      user,
      searchSources,
      requestedModelName,
    } = options;
    // Use the model name from config to determine the appropriate API identifier and region
    // Llama 4 models use us-east5 region and 'meta/llama-4-maverick-17b-128e-instruct-maas'
    // Llama 3.1 models use us-central1 region and 'meta/llama-3.1-405b-instruct-maas'
    const modelName = modelConfig.model_name; // e.g., 'llama3_1', 'llama-4-maverick', etc.

    this.logger.log(
      `Creating Vertex AI Llama stream for model ${modelName} via REST API by user ${user.userId}`,
    );

    if (!userMessageId) {
      throw new InternalServerErrorException(
        'User message ID is required for Llama stream.',
      );
    }

    const llamaMessages = this.transformMessagesToLlama(messages);
    if (llamaMessages.length === 0) {
      throw new InternalServerErrorException(
        'No valid messages remaining after transformation for Llama.',
      );
    }

    const passThroughStream = new PassThrough();
    passThroughStream.write(': init\n\n'); // Prime the pump

    // --- Send pre-computed search sources (if any) as the first SSE chunk ---
    if (searchSources && searchSources.length > 0) {
      this.logger.log(
        `[Vertex Llama] Sending ${searchSources.length} pre-computed search sources to client.`,
      );
      try {
        const cleanedSources = (searchSources as Source[])
          .map((s) => ({
            title: String(s.title || '').trim(),
            link: String(s.link || '').trim(),
            snippet: String(s.snippet || '').trim(),
          }))
          .filter((s) => s.title && s.link && s.snippet);

        const sourceData = { type: 'sources', sources: cleanedSources };
        passThroughStream.write(`data: ${JSON.stringify(sourceData)}\n\n`);
      } catch (jsonErr) {
        this.logger.error(
          `[Vertex Llama] Failed to serialize sources for streaming: ${jsonErr}`,
        );
      }
    }

    const streamAndLog = async () => {
      let fullResponseText = '';
      let usageInfo: LlamaUsage | null = null;
      const assistantMessageUUID = uuidv4();

      try {
        const token = await this.initializeToken();

        // Determine the appropriate region and model based on the requested model
        // Llama 4 models use us-east5 region, while Llama 3.1 uses us-central1
        const isLlama4 =
          modelName.toLowerCase().includes('llama-4') ||
          modelName.toLowerCase().includes('llama4') ||
          modelName.toLowerCase().includes('maverick');

        const region = isLlama4 ? 'us-east5' : 'us-central1';
        const apiUrl = `https://${region}-aiplatform.googleapis.com/v1/projects/${this.googleProjectId}/locations/${region}/endpoints/openapi/chat/completions`;

        // Use correct model identifier based on the model version
        const effectiveModelApiName = isLlama4
          ? 'meta/llama-4-maverick-17b-128e-instruct-maas'
          : 'meta/llama-3.1-405b-instruct-maas';
        this.logger.log(
          `Using Llama model API name: ${effectiveModelApiName} in region: ${region} for requested model: ${modelName}`,
        );

        const requestBody = {
          model: effectiveModelApiName, // Use the potentially hardcoded name
          stream: true,
          messages: llamaMessages,
          temperature: temperature ?? 0.7, // Default temperature if not provided
          // Add other Llama parameters if needed (max_tokens, top_p, etc.)
        };

        this.logger.debug(
          `Vertex Llama Request to ${apiUrl}: ${JSON.stringify({ ...requestBody, messages: '...' })}`,
        ); // Avoid logging full message history

        const response = await fetch(apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json; charset=utf-8',
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify(requestBody),
        });

        if (!response.ok || !response.body) {
          const errorBody = await response.text();
          this.logger.error(
            `Vertex Llama API request failed: ${response.status} ${response.statusText}. Body: ${errorBody}`,
          );
          throw new Error(
            `API request failed: ${response.status} ${response.statusText}`,
          );
        }

        // Process the stream manually
        const reader = response.body.getReader();
        const decoder = new TextDecoder('utf-8');
        let buffer = '';

        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n');
          buffer = lines.pop() ?? ''; // Keep the last partial line

          for (const line of lines) {
            if (line.startsWith('data:')) {
              const jsonText = line.slice(5).trim();
              if (jsonText === '[DONE]') {
                // We might receive usage data before [DONE], handle it there.
                continue;
              }
              try {
                const jsonData = JSON.parse(jsonText);
                const contentChunk = jsonData?.choices?.[0]?.delta?.content;

                if (contentChunk) {
                  fullResponseText += contentChunk;
                  // Format as SSE data event
                  const streamData = {
                    id: `chatcmpl-${assistantMessageUUID}`,
                    object: 'chat.completion.chunk',
                    created: Math.floor(Date.now() / 1000),
                    model: modelName, // Report the DB model name
                    conversation_uuid: dialogId,
                    choices: [
                      {
                        index: 0,
                        delta: { content: contentChunk },
                        finish_reason: null,
                      },
                    ],
                  };
                  passThroughStream.write(
                    `data: ${JSON.stringify(streamData)}\n\n`,
                  );
                }

                // Check for usage data in the chunk
                if (jsonData.usage) {
                  usageInfo = jsonData.usage as LlamaUsage;
                  this.logger.debug(
                    `Received usage info from Llama stream: ${JSON.stringify(usageInfo)}`,
                  );
                }
              } catch (parseError: any) {
                this.logger.warn(
                  `Failed to parse JSON line from Llama stream: "${jsonText}". Error: ${parseError.message}`,
                );
              }
            }
          }
        }
        // Final buffer processing (if needed, though usage usually comes before [DONE])
        if (buffer.startsWith('data:')) {
          const jsonText = buffer.slice(5).trim();
          try {
            const jsonData = JSON.parse(jsonText);
            if (jsonData.usage) {
              usageInfo = jsonData.usage as LlamaUsage;
              this.logger.debug(
                `Received usage info from final Llama buffer: ${JSON.stringify(usageInfo)}`,
              );
            }
          } catch (e) {
            /* ignore final parse error */
          }
        }

        // --- CRITICAL: Log Assistant Message and Tokens BEFORE ending stream ---
        // This ensures all database operations complete before frontend redirects
        if (fullResponseText) {
          await this.logAssistantMessageWithSP(
            assistantMessageUUID,
            dialogId,
            fullResponseText,
            temperature,
            requestedModelName ?? modelName, // Use requested model name for consistency, fallback to config model name
            usageInfo?.completion_tokens ?? 0,
            user.userId,
            getHongKongTime(),
          );
          this.logger.log(
            `[Vertex Llama] ✅ Message saved BEFORE ending stream for ${dialogId}`,
          );
        } else {
          this.logger.warn(
            `Llama response was empty for conversation ${dialogId}. Not saving assistant message.`,
          );
        }

        // Update user message prompt tokens
        if (userMessageId && usageInfo && usageInfo.prompt_tokens > 0) {
          await this.updateUserMessageTokens(
            userMessageId,
            usageInfo.prompt_tokens,
          );
        } else if (userMessageId) {
          this.logger.warn(
            `Prompt token count unavailable or zero from Llama stream for user message ${userMessageId}.`,
          );
        }

        // Send final [DONE] message
        const doneData = {
          id: `chatcmpl-${assistantMessageUUID}`,
          object: 'chat.completion.chunk',
          created: Math.floor(Date.now() / 1000),
          model: modelName, // Report the DB model name
          conversation_uuid: dialogId,
          choices: [{ index: 0, delta: {}, finish_reason: 'stop' }],
          usage: usageInfo
            ? {
                prompt_tokens: usageInfo.prompt_tokens,
                completion_tokens: usageInfo.completion_tokens,
                total_tokens: usageInfo.total_tokens,
              }
            : undefined,
        };
        passThroughStream.write(`data: ${JSON.stringify(doneData)}\n\n`);
        passThroughStream.write('data: [DONE]\n\n');
        await sleep(100);

        // Update token usage for monthly tracking
        if (user?.userId && requestedModelName) {
          try {
            const actualPromptTokens = usageInfo?.prompt_tokens || 0;
            const actualCompletionTokens = usageInfo?.completion_tokens || 0;
            const actualTotalTokens =
              actualPromptTokens + actualCompletionTokens;

            await this.tokenUsageService.updateTokenUsage({
              username: user.userId,
              modelName: requestedModelName,
              tokenDate: getHongKongDateOnly(),
              promptTokens: actualPromptTokens,
              completionTokens: actualCompletionTokens,
              totalTokens: actualTotalTokens,
              isApi: false, // This is a web UI request, not API
            });

            this.logger.debug(
              `[Vertex Llama] Updated token usage for ${user.userId}/${requestedModelName}: prompt=${actualPromptTokens}, completion=${actualCompletionTokens}, total=${actualTotalTokens}`,
            );
          } catch (error) {
            this.logger.error(
              `Failed to update token usage for Vertex Llama: ${error}`,
            );
          }
        }

        // CRITICAL: Only end stream after ALL database operations are complete
        this.logger.debug(
          `[Vertex Llama] All database operations completed, ending stream for ${dialogId}`,
        );
        passThroughStream.end();
      } catch (error: any) {
        this.logger.error(
          `Error in Vertex Llama stream for conversation ${dialogId}: ${error.message}`,
          error.stack,
        );
        const errorPayload = {
          error: {
            message: error.message || 'Unknown Llama stream error',
            code: 500,
          },
        };
        passThroughStream.write(`data: ${JSON.stringify(errorPayload)}\n\n`);
        passThroughStream.destroy(error);
      }
    };

    streamAndLog();
    return passThroughStream;
  }

  // --- Logging Methods using current project's SP and Prisma ---
  private async logAssistantMessageWithSP(
    messageUUID: string,
    conversationUUID: string,
    content: string,
    temperature: number | undefined,
    modelName: string,
    completionTokens: number,
    userId: string,
    receivedAt: Date,
  ): Promise<void> {
    this.logger.debug(
      `Saving Llama assistant message via sp_cvst_InsertMessageWithPrompts for conv ${conversationUUID}...`,
    );
    try {
      const result: { inserted_message_id: number }[] = await this.prisma
        .$queryRaw`
        EXEC sp_cvst_InsertMessageWithPrompts
          @message_uuid = ${messageUUID},
          @conversation_uuid = ${conversationUUID},
          @last_prompt = ${content},
          @temperature = ${temperature},
          @instance_name = 'vertex-ai-llama-rest', -- Indicate source
          @model_name = ${modelName},
          @sender = 'assistant',
          @token_spent = ${completionTokens},
          @ssoid = ${userId},
          @received_at = ${receivedAt},
          @dialog_id = ${conversationUUID}, -- Pass conversationUUID again
          @encryption_key_name = ${this.encryptionKeyName},
          @decryption_cert_name = ${this.decryptionCertName}`;

      if (!result || !result[0]?.inserted_message_id) {
        throw new Error(
          'Stored procedure did not return the inserted message ID.',
        );
      }
      this.logger.log(
        `Llama assistant message saved via SP with ID: ${result[0].inserted_message_id} for conv ${conversationUUID}`,
      );
      // Assume SP handles acl_user_token_spent logging based on token_spent input
    } catch (dbError: any) {
      this.logger.error(
        `Failed to log Llama assistant message via SP for conversation ${conversationUUID}: ${dbError.message}`,
        dbError.stack,
      );
    }
  }

  private async updateUserMessageTokens(
    messageId: number,
    promptTokens: number,
  ): Promise<void> {
    try {
      await this.prisma.message.update({
        where: { message_id: messageId },
        data: { token_spent: promptTokens },
      });
      this.logger.log(
        `Updated prompt token count (${promptTokens}) for user message ID: ${messageId} (Llama)`,
      );
    } catch (error: any) {
      this.logger.error(
        `Failed to update token count for user message ${messageId} (Llama): ${error.message}`,
        error.stack,
      );
    }
  }
}
