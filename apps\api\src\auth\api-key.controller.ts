import {
  Controller,
  Post,
  UseGuards,
  Req,
  Logger,
  HttpException,
  HttpStatus,
  Ip,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport'; // Import AuthGuard
import { ApiKeyService } from './api-key.service';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
// Assuming an AuthenticatedGuard exists that populates req.user from a session or JWT
// If using JWT, it might be JwtAuthGuard. For session, it might be a custom guard.
// Let's assume a generic AuthenticatedGuard for now.
// import { AuthenticatedGuard } from './guards/authenticated.guard'; // Adjust path as needed
import { Throttle } from '@nestjs/throttler'; // For rate limiting

// Define a simple interface for the expected user object on the request
interface AuthenticatedUser {
  userId: string; // User ID from JWT token (corresponds to ssoid)
  email: string;
  type: string;
  dept_unit_code: string;
  // Add other user properties if available and needed
}

// Define a simple interface for the request object
interface AuthenticatedRequest extends Request {
  user: AuthenticatedUser;
}

@ApiTags('Authentication & API Keys (REST API)')
@Controller('rest/auth')
export class ApiKeyController {
  private readonly logger = new Logger(ApiKeyController.name);

  constructor(private readonly apiKeyService: ApiKeyService) {}

  @Post('api-key')
  @UseGuards(AuthGuard('jwt')) // Use JwtAuthGuard, assuming 'jwt' is the name of your JwtStrategy
  @ApiBearerAuth() // If using JWT Bearer token for authentication
  @ApiOperation({
    summary: 'Generate a new API key for the authenticated user.',
  })
  @ApiResponse({ status: 201, description: 'API key generated successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 429, description: 'Too many requests.' })
  @ApiResponse({ status: 500, description: 'Internal server error.' })
  @Throttle({ default: { limit: 5, ttl: 60000 } }) // Example: 5 requests per minute
  async generateNewApiKey(
    @Req() req: AuthenticatedRequest,
    @Ip() clientIp: string,
  ) {
    this.logger.log(`Request to generate API key received for user.`);

    // Ensure user object and userId are present
    if (!req.user || !req.user.userId) {
      this.logger.warn(
        'User or userId missing from request after guard. Check JWT strategy.',
      );
      throw new HttpException(
        'User information is missing.',
        HttpStatus.UNAUTHORIZED,
      );
    }

    const ssoid = req.user.userId; // Use userId as ssoid for the service
    this.logger.log(`Generating API key for SSOID: ${ssoid}, IP: ${clientIp}`);

    try {
      const result = await this.apiKeyService.generateApiKey(ssoid, clientIp);
      return result;
    } catch (error) {
      let errorMessage =
        'An unknown error occurred while generating the API key.';
      let errorStack: string | undefined = undefined; // Corrected type
      if (error instanceof Error) {
        errorMessage = error.message;
        errorStack = error.stack;
      }
      this.logger.error(
        `Failed to generate API key for SSOID ${ssoid}: ${errorMessage}`,
        errorStack,
      );
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        'Could not generate API key.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
