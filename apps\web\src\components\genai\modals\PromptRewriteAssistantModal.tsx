'use client';

import React, { useState, useEffect } from 'react';
import {
  Modal,
  Box,
  Typography,
  Button,
  TextField,
  CircularProgress,
  IconButton,
  Drawer,
  SwipeableDrawer,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import AutoFixHighIcon from '@mui/icons-material/AutoFixHigh';
import { styled } from '@mui/material/styles';

const ModalContainer = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: '80%',
  maxWidth: '800px',
  height: 'auto',
  backgroundColor: theme.palette.background.paper,
  boxShadow: theme.shadows[24],
  p: 0,
  display: 'flex',
  flexDirection: 'column',
  borderRadius: '16px',
  overflow: 'hidden',
  zIndex: 9999, // Ensure it's above other elements like the burger menu
}));

const Header = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  padding: theme.spacing(2),
  borderBottom: `1px solid ${theme.palette.divider}`,
  backgroundColor: theme.palette.background.paper, // Ensure it matches PromptGalleryModal
  color: theme.palette.text.primary, // Ensure it matches PromptGalleryModal
}));

const Footer = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'flex-end',
  gap: '10px',
  padding: theme.spacing(2),
  borderTop: `1px solid ${theme.palette.divider}`,
}));

interface PromptRewriteAssistantModalProps {
  open: boolean;
  onClose: () => void;
  currentPrompt: string;
  onApply: (newPrompt: string) => void;
  onRewrite: (prompt: string) => Promise<string>;
  onGenerate: (idea: string) => Promise<string>;
}

const PromptRewriteAssistantModal: React.FC<
  PromptRewriteAssistantModalProps
> = ({ open, onClose, currentPrompt, onApply, onRewrite, onGenerate }) => {
  const [idea, setIdea] = useState('');
  const [rewrittenPrompt, setRewrittenPrompt] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const isRewriteMode = currentPrompt && currentPrompt.trim() !== '';

  useEffect(() => {
    if (open) {
      setRewrittenPrompt('');
      setIdea('');
      setError(null);
    }
  }, [open]);

  const handleRewrite = async () => {
    if (!currentPrompt) return;
    setIsLoading(true);
    setError(null);
    try {
      const result = await onRewrite(currentPrompt);
      setRewrittenPrompt(result);
    } catch (err) {
      setError('Failed to rewrite prompt. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleGenerate = async () => {
    if (!idea) return;
    setIsLoading(true);
    setError(null);
    try {
      const result = await onGenerate(idea);
      setRewrittenPrompt(result);
    } catch (err) {
      setError('Failed to generate prompt. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleApply = () => {
    onApply(rewrittenPrompt);
    onClose();
  };

  const modalContent = (
    <ModalContainer sx={isMobile ? { width: '100%', height: '73vh', top: 'auto', bottom: 0, left: 0, transform: 'none', borderRadius: '16px 16px 0 0' } : {}}>
      <Header>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <AutoFixHighIcon />
          <Typography variant="h6">Help me write</Typography>
        </Box>
      </Header>
      <Box sx={{ p: 2, overflowY: 'auto', flexGrow: 1 }}>
        {isRewriteMode ? (
          <Box>
            <Typography variant="body1" gutterBottom>
              Original Prompt:
            </Typography>
            <TextField
              fullWidth
              multiline
              rows={4}
              value={currentPrompt}
              variant="standard"
              disabled
              InputProps={{
                disableUnderline: true,
                sx: {
                  borderRadius: '12px',
                  '&.MuiInputBase-root': {
                    backgroundColor: 'transparent',
                  },
                  '& .MuiOutlinedInput-notchedOutline': {
                    border: 'none',
                  },
                  '& .MuiInputBase-input': {
                    overflowY: 'auto',
                    maxHeight: '100px',
                    '&::-webkit-scrollbar': { width: '8px' },
                    '&::-webkit-scrollbar-track': { backgroundColor: 'transparent' },
                    '&::-webkit-scrollbar-thumb': { backgroundColor: (theme) => theme.palette.grey[400], borderRadius: '4px' },
                    '&::-webkit-scrollbar-thumb:hover': { backgroundColor: (theme) => theme.palette.grey[500] },
                  },
                },
              }}
            />
            <Button
              onClick={handleRewrite}
              disabled={isLoading}              
              color="primary"
              variant="contained"
              sx={{ borderRadius: '16px', textTransform:'none' }}
            >
              {isLoading ? <CircularProgress size={24} /> : 'Rewrite'}
            </Button>
          </Box>
        ) : (
          <Box sx={{ p: 2 }}>
            <Typography variant="body1" gutterBottom>
              Enter your idea to generate a prompt:
            </Typography>
            <TextField
              fullWidth
              multiline
              rows={4}
              value={idea}
              onChange={(e) => setIdea(e.target.value)}
              variant="standard"
              placeholder="e.g., a function that calculates the factorial of a number"
              InputProps={{
                disableUnderline: true,
                sx: {
                  borderRadius: '12px',
                  '&.MuiInputBase-root': {
                    backgroundColor: 'transparent',
                  },
                  '& .MuiOutlinedInput-notchedOutline': {
                    border: 'none',
                  },
                  '& .MuiInputBase-input': {
                    overflowY: 'auto',
                    maxHeight: '100px',
                    '&::-webkit-scrollbar': { width: '8px' },
                    '&::-webkit-scrollbar-track': { backgroundColor: 'transparent' },
                    '&::-webkit-scrollbar-thumb': { backgroundColor: (theme) => theme.palette.grey[400], borderRadius: '4px' },
                    '&::-webkit-scrollbar-thumb:hover': { backgroundColor: (theme) => theme.palette.grey[500] },
                  },
                },
              }}
            />
            <Button
              onClick={handleGenerate}
              disabled={isLoading || !idea}
              color="primary"
              variant="contained"
              sx={{ borderRadius: '16px', textTransform:'none' }}
            >
              {isLoading ? <CircularProgress size={24} /> : 'Generate'}
            </Button>
          </Box>
        )}

        {error && (
          <Typography color="error" sx={{ mt: 2 }}>
            {error}
          </Typography>
        )}

        {rewrittenPrompt && (
          <Box sx={{ mt: 2, p: 2 }}>
            <Typography variant="body1" gutterBottom>
              {isRewriteMode ? 'Rewritten Prompt:' : 'Generated Prompt:'}
            </Typography>
            <TextField
              fullWidth
              multiline
              rows={4}
              value={rewrittenPrompt}
              variant="standard"
              onChange={(e) => setRewrittenPrompt(e.target.value)}
              InputProps={{
                disableUnderline: true,
                sx: {
                  borderRadius: '12px',
                  p: 2, // Add padding directly to the TextField
                  '&.MuiInputBase-root': {
                    backgroundColor: (theme) => theme.palette.background.default,
                  },
                  '& .MuiOutlinedInput-notchedOutline': {
                    border: 'none',
                  },
                  '& .MuiInputBase-input': {
                    overflowY: 'auto',
                    maxHeight: '100px',
                    '&::-webkit-scrollbar': { width: '8px' },
                    '&::-webkit-scrollbar-track': { backgroundColor: 'transparent' },
                    '&::-webkit-scrollbar-thumb': { backgroundColor: (theme) => theme.palette.grey[400], borderRadius: '4px' },
                    '&::-webkit-scrollbar-thumb:hover': { backgroundColor: (theme) => theme.palette.grey[500] },
                  },
                },
              }}
            />
          </Box>
        )}
      </Box>

      <Footer>
        <Button
          onClick={onClose}
          color="inherit"
          variant="outlined"
          sx={{ borderRadius: '16px', textTransform:'none' }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleApply}
          color="primary"
          variant="contained"
          disabled={!rewrittenPrompt}
          sx={{ borderRadius: '16px', textTransform:'none' }}
      >
          Insert
        </Button>
      </Footer>
    </ModalContainer>
  );

  if (isMobile) {
    return (
      <SwipeableDrawer
        anchor="bottom"
        open={open}
        onClose={onClose}
        onOpen={() => {}} // Required prop for SwipeableDrawer
        PaperProps={{
          sx: {
            borderTopLeftRadius: 16,
            borderTopRightRadius: 16,
            backdropFilter: 'blur(10px)',
            backgroundColor: 'background.paper',
            height: '73vh',
            zIndex: 9999, // Ensure it's above other elements like the burger menu
          },
        }}
        disableSwipeToOpen={false}
        ModalProps={{
          keepMounted: true,
        }}
      >
        <Box
          onClick={onClose}
          sx={{
            display: 'flex',
            justifyContent: 'center',
            pt: 1,
            pb: 0.5,
            cursor: 'pointer',
          }}
        >
          <Box
            sx={{
              width: 40,
              height: 4,
              borderRadius: 2,
              bgcolor: 'text.secondary',
            }}
          />
        </Box>
        {modalContent}
      </SwipeableDrawer>
    );
  }

  return (
    <Modal
      open={open}
      onClose={onClose}
      disableRestoreFocus
      BackdropProps={{
        sx: {
          backdropFilter: 'blur(10px)',
        },
      }}
    >
      {modalContent}
    </Modal>
  );
};

export default PromptRewriteAssistantModal;