import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { LlmModule } from '../../llm/llm.module'; // For LlmConfigService
import { PrismaModule } from '../../prisma/prisma.module';
import { AuthModule } from '../../auth/auth.module'; // For ApiKeyAuthGuard and AuthService if needed by EmbeddingsService
import { CommonModule } from '../../common/common.module'; // For TokenUsageService
import { EmbeddingsController } from './embeddings.controller';
import { EmbeddingsService } from './embeddings.service'; // Import EmbeddingsService

@Module({
  imports: [
    ConfigModule,
    LlmModule,
    PrismaModule,
    AuthModule, // Guards might need services from here, or the service itself might
    CommonModule, // For TokenUsageService
  ],
  controllers: [EmbeddingsController],
  providers: [
    EmbeddingsService, // Provide EmbeddingsService
  ],
  exports: [
    EmbeddingsService, // Export EmbeddingsService
  ],
})
export class EmbeddingsModule {}
