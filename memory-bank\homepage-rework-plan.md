# Homepage UI Rework Plan (April 30, 2025)

## Overall Goal

Refactor `page.tsx` and related components to address the sidebar overlap issue, implement a new model selection flow (recent pills + search modal), and add a conditional query input directly on the homepage.

## Detailed Plan

1.  **Fix Sidebar Overlap:**
    *   **Files:** `hkbu-genai-platform/apps/web/src/app/page.tsx`, `hkbu-genai-platform/apps/web/src/components/genai/ChatHistorySidebar.tsx` (and potentially related CSS/Tailwind config).
    *   **Action:** Modify the CSS for the main content area (`Box component="main" ...` in `page.tsx`) to ensure it correctly accounts for the sidebar's width, especially on smaller viewports. This might involve adjusting `margin-left`, `padding-left`, or using responsive CSS classes (e.g., Tailwind's `md:ml-64` if the sidebar width is fixed) to prevent the main content from rendering underneath the absolutely positioned or fixed sidebar. Ensure the main content flows correctly next to the sidebar.

2.  **Implement "Recently Used" Model Pills:**
    *   **File:** `hkbu-genai-platform/apps/web/src/app/page.tsx`.
    *   **Action:**
        *   Fetch recent models using the existing `useGetRecentModelsQuery`.
        *   Remove the `<ModelListSection title="Recently used" ... />` component instance.
        *   Render the fetched recent models as selectable pills (e.g., MUI `Chip` or custom `Button`s) directly below the "What do you want to know?" title.
        *   Implement component state (`useState`) to track the `selectedRecentModel` (type `GptModel | null`).
        *   Apply distinct styling (e.g., background color change, border) to the pill corresponding to the `selectedRecentModel`.
        *   Clicking a pill updates the `selectedRecentModel` state.

3.  **Implement Model Search Modal:**
    *   **File:** `hkbu-genai-platform/apps/web/src/app/page.tsx`, Create new file `hkbu-genai-platform/apps/web/src/components/genai/ModelSearchModal.tsx`.
    *   **Action:**
        *   Remove the existing `TextField` used for searching models in `page.tsx`.
        *   Add an `IconButton` containing a `SearchIcon` near the "What do you want to know?" title or recent pills.
        *   Create the `ModelSearchModal` component:
            *   Use MUI `Modal` or `Dialog`.
            *   Include a search `TextField` inside the modal.
            *   Fetch all models using `useGetModelsQuery`.
            *   Display the models in a list, filtered by the modal's search input.
            *   Style the list items similar to the provided screenshot (icon, name, category).
            *   Clicking a model in the list should close the modal and navigate the user to `/chat/new?model=<model_name>`.
        *   In `page.tsx`, manage the open/close state of the modal (`useState`) and trigger it from the new search `IconButton`.

4.  **Add Conditional Homepage Query Input:**
    *   **File:** `hkbu-genai-platform/apps/web/src/app/page.tsx` (or create a new `HomepageInputArea.tsx` component).
    *   **Action:**
        *   Add a new `TextField` component below the recent model pills section.
        *   This input field should be `disabled` by default.
        *   Enable the input field (`disabled={!selectedRecentModel}`) only when `selectedRecentModel` (from step 2) is not null.
        *   Add placeholder text like "Type your query here...".
        *   Implement an `onKeyDown` handler or wrap it in a `<form>` with an `onSubmit` handler.
        *   When the user presses Enter (and `selectedRecentModel` is set and the input is not empty):
            *   Prevent default form submission if using `<form>`.
            *   Get the query text from the input field's state.
            *   Navigate the user to `/chat/new?model=${selectedRecentModel.model_name}&query=${encodeURIComponent(queryText)}`.

5.  **Update Chat Page (`/chat/new`):**
    *   **File:** `hkbu-genai-platform/apps/web/src/app/chat/new/page.tsx`.
    *   **Action:** Modify this page to read the optional `query` URL search parameter. If present, potentially pre-fill the chat input area or automatically send this query as the first message using the selected model.

6.  **Styling and Refinement:**
    *   Ensure all new elements (pills, modal, input area) are styled consistently with the existing UI using MUI and Tailwind CSS.
    *   Test responsiveness thoroughly across different screen sizes.

## Component Interaction Diagram

```mermaid
graph TD
    subgraph page.tsx (Homepage)
        UI_Title["What do you want to know?"]
        UI_RecentPills["Recent Model Pills (Chip/Button)"]
        UI_SearchIcon["Search Icon (IconButton)"]
        UI_HomepageInput["Query Input (TextField)"]

        State_SelectedModel["selectedRecentModel (State)"]
        State_ModalOpen["isSearchModalOpen (State)"]
        State_QueryText["homepageQuery (State)"]

        Logic_FetchRecent["useGetRecentModelsQuery()"]
        Logic_HandlePillClick["handlePillClick()"]
        Logic_HandleSearchClick["handleSearchClick()"]
        Logic_HandleQuerySubmit["handleQuerySubmit()"]

        UI_Title --> UI_RecentPills
        UI_Title --> UI_SearchIcon
        UI_RecentPills --> UI_HomepageInput

        Logic_FetchRecent --> UI_RecentPills

        UI_RecentPills -- onClick --> Logic_HandlePillClick
        Logic_HandlePillClick -- Updates --> State_SelectedModel

        UI_SearchIcon -- onClick --> Logic_HandleSearchClick
        Logic_HandleSearchClick -- Updates --> State_ModalOpen

        State_SelectedModel -- Enables --> UI_HomepageInput
        UI_HomepageInput -- onChange --> State_QueryText
        UI_HomepageInput -- onKeyDown/onSubmit --> Logic_HandleQuerySubmit

        Logic_HandleQuerySubmit -- Reads --> State_SelectedModel
        Logic_HandleQuerySubmit -- Reads --> State_QueryText
        Logic_HandleQuerySubmit -- Navigates --> ChatPageNew

        UI_SearchIcon -- Triggers --> ModelSearchModal
    end

    subgraph ModelSearchModal.tsx
        UI_Modal["Modal/Dialog"]
        UI_ModalSearchInput["Search Input (TextField)"]
        UI_ModalModelList["List of All Models"]

        Logic_FetchAll["useGetModelsQuery()"]
        Logic_FilterModels["Filter Logic"]
        Logic_HandleModelSelect["handleModalModelSelect()"]

        Logic_FetchAll --> UI_ModalModelList
        UI_ModalSearchInput -- Filters --> Logic_FilterModels --> UI_ModalModelList
        UI_ModalModelList -- onClick --> Logic_HandleModelSelect
        Logic_HandleModelSelect -- Navigates --> ChatPageNew
    end

    subgraph ChatHistorySidebar.tsx
        UI_Sidebar["Sidebar Layout"]
        CSS_Sidebar["Styling/Positioning"]
    end

    subgraph page.tsx (Main Layout)
        Layout_FlexContainer["Flex Container (Box)"]
        Layout_SidebarArea["Sidebar Area"]
        Layout_MainContent["Main Content Area (Box)"]
        CSS_MainContent["Responsive Margin/Padding"]

        Layout_FlexContainer --> Layout_SidebarArea
        Layout_FlexContainer --> Layout_MainContent
        Layout_SidebarArea --> UI_Sidebar
        Layout_MainContent --> UI_Title

        CSS_Sidebar -- Affects --> CSS_MainContent
    end

    subgraph ChatPageNew ("/chat/new/page.tsx")
        Logic_ReadParams["Read URL Params (model, query)"]
        Logic_StartChat["Initialize Chat / Send First Message"]

        Logic_ReadParams --> Logic_StartChat
    end

    classDef page fill:#f9f,stroke:#333,stroke-width:1px;
    classDef modal fill:#ccf,stroke:#333,stroke-width:1px;
    classDef chat fill:#cfc,stroke:#333,stroke-width:1px;
    classDef sidebar fill:#ffc,stroke:#333,stroke-width:1px;
    classDef layout fill:#eee,stroke:#333,stroke-width:1px;
    class UI_Title,UI_RecentPills,UI_SearchIcon,UI_HomepageInput,State_SelectedModel,State_ModalOpen,State_QueryText,Logic_FetchRecent,Logic_HandlePillClick,Logic_HandleSearchClick,Logic_HandleQuerySubmit page;
    class UI_Modal,UI_ModalSearchInput,UI_ModalModelList,Logic_FetchAll,Logic_FilterModels,Logic_HandleModelSelect modal;
    class Logic_ReadParams,Logic_StartChat chat;
    class UI_Sidebar,CSS_Sidebar sidebar;
    class Layout_FlexContainer,Layout_SidebarArea,Layout_MainContent,CSS_MainContent layout;