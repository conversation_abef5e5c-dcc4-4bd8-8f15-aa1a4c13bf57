import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsUUID,
  IsBoolean,
  IsArray,
  IsNumber,
  Min,
  Max,
  ValidateNested,
  IsIn,
  IsNotEmpty,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  Validate,
} from 'class-validator';
import { Type } from 'class-transformer';

// Message roles enum matching OpenAI API
export const MESSAGE_ROLES = [
  'system',
  'user',
  'assistant',
  'tool',
  'function',
] as const;
export type MessageRole = (typeof MESSAGE_ROLES)[number];

// Types for vision content
export interface TextContent {
  type: 'text';
  text: string;
}

export interface ImageContent {
  type: 'image_url';
  image_url: {
    url: string;
    detail?: 'low' | 'high' | 'auto';
  };
}

export type MessageContent = string | (TextContent | ImageContent)[];

// Custom validator for message content
@ValidatorConstraint({ name: 'isValidMessageContent', async: false })
export class IsValidMessageContent implements ValidatorConstraintInterface {
  validate(content: any): boolean {
    if (typeof content === 'string') {
      return content.length > 0;
    }

    if (Array.isArray(content)) {
      return content.every((item) => {
        if (item.type === 'text') {
          return typeof item.text === 'string' && item.text.length > 0;
        }
        if (item.type === 'image_url') {
          return (
            item.image_url &&
            typeof item.image_url.url === 'string' &&
            item.image_url.url.length > 0
          );
        }
        return false;
      });
    }

    return false;
  }

  defaultMessage(): string {
    return 'Content must be either a string or an array of content objects with type "text" or "image_url"';
  }
}

// Nested DTO for message content in OpenAI format
class ChatCompletionMessageDto {
  @ApiProperty({
    description: 'The role of the message author.',
    enum: MESSAGE_ROLES,
    example: 'user',
  })
  @IsIn(MESSAGE_ROLES)
  @IsNotEmpty()
  role!: MessageRole;

  @ApiProperty({
    description:
      'The content of the message. Can be a string or an array of content objects for vision prompts.',
    oneOf: [
      { type: 'string' },
      {
        type: 'array',
        items: {
          oneOf: [
            {
              type: 'object',
              properties: {
                type: { type: 'string', enum: ['text'] },
                text: { type: 'string' },
              },
              required: ['type', 'text'],
            },
            {
              type: 'object',
              properties: {
                type: { type: 'string', enum: ['image_url'] },
                image_url: {
                  type: 'object',
                  properties: {
                    url: { type: 'string' },
                    detail: { type: 'string', enum: ['low', 'high', 'auto'] },
                  },
                  required: ['url'],
                },
              },
              required: ['type', 'image_url'],
            },
          ],
        },
      },
    ],
    example:
      'Hello! Can you help me write a Python function to calculate the factorial of a number?',
  })
  @Validate(IsValidMessageContent)
  content!: MessageContent;

  @ApiPropertyOptional({
    description:
      'An optional name for the participant. Provides the model information to differentiate between participants of the same role.',
  })
  @IsString()
  @IsOptional()
  name?: string;
}

// Nested DTO for file information (keeping compatibility with existing system)
class UploadedFileDto {
  @ApiProperty({ description: 'Original filename of the uploaded file.' })
  @IsString()
  filename!: string;

  @ApiProperty({ description: 'MIME type of the uploaded file.' })
  @IsString()
  mimeType!: string;

  @ApiProperty({ description: 'Base64 encoded content of the file.' })
  @IsString()
  content!: string; // Base64 encoded string
}

// REST API DTO for creating a chat completion request (OpenAI compatible)
export class CreateChatCompletionRestDto {
  @ApiProperty({
    description: 'A list of messages comprising the conversation so far.',
    type: [ChatCompletionMessageDto],
    example: [
      {
        role: 'system',
        content:
          'You are a helpful programming assistant. Provide clear, concise code examples and explanations.',
      },
      {
        role: 'user',
        content:
          'Hello! Can you help me write a Python function to calculate the factorial of a number?',
      },
    ],
  })
  @IsArray()
  @IsNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => ChatCompletionMessageDto)
  messages!: ChatCompletionMessageDto[];

  @ApiPropertyOptional({
    description:
      'UUID of the existing chat session. If omitted, a new session will be created.',
    format: 'uuid',
  })
  @IsUUID()
  @IsOptional()
  chat_session_id?: string;

  @ApiPropertyOptional({
    description:
      'Controls randomness: lowering results in less random completions.',
    minimum: 0,
    maximum: 2,
    default: 1,
    example: 0.7,
  })
  @IsNumber()
  @Min(0)
  @Max(2)
  @IsOptional()
  temperature?: number = 1;

  @ApiPropertyOptional({
    description:
      'If set, partial message deltas will be sent, like in ChatGPT. Tokens will be sent as data-only server-sent events as they become available, with the stream terminated by a `data: [DONE]` message.',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  stream?: boolean = false;

  @ApiPropertyOptional({
    description:
      'Flag to indicate whether Google Search should be used to augment the context (if available and configured).',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  useGoogle?: boolean = false;

  @ApiPropertyOptional({
    description: 'Array of uploaded files associated with the prompt.',
    type: [UploadedFileDto],
  })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => UploadedFileDto)
  files?: UploadedFileDto[];

  @ApiPropertyOptional({
    description: 'The maximum number of tokens to generate in the completion.',
    type: Number,
    minimum: 1,
    example: 150,
  })
  @IsNumber()
  @Min(1)
  @IsOptional()
  max_tokens?: number;

  @ApiPropertyOptional({
    description:
      'An alternative to sampling with temperature, called nucleus sampling.',
    type: Number,
    minimum: 0,
    maximum: 1,
    example: 1,
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  @IsOptional()
  top_p?: number;

  // Additional OpenAI compatible parameters can be added here as needed:
  // - n
  // - presence_penalty
  // - frequency_penalty
  // - logit_bias
  // - user
  // - stop
  // - functions
  // - function_call
  // - tools
  // - tool_choice
}
