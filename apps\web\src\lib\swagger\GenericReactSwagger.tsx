'use client';

import { useEffect, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { useSearchParams, useRouter } from 'next/navigation';
import {
  Box,
  Typography,
  Alert,
  CircularProgress,
  Chip,
  Button,
  Breadcrumbs,
  Link,
  Paper,
} from '@mui/material';
import ThemedSwaggerUI from './ThemedSwaggerUI';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import ApiIcon from '@mui/icons-material/Api';
import { ProviderSwaggerConfig } from './provider-swagger';

type Props = {
  spec: Record<string, any>;
  config: ProviderSwaggerConfig;
};

function GenericReactSwagger({ spec, config }: Props) {
  const { data: session, status } = useSession();
  const searchParams = useSearchParams();
  const router = useRouter();
  const isFullscreen = searchParams.get('fullscreen') === 'true';
  const originalConsoleError = useRef(console.error);

  const handleBackClick = () => {
    router.push('/settings/api-service');
  };

  useEffect(() => {
    // Suppress specific React lifecycle warnings from swagger-ui-react
    // These are known issues with swagger-ui-react v5.x and React 18+
    // The library uses deprecated lifecycle methods that trigger warnings in strict mode
    // Functionality is not affected, but we suppress to keep console clean
    // TODO: Remove this when swagger-ui-react updates to modern React patterns
    const suppressedWarnings = [
      'UNSAFE_componentWillReceiveProps',
      'ModelCollapse',
      'OperationContainer',
      'ContentType',
      'Use the `defaultValue` or `value` props on <select> instead of setting `selected` on <option>',
      'selected',
      'option',
      'validateOptionProps',
    ];

    console.error = (...args) => {
      const message = args[0]?.toString() || '';
      const shouldSuppress = suppressedWarnings.some((warning) =>
        message.includes(warning),
      );

      if (!shouldSuppress) {
        originalConsoleError.current(...args);
      }
    };

    // Also suppress React DevTools warnings for swagger-ui-react
    const originalConsoleWarn = console.warn;
    console.warn = (...args) => {
      const message = args[0]?.toString() || '';
      const shouldSuppress = suppressedWarnings.some((warning) =>
        message.includes(warning),
      );

      if (!shouldSuppress) {
        originalConsoleWarn(...args);
      }
    };

    // Restore original console methods on cleanup
    return () => {
      console.error = originalConsoleError.current;
      console.warn = originalConsoleWarn;
    };
  }, []);

  if (status === 'loading') {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: isFullscreen ? '100svh' : '400px',
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  if (!session) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          <Typography>Access Denied</Typography>
          <Typography variant="body2">
            You must be signed in to view this page.
          </Typography>
        </Alert>
      </Box>
    );
  }

  // Fullscreen mode: render minimal content
  if (isFullscreen) {
    return (
      <Box
        sx={{
          width: '100vw',
          height: '100svh',
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
        }}
      >
        {/* Minimal header for fullscreen */}
        <Box
          sx={{
            p: 2,
            borderBottom: '1px solid #e0e0e0',
            display: 'flex',
            alignItems: 'center',
            gap: 2,
          }}
        >
          <ApiIcon sx={{ fontSize: 32, color: 'primary.main' }} />
          <Typography variant="h5">{config.title}</Typography>
          <Box sx={{ display: 'flex', gap: 1, ml: 'auto' }}>
            <Chip
              label={config.chipLabels.provider}
              color="primary"
              size="small"
            />
            <Chip
              label={config.chipLabels.version}
              color="secondary"
              size="small"
            />
          </Box>
        </Box>

        {/* Swagger UI Content */}
        <Box
          sx={{
            flex: 1,
            overflow: 'auto',
            '& .swagger-ui': {
              maxWidth: 'none',
            },
          }}
        >
          <ThemedSwaggerUI spec={spec} />
        </Box>
      </Box>
    );
  }

  // Normal mode: render with navigation
  return (
    <Box sx={{ p: 3 }}>
      {/* Breadcrumb Navigation */}
      <Breadcrumbs
        separator={<NavigateNextIcon fontSize="small" />}
        aria-label="breadcrumb"
        sx={{ mb: 3 }}
      >
        <Link
          underline="hover"
          color="inherit"
          href="/settings"
          onClick={(e) => {
            e.preventDefault();
            router.push('/settings');
          }}
        >
          Settings
        </Link>
        <Link
          underline="hover"
          color="inherit"
          href="/settings/api-service"
          onClick={(e) => {
            e.preventDefault();
            router.push('/settings/api-service');
          }}
        >
          API Service
        </Link>
        <Typography color="text.primary">{config.title}</Typography>
      </Breadcrumbs>

      {/* Back Button */}
      <Button
        variant="outlined"
        startIcon={<ArrowBackIcon />}
        onClick={handleBackClick}
        sx={{ mb: 3 }}
      >
        Back to API Service
      </Button>

      {/* Page Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
        <ApiIcon sx={{ fontSize: 40, color: 'primary.main' }} />
        <Box>
          <Typography variant="h4" gutterBottom>
            {config.title}
          </Typography>
          <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
            <Chip
              label={config.chipLabels.provider}
              color="primary"
              size="small"
            />
            <Chip
              label={config.chipLabels.version}
              color="secondary"
              size="small"
            />
          </Box>
        </Box>
      </Box>

      {/* Swagger UI Content */}
      <Paper sx={{ p: 0, mt: 2 }}>
        <Box
          sx={{
            '& .swagger-ui': {
              maxWidth: '100%',
            },
          }}
        >
          <ThemedSwaggerUI spec={spec} />
        </Box>
      </Paper>
    </Box>
  );
}

export default GenericReactSwagger;
