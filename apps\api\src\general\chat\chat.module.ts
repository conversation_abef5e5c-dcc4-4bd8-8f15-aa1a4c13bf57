import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config'; // Added ConfigModule
import { PrismaModule } from '../../prisma/prisma.module'; // Corrected: Import local PrismaModule
import { ChatService } from './chat.service';
import { ChatController } from './chat.controller';
import { ChatCompletionModule } from './chat-completion/chat-completion.module'; // Import moved ChatCompletionModule
import { ChatCompletionService } from './chat-completion/chat-completion.service'; // Import the service

@Module({
  imports: [PrismaModule, ConfigModule, ChatCompletionModule], // Add ChatCompletionModule
  controllers: [ChatController],
  providers: [ChatService],
  exports: [ChatCompletionModule], // Re-export the module to make its providers available
})
export class ChatModule {}
