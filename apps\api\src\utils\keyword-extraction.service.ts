import {
  Injectable,
  Logger,
  InternalServerErrorException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { LlmConfigService } from '../llm/llm-config.service';
import { ChatOpenAI } from '@langchain/openai';
import { HumanMessage } from '@langchain/core/messages';

@Injectable()
export class KeywordExtractionService {
  private readonly logger = new Logger(KeywordExtractionService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly llmConfigService: LlmConfigService,
  ) {}

  /**
   * Extracts 3 relevant search phrases from a user prompt using GPT-4.1-mini
   * @param userPrompt - The original user prompt
   * @param deptUnitCode - User's department code for model access
   * @param conversationContext - Optional array of recent conversation messages for context
   * @returns Array of search phrases (2-4 words each)
   */
  async extractKeywords(
    userPrompt: string,
    deptUnitCode: string,
    conversationContext?: string[],
  ): Promise<string[]> {
    try {
      this.logger.log(`🔍 SEARCH PHRASE EXTRACTION STARTED`);
      this.logger.log(
        `📝 User Prompt: "${userPrompt.substring(0, 150)}${userPrompt.length > 150 ? '...' : ''}"`,
      );
      this.logger.log(`🏢 Department: ${deptUnitCode}`);

      if (conversationContext && conversationContext.length > 0) {
        this.logger.log(
          `📚 Context Available: ${conversationContext.length} recent messages`,
        );
        this.logger.log(
          `🔗 Context Preview: "${conversationContext.join(' | ').substring(0, 100)}..."`,
        );
      }

      // Get GPT-4.1-mini configuration
      const llmConfig = await this.llmConfigService.getLlmConfig(
        deptUnitCode,
        'gpt-4.1-mini',
      );
      this.logger.log(
        `🤖 Using GPT-4.1-mini model for search phrase extraction: ${llmConfig.deploymentName}`,
      );

      // Create GPT-4.1-mini model instance
      const modelConfig = {
        temperature: 0.1, // Low temperature for consistent phrase extraction
        maxTokens: 50, // Use standard maxTokens for GPT-4.1-mini
        modelName: llmConfig.deploymentName ?? 'gpt-4.1-mini',
        configuration: {
          apiKey: llmConfig.apiKey,
          baseURL: `https://${llmConfig.instanceName}.openai.azure.com/openai/deployments/${llmConfig.deploymentName ?? 'gpt-4.1-mini'}`,
          defaultQuery: { 'api-version': llmConfig.apiVersion },
        },
      };

      this.logger.debug(
        `🔧 GPT-4.1-mini Model Configuration:`,
        JSON.stringify(modelConfig, null, 2),
      );
      const keywordModel = new ChatOpenAI(modelConfig);

      // Specialized prompt for search term generation with context support
      let keywordExtractionPrompt: string;

      if (conversationContext && conversationContext.length > 0) {
        // Create contextual prompt when conversation history is available
        const contextText = conversationContext.join(' -> ');
        keywordExtractionPrompt = `Based on this conversation context: "${contextText}" and the latest user question: "${userPrompt}", generate exactly 3 effective search phrases (2-4 words each) that will find relevant information when searched on Google. Each phrase should be a complete search term that combines the conversation context with the current question. Return only the phrases, separated by commas.`;
      } else {
        // Use simple prompt for single message or no context
        keywordExtractionPrompt = `Based on the user query "${userPrompt}", generate exactly 3 effective search phrases (2-4 words each) that will find relevant information when searched on Google. Each phrase should be a complete search term. Return only the phrases, separated by commas.`;
      }

      // Generate search phrases using GPT-4.1-mini
      this.logger.log(
        `💭 Sending search phrase extraction request to GPT-4.1-mini...`,
      );
      const response = await keywordModel.invoke([
        new HumanMessage(keywordExtractionPrompt),
      ]);

      // Parse search phrases from response
      const phrasesText =
        typeof response.content === 'string'
          ? response.content
          : String(response.content);
      this.logger.log(`🎯 GPT-4.1-mini Response: "${phrasesText}"`);

      const phrases = phrasesText
        .split(',')
        .map((phrase) => phrase.trim())
        .filter((phrase) => phrase.length > 0 && phrase.length < 100) // Filter valid phrases (increased length limit)
        .slice(0, 3); // Limit to 3 phrases max

      this.logger.log(`✅ EXTRACTED SEARCH PHRASES: [${phrases.join(', ')}]`);

      if (phrases.length === 0) {
        this.logger.warn(
          '⚠️ No valid phrases extracted, falling back to intelligent search phrase generation',
        );
        return this.generateFallbackSearchTerms(userPrompt);
      }

      return phrases;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Error extracting keywords: ${errorMessage}`,
        errorStack,
      );

      // Fallback: intelligent search phrase generation
      this.logger.warn(
        '⚠️ Falling back to intelligent search phrase generation due to error',
      );
      return this.generateFallbackSearchTerms(userPrompt);
    }
  }

  /**
   * Generates intelligent search phrases when GPT-4.1-mini fails
   * Handles question patterns and generates context-aware search phrases
   */
  private generateFallbackSearchTerms(userPrompt: string): string[] {
    const searchTerms: string[] = [];

    // Clean the prompt - remove punctuation but preserve structure
    const cleanPrompt = userPrompt
      .replace(/[?!.,;]+/g, '')
      .trim()
      .toLowerCase();

    // Common name expansions and corrections
    const nameExpansions: Record<string, string> = {
      elon: 'Elon Musk',
      trump: 'Donald Trump',
      biden: 'Joe Biden',
      tesla: 'Tesla company',
      spacex: 'SpaceX company',
      openai: 'OpenAI company',
      chatgpt: 'ChatGPT OpenAI',
      google: 'Google company',
    };

    // Detect question patterns and generate appropriate search terms
    if (cleanPrompt.match(/^(who is|who's)\s+(.+)/)) {
      const subject = cleanPrompt.replace(/^(who is|who's)\s+/, '');
      const expandedSubject =
        nameExpansions[subject] || this.capitalizeWords(subject);

      searchTerms.push(`${expandedSubject} biography`);
      searchTerms.push(`who is ${expandedSubject}`);
      searchTerms.push(`${expandedSubject} profile`);
      searchTerms.push(`${expandedSubject} achievements`);
    } else if (cleanPrompt.match(/^(what is|what's)\s+(.+)/)) {
      const subject = cleanPrompt.replace(/^(what is|what's)\s+/, '');
      const expandedSubject =
        nameExpansions[subject] || this.capitalizeWords(subject);

      searchTerms.push(`what is ${expandedSubject}`);
      searchTerms.push(`${expandedSubject} definition`);
      searchTerms.push(`${expandedSubject} explanation`);
      searchTerms.push(`${expandedSubject} overview`);
    } else if (cleanPrompt.match(/^(how does|how do|how to)\s+(.+)/)) {
      const subject = cleanPrompt.replace(/^(how does|how do|how to)\s+/, '');

      searchTerms.push(`how ${subject} works`);
      searchTerms.push(`${subject} tutorial`);
      searchTerms.push(`${subject} guide`);
      searchTerms.push(`${subject} explained`);
    } else if (cleanPrompt.match(/^(why|where|when)\s+(.+)/)) {
      // Handle other question words
      searchTerms.push(cleanPrompt);

      // Extract significant words and create variations
      const significantWords = this.extractSignificantWords(cleanPrompt);
      if (significantWords.length > 0) {
        searchTerms.push(significantWords.join(' '));
        // Add expanded version if any word has expansion
        const expandedWords = significantWords.map(
          (word) => nameExpansions[word] || word,
        );
        if (expandedWords.join(' ') !== significantWords.join(' ')) {
          searchTerms.push(expandedWords.join(' '));
        }
      }
    } else {
      // For non-question prompts, extract and expand significant words
      const significantWords = this.extractSignificantWords(cleanPrompt);

      if (significantWords.length > 0) {
        // Add the main terms
        searchTerms.push(significantWords.join(' '));

        // Add expanded versions
        const expandedWords = significantWords.map(
          (word) => nameExpansions[word] || word,
        );
        if (expandedWords.join(' ') !== significantWords.join(' ')) {
          searchTerms.push(expandedWords.join(' '));
        }

        // Add individual important terms if they have expansions
        for (const word of significantWords) {
          if (nameExpansions[word]) {
            searchTerms.push(nameExpansions[word]);
          }
        }
      }
    }

    // Remove duplicates and empty terms
    const uniqueTerms = [...new Set(searchTerms)]
      .filter((term) => term.length > 0)
      .slice(0, 3); // Limit to 3 terms

    this.logger.log(
      `🔄 INTELLIGENT FALLBACK PHRASES: [${uniqueTerms.join(', ')}]`,
    );
    return uniqueTerms;
  }

  /**
   * Extracts significant words from text, filtering out common words
   */
  private extractSignificantWords(text: string): string[] {
    const commonWords = new Set([
      'the',
      'and',
      'or',
      'but',
      'in',
      'on',
      'at',
      'to',
      'for',
      'of',
      'with',
      'by',
      'a',
      'an',
      'is',
      'are',
      'was',
      'were',
      'be',
      'been',
      'have',
      'has',
      'had',
      'do',
      'does',
      'did',
      'will',
      'would',
      'could',
      'should',
      'can',
      'may',
      'might',
      'this',
      'that',
      'these',
      'those',
      'i',
      'you',
      'he',
      'she',
      'it',
      'we',
      'they',
      'me',
      'him',
      'her',
      'us',
      'them',
      'my',
      'your',
      'his',
      'her',
      'its',
      'our',
      'their',
    ]);

    return text
      .split(/\s+/)
      .map((word) => word.toLowerCase().trim())
      .filter((word) => word.length > 2 && !commonWords.has(word))
      .slice(0, 5);
  }

  /**
   * Capitalizes the first letter of each word
   */
  private capitalizeWords(text: string): string {
    return text
      .split(' ')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }
}
