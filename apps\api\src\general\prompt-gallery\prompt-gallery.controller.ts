import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
} from '@nestjs/common';
import { PromptGalleryService } from './prompt-gallery.service';
import { CreatePromptGalleryDto } from './dto/create-prompt-gallery.dto';
import { UpdatePromptGalleryDto } from './dto/update-prompt-gallery.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { GeneralRateLimitGuard } from '../../common/guards/general-rate-limit.guard';

@UseGuards(JwtAuthGuard, GeneralRateLimitGuard)
@Controller('general/prompt-gallery')
export class PromptGalleryController {
  constructor(private readonly promptGalleryService: PromptGalleryService) {}

  @Post()
  create(
    @Request() req,
    @Body() createPromptGalleryDto: CreatePromptGalleryDto,
  ) {
    return this.promptGalleryService.create(
      createPromptGalleryDto,
      req.user.userId,
    );
  }

  @Get()
  findAll(@Request() req) {
    return this.promptGalleryService.findAll(req.user.userId);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.promptGalleryService.findOne(+id);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updatePromptGalleryDto: UpdatePromptGalleryDto,
  ) {
    return this.promptGalleryService.update(+id, updatePromptGalleryDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.promptGalleryService.remove(+id);
  }
}
