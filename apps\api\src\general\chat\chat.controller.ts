import {
  Controller,
  Get,
  Post,
  Body,
  Query,
  ValidationPipe,
  Patch,
  Delete,
  UseGuards,
  Req,
  Ip,
  HttpCode,
  HttpStatus,
  Logger,
  ForbiddenException,
  Param,
} from '@nestjs/common'; // Added Post, Body, UseGuards, Req, Ip, HttpCode, HttpStatus, Logger, ForbiddenException, Param
import { ChatService } from './chat.service';
import { ChatCompletionService } from './chat-completion/chat-completion.service'; // Import ChatCompletionService
import { CreateChatCompletionDto } from './chat-completion/dto/create-chat-completion.dto'; // Import DTO for body
import { IsNotEmpty, IsString, IsUUID } from 'class-validator';
import {
  ApiTags,
  ApiOperation,
  ApiQuery,
  ApiProperty,
  ApiBody,
  ApiBearerAuth,
} from '@nestjs/swagger'; // Added ApiProperty, ApiBody, ApiBearerAuth
import { AuthGuard } from '@nestjs/passport'; // For JWT auth
import { AuthenticatedUser } from '../../auth/user.interface'; // User interface
import { Request as ExpressRequest } from 'express'; // Renamed to avoid conflict with NestJS Req decorator if any confusion
import { ModelRateLimitGuard } from '../../common/guards/model-rate-limit.guard'; // Rate limiting
import { GeneralRateLimitGuard } from '../../common/guards/general-rate-limit.guard';

// DTO for Query Parameter
export class GetHistoryMessagesQueryDto {
  @ApiProperty({
    description: 'The UUID of the chat session',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsNotEmpty()
  @IsUUID()
  chat_session_id!: string; // Added definite assignment assertion
}

// DTO for Share Response
export class ShareConversationResponseDto {
  @ApiProperty({
    description: 'The share ID for the conversation',
    example: '123e4567-e89b-12d3-a456-************',
  })
  shareId!: string;

  @ApiProperty({
    description: 'The share URL for the conversation',
    example: '/chat/shared/123e4567-e89b-12d3-a456-************',
  })
  shareUrl!: string;
}

// DTO for Duplicate Response
export class DuplicateConversationResponseDto {
  @ApiProperty({
    description: 'The new conversation UUID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  conversationUuid!: string;

  @ApiProperty({
    description: 'The redirect URL for the new conversation',
    example: '/chat/123e4567-e89b-12d3-a456-************',
  })
  redirectUrl!: string;
}

export class RenameConversationDto {
  @ApiProperty({
    description: 'The new title for the conversation',
    example: 'My Renamed Conversation',
  })
  @IsNotEmpty()
  @IsString()
  newTitle!: string;
}

@ApiTags('Chat (Frontend)') // Renamed tag for clarity
@Controller('general/chat') // Base path for frontend chat functionalities
@UseGuards(AuthGuard('jwt'), GeneralRateLimitGuard) // Apply JWT guard and general rate limiting
export class ChatController {
  private readonly logger = new Logger(ChatController.name); // Add logger

  constructor(
    private readonly chatService: ChatService,
    private readonly chatCompletionService: ChatCompletionService, // Inject ChatCompletionService
  ) {}

  @Post('completions') // New endpoint: /general/chat/completions
  @UseGuards(AuthGuard('jwt'), ModelRateLimitGuard) // Protect with auth and rate limiting
  //@ApiBearerAuth() // Indicate JWT Bearer token for Swagger
  @ApiOperation({
    summary: 'Create a chat completion for the frontend application.',
  })
  @ApiBody({ type: CreateChatCompletionDto })
  @HttpCode(HttpStatus.OK)
  async createChatCompletion(
    @Body(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
    createChatDto: CreateChatCompletionDto,
    @Req() req: ExpressRequest & { user: AuthenticatedUser }, // Get authenticated user
    @Ip() clientIp: string, // Get client IP
  ) {
    this.logger.log(
      `Frontend chat completion request for model [${createChatDto.model}] by user [${req.user.userId}]`,
    );
    // Note: The ChatCompletionService.createCompletion expects userId (ssoid)
    // The createChatDto should contain 'model', 'prompt', etc.
    // The service internally handles LlmConfigService to get full model config.
    const result = await this.chatCompletionService.createCompletion(
      createChatDto,
      req.user,
    );

    // If the result is a Readable stream, set the Content-Type header for SSE
    if (result instanceof require('stream').Readable) {
      // NestJS handles setting headers for streaming responses automatically when returning a Readable stream
      // No need to manually set headers here.
      return result;
    }
    return result;
  }

  @Get('history/all')
  @UseGuards(AuthGuard('jwt'))
  @ApiOperation({ summary: 'Get all conversation history for the user' })
  async getAllHistory(
    @Req() req: ExpressRequest & { user: AuthenticatedUser },
  ) {
    this.logger.log(`Full history request by user [${req.user.userId}]`);
    return this.chatService.getAllHistory(req.user.userId);
  }

  @Get('history_messages')
  @UseGuards(AuthGuard('jwt')) // Protect this specific endpoint
  //@ApiBearerAuth()
  @ApiOperation({
    summary: 'Get decrypted chat history for a specific session',
  })
  @ApiQuery({ type: GetHistoryMessagesQueryDto }) // Describe query parameters for Swagger
  async getHistoryMessages(
    @Query(new ValidationPipe({ transform: true })) // Apply validation pipe
    query: GetHistoryMessagesQueryDto,
    @Req() req: ExpressRequest & { user: AuthenticatedUser }, // Use Req decorator
  ) {
    this.logger.log(
      `History messages request for session [${query.chat_session_id}]`,
    );
    // Assuming req.user is populated by AuthGuard('jwt')
    const user = req.user;
    if (!user || !user.userId) {
      // Use userId
      this.logger.error(
        'User or userId not found on request for history_messages',
      );
      throw new ForbiddenException(
        'User authentication failed or user identifier missing.',
      );
    }
    return this.chatService.getHistoryMessages(
      query.chat_session_id,
      user.userId,
    ); // Use userId
  }

  @Post('share/:conversationId')
  @UseGuards(AuthGuard('jwt'))
  @ApiOperation({
    summary: 'Generate a share link for a conversation',
  })
  @ApiProperty({ type: ShareConversationResponseDto })
  async shareConversation(
    @Param('conversationId') conversationId: string,
    @Req() req: ExpressRequest & { user: AuthenticatedUser },
  ): Promise<ShareConversationResponseDto> {
    this.logger.log(
      `Share conversation request for [${conversationId}] by user [${req.user.userId}]`,
    );

    const shareId = await this.chatService.generateShareLink(
      conversationId,
      req.user.userId,
    );

    return {
      shareId,
      shareUrl: `/chat/shared/${shareId}`,
    };
  }

  @Get('shared/:shareId')
  @UseGuards(AuthGuard('jwt'))
  @ApiOperation({
    summary: 'Access a shared conversation and create a duplicate',
  })
  @ApiProperty({ type: DuplicateConversationResponseDto })
  async accessSharedConversation(
    @Param('shareId') shareId: string,
    @Req() req: ExpressRequest & { user: AuthenticatedUser },
  ): Promise<DuplicateConversationResponseDto> {
    this.logger.log(
      `Access shared conversation [${shareId}] by user [${req.user.userId}]`,
    );

    const newConversationUuid =
      await this.chatService.duplicateSharedConversation(
        shareId,
        req.user.userId,
      );

    return {
      conversationUuid: newConversationUuid,
      redirectUrl: `/chat/${newConversationUuid}`,
    };
  }

  @Patch(':conversationId/title')
  @UseGuards(AuthGuard('jwt'))
  @ApiOperation({ summary: 'Rename a conversation' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async renameConversation(
    @Param('conversationId') conversationId: string,
    @Body() renameConversationDto: RenameConversationDto,
    @Req() req: ExpressRequest & { user: AuthenticatedUser },
  ) {
    this.logger.log(
      `Rename conversation request for [${conversationId}] by user [${req.user.userId}]`,
    );
    await this.chatService.renameConversation(
      conversationId,
      renameConversationDto.newTitle,
      req.user.userId,
    );
  }

  @Delete(':conversationId')
  @UseGuards(AuthGuard('jwt'))
  @ApiOperation({ summary: 'Soft delete a conversation' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async softDeleteConversation(
    @Param('conversationId') conversationId: string,
    @Req() req: ExpressRequest & { user: AuthenticatedUser },
  ) {
    this.logger.log(
      `Soft delete conversation request for [${conversationId}] by user [${req.user.userId}]`,
    );
    await this.chatService.softDeleteConversation(
      conversationId,
      req.user.userId,
    );
  }
}
