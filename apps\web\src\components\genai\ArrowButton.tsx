import { Box, IconButton } from '@mui/material';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';

export enum Direction {
  LEFT = 'left',
  RIGHT = 'right',
}

export default function ArrowButton({
  direction,
  onClick,
  disabled,
}: {
  direction: Direction;
  onClick?: () => void;
  disabled?: boolean;
}) {
  const isLeft = direction === Direction.LEFT;
  return (
    <>
      <Box
        height={'100%'}
        position={'absolute'}
        display={'flex'}
        alignItems={'center'}
        width={{ xs: 60, md: 100 }}
        left={isLeft ? -5 : 'auto'}
        right={isLeft ? 'auto' : -5}
        sx={{
          opacity: disabled ? 0 : 1,
          pointerEvents: 'none',
          background: (theme) =>
            `linear-gradient(to left, ${theme.palette.background.default}, ${theme.palette.background.default} 40%, transparent 70%)`,
          transform: isLeft ? 'rotate(0.5turn)' : undefined,
          transition: 'opacity 0.3s ease-in-out',
        }}
      />

      <IconButton
        onClick={onClick}
        sx={{
          bgcolor: 'background.default',
          pointerEvents: disabled ? 'none' : undefined,
          opacity: disabled ? 0 : 1,
          position: 'absolute',
          left: isLeft ? -5 : 'auto',
          right: isLeft ? 'auto' : -5,
          alignSelf: 'center',
          border: '2px solid',
          borderColor: 'divider',
          boxShadow: '0 1px 4px rgba(0,0,0,0.1)',
          '&:hover': { bgcolor: 'action.hover' },
          transform: isLeft ? 'rotate(0.5turn)' : undefined,
          transition: 'opacity 0.3s ease-in-out',
          width: { xs: 30, md: 44 },
          height: { xs: 30, md: 44 },
        }}
      >
        <ArrowForwardIosIcon
          sx={{
            width: { xs: 16, md: 24 },
            height: { xs: 16, md: 24 },
          }}
        />
      </IconButton>
    </>
  );
}
