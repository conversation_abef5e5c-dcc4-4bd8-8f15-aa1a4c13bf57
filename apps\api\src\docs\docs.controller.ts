import {
  <PERSON>,
  Get,
  Param,
  Lo<PERSON>,
  <PERSON><PERSON>,
  HttpStatus,
  UseGuards,
  Request,
  HttpException,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { SkipThrottle } from '@nestjs/throttler';
import { Response, Request as ExpressRequest } from 'express';
import { PrismaService } from '../prisma/prisma.service';
import { SwaggerGeneratorService } from './swagger-generator.service';
import { providerConfigs } from './provider-configs';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AuthenticatedUser } from '../auth/user.interface';

@ApiTags('API Documentation (REST API)')
@Controller('general/rest/docs')
@SkipThrottle()
export class DocsController {
  private readonly logger = new Logger(DocsController.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly swaggerGeneratorService: SwaggerGeneratorService,
  ) {}

  @Get('gpt-swagger.json')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Get OpenAPI specification for GPT models.',
  })
  @ApiResponse({
    status: 200,
    description: 'Swagger JSON specification for GPT models.',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized.',
  })
  async getGPTSwaggerSpec(
    @Request() req: ExpressRequest & { user: AuthenticatedUser },
  ): Promise<any> {
    this.logger.log('Request for GPT Swagger spec');

    try {
      const host = req.headers.host || 'genai.hkbu.edu.hk';

      return await this.swaggerGeneratorService.generateSwaggerSpec(
        providerConfigs.gpt,
        req.user.type,
        req.user.userId,
        host,
      );
    } catch (error) {
      this.logger.error('Error generating GPT swagger spec:', error);
      throw new HttpException(
        'Failed to generate swagger specification',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('qwen-swagger.json')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Get OpenAPI specification for Qwen models.',
  })
  @ApiResponse({
    status: 200,
    description: 'Swagger JSON specification for Qwen models.',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized.',
  })
  async getQwenSwaggerSpec(
    @Request() req: ExpressRequest & { user: AuthenticatedUser },
  ): Promise<any> {
    this.logger.log('Request for Qwen Swagger spec');

    try {
      const host = req.headers.host || 'genai.hkbu.edu.hk';

      return await this.swaggerGeneratorService.generateSwaggerSpec(
        providerConfigs.qwen,
        req.user.type,
        req.user.userId,
        host,
      );
    } catch (error) {
      this.logger.error('Error generating Qwen swagger spec:', error);
      throw new HttpException(
        'Failed to generate swagger specification',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('gemini-swagger.json')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Get OpenAPI specification for Gemini models.',
  })
  @ApiResponse({
    status: 200,
    description: 'Swagger JSON specification for Gemini models.',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized.',
  })
  async getGeminiSwaggerSpec(
    @Request() req: ExpressRequest & { user: AuthenticatedUser },
  ): Promise<any> {
    this.logger.log('Request for Gemini Swagger spec');

    try {
      const host = req.headers.host || 'genai.hkbu.edu.hk';

      return await this.swaggerGeneratorService.generateSwaggerSpec(
        providerConfigs.gemini,
        req.user.type,
        req.user.userId,
        host,
      );
    } catch (error) {
      this.logger.error('Error generating Gemini swagger spec:', error);
      throw new HttpException(
        'Failed to generate swagger specification',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('llama-swagger.json')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Get OpenAPI specification for Llama models.',
  })
  @ApiResponse({
    status: 200,
    description: 'Swagger JSON specification for Llama models.',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized.',
  })
  async getLlamaSwaggerSpec(
    @Request() req: ExpressRequest & { user: AuthenticatedUser },
  ): Promise<any> {
    this.logger.log('Request for Llama Swagger spec');

    try {
      const host = req.headers.host || 'genai.hkbu.edu.hk';

      return await this.swaggerGeneratorService.generateSwaggerSpec(
        providerConfigs.llama,
        req.user.type,
        req.user.userId,
        host,
      );
    } catch (error) {
      this.logger.error('Error generating Llama swagger spec:', error);
      throw new HttpException(
        'Failed to generate swagger specification',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('deepseek-swagger.json')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Get OpenAPI specification for DeepSeek models.',
  })
  @ApiResponse({
    status: 200,
    description: 'Swagger JSON specification for DeepSeek models.',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized.',
  })
  async getDeepSeekSwaggerSpec(
    @Request() req: ExpressRequest & { user: AuthenticatedUser },
  ): Promise<any> {
    this.logger.log('Request for DeepSeek Swagger spec');

    try {
      const host = req.headers.host || 'genai.hkbu.edu.hk';

      return await this.swaggerGeneratorService.generateSwaggerSpec(
        providerConfigs.deepseek,
        req.user.type,
        req.user.userId,
        host,
      );
    } catch (error) {
      this.logger.error('Error generating DeepSeek swagger spec:', error);
      throw new HttpException(
        'Failed to generate swagger specification',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':apiVersion/swagger.json')
  @ApiOperation({
    summary: 'Get OpenAPI (Swagger) specification for a given API version.',
  })
  @ApiParam({
    name: 'apiVersion',
    description:
      'The API version for which to retrieve the specification (e.g., 2024-02-01).',
    required: true,
  })
  @ApiResponse({ status: 200, description: 'Swagger JSON specification.' })
  @ApiResponse({
    status: 404,
    description: 'Specification not found for the given API version.',
  })
  async getSwaggerSpec(
    @Param('apiVersion') apiVersion: string,
    @Res() response: Response,
  ): Promise<void> {
    this.logger.log(`Request for Swagger spec, API version: ${apiVersion}`);

    // In a real implementation:
    // 1. Fetch available models and endpoints for this apiVersion using LlmConfigService.
    // 2. Use @nestjs/swagger DocumentBuilder and SwaggerModule.createDocument()
    //    to generate the spec dynamically by passing in module references
    //    that contain the controllers with @Api... decorators (e.g., ChatCompletionsModule, EmbeddingsModule).
    //    This requires careful setup of which modules/controllers to scan for a given apiVersion.
    //
    // For now, returning a placeholder basic OpenAPI spec.

    // Placeholder: Check if this apiVersion is one we "support" (e.g., based on LlmConfigService)
    // const supportedVersions = await this.llmConfigService.getUniqueApiVersions(); // Method to be created
    // if (!supportedVersions.includes(apiVersion)) {
    //    response.status(HttpStatus.NOT_FOUND).json({ message: `Swagger specification not found for API version: ${apiVersion}` });
    //    return;
    // }

    const placeholderSpec = {
      openapi: '3.0.0',
      info: {
        title: `HKBU GenAI Platform API - Version ${apiVersion}`,
        description: `API specification for version ${apiVersion}. This is a placeholder.`,
        version: apiVersion,
      },
      paths: {
        [`/general/${apiVersion}/deployments/{modelDeploymentName}/chat/completions`]:
          {
            post: {
              summary: 'Placeholder for Chat Completions',
              tags: ['LLM - Chat Completions'],
              parameters: [
                {
                  name: 'apiVersion',
                  in: 'path',
                  required: true,
                  schema: { type: 'string' },
                  example: apiVersion,
                },
                {
                  name: 'modelDeploymentName',
                  in: 'path',
                  required: true,
                  schema: { type: 'string' },
                },
              ],
              requestBody: {
                content: {
                  'application/json': {
                    schema: {
                      $ref: '#/components/schemas/CreateChatCompletionDto',
                    },
                  },
                },
              },
              responses: { '200': { description: 'Success' } },
              security: [{ 'api-key': [] }],
            },
          },
        [`/general/${apiVersion}/deployments/{modelDeploymentName}/embeddings`]:
          {
            post: {
              summary: 'Placeholder for Embeddings',
              tags: ['LLM - Embeddings'],
              parameters: [
                {
                  name: 'apiVersion',
                  in: 'path',
                  required: true,
                  schema: { type: 'string' },
                  example: apiVersion,
                },
                {
                  name: 'modelDeploymentName',
                  in: 'path',
                  required: true,
                  schema: { type: 'string' },
                },
              ],
              requestBody: {
                content: {
                  'application/json': {
                    schema: {
                      $ref: '#/components/schemas/CreateEmbeddingsDto',
                    },
                  },
                },
              },
              responses: { '200': { description: 'Success' } },
              security: [{ 'api-key': [] }],
            },
          },
      },
      components: {
        schemas: {
          // Placeholder DTOs - these would be generated by @nestjs/swagger from actual DTO classes
          CreateChatCompletionDto: {
            type: 'object',
            properties: {
              prompt: { type: 'string' },
              model: { type: 'string' },
            },
          },
          CreateEmbeddingsDto: {
            type: 'object',
            properties: {
              input: { type: 'string' },
              model: { type: 'string' },
            },
          },
        },
        securitySchemes: {
          'api-key': { type: 'apiKey', name: 'api-key', in: 'header' },
        },
      },
    };

    response.status(HttpStatus.OK).json(placeholderSpec);
  }
}
