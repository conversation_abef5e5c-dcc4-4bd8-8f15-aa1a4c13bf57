'use client';
import { useEffect } from 'react';
import { useChatLayout } from '@/contexts/ChatLayoutContext';
// NEW IMPORTS
import Link from 'next/link';
import HkbuLogo from '@/assets/icons/HkbuLogo';
import { Box, Button, Typography } from '@mui/material';
export default function NotFound() {
  const { markNotFound, setIsNotFound } = useChatLayout();
  useEffect(() => {
    markNotFound?.();
    return () => setIsNotFound(false);
  }, [markNotFound]);
  return (
    // REPLACE INLINE STYLES WITH MUI BOX FOR BETTER UI
    <Box
      sx={{
        width: '100vw',
        minHeight: '100svh',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        bgcolor: 'background.default',
        p: 2,
        textAlign: 'center',
      }}
    >
      {/* Brand / decorative icon */}
      <HkbuLogo
        height={64}
        style={{ marginBottom: 16, fill: 'currentColor', color: '#00529c' }}
      />

      {/* Large error code */}
      <Typography
        variant="h2"
        component="h1"
        sx={{ fontWeight: 700, mb: 1 }}
      >
        404
      </Typography>

      {/* Primary message */}
      <Typography variant="h5" sx={{ mb: 1 }}>
        Page Not Found
      </Typography>

      {/* Secondary description */}
      <Typography
        variant="body1"
        color="text.secondary"
        sx={{ mb: 4, maxWidth: 420 }}
      >
        The page you are looking for does not exist or has been moved.
      </Typography>

      {/* Action button to navigate home */}
      <Link href="/" passHref legacyBehavior>
        <Button variant="contained" color="primary">
          Go Home
        </Button>
      </Link>
    </Box>
  );
}
