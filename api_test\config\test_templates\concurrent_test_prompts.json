{"description": "Prompt templates for concurrent conversation testing", "version": "1.0.0", "prompt_sets": {"quick_facts": {"name": "Quick Factual Questions", "description": "Short factual questions for fast response testing", "concurrent_count": 8, "prompts": ["What is the capital of France?", "How many continents are there?", "What is 15 + 27?", "What year did World War II end?", "What is the largest ocean?", "How many sides does a hexagon have?", "What is the speed of light?", "What is the chemical symbol for gold?"], "expected_response_time": 5, "validation": {"check_uniqueness": true, "min_response_length": 5, "max_response_time": 10}}, "creative_tasks": {"name": "Creative Writing Tasks", "description": "Creative prompts that should generate unique responses", "concurrent_count": 5, "prompts": ["Write a haiku about technology.", "Create a short story opening about a mysterious door.", "Describe a futuristic city in 50 words.", "Write a limerick about a robot.", "Compose a poem about the ocean."], "expected_response_time": 15, "validation": {"check_uniqueness": true, "min_response_length": 20, "max_response_time": 30, "creativity_check": true}}, "technical_explanations": {"name": "Technical Explanations", "description": "Technical concepts requiring detailed explanations", "concurrent_count": 4, "prompts": ["Explain how machine learning works in simple terms.", "What is quantum computing and how does it differ from classical computing?", "Describe the process of photosynthesis.", "How do solar panels convert sunlight to electricity?"], "expected_response_time": 20, "validation": {"check_technical_accuracy": true, "min_response_length": 100, "max_response_time": 45}}, "math_problems": {"name": "Mathematical Problems", "description": "Various math problems to test computational consistency", "concurrent_count": 6, "prompts": ["What is 123 * 456?", "Calculate the area of a circle with radius 7.", "Solve: 2x + 5 = 17", "What is the square root of 144?", "Convert 75% to a decimal.", "What is 8! (8 factorial)?"], "expected_response_time": 8, "validation": {"check_mathematical_accuracy": true, "exact_answer_required": true, "max_response_time": 15}}, "language_tasks": {"name": "Language Processing Tasks", "description": "Language understanding and generation tasks", "concurrent_count": 5, "prompts": ["Translate 'Hello, how are you?' to Spanish.", "What is the plural of 'mouse' (the animal)?", "Define the word 'serendipity'.", "Give me three synonyms for 'happy'.", "What is the past tense of 'swim'?"], "expected_response_time": 10, "validation": {"check_language_accuracy": true, "min_response_length": 5, "max_response_time": 20}}, "reasoning_puzzles": {"name": "Logic and Reasoning", "description": "Puzzles requiring logical thinking", "concurrent_count": 3, "prompts": ["If all roses are flowers and some flowers fade quickly, can we conclude that some roses fade quickly?", "A farmer has 17 sheep, and all but 9 die. How many are left?", "What comes next in this sequence: 2, 4, 8, 16, ?"], "expected_response_time": 15, "validation": {"check_logical_reasoning": true, "require_explanation": true, "max_response_time": 30}}, "mixed_difficulty": {"name": "Mixed Difficulty Levels", "description": "Combination of easy and complex tasks", "concurrent_count": 7, "prompts": ["What color is the sky?", "Explain <PERSON>'s theory of relativity briefly.", "Count from 1 to 5.", "Describe the economic impact of artificial intelligence.", "What is 2 + 2?", "Analyze the themes in <PERSON>'s Hamlet.", "Name three types of clouds."], "expected_response_time": 15, "validation": {"variable_complexity": true, "adaptive_timeout": true}}}, "isolation_tests": {"description": "Tests to ensure conversation isolation in concurrent scenarios", "conversation_bleeding_test": {"name": "Conversation Bleeding Detection", "description": "Test if responses contain information from other concurrent conversations", "prompts": ["My name is <PERSON> and I love cats. What pet would you recommend for me?", "My name is <PERSON> and I'm allergic to animals. What hobby should I try?", "My name is <PERSON> and I live in Alaska. What's the weather like where I am?", "My name is <PERSON> and I'm a vegetarian chef. What recipe should I try?", "My name is <PERSON> and I collect stamps. What's an interesting fact about my hobby?"], "validation": {"check_name_isolation": true, "check_context_isolation": true, "no_cross_conversation_data": true}}}, "load_scenarios": {"burst_load": {"name": "<PERSON><PERSON><PERSON> Load Test", "concurrent_count": 10, "repeat_cycles": 3, "cycle_interval": 2, "prompts": ["Quick question: What is 5 * 5?", "Fast fact: Capital of Japan?", "Simple math: 100 / 4?", "Basic science: What gas do plants produce?", "Quick history: When did WWII end?"]}, "sustained_load": {"name": "Sustained Load Test", "concurrent_count": 5, "repeat_cycles": 10, "cycle_interval": 5, "prompts": ["Tell me about renewable energy sources.", "Explain how vaccines work.", "Describe the water cycle.", "What is machine learning?", "How do computers store data?"]}}, "performance_benchmarks": {"response_time_targets": {"simple_questions": 5, "moderate_complexity": 15, "complex_analysis": 30}, "throughput_targets": {"requests_per_minute": 60, "concurrent_capacity": 10}, "quality_metrics": {"accuracy_threshold": 0.95, "relevance_threshold": 0.9, "completeness_threshold": 0.85}}}