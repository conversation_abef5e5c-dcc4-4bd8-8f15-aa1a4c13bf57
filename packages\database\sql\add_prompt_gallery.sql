BEGIN TRY

BEGIN TRAN;

-- CreateTable
CREATE TABLE [dbo].[task] (
    [id] INT NOT NULL IDENTITY(1,1),
    [name] NVARCHAR(100) NOT NULL,
    [create_dt] DATETIME NOT NULL CONSTRAINT [task_create_dt_df] DEFAULT CURRENT_TIMESTAMP,
    [update_dt] DATETIME NOT NULL,
    CONSTRAINT [task_pkey] PRIMARY KEY CLUSTERED ([id])
);

-- CreateTable
CREATE TABLE [dbo].[prompt_gallery] (
    [id] INT NOT NULL IDENTITY(1,1),
    [system_instruction] NVARCHAR(max),
    [prompt_content] NVARCHAR(max) NOT NULL,
    [user_id] VARCHAR(30),
    [is_default] BIT NOT NULL CONSTRAINT [prompt_gallery_is_default_df] DEFAULT 0,
    [create_dt] DATETIME NOT NULL CONSTRAINT [prompt_gallery_create_dt_df] DEFAULT CURRENT_TIMESTAMP,
    [update_dt] DATETIME NOT NULL,
    CONSTRAINT [prompt_gallery_pkey] PRIMARY KEY CLUSTERED ([id])
);

-- CreateTable
CREATE TABLE [dbo].[prompt_gallery_task] (
    [prompt_gallery_id] INT NOT NULL,
    [task_id] INT NOT NULL,
    CONSTRAINT [prompt_gallery_task_pkey] PRIMARY KEY CLUSTERED ([prompt_gallery_id],[task_id])
);

-- AddForeignKey
ALTER TABLE [dbo].[prompt_gallery] ADD CONSTRAINT [prompt_gallery_user_id_fkey] FOREIGN KEY ([user_id]) REFERENCES [dbo].[acl_user]([username]) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[prompt_gallery_task] ADD CONSTRAINT [prompt_gallery_task_prompt_gallery_id_fkey] FOREIGN KEY ([prompt_gallery_id]) REFERENCES [dbo].[prompt_gallery]([id]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[prompt_gallery_task] ADD CONSTRAINT [prompt_gallery_task_task_id_fkey] FOREIGN KEY ([task_id]) REFERENCES [dbo].[task]([id]) ON DELETE NO ACTION ON UPDATE CASCADE;

-- Add permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON [dbo].[task] TO [chatgptdb_user];
GRANT SELECT, INSERT, UPDATE, DELETE ON [dbo].[prompt_gallery] TO [chatgptdb_user];
GRANT SELECT, INSERT, UPDATE, DELETE ON [dbo].[prompt_gallery_task] TO [chatgptdb_user];

COMMIT TRAN;

END TRY
BEGIN CATCH

IF @@TRANCOUNT > 0
BEGIN
    ROLLBACK TRAN;
END;
THROW

END CATCH