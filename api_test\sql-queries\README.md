# SQL Queries

This directory contains SQL queries for manual database verification and analysis.

## Files

### `check-db-queries.sql`
Collection of SQL queries for verifying token usage data in the database.
- Check token usage for specific users and models
- Verify API vs Web UI usage tracking
- Identify duplicate entries
- Analyze token usage patterns

## Usage

Run these queries directly in your SQL Server Management Studio or database client to manually verify token usage tracking.

The queries are designed to check the `acl_user_token_spent` table for:
- Daily token usage records
- API vs Web UI differentiation (`is_api` field)
- Model name consistency
- Token count accuracy