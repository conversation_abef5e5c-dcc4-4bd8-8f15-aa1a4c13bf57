import { Injectable } from '@nestjs/common';

@Injectable()
export class TextService {
  /**
   * Safely truncates text at Unicode character boundaries
   * Handles multi-byte characters (like Chinese, Japanese, emoji) correctly
   * @param text - The text to truncate
   * @param maxLength - Maximum number of Unicode characters (not code units)
   * @param suffix - Suffix to add when text is truncated (default: '...')
   * @returns Truncated text with suffix if needed
   */
  truncateText(
    text: string,
    maxLength: number,
    suffix: string = '...',
  ): string {
    if (!text || typeof text !== 'string') {
      return text || '';
    }

    // Use Array.from to properly handle Unicode characters
    // This converts the string to an array of actual Unicode characters
    const characters = Array.from(text);

    if (characters.length <= maxLength) {
      return text;
    }

    // Truncate at character boundary and add suffix
    return characters.slice(0, maxLength).join('') + suffix;
  }

  /**
   * Gets the actual Unicode character count (not code unit count)
   * @param text - The text to count
   * @returns Number of Unicode characters
   */
  getCharacterCount(text: string): number {
    if (!text || typeof text !== 'string') {
      return 0;
    }
    return Array.from(text).length;
  }
}
