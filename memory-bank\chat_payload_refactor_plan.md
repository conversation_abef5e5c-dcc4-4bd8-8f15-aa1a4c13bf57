# Chat Payload Refactor Plan

This plan outlines the steps to refactor the chat completion API payload to send only the latest prompt and a session ID, instead of the full message history. The backend will retrieve the history based on the session ID.

## Updated Plan:

1.  **Backend - Modify API Contract & Service Logic:**
    *   **`apps/api/src/general/chat-completion/dto/create-chat-completion.dto.ts`**:
        *   Change `messages: ...[]` to `prompt: string`.
        *   Change `conversation_uuid?: string` to `chat_session_id?: string`.
    *   **`apps/api/src/general/chat-completion/chat-completion.service.ts`**:
        *   Update `createCompletion` signature to accept the modified DTO (with `chat_session_id`).
        *   Destructure `chat_session_id` instead of `conversation_uuid`.
        *   Use `chat_session_id` when determining `isNewConversation` and setting `currentConversationUUID`.
        *   Use `currentConversationUUID` (derived from `chat_session_id`) for internal logic like `getDecryptedChatHistory`, `saveMessageWithSP`, `generateAndSaveTitle`, etc.
        *   Modify `llmMessages` construction: `llmMessages: BaseMessage[] = [...chatHistory, new HumanMessage(prompt)];`.
        *   Update `saveMessageWithSP` call for the user message to use `prompt`.
        *   **Important:** Ensure the `conversation_uuid` field in the *response* stream chunks remains `conversation_uuid`, as the frontend (`apiSlice.ts` line 280) expects this specific field name to update the Redux state correctly.
    *   **`apps/api/src/general/chat-completion/chat-completion.controller.ts`**:
        *   Ensure the controller method uses the updated `CreateChatCompletionDto` (with `chat_session_id`).

2.  **Frontend - Update API Slice & Component:**
    *   **`apps/web/src/lib/store/apiSlice.ts`**:
        *   Modify `ChatCompletionRequest` interface: replace `messages` with `prompt`, replace `conversation_uuid?` with `chat_session_id?: string`.
        *   Update `chatCompletion` mutation's `queryFn`:
            *   Adjust `args` type.
            *   Modify `baseRequestBody` construction to use `prompt: args.prompt`.
            *   Modify the check for `isValidUuid` (line 220) to use `args.chat_session_id`.
            *   Modify the `requestBody` construction (line 221) to send `chat_session_id: args.chat_session_id` if valid.
            *   Keep the logic that extracts `conversation_uuid` from the *response* chunks (line 280) as is.
    *   **`apps/web/src/app/chat/[chatId]/page.tsx`**:
        *   Update `handleSubmit`:
            *   Remove `apiMessages` construction.
            *   Modify `requestBody` construction to use `prompt: messageContent` and `chat_session_id: (Array.isArray(chatId) ? chatId[0] : chatId) || undefined`.
            *   Update the dependency array (line 176) to reflect the change from `conversationId` to `chatId`.

## Updated Diagram:

```mermaid
sequenceDiagram
    participant FE as Frontend (page.tsx)
    participant FS as Frontend (apiSlice.ts)
    participant BE as Backend (Controller/Service)
    participant DB as Database
    participant LLM as Language Model

    FE->>FE: User types prompt, clicks Send
    FE->>FS: Calls triggerChatCompletion({ prompt: "new message", chat_session_id: "uuid", ... })
    FS->>BE: POST /general/chat/completions (Body: { prompt: "new message", chat_session_id: "uuid", ... })
    BE->>BE: Extracts chat_session_id as currentConversationUUID
    BE->>DB: EXEC sp_cvst_GetDecryptedMessagesByConversationUUID(currentConversationUUID)
    DB-->>BE: Returns chatHistory
    BE->>BE: Combines chatHistory + new prompt ("new message")
    BE->>DB: EXEC sp_cvst_InsertMessageWithPrompts (user prompt: "new message", conversation_uuid: currentConversationUUID)
    DB-->>BE: Returns user_message_id
    BE->>LLM: Sends combined history + prompt
    LLM-->>BE: Streams response chunks
    BE->>DB: EXEC sp_cvst_InsertMessageWithPrompts (AI response, conversation_uuid: currentConversationUUID)
    DB-->>BE: Returns ai_message_id
    BE-->>FS: Streams response chunks (incl. conversation_uuid: currentConversationUUID) # Response keeps conversation_uuid
    FS->>FE: Updates Redux store (updates UI)