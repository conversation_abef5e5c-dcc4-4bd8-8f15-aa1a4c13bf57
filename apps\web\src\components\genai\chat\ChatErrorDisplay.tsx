'use client';

import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import Alert from '@mui/material/Alert';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';
import Box from '@mui/material/Box';
import { RootState } from '@/lib/store/store';
import { setChatErrorMessage } from '@/lib/store/chatSlice';

const ChatErrorDisplay: React.FC = () => {
  const dispatch = useDispatch();
  const errorMessage = useSelector(
    (state: RootState) => state.chat.chatErrorMessage,
  );

  // Auto-dismiss error after 5 seconds
  useEffect(() => {
    if (errorMessage) {
      const timer = setTimeout(() => {
        dispatch(setChatErrorMessage(undefined));
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [errorMessage, dispatch]);

  if (!errorMessage) {
    return null;
  }

  return (
    <Box sx={{ p: 2 }}>
      <Alert
        severity="error"
        action={
          <IconButton
            aria-label="close"
            color="inherit"
            size="small"
            onClick={() => {
              dispatch(setChatErrorMessage(undefined));
            }}
          >
            <CloseIcon fontSize="inherit" />
          </IconButton>
        }
      >
        {errorMessage}
      </Alert>
    </Box>
  );
};

export default ChatErrorDisplay;