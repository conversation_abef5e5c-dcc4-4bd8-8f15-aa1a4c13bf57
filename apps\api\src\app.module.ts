import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt'; // Import JwtModule
import { PassportModule } from '@nestjs/passport'; // Import PassportModule
// AppController and AppService removed
import { JwtStrategy } from './auth/jwt.strategy'; // Import JwtStrategy directly
import { PrismaModule } from './prisma/prisma.module'; // Import PrismaModule
import { GeneralModule } from './general/general.module'; // Import GeneralModule
import { AuthModule } from './auth/auth.module'; // Import AuthModule
import { LlmModule } from './llm/llm.module'; // Import LlmModule
import { DocsModule } from './docs/docs.module'; // Import DocsModule
import { ThrottlerGuard, ThrottlerModule } from '@nestjs/throttler'; // Import Throttler
import { ThrottlerStorageRedisService } from 'nestjs-throttler-storage-redis';
import { APP_GUARD } from '@nestjs/core'; // Import APP_GUARD for global guards
import { MailerModule } from '@nestjs-modules/mailer'; // Import MailerModule
import { ChatModule } from './general/chat/chat.module'; // Re-import ChatModule from new location
import { SpeechModule } from './general/speech/speech.module'; // Re-import SpeechModule from new location
import { RedisModule } from './redis/redis.module'; // Import RedisModule
import { CommonModule } from './common/common.module'; // Import CommonModule
import { InjectRedis } from '@nestjs-modules/ioredis'; // Import Redis injection decorator
import { Redis } from 'ioredis'; // Import Redis type
import { keyVaultLoader } from './config/key-vault.config';
import { CronModule } from './cron/cron.module';
import { ScheduleModule } from '@nestjs/schedule';
// import { ChatModule } from './chat/chat.module'; // REMOVED as module was deleted
// import { RecentModelsModule } from './recent-models/recent-models.module'; // REMOVED
// import { ChatModule } from './chat/chat.module'; // Moved to GeneralModule
// import { SpeechModule } from './speech/speech.module'; // Moved to GeneralModule

@Module({
  imports: [
    // Add missing imports key
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
      load: [keyVaultLoader],
    }),
    ScheduleModule.forRoot(),
    PassportModule.register({ defaultStrategy: 'jwt' }), // Explicitly set default strategy
    JwtModule.registerAsync({
      // Configure JwtModule dynamically
      imports: [ConfigModule], // Import ConfigModule to use ConfigService
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('BUAM_NEXTAUTH_JWT_SECRET'), // Read the aligned secret name
        signOptions: { expiresIn: '1d' }, // Example: Token expires in 1 day
      }),
      inject: [ConfigService], // Inject ConfigService
    }),
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule, RedisModule],
      useFactory: async (configService: ConfigService, redis: Redis) => ({
        throttlers: [
          {
            ttl: 60000, // 60 seconds
            limit: 60, // 60 requests per minute (default)
          },
        ],
        storage: new ThrottlerStorageRedisService(redis),
      }),
      inject: [ConfigService, 'default_IORedisModuleConnectionToken'],
    }),
    MailerModule.forRootAsync({
      // Configure MailerModule dynamically
      imports: [ConfigModule], // Ensure ConfigModule is available
      useFactory: async (configService: ConfigService) => ({
        transport: {
          host: configService.get<string>('MAILER_HOST'),
          port: configService.get<number>('MAILER_PORT'),
          secure: false,
        },
        defaults: {
          from: configService.get<string>('MAILER_FROM'),
        },
        // Add template configuration if needed (e.g., Handlebars)
        // template: {
        //   dir: join(__dirname, 'templates'),
        //   adapter: new HandlebarsAdapter(),
        //   options: {
        //     strict: true,
        //   },
        // },
      }),
      inject: [ConfigService], // Inject ConfigService
    }),
    RedisModule, // Add RedisModule
    CommonModule, // Add CommonModule
    PrismaModule, // Add PrismaModule
    AuthModule, // Add AuthModule
    LlmModule, // Add LlmModule
    DocsModule, // Add DocsModule
    GeneralModule, // Still needed for DI and its own routes/providers
    ChatModule, // Still needed for DI
    SpeechModule, // Still needed for DI
    CronModule,
    // RouterModule.register(routes), // Removed RouterModule configuration
    // ChatModule, // REMOVED as module was deleted
    // RecentModelsModule, // REMOVED
    // AuthModule is removed from imports
    // Ensure trailing comma if needed by formatter/linter
  ],
  controllers: [], // No AppController
  providers: [
    JwtStrategy, // Keep JwtStrategy
    {
      // Provide ThrottlerGuard globally
      provide: APP_GUARD,
      useClass: ThrottlerGuard,
    },
  ],
})
export class AppModule {}
