import { useTheme } from '@mui/material';
import React from 'react';

const ExpandIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => {
  const theme = useTheme();
  return (
    <svg
      width="20"
      viewBox="0 0 28 23"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M16.3543 0L14.0255 2.29967L21.6954 9.87364H6.58949V13.1264H21.6954L14.0255 20.7003L16.3543 23L28 11.5L16.3543 0ZM3.29392 22.8845V0.115472H0V22.8845H3.29392Z"
        fill={theme.palette.text.secondary}
      />
    </svg>
  );
};

export default ExpandIcon;
