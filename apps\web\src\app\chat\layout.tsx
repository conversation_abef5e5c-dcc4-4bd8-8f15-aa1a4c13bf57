'use client';

import React, { useEffect } from 'react';
import { useSearchParams, usePathname } from 'next/navigation';
import { useGetModelsQuery } from '@/lib/store/apiSlice';
import { useAppDispatch, useAppSelector } from '@/lib/store/hooks';
import { setModel, selectAvailableModels } from '@/lib/store/modelSlice';
import { GptModel } from '@/lib/types/common';
import 'katex/dist/katex.min.css';
import { selectIsAuthenticated } from '@/lib/store/authSlice';
import { useSelector } from 'react-redux';

function ChatLayoutInitializer() {
  const {
    data: modelsData,
    isLoading: isLoadingModels,
    isError: isErrorModels,
  } = useGetModelsQuery();
  const availableModels = useAppSelector(selectAvailableModels);
  const dispatch = useAppDispatch();
  const searchParams = useSearchParams();
  const pathname = usePathname();

  React.useEffect(() => {
    if (isLoadingModels) console.log('Fetching model list...');
    if (isErrorModels) console.error('Error fetching model list.');
  }, [isLoadingModels, isErrorModels]);

  useEffect(() => {
    if (
      pathname === '/chat/new' &&
      availableModels &&
      availableModels.length > 0
    ) {
      const urlModelName = searchParams.get('model');
      if (urlModelName) {
        const foundModel = availableModels.find(
          (m: GptModel) =>
            m.model_name === urlModelName && m.availability_status === 'A',
        );

        if (foundModel) {
          dispatch(setModel(foundModel));
        } else {
          console.warn(
            `[Layout] Model "${urlModelName}" from URL not found or not available in the list.`,
          );
        }
      }
    }
  }, [searchParams, pathname, availableModels, dispatch]);

  return null;
}

export default function ChatLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const isAuthenticated = useSelector(selectIsAuthenticated);

  return (
    <>
      <React.Suspense fallback={null}>
        {isAuthenticated && <ChatLayoutInitializer />}
      </React.Suspense>
      {children}
    </>
  );
}
