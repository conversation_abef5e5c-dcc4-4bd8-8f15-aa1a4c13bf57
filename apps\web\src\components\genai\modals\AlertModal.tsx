import { Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline';

// Interface for button props remains the same
type ButtonProps = {
  text: string;
  style?: 'primary' | 'secondary';
  onClick?: () => void;
  disableAutoCloseModal?: boolean;
  otherProps?: React.DetailedHTMLProps<
    React.ButtonHTMLAttributes<HTMLButtonElement>,
    HTMLButtonElement
  >;
};

// Updated props interface for AlertModal
export type AlertModalProps = {
  show: boolean;
  message?: string;
  buttons?: ButtonProps[];
  onClose?: () => void; // Make onClose optional as Dialog requires it, but we might have buttons handle closing
};

// Refactored AlertModal component to use props
const AlertModal = ({ show, message, buttons, onClose }: AlertModalProps) => {
  // Default close handler if none is provided
  const handleClose = onClose ?? (() => {});

  return (
    // Use the 'show' prop to control the Transition.Root visibility
    <Transition.Root show={show} as={Fragment}>
      <Dialog
        as="div"
        className="relative z-50"
        // Use the handleClose (derived from onClose prop) for the Dialog
        onClose={handleClose}
      >
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white dark:bg-gray-800 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-xl">
                <div className="bg-white dark:bg-gray-800 px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
                  <div className="sm:flex sm:items-start">
                    <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-amber-100 sm:mx-0 sm:h-10 sm:w-10">
                      <ExclamationTriangleIcon
                        className="h-6 w-6 text-amber-600"
                        aria-hidden="true"
                      />
                    </div>
                    <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
                      <Dialog.Title
                        as="h3"
                        className="text-base font-semibold leading-6 text-gray-900 dark:text-white"
                      >
                        Warning
                      </Dialog.Title>
                      <div className="mt-2">
                        {/* Use the message prop */}
                        <p className="text-sm text-gray-500 dark:text-gray-300">
                          {message}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="bg-gray-50 dark:bg-gray-700 px-4 py-3 flex flex-col sm:flex-row-reverse sm:px-6 gap-2">
                  {/* Use the buttons prop */}
                  {buttons?.map((button, index) => (
                    <button
                      key={index}
                      type="button"
                      className={`inline-flex w-full justify-center rounded-md px-3 py-2 text-sm font-semibold shadow-sm sm:w-auto border ${
                        button.style === 'primary'
                          ? 'bg-amber-500 text-white hover:bg-amber-600 border-amber-500'
                          : 'bg-white dark:bg-gray-600 text-gray-900 dark:text-gray-200 ring-1 ring-inset ring-gray-300 dark:ring-gray-500 hover:bg-gray-50 dark:hover:bg-gray-500' // Adjusted secondary style
                      }`}
                      onClick={() => {
                        // Call button's specific onClick if provided
                        if (typeof button.onClick === 'function') {
                          button.onClick();
                        }
                        // Call the main onClose handler unless disabled
                        if (!button.disableAutoCloseModal) {
                          handleClose();
                        }
                      }}
                      {...button.otherProps}
                    >
                      {button.text}
                    </button>
                  ))}
                  {/* Add a default close button if no buttons are provided */}
                  {!buttons ||
                    (buttons.length === 0 && (
                      <button
                        type="button"
                        className="inline-flex w-full justify-center rounded-md bg-white dark:bg-gray-600 px-3 py-2 text-sm font-semibold text-gray-900 dark:text-gray-200 shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-500 hover:bg-gray-50 dark:hover:bg-gray-500 sm:w-auto"
                        onClick={handleClose}
                      >
                        Close
                      </button>
                    ))}
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
};

export default AlertModal;
