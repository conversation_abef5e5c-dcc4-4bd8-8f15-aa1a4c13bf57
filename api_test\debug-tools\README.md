# Debug Tools

This directory contains debugging and diagnostic tools for API testing.

## Files

### `debug-model-mapping.py`
Tool for debugging model name mapping issues.
- Tests specific model deployment names
- Shows where tokens are actually being recorded
- Helps identify model name normalization problems

### `check-db-updates.py`
Database update verification tool.
- Makes API calls and monitors database changes
- Provides detailed logging of token usage updates
- Useful for debugging token tracking issues

## Usage

These tools are primarily for developers debugging token usage or model mapping issues.

```bash
# Debug model mapping
python debug-model-mapping.py

# Check database updates
python check-db-updates.py
```