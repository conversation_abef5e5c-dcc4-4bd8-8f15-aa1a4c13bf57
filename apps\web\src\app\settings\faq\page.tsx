'use client';

import React from 'react';
import Typography from '@mui/material/Typography';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import Container from '@mui/material/Container';
import Box from '@mui/material/Box';
import { event } from '@/components/genai/GoogleAnalytics';
import { Toolbar, useMediaQuery, useTheme } from '@mui/material';

const FaqPage: React.FC = () => {
  const theme = useTheme(); // Access theme
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const availableSections = [
    {
      title: 'Azure OpenAI',
      detail: (
        <table className="table-auto w-full border-collapse border border-gray-400 text-xs">
          <thead>
            <tr>
              <th className="border border-gray-300 p-2">Model</th>
              <th className="border border-gray-300 p-2">Token Limit</th>
              <th className="border border-gray-300 p-2">Knowledge Cut-off</th>
              <th className="border border-gray-300 p-2">
                <a
                  href="#supportvision"
                  className="text-blue-600 hover:underline"
                  onClick={() => {
                    event({
                      action: 'click',
                      category: 'faq',
                      label: 'support_vision_link',
                      value: 1,
                    });
                  }}
                >
                  Support Vision
                </a>
              </th>
              <th className="border border-gray-300 p-2">Service Provider</th>
              <th className="border border-gray-300 p-2">API Available</th>
              <th className="border border-gray-300 p-2">Characteristic</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="border border-gray-300 p-2">
                <a
                  href="https://azure.microsoft.com/en-us/blog/introducing-gpt-4o-openais-new-flagship-multimodal-model-now-in-preview-on-azure/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline"
                  onClick={() => {
                    event({
                      action: 'click',
                      category: 'faq',
                      label: 'gpt4o_link',
                      value: 1,
                    });
                  }}
                >
                  GPT-4o
                </a>
              </td>
              <td className="border border-gray-300 p-2">128K</td>
              <td className="border border-gray-300 p-2">Oct 2023</td>
              <td className="border border-gray-300 p-2">No</td>
              <td className="border border-gray-300 p-2" rowSpan={5}>
                Microsoft Azure OpenAI
              </td>
              <td className="border border-gray-300 p-2">Yes</td>
              <td className="border border-gray-300 p-2">
                Fast and capable of vision-based tasks
              </td>
            </tr>
            <tr>
              <td className="border border-gray-300 p-2">
                <a
                  href="https://azure.microsoft.com/en-us/blog/openais-fastest-model-gpt-4o-mini-is-now-available-on-azure-ai/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline"
                  onClick={() => {
                    event({
                      action: 'click',
                      category: 'faq',
                      label: 'gpt4o_mini_link',
                      value: 1,
                    });
                  }}
                >
                  GPT-4o Mini
                </a>
              </td>
              <td className="border border-gray-300 p-2">128K</td>
              <td className="border border-gray-300 p-2">Oct 2023</td>
              <td className="border border-gray-300 p-2">Yes</td>
              <td className="border border-gray-300 p-2">Yes</td>
              <td className="border border-gray-300 p-2">
                Efficient for small tasks
              </td>
            </tr>
            <tr>
              <td className="border border-gray-300 p-2">
                <a
                  href="https://azure.microsoft.com/en-us/blog/introducing-o1-openais-new-reasoning-model-series-for-developers-and-enterprises-on-azure/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline"
                  onClick={() => {
                    event({
                      action: 'click',
                      category: 'faq',
                      label: 'o1_link',
                      value: 1,
                    });
                  }}
                >
                  o1 (API Only)
                </a>
              </td>
              <td className="border border-gray-300 p-2">128K</td>
              <td className="border border-gray-300 p-2">Oct 2023</td>
              <td className="border border-gray-300 p-2">Yes</td>
              <td className="border border-gray-300 p-2">Yes</td>
              <td className="border border-gray-300 p-2">
                Model offer enhanced reasoning abilities.
              </td>
            </tr>
            <tr>
              <td className="border border-gray-300 p-2">
                <a
                  href="https://azure.microsoft.com/en-us/blog/introducing-o1-openais-new-reasoning-model-series-for-developers-and-enterprises-on-azure/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline"
                  onClick={() => {
                    event({
                      action: 'click',
                      category: 'faq',
                      label: 'o1_mini_link',
                      value: 1,
                    });
                  }}
                >
                  o1-Mini (API Only)
                </a>
              </td>
              <td className="border border-gray-300 p-2">128K</td>
              <td className="border border-gray-300 p-2">Oct 2023</td>
              <td className="border border-gray-300 p-2">Yes</td>
              <td className="border border-gray-300 p-2">Yes</td>
              <td className="border border-gray-300 p-2">
                Reasoning model with a smaller size, faster response time.
              </td>
            </tr>
            <tr>
              <td className="border border-gray-300 p-2">
                <a
                  href="https://azure.microsoft.com/en-us/blog/announcing-the-availability-of-the-o3-mini-reasoning-model-in-microsoft-azure-openai-service/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline"
                  onClick={() => {
                    event({
                      action: 'click',
                      category: 'faq',
                      label: 'o3_mini_link',
                      value: 1,
                    });
                  }}
                >
                  o3-Mini
                </a>
              </td>
              <td className="border border-gray-300 p-2">Jul 2024</td>
              <td className="border border-gray-300 p-2">Yes</td>
              <td className="border border-gray-300 p-2">Yes</td>
              <td className="border border-gray-300 p-2">
                Enhanced reasoning model with reasoning effort control
              </td>
            </tr>
          </tbody>
        </table>
      ),
    },
    {
      title: 'Anthropic Claude 3',
      detail: (
        <table className="table-auto w-full border-collapse border border-gray-400 text-xs">
          <thead>
            <tr>
              <th className="border border-gray-300 p-2">Model</th>
              <th className="border border-gray-300 p-2">Knowledge Cut-off</th>
              <th className="border border-gray-300 p-2">Support Vision</th>
              <th className="border border-gray-300 p-2">Service Provider</th>
              <th className="border border-gray-300 p-2">API Available</th>
              <th className="border border-gray-300 p-2">Characteristic</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="border border-gray-300 p-2">
                <a
                  href="https://www.anthropic.com/news/claude-3-5-sonnet"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline"
                  onClick={() => {
                    event({
                      action: 'click',
                      category: 'faq',
                      label: 'claude_3_5_sonnet_link',
                      value: 1,
                    });
                  }}
                >
                  Claude 3.5 Sonnet
                </a>
              </td>
              <td className="border border-gray-300 p-2">Aug 2023</td>
              <td className="border border-gray-300 p-2">Yes</td>
              <td className="border border-gray-300 p-2" rowSpan={2}>
                Google Cloud Platform
              </td>
              <td className="border border-gray-300 p-2">Yes</td>
              <td className="border border-gray-300 p-2">Fast and versatile</td>
            </tr>
            <tr>
              <td className="border border-gray-300 p-2">
                <a
                  href="https://www.anthropic.com/news/claude-3-family"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline"
                  onClick={() => {
                    event({
                      action: 'click',
                      category: 'faq',
                      label: 'claude_3_haiku_link',
                      value: 1,
                    });
                  }}
                >
                  Claude 3 Haiku
                </a>
              </td>
              <td className="border border-gray-300 p-2">Aug 2023</td>
              <td className="border border-gray-300 p-2">Yes</td>
              <td className="border border-gray-300 p-2">Yes</td>
              <td className="border border-gray-300 p-2">
                Fast and capable of handling small tasks
              </td>
            </tr>
          </tbody>
        </table>
      ),
    },
    {
      title: 'Google Gemini',
      detail: (
        <table className="table-auto w-full border-collapse border border-gray-400 text-xs">
          <thead>
            <tr>
              <th className="border border-gray-300 p-2">Model</th>
              <th className="border border-gray-300 p-2">Knowledge Cut-off</th>
              <th className="border border-gray-300 p-2">Support Vision</th>
              <th className="border border-gray-300 p-2">Service Provider</th>
              <th className="border border-gray-300 p-2">API Available</th>
              <th className="border border-gray-300 p-2">Characteristic</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="border border-gray-300 p-2">
                <a
                  href="https://deepmind.google/technologies/gemini/pro/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline"
                  onClick={() => {
                    event({
                      action: 'click',
                      category: 'faq',
                      label: 'gemini_1_5_pro_link',
                      value: 1,
                    });
                  }}
                >
                  Gemini 1.5 Pro
                </a>
              </td>
              <td className="border border-gray-300 p-2">Nov 2023</td>
              <td className="border border-gray-300 p-2">Yes</td>
              <td className="border border-gray-300 p-2" rowSpan={2}>
                Google Cloud Platform
              </td>
              <td className="border border-gray-300 p-2">Yes</td>
              <td className="border border-gray-300 p-2">
                Good performance for complex tasks
              </td>
            </tr>
            <tr>
              <td className="border border-gray-300 p-2">
                <a
                  href="https://deepmind.google/technologies/gemini/pro/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline"
                  onClick={() => {
                    event({
                      action: 'click',
                      category: 'faq',
                      label: 'gemini_1_5_pro_flash_link',
                      value: 1,
                    });
                  }}
                >
                  Gemini 1.5 Pro Flash
                </a>
              </td>
              <td className="border border-gray-300 p-2">Nov 2023</td>
              <td className="border border-gray-300 p-2">Yes</td>
              <td className="border border-gray-300 p-2">Yes</td>
              <td className="border border-gray-300 p-2">Fast and versatile</td>
            </tr>
          </tbody>
        </table>
      ),
    },
    {
      title: 'Facebook Llama',
      detail: (
        <table className="table-auto w-full border-collapse border border-gray-400 text-xs">
          <thead>
            <tr>
              <th className="border border-gray-300 p-2">Model</th>
              <th className="border border-gray-300 p-2">Knowledge Cut-off</th>
              <th className="border border-gray-300 p-2">Support Vision</th>
              <th className="border border-gray-300 p-2">Service Provider</th>
              <th className="border border-gray-300 p-2">API Available</th>
              <th className="border border-gray-300 p-2">Characteristic</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="border border-gray-300 p-2">
                <a
                  href="https://ai.meta.com/blog/meta-llama-3-1/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline"
                  onClick={() => {
                    event({
                      action: 'click',
                      category: 'faq',
                      label: 'llama_3_1_405b_link',
                      value: 1,
                    });
                  }}
                >
                  Llama 3.1 405B
                </a>
              </td>
              <td className="border border-gray-300 p-2">Dec 2023</td>
              <td className="border border-gray-300 p-2">No</td>
              <td className="border border-gray-300 p-2">
                Google Cloud Platform
              </td>
              <td className="border border-gray-300 p-2">Yes</td>
              <td className="border border-gray-300 p-2">
                Optimized for multilingual dialogue
              </td>
            </tr>
          </tbody>
        </table>
      ),
    },
    {
      title: 'DeepSeek',
      detail: (
        <table className="table-auto w-full border-collapse border border-gray-400 text-xs">
          <thead>
            <tr>
              <th className="border border-gray-300 p-2">Model</th>
              <th className="border border-gray-300 p-2">Knowledge Cut-off</th>
              <th className="border border-gray-300 p-2">Support Vision</th>
              <th className="border border-gray-300 p-2">Service Provider</th>
              <th className="border border-gray-300 p-2">API Available</th>
              <th className="border border-gray-300 p-2">Characteristic</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="border border-gray-300 p-2">
                <a
                  href="https://api-docs.deepseek.com/news/news250120"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline"
                  onClick={() => {
                    event({
                      action: 'click',
                      category: 'faq',
                      label: 'deepseek_r1_link',
                      value: 1,
                    });
                  }}
                >
                  DeepSeek-R1
                </a>
              </td>
              <td className="border border-gray-300 p-2">Jan 2025</td>
              <td className="border border-gray-300 p-2">No</td>
              <td className="border border-gray-300 p-2">Microsoft Azure</td>
              <td className="border border-gray-300 p-2">Yes</td>
              <td className="border border-gray-300 p-2">
                Exceptional Chinese language proficiency; strong reasoning
              </td>
            </tr>
            <tr>
              <td className="border border-gray-300 p-2">
                <a
                  href="https://api-docs.deepseek.com/news/news1226"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline"
                  onClick={() => {
                    event({
                      action: 'click',
                      category: 'faq',
                      label: 'deepseek_v3_link',
                      value: 1,
                    });
                  }}
                >
                  DeepSeek-V3
                </a>
              </td>
              <td className="border border-gray-300 p-2">Jul 2024</td>
              <td className="border border-gray-300 p-2">No</td>
              <td className="border border-gray-300 p-2">Microsoft Azure</td>
              <td className="border border-gray-300 p-2">Yes</td>
              <td className="border border-gray-300 p-2">
                Exceptional Chinese language proficiency; strong
                Mixture-of-Experts language model
              </td>
            </tr>
          </tbody>
        </table>
      ),
    },
    {
      title: 'Qwen',
      detail: (
        <table className="table-auto w-full border-collapse border border-gray-400 text-xs">
          <thead>
            <tr>
              <th className="border border-gray-300 p-2">Model</th>
              <th className="border border-gray-300 p-2">Knowledge Cut-off</th>
              <th className="border border-gray-300 p-2">Support Vision</th>
              <th className="border border-gray-300 p-2">Service Provider</th>
              <th className="border border-gray-300 p-2">API Available</th>
              <th className="border border-gray-300 p-2">Characteristic</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="border border-gray-300 p-2">
                <a
                  href="https://www.alibabacloud.com/help/en/model-studio/developer-reference/what-is-qwen-llm"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline"
                  onClick={() => {
                    event({
                      action: 'click',
                      category: 'faq',
                      label: 'qwen_2_5_max_link',
                      value: 1,
                    });
                  }}
                >
                  Qwen2.5-Max
                </a>
              </td>
              <td className="border border-gray-300 p-2">Dec 2024</td>
              <td className="border border-gray-300 p-2">No</td>
              <td className="border border-gray-300 p-2">Alibaba Cloud</td>
              <td className="border border-gray-300 p-2">Yes</td>
              <td className="border border-gray-300 p-2">
                Exceptional performance, complex reasoning, excels in Chinese
                understanding.
              </td>
            </tr>
            <tr>
              <td className="border border-gray-300 p-2">
                <a
                  href="https://www.alibabacloud.com/help/en/model-studio/developer-reference/what-is-qwen-llm"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline"
                  onClick={() => {
                    event({
                      action: 'click',
                      category: 'faq',
                      label: 'qwen_plus_link',
                      value: 1,
                    });
                  }}
                >
                  Qwen-Plus
                </a>
              </td>
              <td className="border border-gray-300 p-2">Dec 2024</td>
              <td className="border border-gray-300 p-2">No</td>
              <td className="border border-gray-300 p-2">Alibaba Cloud</td>
              <td className="border border-gray-300 p-2">Yes</td>
              <td className="border border-gray-300 p-2">
                Balanced performance, speed, cost-effective, strong Chinese
                language support.
              </td>
            </tr>
          </tbody>
        </table>
      ),
    },
  ];

  const usageLimitSections = [
    {
      title: 'Azure OpenAI',
      detail: (
        <table className="table-auto w-full border-collapse border border-gray-400 text-xs">
          <thead>
            <tr>
              <th className="border border-gray-300 p-2">GPT-4o</th>
              <th className="border border-gray-300 p-2">GPT-4o Mini</th>
              <th className="border border-gray-300 p-2">o1</th>
              <th className="border border-gray-300 p-2">o1-mini</th>
              <th className="border border-gray-300 p-2">o3-mini</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="border border-gray-300 p-2">500,000</td>
              <td className="border border-gray-300 p-2">8,000,000</td>
              <td className="border border-gray-300 p-2">100,000</td>
              <td className="border border-gray-300 p-2">500,000</td>
              <td className="border border-gray-300 p-2">500,000</td>
            </tr>
          </tbody>
        </table>
      ),
    },
    {
      title: 'Anthropic',
      detail: (
        <table className="table-auto w-full border-collapse border border-gray-400 text-xs">
          <thead>
            <tr>
              <th className="border border-gray-300 p-2">Claude 3.5 Sonnet</th>
              <th className="border border-gray-300 p-2">Claude 3 Haiku</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="border border-gray-300 p-2">200,000</td>
              <td className="border border-gray-300 p-2">2,000,000</td>
            </tr>
          </tbody>
        </table>
      ),
    },
    {
      title: 'Google Gemini',
      detail: (
        <table className="table-auto w-full border-collapse border border-gray-400 text-xs">
          <thead>
            <tr>
              <th className="border border-gray-300 p-2">Gemini 1.5 Pro</th>
              <th className="border border-gray-300 p-2">
                Gemini 1.5 Pro Flash
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="border border-gray-300 p-2">500,000</td>
              <td className="border border-gray-300 p-2">5,000,000</td>
            </tr>
          </tbody>
        </table>
      ),
    },
    {
      title: 'Facebook Llama',
      detail: (
        <table className="table-auto w-full border-collapse border border-gray-400 text-xs">
          <thead>
            <tr>
              <th className="border border-gray-300 p-2">
                Facebook Llama 3.1 405b
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="border border-gray-300 p-2">200,000</td>
            </tr>
          </tbody>
        </table>
      ),
    },
    {
      title: 'DeepSeek',
      detail: (
        <table className="table-auto w-full border-collapse border border-gray-400 text-xs">
          <thead>
            <tr>
              <th className="border border-gray-300 p-2">DeepSeek-R1</th>
              <th className="border border-gray-300 p-2">DeepSeek-V3</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="border border-gray-300 p-2">5,000,000</td>
              <td className="border border-gray-300 p-2">5,000,000</td>
            </tr>
          </tbody>
        </table>
      ),
    },
    {
      title: 'Qwen',
      detail: (
        <table className="table-auto w-full border-collapse border border-gray-400 text-xs">
          <thead>
            <tr>
              <th className="border border-gray-300 p-2">Qwen2.5-Max</th>
              <th className="border border-gray-300 p-2">Qwen-Plus</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="border border-gray-300 p-2">1,500,000</td>
              <td className="border border-gray-300 p-2">8,000,000</td>
            </tr>
          </tbody>
        </table>
      ),
    },
  ];

  return (
    <Box
      sx={{
        height: '100svh',
        display: 'flex',
        flexDirection: 'column',
        p: 0,
      }}
    >
      {isMobile ? (
        <Toolbar
          variant="dense"
          disableGutters
          sx={{
            justifyContent: 'space-between',
            position: 'relative',
            my: 0.5,
          }}
        >
          <Box sx={{ width: 34, display: { xs: undefined, md: 'none' } }} />
          <Typography
            variant="h6"
            noWrap
            component="div"
            sx={{
              flex: 1,
              textAlign: 'center',
              fontWeight: 'medium',
              opacity: 0.7,
              cursor: 'pointer',
              fontSize: '1rem',
            }}
          >
            Frequently Asked Questions
          </Typography>
          <Box sx={{ width: 34, display: { xs: undefined, md: 'none' } }} />
        </Toolbar>
      ) : (
        <Box sx={{ mt: 3, mb: { xs: 2, md: 4 }, maxWidth: '72rem', width:'100%', alignSelf: 'center', px: 4 }}>
          <Typography
            variant="h4"
            component="h1"
            gutterBottom
            fontWeight={{ xs: 600, md: 400 }}
            fontSize={{ xs: 25, md: '2.125rem' }}
            textAlign={{ xs: 'center', md: 'start' }}
          >
            Frequently Asked Questions
          </Typography>
        </Box>
      )}
      <Box display={'flex'} flexDirection={'column'} flex={1} overflow={'auto'} pb={2}>
        <ol className="list-decimal text-sm max-w-6xl w-full self-center px-[32px]">
          <li style={{ fontWeight: 'bold' }} className="pt-3">
            <p style={{ fontWeight: 'bold' }}>Which models are available?</p>
            <div className="mx-auto pt-3">
              <div className="flex flex-col overflow-auto" style={{ gap: 8 }}>
                {availableSections.map((section) => (
                  <Accordion
                    key={section.title}
                    variant={'outlined'}
                    sx={{ border: 'none', '&::before': { display: 'none' } }}
                     onChange={() => {
                       event({
                         action: 'click',
                         category: 'faq',
                         label: `toggle_accordion_${section.title}`,
                         value: 1,
                       });
                     }}
                  >
                    <AccordionSummary
                      expandIcon={<ExpandMoreIcon />}
                      sx={{ borderRadius: '10px' }}
                    >
                      <Typography sx={{ fontWeight: 'semibold' }}>
                        {section.title}
                      </Typography>
                    </AccordionSummary>
                    <AccordionDetails sx={{ overflowX: 'auto' }}>
                      {section.detail}
                    </AccordionDetails>
                  </Accordion>
                ))}
              </div>
              <ul className="list-disc mt-2"></ul>
            </div>
          </li>
          <li style={{ fontWeight: 'bold' }} className="pt-3">
            <p style={{ fontWeight: 'bold' }}>
              How to switch between AI models?
            </p>
            You can switch between models using the navigation bar in the left
            menu.
          </li>
          <li style={{ fontWeight: 'bold' }} className="pt-3">
            <p style={{ fontWeight: 'bold' }}>Is there any usage limit?</p>
            Yes, to ensure fair use of University resources, a monthly usage
            quota has been imposed to monitor consumption patterns. For
            students, the token allowance for each model within each calendar
            month is listed below:
            <div
              className="flex flex-col overflow-auto mt-4 mb-4"
              style={{ gap: 8 }}
            >
              {usageLimitSections.map((section) => (
                <Accordion
                  key={section.title}
                  variant={'outlined'}
                  sx={{ border: 'none', '&::before': { display: 'none' } }}
                   onChange={() => {
                     event({
                       action: 'click',
                       category: 'faq',
                       label: `toggle_accordion_${section.title}`,
                       value: 1,
                     });
                   }}
                >
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    sx={{ borderRadius: '10px' }}
                  >
                    <Typography sx={{ fontWeight: 'semibold' }}>
                      {section.title}
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails sx={{ overflowX: 'auto' }}>
                    {section.detail}
                  </AccordionDetails>
                </Accordion>
              ))}
            </div>
            <p>
              Tips: You can clear the conversation history before sending a new
              message to reduce token usage.
            </p>
          </li>
          <li style={{ fontWeight: 'bold' }} className="pt-3">
            <p style={{ fontWeight: 'bold' }}>
              What are "tokens" and how to calculate them?
            </p>
            Please refer to{' '}
            <a
              href="https://help.openai.com/en/articles/4936856-what-are-tokens-and-how-to-count-them"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 hover:underline"
               onClick={() => {
                 event({
                   action: 'click',
                   category: 'faq',
                   label: 'tokens_link',
                   value: 1,
                 });
               }}
            >
              this link
            </a>{' '}
            for an explanation of token from OpenAI GPT. For models from other
            service providers, token calculation is similar to that of GPT.
          </li>
          <li style={{ fontWeight: 'bold' }} className="pt-3">
            <p style={{ fontWeight: 'bold' }}>What is "temperature"? </p>
            Temperature in LLM Model controls the "creativity" or randomness of
            the generated response. A higher temperature (e.g., 0.8) results in
            more diverse and creative output, while a lower temperature (e.g.,
            0.3) makes the output more deterministic and focused. You can refer
            to{' '}
            <a
              href="https://community.openai.com/t/cheat-sheet-mastering-temperature-and-top-p-in-chatgpt-api-a-few-tips-and-tricks-on-controlling-the-creativity-deterministic-output-of-prompt-responses/172683"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 hover:underline"
               onClick={() => {
                 event({
                   action: 'click',
                   category: 'faq',
                   label: 'temperature_link',
                   value: 1,
                 });
               }}
            >
              this link
            </a>{' '}
            for more details.
          </li>
          <li
            style={{ fontWeight: 'bold' }}
            className="pt-3"
            id="supportvision"
          >
            <p style={{ fontWeight: 'bold' }}>
              What is the Vision feature, and what file formats are accepted
              when uploading a document to the HKBU GenAI Platform?
            </p>
            <p className="font-normal">
              The Vision feature allows the AI to "see," "recognize," and
              "understand" images. This capability is natively supported by the
              GPT-4o, o1, Gemini-1.5 Pro, Gemini-1.5 Flash, Claude 3.5 Sonnet
              and Claude 3 Haiku.
              <br />
              For other models including GPT-4o Mini, Llama 3.1, DeepSeek-R1,
              DeepSeek-V3, Qwen2.5-Max and Qwen-Plus, which do not support
              Vision, we have applied traditional OCR technology to retrieve
              text from various file formats. The supported formats include:
            </p>
            <div className="mx-auto px-3">
              <ul className="list-disc font-normal ">
                <li>Most image file formats (jpg, png, tif, bmp, etc.)</li>
                <li>
                  Microsoft Office files: Word documents (doc, docx), Excel
                  worksheets (xls, xlsx), PowerPoint slides (ppt, pptx)
                </li>
                <li>Adobe PDFs (pdf), Text files (txt)</li>
              </ul>
            </div>
          </li>
          <li style={{ fontWeight: 'bold' }} className="pt-3">
            <p style={{ fontWeight: 'bold' }}>
              Does HKBU GenAI Platform collect data and where are the data
              stored?
            </p>
            Yes, data will be logged to ensure the proper and fair use of
            University resources. The data is encrypted and stored in the ITO
            data centre. Please refer to the{' '}
            <a
              className="cursor-pointer text-blue-600 hover:underline"
              href="/settings/terms-and-conditions"
               onClick={() => {
                 event({
                   action: 'click',
                   category: 'faq',
                   label: 'terms_and_conditions_link',
                   value: 1,
                 });
               }}
            >
              Terms and Conditions
            </a>{' '}
            for details.
          </li>
          <li style={{ fontWeight: 'bold' }} className="pt-3">
            <p style={{ fontWeight: 'bold' }}>
              Will the data be used for model training?
            </p>
            No, the data will not be used for model training.
          </li>
          <li style={{ fontWeight: 'bold' }} className="pt-3">
            <p style={{ fontWeight: 'bold' }}>
              Is the LLM Model API connectivity available?
            </p>
            Yes, the RESTful API connectivity is in place and is currently being
            tested by a selected group of users. Rate limit has imposed to
            optimize the resource usage.
            <br />
            <br />
            If you have any enquiries about the rate limit, please contact the
            ITO Service Centre (3411 7899,{' '}
            <a
              href="mailto:<EMAIL>"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 hover:underline"
               onClick={() => {
                 event({
                   action: 'click',
                   category: 'faq',
                   label: 'email_link',
                   value: 1,
                 });
               }}
            >
              <EMAIL>
            </a>
            ) or use our online feedback form.
          </li>
        </ol>
      </Box>
    </Box>
  );
};

export default FaqPage;
