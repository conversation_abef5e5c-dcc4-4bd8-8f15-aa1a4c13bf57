import requests
import json
import time

# Configuration
BASE_URL = "http://localhost:3003/api/v0"
API_KEY = "80b60025-07b4-4097-ba1d-e14695359dd0"

headers = {
    'Content-Type': 'application/json',
    'api-key': API_KEY
}

def debug_single_request(model_name="gpt-4.1-mini"):
    """Make a single request and examine all headers for rate limiting info"""
    url = f"{BASE_URL}/rest/deployments/{model_name}/chat/completions"
    params = {"api-version": "2024-02-01"}
    
    data = {
        "messages": [{"role": "user", "content": "Hi"}],
        "max_tokens": 5,
        "temperature": 0.1
    }
    
    print(f"Making single request to {model_name}...")
    print(f"URL: {url}")
    print(f"Headers sent: {headers}")
    
    try:
        response = requests.post(url, headers=headers, json=data, params=params, timeout=10)
        
        print(f"\nResponse Status: {response.status_code}")
        print(f"Response Headers:")
        for header, value in response.headers.items():
            if 'rate' in header.lower() or 'limit' in header.lower() or 'x-' in header.lower():
                print(f"  {header}: {value}")
        
        print(f"\nAll Response Headers:")
        for header, value in response.headers.items():
            print(f"  {header}: {value}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"\nResponse successful")
            return True
        elif response.status_code == 429:
            error_data = response.json()
            print(f"\nRate Limited! Response: {json.dumps(error_data, indent=2)}")
            return False
        else:
            print(f"\nUnexpected status: {response.text[:500]}")
            return False
            
    except Exception as e:
        print(f"Error: {e}")
        return False

def test_rapid_requests(model_name="gpt-4.1-mini", num_requests=10):
    """Make rapid requests with detailed logging"""
    url = f"{BASE_URL}/rest/deployments/{model_name}/chat/completions"
    params = {"api-version": "2024-02-01"}
    
    data = {
        "messages": [{"role": "user", "content": "Hi"}],
        "max_tokens": 5,
        "temperature": 0.1
    }
    
    print(f"\nMaking {num_requests} rapid requests to {model_name}...")
    
    results = []
    
    for i in range(num_requests):
        try:
            response = requests.post(url, headers=headers, json=data, params=params, timeout=10)
            
            rate_limit_headers = {}
            for header, value in response.headers.items():
                if 'rate' in header.lower() or 'limit' in header.lower():
                    rate_limit_headers[header] = value
            
            result = {
                'request_num': i + 1,
                'status_code': response.status_code,
                'rate_headers': rate_limit_headers
            }
            
            if response.status_code == 429:
                try:
                    result['error_data'] = response.json()
                except:
                    result['error_data'] = response.text[:200]
                print(f"  Request {i+1}: RATE LIMITED (429)")
                print(f"    Headers: {rate_limit_headers}")
                results.append(result)
                break
            elif response.status_code == 200:
                print(f"  Request {i+1}: SUCCESS - Headers: {rate_limit_headers}")
            else:
                print(f"  Request {i+1}: STATUS {response.status_code}")
                
            results.append(result)
            
            # Small delay to avoid overwhelming
            time.sleep(0.1)
            
        except Exception as e:
            print(f"  Request {i+1}: ERROR - {e}")
            break
    
    return results

def check_redis_status():
    """Check if we can infer Redis status from rate limit headers"""
    print("\n" + "="*60)
    print("DEBUGGING REDIS/RATE LIMITING STATUS")
    print("="*60)
    
    # Make a request and check for rate limit headers
    success = debug_single_request()
    
    if success:
        print("\nFirst request successful - checking for rate limit headers...")
        
        # Make a few more requests to see if rate limit headers update
        print("\nMaking 5 more requests to check header progression...")
        results = test_rapid_requests(num_requests=5)
        
        has_rate_headers = any(r.get('rate_headers') for r in results)
        
        if has_rate_headers:
            print("\n✅ Rate limit headers found - rate limiting system is working")
            print("If no 429 errors, the limit might be higher than expected")
        else:
            print("\n❌ No rate limit headers found - rate limiting system may not be active")
            print("Possible issues:")
            print("  1. Redis connection problem")
            print("  2. ModelRateLimitGuard not properly applied")
            print("  3. Model name extraction issue")
            print("  4. Different rate limit configuration")
    
    return results

def check_rate_limit_endpoint():
    """Check the rate limit status endpoint"""
    print("\n" + "="*60)
    print("CHECKING RATE LIMIT STATUS ENDPOINT")
    print("="*60)
    
    url = f"{BASE_URL}/rest/rate-limit/usage"
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Rate limit endpoint working")
            print(f"Found {len(data.get('usage', []))} models")
            
            # Look for our test model
            test_model = "gpt-4.1-mini"
            for usage in data.get('usage', []):
                if usage.get('model') == test_model:
                    print(f"\n📊 {test_model} rate limit status:")
                    print(f"  Used: {usage.get('used', 0)}")
                    print(f"  Remaining: {usage.get('remaining', 0)}")
                    print(f"  Limit: {usage.get('limit', 0)}")
                    print(f"  Reset at: {usage.get('resetAt', 'N/A')}")
                    break
            else:
                print(f"⚠️  {test_model} not found in rate limit data")
                
        else:
            print(f"❌ Rate limit endpoint failed: {response.status_code}")
            print(f"Response: {response.text[:200]}")
            
    except Exception as e:
        print(f"❌ Error accessing rate limit endpoint: {e}")

def main():
    print("="*80)
    print("DEBUGGING RATE LIMITING SYSTEM")
    print("="*80)
    
    # Check basic connectivity and headers
    check_redis_status()
    
    # Check rate limit endpoint
    check_rate_limit_endpoint()
    
    print("\n" + "="*80)
    print("DEBUG COMPLETE")
    print("="*80)

if __name__ == "__main__":
    main()