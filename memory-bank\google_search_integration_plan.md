# Plan: Google Search Integration with Source Extraction

**Goal:** Modify the backend API (`ChatCompletionService`) to extract Google Search results (title, link, snippet) when the tool is used and include them as a `sources` array in the API response (both streaming and non-streaming) for the frontend to display.

**Core Problem:** The current implementation in `hkbu-genai-platform/apps/api/src/general/chat-completion/chat-completion.service.ts` uses Langchain's tool binding, but doesn't explicitly capture and forward the raw results from the `GoogleCustomSearch` tool.

**Frontend Requirements:**
*   Expects a `sources` array in the API response.
*   Can handle a dedicated SSE chunk for sources during streaming: `{ type: "sources", sources: [...] }`.
*   Each source object should have the structure: `{ title: string, link: string, snippet: string }`.
*   Database persistence for sources is **not** required at this time.

**Proposed Plan:**

1.  **Capture Search Results within Langchain Flow:**
    *   Modify the stream processing logic (`processLangchainStreamWithTools`) and the non-streaming invocation logic to inspect the events/results returned by Langchain's `.stream()` and `.invoke()` methods, specifically looking for tool outputs (`ToolMessage` or similar containing the result from `GoogleCustomSearch`).
    *   Parse the output string from `@langchain/community/tools/google_custom_search` to extract individual results (title, link, snippet) using regex or string manipulation. Format these into the required `{ title: string, link: string, snippet: string }` structure.

2.  **Modify Streaming Response (`processLangchainStreamWithTools`):**
    *   Initialize `let sources: Array<{ title: string, link: string, snippet: string }> = [];`.
    *   In the stream loop (`for await...of langchainStream`), identify tool output chunks, parse them, and populate the `sources` array.
    *   Immediately after populating `sources`, send a dedicated SSE chunk: `data: ${JSON.stringify({ type: "sources", sources: sources })}\n\n`.
    *   Continue streaming regular LLM content chunks (`delta: { content: ... }`) as normal.
    *   The final `[DONE]` message will not include sources again.

3.  **Modify Non-Streaming Response:**
    *   After `modelOrRunnable.invoke()`, inspect the `aiResponse` object for tool results metadata (e.g., `aiResponse.tool_calls` or similar).
    *   Parse the tool output string found in the response metadata, populate the `sources` array.
    *   Add the `sources` array to the final JSON object returned to the client.

4.  **Refinement & Error Handling:**
    *   Add specific logging for source extraction, parsing, and sending steps.
    *   Ensure graceful handling if the tool doesn't run or if parsing fails; the `sources` array should simply remain empty in these cases.

**Visual Flow (Streaming):**

```mermaid
sequenceDiagram
    participant FE as Frontend
    participant API as ChatCompletionService
    participant LC as Langchain (Model + Tool)
    participant GS as GoogleSearchTool

    FE->>+API: POST /chat-completion (prompt, useGoogle=true, stream=true)
    API->>+LC: stream(messages, tools=[GS])
    LC->>LC: LLM decides to use GS
    LC->>+GS: invoke(query)
    GS-->>-LC: Raw Search Results (string)
    LC->>LC: LLM processes results + starts generating text
    LC-->>API: Stream Chunk (Tool Output)
    API->>API: Parse results -> sources = [{title, link, snippet}, ...]
    API-->>FE: SSE Chunk { type: "sources", sources: [...] }
    LC-->>API: Stream Chunk (LLM Content Delta 1)
    API-->>FE: SSE Chunk { ..., choices: [{ delta: { content: "..." } }] }
    LC-->>API: Stream Chunk (LLM Content Delta 2)
    API-->>FE: SSE Chunk { ..., choices: [{ delta: { content: "..." } }] }
    LC-->>API: Stream Chunk (Finish)
    API-->>FE: SSE Chunk { ..., choices: [{ delta: {}, finish_reason: "stop" }] }
    API-->>FE: SSE [DONE] message