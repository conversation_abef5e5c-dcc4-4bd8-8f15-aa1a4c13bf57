import { Injectable, UnauthorizedException, Logger } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy as CustomStrategy } from 'passport-custom'; // Import CustomStrategy
import { PrismaService } from '../prisma/prisma.service';
import { Request } from 'express';

// This interface would represent the user object attached to the request after successful validation
export interface ApiUserPayload {
  ssoid: string;
  dept_unit_code: string; // Needed for authorization checks
  // Add other relevant user fields fetched from DB
  [key: string]: any;
}

@Injectable()
export class ApiKeyStrategy extends PassportStrategy(
  CustomStrategy,
  'api-key',
) {
  // Use CustomStrategy
  private readonly logger = new Logger(ApiKeyStrategy.name);

  constructor(private readonly prisma: PrismaService) {
    super(); // No arguments needed for CustomStrategy's constructor in this pattern
  }

  /**
   * This method is automatically called by the NestJS Passport infrastructure.
   * For passport-custom, it receives the Express request object.
   * @param req The Express request object.
   * @returns The user payload if the key is valid.
   * @throws UnauthorizedException if the key is invalid or validation fails.
   */
  async validate(req: Request): Promise<ApiUserPayload> {
    const apiKeyHeader = req.headers['api-key'] || req.headers['apikey'];
    const apiKey = Array.isArray(apiKeyHeader) ? apiKeyHeader[0] : apiKeyHeader;

    this.logger.debug(`Validating API key from header...`);

    if (!apiKey || typeof apiKey !== 'string') {
      this.logger.warn('API key missing or invalid format in request headers.');
      throw new UnauthorizedException('API key is missing or invalid.');
    }

    this.logger.debug(`Extracted API key: ${apiKey.substring(0, 5)}...`);

    try {
      const userRecord = await this.prisma.$queryRaw<
        ApiUserPayload[]
      >`EXEC sp_acl_GetUserDetailsByApiKey @api_key=${apiKey}`;

      if (!userRecord || userRecord.length === 0) {
        this.logger.warn(
          `API key not found or invalid: ${apiKey.substring(0, 5)}...`,
        );
        throw new UnauthorizedException('Invalid API key.');
      }

      const user = userRecord[0];
      // The stored procedure returns user details with username instead of ssoid
      const ssoid = user.ssoid || user.username;
      if (!ssoid || !user.dept_unit_code) {
        this.logger.error(
          `User record for API key ${apiKey.substring(0, 5)}... is missing essential fields (username/ssoid, dept_unit_code).`,
        );
        throw new UnauthorizedException(
          'User data associated with API key is incomplete.',
        );
      }

      this.logger.log(`API key validated successfully for user: ${ssoid}`);

      // Map the database user object to match AuthenticatedUser interface
      // This ensures consistency between JWT and API key authentication
      const authenticatedUser = {
        // Spread all user fields first
        ...user,
        // Then override specific fields to match AuthenticatedUser interface
        userId: ssoid, // Map username/ssoid to userId for consistency
        ssoid: ssoid, // Also include ssoid for AuthService compatibility
        email: user.email || '',
        type: user.employee_type || user.type || '', // e.g., 'STAFF', 'STUDENT'
      };

      return authenticatedUser; // This will be available as req.user if validation succeeds
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      let errorMessage = 'An unknown error occurred during API key validation.';
      if (error instanceof Error) {
        errorMessage = error.message;
      }
      this.logger.error(
        `Error during API key validation: ${errorMessage}`,
        error instanceof Error ? error.stack : undefined,
      );
      throw new UnauthorizedException(
        'API key validation failed due to an internal error.',
      );
    }
  }
}
