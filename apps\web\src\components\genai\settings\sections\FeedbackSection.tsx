'use client';

import React, { useState } from 'react';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
import Snackbar from '@mui/material/Snackbar';
import Alert from '@mui/material/Alert';

const FeedbackSection: React.FC = () => {
  const [feedback, setFeedback] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error'>(
    'success',
  );

  const handleSubmit = async () => {
    if (!feedback.trim()) {
      setSnackbarMessage('Feedback cannot be empty.');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
      return;
    }
    setIsLoading(true);
    // Placeholder for actual submission logic
    // In a real app, you would call an API endpoint here.
    // Example:
    // try {
    //   const response = await fetch('/api/feedback', {
    //     method: 'POST',
    //     headers: { 'Content-Type': 'application/json' },
    //     body: JSON.stringify({ message: feedback }),
    //   });
    //   if (response.ok) {
    //     setSnackbarMessage('Feedback submitted successfully!');
    //     setSnackbarSeverity('success');
    //     setFeedback(''); // Clear textarea
    //   } else {
    //     setSnackbarMessage('Failed to submit feedback. Please try again.');
    //     setSnackbarSeverity('error');
    //   }
    // } catch (error) {
    //   setSnackbarMessage('An error occurred. Please try again.');
    //   setSnackbarSeverity('error');
    // }

    // Simulating API call
    await new Promise((resolve) => setTimeout(resolve, 1500));
    console.log('Feedback submitted:', feedback);
    setSnackbarMessage('Feedback submitted successfully! (Placeholder)');
    setSnackbarSeverity('success');
    setFeedback('');

    setIsLoading(false);
    setSnackbarOpen(true);
  };

  const handleCloseSnackbar = (
    event?: React.SyntheticEvent | Event,
    reason?: string,
  ) => {
    if (reason === 'clickaway') {
      return;
    }
    setSnackbarOpen(false);
  };

  return (
    <Box className="text-sm" sx={{ color: 'text.primary' }}>
      <Typography variant="body1" paragraph sx={{ fontWeight: 'medium' }}>
        Please input your feedback to help us improve our service. Your input is
        valuable to us.
      </Typography>
      <TextField
        fullWidth
        multiline
        rows={6}
        variant="outlined"
        label="Your Feedback"
        value={feedback}
        onChange={(e) => setFeedback(e.target.value)}
        disabled={isLoading}
        inputProps={{ maxLength: 1000 }}
        helperText={`${feedback.length}/1000 characters`}
        sx={{ mb: 2 }}
      />
      <Button
        variant="contained"
        color="primary"
        onClick={handleSubmit}
        disabled={isLoading || !feedback.trim()}
        startIcon={
          isLoading ? <CircularProgress size={20} color="inherit" /> : null
        }
      >
        {isLoading ? 'Submitting...' : 'Submit Feedback'}
      </Button>
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbarSeverity}
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default FeedbackSection;
