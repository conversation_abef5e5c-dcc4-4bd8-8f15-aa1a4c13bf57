import { useState } from 'react'; // Removed useContext
import { useSession } from 'next-auth/react';
// Removed: import { AppContext } from "../../context/AppContext";
import { useAppDispatch, useAppSelector } from '@/lib/store/hooks'; // Use path alias
import { RootState } from '@/lib/store/store'; // Import RootState
// --- Redux Actions/Selectors ---
import {
  // selectAppState, // Unused
  closeFaqModal,
  openImgGenModal, // Assuming this exists
  // closeImgGenModal, // Unused
  openTncModal,
  closeTncModal,
  setTncModalShowAgree,
  closeFeedbackModal,
  openFeedbackSuccessModal,
  closeFeedbackSuccessModal,
  closeContactUsModal,
  closeHealthCheckModal,
  // Removed potentially problematic individual selectors
  selectAppState, // Keep main app state selector
} from '@/lib/store/appSlice';
import { setChatErrorMessage } from '@/lib/store/chatSlice'; // Use path alias
// import { GptModel } from "@/lib/types/common"; // Unused

// --- Component Imports ---
import FaqModal from './FaqModal'; // Keep relative as it's in the same folder
import FeedbackContactUsModal from './FeedbackContactUsModal';
import FeedbackSuccessModal from './FeedbackSuccessModal';
import ApiKeyModal from './ApiKeyModal';
import TncModal from './TncModal';
import HealthCheckModal from './HealthCheckModal';

const MenuModals = () => {
  const dispatch = useAppDispatch();
  // --- Select relevant state ---
  // Use specific selectors now that they are assumed to be added to appSlice
  // Access modal states directly from appState
  const appState = useAppSelector(selectAppState);
  const {
    showFaqModal,
    // showImgGenModal, // Unused
    showTncModal,
    tncModalShowAgree,
    showFeedbackModal,
    showFeedbackSuccessModal,
    showContactUsModal,
    showHealthCheckModal,
    showPromptEngModal, // Needed for PromptEngModal below
  } = appState;
  const modelList =
    useAppSelector((state: RootState) => state.app.modelList) ?? []; // Get modelList directly

  // const { data: session } = useSession(); // Unused
  const basePath = process.env.NEXT_PUBLIC_BASE_PATH;
  const [isFeedbackLoading, setIsFeedbackLoading] = useState<boolean>(false);
  const [isContactUsLoading, setIsContactUsLoading] = useState<boolean>(false);

  const agreeTnc = async () => {
    const setTNCStatus = async () => {
      const data = await fetch(`${basePath}/api/general/set_agreed_tnc`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({}),
      });
      const json = await data.json();
      return json;
    };

    setTNCStatus()
      .then((data) => {
        if (data.success) {
          // Use Redux actions
          dispatch(setTncModalShowAgree(false));
          dispatch(closeTncModal());
        }
      })
      .catch((error) => {
        // Add error handling
        console.error('Error setting TNC status:', error);
        dispatch(
          setChatErrorMessage(
            'Failed to save Terms & Conditions agreement. Please try again.',
          ),
        );
      });
  };

  const handleFeedbackSubmit = async (message: string) => {
    setIsFeedbackLoading(true);
    try {
      if (message.length == 0) {
        throw 'Please input your feedback.';
      }

      const fetchResult = await fetch(`${basePath}/api/general/feedback`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message }),
      });

      if (fetchResult.status !== 200) {
        throw 'Failed to submit feedback. Please try again later.'; // More descriptive error
      }

      // Use Redux actions
      dispatch(closeFeedbackModal());
      dispatch(openFeedbackSuccessModal());
    } catch (error) {
      // Use Redux action, ensure payload matches action creator
      const errorMessage =
        typeof error === 'string'
          ? error
          : 'An unknown error occurred submitting feedback.';
      dispatch(setChatErrorMessage(errorMessage));
    }

    setIsFeedbackLoading(false);
  };

  // No longer need separate prop objects if using standard onClose

  return (
    <>
      {/* Pass correct props to FaqModal - Assuming onClose is standard */}
      <FaqModal
        show={appState.showFaqModal} // Use appState
        onClose={() => dispatch(closeFaqModal())} // Assumed action
        // Keep openTabFn logic, using assumed actions
        openTabFn={(des: string) => {
          // Added type for 'des'
          if (des === 'image') {
            dispatch(openImgGenModal()); // Assumed action
          } else {
            dispatch(openTncModal()); // Assumed action
          }
          dispatch(closeFaqModal()); // Assumed action
        }}
      />
      {/* ApiKeyModal likely needs similar state/dispatch integration */}
      <ApiKeyModal />

      {/* Pass correct props to TncModal - Assuming onClose */}
      <TncModal
        show={appState.showTncModal} // Use appState
        agree={appState.tncModalShowAgree} // Use appState
        agreeFn={agreeTnc} // Pass the handler function
        onClose={() => dispatch(closeTncModal())} // Assumed action
      />

      {/* Pass correct props to FeedbackModal - Pass dispatch action to both onClose and closeFn */}
      <FeedbackContactUsModal
        open={appState.showFeedbackModal}
        handleClose={() => dispatch(closeFeedbackModal())}
        handleSubmitContact={handleFeedbackSubmit}
        isLoadingContact={isFeedbackLoading}
      />

      {/* Pass correct props to FeedbackSuccessModal - Assuming only onClose needed */}
      <FeedbackSuccessModal
        show={appState.showFeedbackSuccessModal} // Use appState
        onClose={() => dispatch(closeFeedbackSuccessModal())}
        closeFn={() => dispatch(closeFeedbackSuccessModal())} // Added missing closeFn
      />

      {/* Pass correct props to ContactUsModal - Assuming onClose */}
      <FeedbackContactUsModal
        open={appState.showContactUsModal}
        handleClose={() => dispatch(closeContactUsModal())}
        handleSubmitContact={() => {}} // No submit function for contact us
        isLoadingContact={isContactUsLoading}
      />

      {/* Pass correct props to HealthCheckModal - Pass dispatch action to both onClose and closeFn */}
      <HealthCheckModal
        show={appState.showHealthCheckModal} // Use appState
        onClose={() => dispatch(closeHealthCheckModal())} // Standard close handler
        closeFn={() => dispatch(closeHealthCheckModal())} // Satisfy required prop
        modelList={modelList} // Pass modelList from Redux state
      />
    </>
  );
};

export default MenuModals;
