import { PrismaClient } from '@hkbu-genai-platform/database/generated/client'; // Import from package subpath defined in exports

// PrismaClient is attached to the `global` object in development to prevent
// exhausting database connection limit.
// Learn more: https://pris.ly/d/help/next-js-best-practices

declare global {
  // eslint-disable-next-line no-var
  var prisma: PrismaClient | undefined;
}

export const prisma =
  global.prisma ||
  new PrismaClient({
    // log: ['query'], // Optional: Log Prisma queries
  });

if (process.env.NODE_ENV !== 'production') {
  global.prisma = prisma;
}

export default prisma;
