import requests
import json
import time
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:3003/api/v0"
API_KEY = "80b60025-07b4-4097-ba1d-e14695359dd0"

headers = {
    'Content-Type': 'application/json',
    'api-key': API_KEY
}

def get_token_usage():
    """Get current token usage for all models"""
    url = f"{BASE_URL}/rest/rate-limit/token-usage"
    response = requests.get(url, headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        usage_dict = {}
        for usage in data.get('usage', []):
            usage_dict[usage['modelName']] = usage
        return usage_dict
    else:
        print(f"Error getting token usage: {response.status_code} - {response.text}")
        return {}

def test_model_token_tracking(model_name, api_version="2024-02-01", max_tokens=20):
    """Test a single model's token tracking"""
    url = f"{BASE_URL}/rest/deployments/{model_name}/chat/completions"
    
    data = {
        "messages": [
            {"role": "system", "content": "You are a helpful assistant. Respond briefly."},
            {"role": "user", "content": f"Say 'Testing {model_name}' in exactly 3 words."}
        ],
        "temperature": 0.7,
        "max_tokens": max_tokens
    }
    
    params = {"api-version": api_version}
    
    try:
        print(f"Testing {model_name}...")
        response = requests.post(url, headers=headers, json=data, params=params, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            content = result['choices'][0]['message']['content']
            usage = result.get('usage', {})
            total_tokens = usage.get('total_tokens', 0)
            prompt_tokens = usage.get('prompt_tokens', 0)
            completion_tokens = usage.get('completion_tokens', 0)
            
            print(f"  SUCCESS: {content}")
            print(f"  Tokens: {prompt_tokens} input + {completion_tokens} output = {total_tokens} total")
            return True, total_tokens, prompt_tokens, completion_tokens
        else:
            print(f"  FAILED: HTTP {response.status_code}: {response.text[:200]}")
            return False, 0, 0, 0
    except requests.exceptions.Timeout:
        print(f"  TIMEOUT: Request timed out after 30s")
        return False, 0, 0, 0
    except Exception as e:
        print(f"  ERROR: {str(e)[:200]}")
        return False, 0, 0, 0

def test_rate_limit_blocking():
    """Test that rate limiting blocks requests when limit is exceeded"""
    print("\n" + "="*60)
    print("TESTING RATE LIMIT BLOCKING")
    print("="*60)
    
    # Use a model for rapid requests to trigger rate limit
    test_model = "gpt-4.1-mini"  # Should have good rate limit settings
    url = f"{BASE_URL}/rest/deployments/{test_model}/chat/completions"
    params = {"api-version": "2024-02-01"}
    
    data = {
        "messages": [
            {"role": "user", "content": "Hi"}
        ],
        "max_tokens": 5,
        "temperature": 0.1
    }
    
    print(f"Making rapid requests to {test_model} to trigger rate limit...")
    print("Rate limit should be 60 requests per minute per model")
    
    successful_requests = 0
    rate_limited_requests = 0
    
    # Make requests rapidly
    for i in range(70):  # Try more than the 60 request limit
        try:
            response = requests.post(url, headers=headers, json=data, params=params, timeout=10)
            
            if response.status_code == 200:
                successful_requests += 1
                if i < 10 or i % 10 == 0:  # Show first 10, then every 10th
                    print(f"  Request {i+1}: SUCCESS")
            elif response.status_code == 429:
                rate_limited_requests += 1
                if rate_limited_requests == 1:
                    print(f"  Request {i+1}: RATE LIMITED (429) - First rate limit hit!")
                    try:
                        error_data = response.json()
                        print(f"    Error message: {error_data.get('message', 'No message')}")
                    except:
                        pass
                elif rate_limited_requests <= 5:
                    print(f"  Request {i+1}: RATE LIMITED (429)")
            else:
                print(f"  Request {i+1}: UNEXPECTED {response.status_code}")
                
        except requests.exceptions.Timeout:
            print(f"  Request {i+1}: TIMEOUT")
            break
        except Exception as e:
            print(f"  Request {i+1}: ERROR - {str(e)[:100]}")
            break
    
    print(f"\nRate Limit Test Results:")
    print(f"  Successful requests: {successful_requests}")
    print(f"  Rate limited requests: {rate_limited_requests}")
    print(f"  Total requests attempted: {successful_requests + rate_limited_requests}")
    
    if rate_limited_requests > 0:
        print("  RESULT: Rate limiting is WORKING - requests were blocked with 429 status")
        return True
    else:
        print("  RESULT: Rate limiting NOT WORKING - no 429 responses received")
        return False

def test_token_limit_blocking():
    """Test token limit blocking by checking current usage"""
    print("\n" + "="*60)
    print("TESTING TOKEN LIMIT STATUS")
    print("="*60)
    
    usage_data = get_token_usage()
    
    models_near_limit = []
    models_with_room = []
    
    for model_name, usage in usage_data.items():
        if usage['modelName'] in ['text-embedding-3-large', 'text-embedding-3-small']:
            continue  # Skip embedding models
            
        total_used = usage['totalTokensUsed']
        monthly_limit = usage['monthlyLimit']
        remaining = usage['remaining']
        
        percentage_used = (total_used / monthly_limit) * 100
        
        print(f"{model_name}:")
        print(f"  Used: {total_used:,} / {monthly_limit:,} ({percentage_used:.1f}%)")
        print(f"  Remaining: {remaining:,}")
        
        if percentage_used >= 99.9:
            models_near_limit.append(model_name)
            print(f"  STATUS: NEAR LIMIT - Would block pre-requests in old system")
        elif remaining < 100:
            models_near_limit.append(model_name)
            print(f"  STATUS: LOW TOKENS - Good for testing limit behavior")
        else:
            models_with_room.append(model_name)
            print(f"  STATUS: PLENTY OF ROOM")
        print()
    
    print(f"Summary:")
    print(f"  Models near token limit: {len(models_near_limit)}")
    print(f"  Models with room: {len(models_with_room)}")
    
    if models_near_limit:
        print(f"\nModels that would be blocked in old system: {models_near_limit}")
        print("In new system: These users can still make requests until they exceed the limit")
    
    return models_near_limit, models_with_room

def main():
    print("="*80)
    print("MULTI-MODEL TOKEN TRACKING + RATE LIMIT TEST")
    print("="*80)
    print(f"Timestamp: {datetime.now().isoformat()}")
    print()
    
    # Test multiple models for token tracking
    models_to_test = [
        ("gpt-4.1", "2024-02-01"),
        ("gpt-4.1-mini", "2024-02-01"),
        ("qwen-plus", "2024-02-01"),
        ("qwen-max", "2024-02-01"),
        ("deepseek-v3", "2024-02-01"),
        ("gemini-2.5-flash", "2024-02-01"),
        ("o1", "2024-02-01"),
        ("o3-mini", "2024-02-01"),
    ]
    
    print("1. TESTING TOKEN TRACKING ACROSS MULTIPLE MODELS")
    print("-" * 60)
    
    # Get initial usage
    initial_usage = get_token_usage()
    
    test_results = []
    for model_name, api_version in models_to_test:
        success, total_tokens, prompt_tokens, completion_tokens = test_model_token_tracking(
            model_name, api_version
        )
        test_results.append({
            'model': model_name,
            'success': success,
            'tokens': total_tokens,
            'prompt_tokens': prompt_tokens,
            'completion_tokens': completion_tokens
        })
    
    # Wait for token usage to be recorded
    print(f"\n2. WAITING 10 SECONDS FOR TOKEN USAGE TO BE RECORDED...")
    time.sleep(10)
    
    # Check updated usage
    print("\n3. CHECKING UPDATED TOKEN USAGE")
    print("-" * 60)
    updated_usage = get_token_usage()
    
    successful_tracking = 0
    failed_tracking = 0
    
    for result in test_results:
        if not result['success']:
            continue
            
        model_name = result['model']
        api_tokens = result['tokens']
        
        if model_name in initial_usage and model_name in updated_usage:
            initial_total = initial_usage[model_name]['totalTokensUsed']
            updated_total = updated_usage[model_name]['totalTokensUsed']
            difference = updated_total - initial_total
            
            print(f"{model_name}:")
            print(f"  API reported: {api_tokens} tokens")
            print(f"  DB difference: {difference} tokens")
            
            if difference == api_tokens:
                print(f"  STATUS: PERFECT MATCH")
                successful_tracking += 1
            elif difference > 0:
                print(f"  STATUS: PARTIAL MATCH (close but not exact)")
                successful_tracking += 1
            else:
                print(f"  STATUS: NO UPDATE DETECTED")
                failed_tracking += 1
        else:
            print(f"{model_name}: NOT FOUND in usage data")
            failed_tracking += 1
        print()
    
    # Test rate limiting
    print("4. TESTING RATE LIMIT BLOCKING")
    rate_limit_working = test_rate_limit_blocking()
    
    # Check token limits
    print("\n5. CHECKING TOKEN LIMIT STATUS")
    models_near_limit, models_with_room = test_token_limit_blocking()
    
    # Summary
    print("\n" + "="*80)
    print("TEST SUMMARY")
    print("="*80)
    
    successful_tests = sum(1 for r in test_results if r['success'])
    total_tests = len(test_results)
    
    print(f"Model API Tests: {successful_tests}/{total_tests} successful")
    print(f"Token Tracking: {successful_tracking}/{successful_tests} perfect/good matches")
    print(f"Rate Limiting: {'WORKING' if rate_limit_working else 'NOT WORKING'}")
    print(f"Models tested: {[r['model'] for r in test_results if r['success']]}")
    
    if failed_tracking > 0:
        print(f"WARNING: {failed_tracking} models had token tracking issues")
    
    print(f"\nToken Limit Analysis:")
    print(f"  Models near token limit: {len(models_near_limit)}")
    print(f"  Models with room: {len(models_with_room)}")

if __name__ == "__main__":
    main()