// Common types shared across the web application

export type HtmlFile = { name: string; size: number; url?: string };

// Centralized GptModel type
export type GptModel = {
  display_name: string;
  deployment_name: string;
  model_name: string;
  // Making seq optional to handle potential inconsistencies during loading/fetching
  seq?: number;
  availability_status?: string; // Made availability_status optional (string | undefined)
  supported_file_types?: string[];
  token_limit?: number;
  temperature?: number;
  category?: string; // Added category field
};

// Add other common types here as needed
