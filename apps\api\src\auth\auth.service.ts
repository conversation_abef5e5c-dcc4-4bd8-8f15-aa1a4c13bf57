import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { ApiUserPayload } from './api-key.strategy'; // Assuming ApiUserPayload includes ssoid and dept_unit_code

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Checks if a user, identified by their payload (from API key validation),
   * is authorized to use the RESTful API service.
   * This logic is adapted from the old project's check_enable_restful_api.ts.
   * @param user - The user payload containing ssoid and dept_unit_code.
   * @returns True if authorized, false otherwise.
   */
  async isUserAuthorizedForApiService(user: ApiUserPayload): Promise<boolean> {
    if (!user || !user.ssoid || !user.dept_unit_code) {
      this.logger.warn(
        'Authorization check failed: User payload is incomplete.',
      );
      return false;
    }

    const { ssoid, dept_unit_code } = user;
    this.logger.debug(
      `Performing API authorization check for ssoid: ${ssoid}, dept: ${dept_unit_code}`,
    );

    // Direct allow for specific departments
    const directlyAllowedDepts = ['ITO', 'SCI', 'SCID', 'ARTT', 'HSWB', 'BAST'];
    if (directlyAllowedDepts.includes(dept_unit_code.toUpperCase())) {
      this.logger.log(
        `User ${ssoid} from dept ${dept_unit_code} is directly allowed.`,
      );
      return true;
    }

    try {
      // Check if department reports to SCI
      // Ensure the stored procedure name and parameters match your database schema
      const deptResult = await this.prisma.$queryRaw<
        Array<{ report_unit_code: string }>
      >`EXEC sp_mtr_GetDeptByDeptUnitCode @dept_unit_code=${dept_unit_code}`;

      if (
        deptResult &&
        deptResult.length > 0 &&
        deptResult[0].report_unit_code === 'SCI'
      ) {
        this.logger.log(
          `User ${ssoid} from dept ${dept_unit_code} (reports to SCI) is allowed.`,
        );
        return true;
      }

      // Check if user is in the dedicated API user list
      // Ensure the stored procedure name and parameters match your database schema
      const apiUserResult = await this.prisma.$queryRaw<
        Array<{ ssoid: string }>
      >`EXEC sp_acl_GetApiUserByUsername @username=${ssoid}`; // Assuming username in sp_acl_GetApiUserByUsername corresponds to ssoid

      if (apiUserResult && apiUserResult.length > 0) {
        this.logger.log(
          `User ${ssoid} found in dedicated API user list and is allowed.`,
        );
        return true;
      }

      this.logger.warn(
        `User ${ssoid} from dept ${dept_unit_code} is not authorized for API service.`,
      );
      return false;
    } catch (error) {
      let errorMessage =
        'An unknown error occurred during API authorization check.';
      if (error instanceof Error) {
        errorMessage = error.message;
      }
      this.logger.error(
        `Database error during API authorization for ssoid ${ssoid}: ${errorMessage}`,
        error instanceof Error ? error.stack : undefined,
      );
      return false; // Deny access on error by default
    }
  }
}
