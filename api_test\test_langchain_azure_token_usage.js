/**
 * Test script to verify <PERSON><PERSON><PERSON>n Azure OpenAI token usage extraction
 * This tests the same configuration we implemented in the chat-completion.service.ts
 */

import { ChatOpenAI } from '@langchain/openai';
import { HumanMessage } from '@langchain/core/messages';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
dotenv.config({ path: join(__dirname, '.env') });

async function testNonStreaming() {
  console.log('\n=== Testing Non-Streaming Azure OpenAI with LangChain ===\n');
  
  const model = new ChatOpenAI({
    temperature: 0.7,
    modelName: process.env.AZURE_OPENAI_DEPLOYMENT || 'chatgpt-4.1',
    streamUsage: true, // Enable token usage tracking
    configuration: {
      apiKey: process.env.AZURE_OPENAI_API_KEY,
      baseURL: `https://${process.env.AZURE_OPENAI_INSTANCE}.openai.azure.com/openai/deployments/${process.env.AZURE_OPENAI_DEPLOYMENT}`,
      defaultQuery: { 'api-version': process.env.AZURE_OPENAI_API_VERSION },
    },
    modelKwargs: {
      stream_options: { include_usage: true }, // Request token usage
    },
  });

  try {
    const messages = [new HumanMessage("What is 2+2? Reply with just the number.")];
    const response = await model.invoke(messages);
    
    console.log('Response:', response.content);
    console.log('\nToken Usage Locations:');
    console.log('- usage_metadata:', response.usage_metadata);
    console.log('- response_metadata.tokenUsage:', response.response_metadata?.tokenUsage);
    console.log('- response_metadata.usage:', response.response_metadata?.usage);
    console.log('- response_metadata.llmOutput.usage:', response.response_metadata?.llmOutput?.usage);
    
    // Extract tokens using our enhanced logic
    const usage = 
      response.usage_metadata ?? 
      response.response_metadata?.tokenUsage ??
      response.response_metadata?.usage ??
      response.response_metadata?.llmOutput?.usage ??
      {};
    
    const promptTokens = 
      usage.input_tokens ?? 
      usage.prompt_tokens ?? 
      usage.promptTokenCount ?? 
      0;
    const completionTokens = 
      usage.output_tokens ?? 
      usage.completion_tokens ?? 
      usage.completionTokenCount ?? 
      0;
    
    console.log(`\nExtracted Tokens: prompt=${promptTokens}, completion=${completionTokens}`);
    
  } catch (error) {
    console.error('Error:', error.message);
  }
}

async function testStreaming() {
  console.log('\n=== Testing Streaming Azure OpenAI with LangChain ===\n');
  
  const model = new ChatOpenAI({
    temperature: 0.7,
    modelName: process.env.AZURE_OPENAI_DEPLOYMENT || 'chatgpt-4.1',
    streamUsage: true, // Enable token usage tracking
    configuration: {
      apiKey: process.env.AZURE_OPENAI_API_KEY,
      baseURL: `https://${process.env.AZURE_OPENAI_INSTANCE}.openai.azure.com/openai/deployments/${process.env.AZURE_OPENAI_DEPLOYMENT}`,
      defaultQuery: { 'api-version': process.env.AZURE_OPENAI_API_VERSION },
    },
    modelKwargs: {
      stream_options: { include_usage: true }, // Request token usage
    },
  });

  try {
    const messages = [new HumanMessage("Count from 1 to 5")];
    const stream = await model.stream(messages);
    
    let promptTokens = 0;
    let completionTokens = 0;
    let content = '';
    
    console.log('Streaming chunks:');
    for await (const chunk of stream) {
      if (chunk.content) {
        content += chunk.content;
        process.stdout.write(chunk.content);
      }
      
      // Check for token usage in each chunk
      const usage = 
        chunk.usage_metadata ?? 
        chunk.response_metadata?.tokenUsage ??
        chunk.response_metadata?.usage ??
        {};
      
      if (usage.prompt_tokens > 0) promptTokens = usage.prompt_tokens;
      if (usage.completion_tokens > 0) completionTokens = usage.completion_tokens;
      if (usage.input_tokens > 0) promptTokens = usage.input_tokens;
      if (usage.output_tokens > 0) completionTokens = usage.output_tokens;
    }
    
    console.log(`\n\nFinal Token Count: prompt=${promptTokens}, completion=${completionTokens}`);
    
  } catch (error) {
    console.error('Error:', error.message);
  }
}

async function main() {
  console.log('Testing LangChain Azure OpenAI Token Usage Extraction');
  console.log('====================================================');
  console.log('Configuration:');
  console.log(`- Instance: ${process.env.AZURE_OPENAI_INSTANCE}`);
  console.log(`- Deployment: ${process.env.AZURE_OPENAI_DEPLOYMENT}`);
  console.log(`- API Version: ${process.env.AZURE_OPENAI_API_VERSION}`);
  
  await testNonStreaming();
  await testStreaming();
}

main().catch(console.error);