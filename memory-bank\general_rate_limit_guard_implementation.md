# General Rate Limit Guard Implementation

**Date:** 2025-07-22

## Purpose
Provide a unified 60 requests-per-minute rate limit for all remaining `/general/**` API endpoints that were not already covered by existing guards (`ModelRateLimitGuard`, `PromptSpeechRateLimitGuard`).

## Key Points
1. **Guard Name:** `GeneralRateLimitGuard`.
2. **Scope:** Automatically applied to controllers:
   * `GeneralController`
   * `ChatController` (except `/chat/completions` which is skipped internally)
   * `EmbeddingsController`
   * `PromptGalleryController`
   * `TasksController`
   * `RecentModelsRestController`
   * `RestModelsController`
   * `RateLimitController`
3. **Logic**
   * Limit: `60` requests per `60` seconds per user (fallback to client IP if unauthenticated).
   * Redis key: `rate_limit:general:<identifier>`.
   * Skips URLs already handled by specialised guards: `/prompt-rewrite`, `/speech`, `/chat/completions`, `/rest/deployments`.
   * Sets standard `X-RateLimit-*` headers and fails-open on Redis errors.
4. **Registration:** Added to `CommonModule` providers/exports so it is injectable across the app.
5. **Testing:** Unit test in `src/common/guards/__tests__/general-rate-limit.guard.spec.ts` verifies the 61st request is blocked with HTTP 429.

## Follow-ups
* Monitor production Redis performance; consider moving key TTL constants to config if values change per environment. 