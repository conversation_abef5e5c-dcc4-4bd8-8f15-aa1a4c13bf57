> "./apps/web/.env"
echo "NODE_ENV=\"$NODE_ENV\"" >> "./apps/web/.env"
echo "DATABASE_URL=\"$DATABASE_URL\"" >> "./apps/web/.env"
echo "NEXTAUTH_URL=\"$NEXTAUTH_URL\"" >> "./apps/web/.env"
echo "NEXTAUTH_SECRET=\"$NEXTAUTH_SECRET\"" >> "./apps/web/.env"
echo "NEXT_PUBLIC_BASE_PATH=\"$NEXT_PUBLIC_BASE_PATH\"" >> "./apps/web/.env"
echo "BUAM_OAUTH=\"$BUAM_OAUTH\"" >> "./apps/web/.env"
echo "BUAM_OAUTH_TOKEN=\"$BUAM_OAUTH_TOKEN\"" >> "./apps/web/.env"
echo "BUAM_OAUTH_USERINFO=\"$BUAM_OAUTH_USERINFO\"" >> "./apps/web/.env"
echo "BUAM_OAUTH_CLIENTID=\"$BUAM_OAUTH_CLIENTID\"" >> "./apps/web/.env"
echo "BUAM_OAUTH_CLIENTSECRET=\"$BUAM_OAUTH_CLIENTSECRET\"" >> "./apps/web/.env"
echo "BUAM_NEXTAUTH_SESSION_EXPIRY=\"$BUAM_NEXTAUTH_SESSION_EXPIRY\"" >> "./apps/web/.env"
echo "BUAM_NEXTAUTH_JWT_SECRET=\"$BUAM_NEXTAUTH_JWT_SECRET\"" >> "./apps/web/.env"
echo "NEXT_PUBLIC_API_BASE_URL=\"$NEXT_PUBLIC_API_BASE_URL\"" >> "./apps/web/.env"
echo "NEXT_PUBLIC_DALL_E_ENABLE=\"$NEXT_PUBLIC_DALL_E_ENABLE\"" >> "./apps/web/.env"
echo "NEXT_PUBLIC_CHANGE_THEME_ENABLE=\"$NEXT_PUBLIC_CHANGE_THEME_ENABLE\"" >> "./apps/web/.env"
echo "NEXT_PUBLIC_ADOBE_EXPRESS_ENABLE=\"$NEXT_PUBLIC_ADOBE_EXPRESS_ENABLE\"" >> "./apps/web/.env"
echo "NEXT_PUBLIC_ADOBE_FIREFLY_ENABLE=\"$NEXT_PUBLIC_ADOBE_FIREFLY_ENABLE\"" >> "./apps/web/.env"
echo "NEXT_PUBLIC_RESTFUL_API_ENABLE=\"$NEXT_PUBLIC_RESTFUL_API_ENABLE\"" >> "./apps/web/.env"
echo "NEXT_PUBLIC_REQUEST_QUOTE_ENABLE=\"$NEXT_PUBLIC_REQUEST_QUOTE_ENABLE\"" >> "./apps/web/.env"
echo "NEXT_PUBLIC_TOKEN_USAGE_ENABLE=\"$NEXT_PUBLIC_TOKEN_USAGE_ENABLE\"" >> "./apps/web/.env"
echo "NEXT_PUBLIC_IOS_WEBVIEW_EXPORT_HISTORY_DISABLE=\"$NEXT_PUBLIC_IOS_WEBVIEW_EXPORT_HISTORY_DISABLE\"" >> "./apps/web/.env"
echo "NEXT_PUBLIC_UPLOAD_FILE_SIZE_LIMIT=\"$NEXT_PUBLIC_UPLOAD_FILE_SIZE_LIMIT\"" >> "./apps/web/.env"
echo "NEXT_PUBLIC_ENCRYPTION_KEY_1=\"$NEXT_PUBLIC_ENCRYPTION_KEY_1\"" >> "./apps/web/.env"
echo "NEXT_PUBLIC_ENCRYPTION_KEY_2=\"$NEXT_PUBLIC_ENCRYPTION_KEY_2\"" >> "./apps/web/.env"
echo "NEXT_PUBLIC_ENCRYPTION_KEY_3=\"$NEXT_PUBLIC_ENCRYPTION_KEY_3\"" >> "./apps/web/.env"
echo "NEXT_PUBLIC_ENCRYPTION_KEY_4=\"$NEXT_PUBLIC_ENCRYPTION_KEY_4\"" >> "./apps/web/.env"
echo "NEXT_PUBLIC_ENCRYPTION_KEY_5=\"$NEXT_PUBLIC_ENCRYPTION_KEY_5\"" >> "./apps/web/.env"
echo "NEXT_PUBLIC_STEAM_TIMEOUT=\"$NEXT_PUBLIC_STEAM_TIMEOUT\"" >> "./apps/web/.env"
echo "NEXT_PUBLIC_NO_OF_RETRY=\"$NEXT_PUBLIC_NO_OF_RETRY\"" >> "./apps/web/.env"
echo "NEXT_PUBLIC_WHITELIST_LLM_HEALTH_CHECK=\"$NEXT_PUBLIC_WHITELIST_LLM_HEALTH_CHECK\"" >> "./apps/web/.env"
echo "NEXT_PUBLIC_IOS_WEBVIEW_EXPORT_HISTORY_DISABLE_MESSAGE=\"$NEXT_PUBLIC_IOS_WEBVIEW_EXPORT_HISTORY_DISABLE_MESSAGE\"" >> "./apps/web/.env"
echo "NEXT_PUBLIC_REQUEST_QUOTE_QUOTE_OPTION=\"$NEXT_PUBLIC_REQUEST_QUOTE_QUOTE_OPTION\"" >> "./apps/web/.env"
echo "NEXT_PUBLIC_REQUEST_QUOTE_DURATION_OPTION=\"$NEXT_PUBLIC_REQUEST_QUOTE_DURATION_OPTION\"" >> "./apps/web/.env"
echo "REQUEST_QUOTE_EMAIL=\"$REQUEST_QUOTE_EMAIL\"" >> "./apps/web/.env"
echo "NEXT_PUBLIC_REQUEST_QUOTE_TITLE=\"$NEXT_PUBLIC_REQUEST_QUOTE_TITLE\"" >> "./apps/web/.env"
echo "NEXT_PUBLIC_REQUEST_QUOTE_MESSAGE=\"$NEXT_PUBLIC_REQUEST_QUOTE_MESSAGE\"" >> "./apps/web/.env"
echo "NEXT_PUBLIC_REQUEST_QUOTE_SUCCESS_MESSAGE=\"$NEXT_PUBLIC_REQUEST_QUOTE_SUCCESS_MESSAGE\"" >> "./apps/web/.env"
echo "NEXT_PUBLIC_SCRAPE_URL_LIMIT=\"$NEXT_PUBLIC_SCRAPE_URL_LIMIT\"" >> "./apps/web/.env"
echo "NEXT_PUBLIC_SUBMISSION_FLOW_TIMEOUT=\"$NEXT_PUBLIC_SUBMISSION_FLOW_TIMEOUT\"" >> "./apps/web/.env"
echo "NEXT_PUBLIC_MAX_TOKEN_PROMPT=\"$NEXT_PUBLIC_MAX_TOKEN_PROMPT\"" >> "./apps/web/.env"
echo "NEXT_PUBLIC_HIGH_TRAFFIC_MODELS=\"$NEXT_PUBLIC_HIGH_TRAFFIC_MODELS\"" >> "./apps/web/.env"
echo "NEXT_PUBLIC_HIGH_TRAFFIC_MODELS_MESSAGE=\"$NEXT_PUBLIC_HIGH_TRAFFIC_MODELS_MESSAGE\"" >> "./apps/web/.env"
echo "NEXT_PUBLIC_GOOGLE_MEASUREMENT_ID=\"$NEXT_PUBLIC_GOOGLE_MEASUREMENT_ID\"" >> "./apps/web/.env"
echo "SENTRY_DSN=\"$SENTRY_DSN\"" >> "./apps/web/.env"
echo "NEXT_PUBLIC_SENTRY_DSN=\"$NEXT_PUBLIC_SENTRY_DSN\"" >> "./apps/web/.env"
echo "SENTRY_AUTH_TOKEN=\"$SENTRY_AUTH_TOKEN\"" >> "./apps/web/.env"
echo "ANALYZE=\"$ANALYZE\"" >> "./apps/web/.env"
echo "HTTPS_AGENT_PORT=\"$HTTPS_AGENT_PORT\"" >> "./apps/web/.env"
