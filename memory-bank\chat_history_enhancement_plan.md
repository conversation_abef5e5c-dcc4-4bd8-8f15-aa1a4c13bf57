# Chat History Enhancement Plan

**Overall Goal:** Modify the chat history display to show only the recent 10 chats in the sidebar (grouped by specific dates), with a "View All" button leading to a new paginated page displaying all chat history. Both views will show chat title, LLM model icon, and the date of the chat in DD/MM/YYYY format.

**Pre-requisite: Backend API Changes**

*   The backend API endpoint (assumed to be `/general/conversations/history`, or a new one like `/general/conversations/list`) will be updated to:
    1.  Return a **flat list** of conversation items.
    2.  Each item in the list must include:
        *   `id: string` (conversation_uuid)
        *   `title: string`
        *   `model: string | null` (model_name used for the conversation)
        *   `updated_at: string` (ISO 8601 timestamp string, e.g., "2025-06-06T10:30:00Z")
    3.  The endpoint must support **pagination** via query parameters (e.g., `page: number`, `limit: number`).
    4.  The endpoint must support **sorting**, defaulting to `updated_at` in descending order (most recent first).

---

**Phase 1: Frontend - API Slice Adaptation**

**File to Modify:** `hkbu-genai-platform/apps/web/src/lib/store/apiSlice.ts`

1.  **Define `ConversationHistoryItem` and `PaginatedConversationHistoryResponse` Types:**
    *   Add new interfaces:
    ```typescript
    // Add near other type definitions
    export interface ConversationHistoryItem {
      id: string;
      title: string;
      model: string | null;
      updated_at: string; // ISO Date string
    }

    export interface PaginatedConversationHistoryResponse {
      items: ConversationHistoryItem[];
      totalItems: number;
      totalPages: number;
      currentPage: number;
    }
    ```

2.  **Modify `getConversationHistoryQuery`:**
    *   Change query arguments to: `{ page: number; limit: number }`.
    *   Update return type to `PaginatedConversationHistoryResponse`.
    *   Adjust the `query` function for pagination and sorting.
    ```typescript
    // Inside apiSlice endpoints:
    getConversationHistory: builder.query<PaginatedConversationHistoryResponse, { page: number; limit: number }>({
        query: ({ page, limit }) => ({
            url: `general/conversations/history?page=${page}&limit=${limit}&sort_by=updated_at&order=desc`, // Example URL
            method: 'GET',
        }),
        providesTags: (result) =>
            result?.items
                ? [
                    ...result.items.map(({ id }) => ({ type: 'History' as const, id })),
                    { type: 'History', id: 'LIST' },
                  ]
                : [{ type: 'History', id: 'LIST' }],
        // transformResponse might be needed if backend response structure differs.
    }),
    ```

---

**Phase 2: Frontend - Sidebar Modifications**

**File to Modify:** `hkbu-genai-platform/apps/web/src/components/genai/ChatHistorySidebar.tsx`

1.  **Import:** `ConversationHistoryItem`, `Button`, date formatting utility.

2.  **Fetch Recent History:**
    ```typescript
    const { data: historyData, isLoading, isError } = useGetConversationHistoryQuery({ page: 1, limit: 10 });
    const recentHistoryItems = historyData?.items;
    ```

3.  **Group by Date (Client-Side):**
    *   Implement `groupHistoryByDate` function to group `recentHistoryItems` by "DD/MM/YYYY" from `updated_at`.
    *   Sort these date groups chronologically (most recent first).
    ```typescript
    const groupHistoryByDate = (items: ConversationHistoryItem[] | undefined) => {
        if (!items) return {};
        return items.reduce((acc, item) => {
            const date = new Date(item.updated_at);
            const formattedDate = `${String(date.getDate()).padStart(2, '0')}/${String(date.getMonth() + 1).padStart(2, '0')}/${date.getFullYear()}`;
            if (!acc[formattedDate]) {
                acc[formattedDate] = [];
            }
            acc[formattedDate].push(item);
            return acc;
        }, {} as Record<string, ConversationHistoryItem[]>);
    };

    const groupedRecentHistory = groupHistoryByDate(recentHistoryItems);
    const sortedDateGroups = Object.keys(groupedRecentHistory).sort((a, b) => {
        const dateA = new Date(a.split('/').reverse().join('-'));
        const dateB = new Date(b.split('/').reverse().join('-'));
        return dateB.getTime() - dateA.getTime();
    });
    ```

4.  **Render Grouped History:**
    *   Iterate `sortedDateGroups` then `groupedRecentHistory[dateGroup]`.
    *   Display date group headers.
    *   Render individual chat items (title, model icon, link to `/chat/[item.id]`).
    *   Add a "View All" button linking to `/chat/history` below the list.
    ```jsx
    {/* ... existing expanded check ... */}
    {isLoading ? ( /* ... */ ) : isError ? ( /* ... */ ) : recentHistoryItems && recentHistoryItems.length > 0 ? (
        sortedDateGroups.map((dateGroup) => (
            <Box key={dateGroup} sx={{ mb: 2 }}>
                {/* Date Group Header */}
                {/* ... */}
                {!collapsedGroups[dateGroup] && (
                    <List dense>
                        {groupedRecentHistory[dateGroup].map((item: ConversationHistoryItem) => (
                            <Link href={`/chat/${item.id}`} key={item.id} passHref legacyBehavior>
                                {/* ListItemButton with Icon, Title */}
                            </Link>
                        ))}
                    </List>
                )}
            </Box>
        ))
    ) : ( /* No recent history message */ )}

    {/* "View All" Button */}
    {recentHistoryItems && recentHistoryItems.length > 0 && (
         <Link href="/chat/history" passHref legacyBehavior>
            <ListItemButton sx={{ mt: 2, /* ... styling ... */ }}>
                <ListItemText primary="View All" /* ... */ />
            </ListItemButton>
        </Link>
    )}
    {/* ... */}
    ```

---

**Phase 3: Frontend - New Chat History Page**

**File to Create:** `hkbu-genai-platform/apps/web/src/app/chat/history/page.tsx`

1.  **Component `ChatHistoryPage`:**
    *   Use `'use client';`.
    *   Import React hooks, Next.js `Link`, MUI components (`Container`, `Paper`, `Typography`, `List`, `ListItemButton`, `ListItemIcon`, `ListItemText`, `CircularProgress`, `Pagination`).
    *   Import `useGetConversationHistoryQuery`, `ConversationHistoryItem` from `apiSlice.ts`.
    *   Import `modelInfo`.
    *   Define `formatDate` helper and `ITEMS_PER_PAGE` constant.

2.  **State and Data Fetching:**
    ```typescript
    const [currentPage, setCurrentPage] = useState(1);
    const { data: historyPageData, isLoading, isError, isFetching } =
        useGetConversationHistoryQuery({ page: currentPage, limit: ITEMS_PER_PAGE });

    const handlePageChange = (event: React.ChangeEvent<unknown>, value: number) => {
        setCurrentPage(value);
    };
    ```

3.  **Rendering Logic:**
    *   Main layout with `Container` and `Paper`.
    *   Page title "Chat History".
    *   Loading and error states.
    *   If data exists:
        *   Map `historyPageData.items` to `ListItemButton` components.
        *   Each item displays:
            *   Model Icon (using `getModelIcon` helper similar to sidebar).
            *   Chat Title.
            *   Formatted Date (`formatDate(item.updated_at)`).
            *   Link to `/chat/${item.id}`.
        *   Display MUI `Pagination` component if `historyPageData.totalPages > 1`.
    *   "No chat history found" message if applicable.

    ```jsx
    // Simplified JSX structure
    export default function ChatHistoryPage() {
        // ... state and hooks ...

        // ... getModelIcon helper ...

        // ... formatDate helper ...

        return (
            <Container maxWidth="md" sx={{ py: 4 }}>
                <Paper elevation={3} sx={{ p: 3 }}>
                    <Typography variant="h4" component="h1" gutterBottom>Chat History</Typography>
                    {/* Loading/Error States */}
                    {!isLoading && !isError && historyPageData && historyPageData.items.length > 0 && (
                        <>
                            <List>
                                {historyPageData.items.map((item: ConversationHistoryItem) => (
                                    <Link href={`/chat/${item.id}`} key={item.id} passHref legacyBehavior>
                                        <ListItemButton /* ... */>
                                            <ListItemIcon /* ... */>{getModelIcon(item.model)}</ListItemIcon>
                                            <ListItemText
                                                primary={item.title || "Untitled Chat"}
                                                secondary={`Date: ${formatDate(item.updated_at)}`}
                                            />
                                        </ListItemButton>
                                    </Link>
                                ))}
                            </List>
                            {historyPageData.totalPages > 1 && (
                                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
                                    <Pagination
                                        count={historyPageData.totalPages}
                                        page={currentPage}
                                        onChange={handlePageChange}
                                        color="primary"
                                        disabled={isFetching}
                                    />
                                </Box>
                            )}
                        </>
                    )}
                    {/* No history message */}
                </Paper>
            </Container>
        );
    }
    ```

---
**Mermaid Diagram of Proposed Component Interaction:**

```mermaid
graph TD
    subgraph Backend API
        direction LR
        API_Endpoint["/general/conversations/history (Modified)"]
        API_Endpoint -- Returns Paginated & Sorted --> FlatList[Flat List of Conversations (id, title, model, updated_at, pagination_meta)]
    end

    subgraph Frontend
        direction LR
        Sidebar[ChatHistorySidebar.tsx] -- Fetches (page=1, limit=10) --> RTK_Query_Sidebar[useGetConversationHistoryQuery]
        RTK_Query_Sidebar -- Calls --> API_Endpoint
        RTK_Query_Sidebar -- Returns --> Sidebar_Data[PaginatedResponse (Recent 10 Items with updated_at)]
        Sidebar -- Processes items, Groups by DD/MM/YYYY --> Display_Sidebar[Display Recent 10 (Grouped by Date)]
        Sidebar -- Contains --> ViewAllButton["View All" Button (Link to /chat/history)]

        HistoryPage[/chat/history/page.tsx] -- Navigated to by --> ViewAllButton
        HistoryPage -- Fetches (page=N, limit=M) --> RTK_Query_HistoryPage[useGetConversationHistoryQuery]
        RTK_Query_HistoryPage -- Calls --> API_Endpoint
        RTK_Query_HistoryPage -- Returns --> HistoryPage_Data[PaginatedResponse (Items with updated_at, totalPages)]
        HistoryPage -- Displays --> Display_HistoryPage[All Chats (Title, Model Icon, DD/MM/YYYY Date)]
        HistoryPage -- Includes --> PaginationControls[Pagination UI (uses totalPages)]

        ModelInfo[modelInfo.tsx] -- Provides Icon Mapping --> Sidebar
        ModelInfo -- Provides Icon Mapping --> HistoryPage
    end