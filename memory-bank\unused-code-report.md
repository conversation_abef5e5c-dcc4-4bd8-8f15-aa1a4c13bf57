# Unused Code Report (hkbu-genai-platform)

This report lists code artifacts identified as potentially unused in the `apps/api` (NestJS) and `apps/web` (Next.js) applications within the `hkbu-genai-platform` monorepo. These items were flagged by `ts-prune` and manually verified by searching for imports or usage.

**Please review this list carefully before approving deletion.** Some items might be used dynamically or through means not detected by static analysis.

---

## Backend (`hkbu-genai-platform/apps/api`)

| File                                       | Type     | Name             | Notes                                  |
| :----------------------------------------- | :------- | :--------------- | :------------------------------------- |
| `src/utils/prompt-processing.ts`           | Function | `removeAtSymbol` | Defined but not imported or called.    |

---

## Frontend (`hkbu-genai-platform/apps/web`)

| File                                                     | Type              | Name                       | Notes                               |
| :------------------------------------------------------- | :---------------- | :------------------------- | :---------------------------------- |
| `src/components/ModelSelectionCard.tsx`                  | Component         | `ModelSelectionCard`       | Defined but not imported or used.   |
| `src/components/chat/ChatHistoryPanel.tsx`               | Component         | `ChatHistoryPanel`         | Defined but not imported or used.   |
| `src/components/genai/ChatInterface.tsx`                 | Component         | `ChatInterface`            | Defined but not imported or used.   |
| `src/components/genai/HomeScreen.tsx`                    | Component         | `HomeScreen`               | Defined but not imported or used.   |
| `src/lib/utils/ConvertCodeSnippet.tsx`                   | Utility Component | `ConvertCodeSnippet`       | Defined but not imported or used.   |
| `src/lib/utils/ConvertHyperLink.tsx`                     | Utility Component | `ConvertHyperLink`         | Defined but not imported or used.   |
| `src/lib/utils/ConvertMarkdown.tsx`                      | Utility Function  | `ConvertMarkdown`          | Defined but not imported or called. |
| `src/lib/utils/ConvertTable.tsx`                         | Utility Function  | `ConvertTable`             | Defined but not imported or called. |
| `src/lib/utils/ConvertTextStyle.tsx`                     | Utility Function  | `ConvertTextStyle`         | Defined but not imported or called. |
| `src/lib/utils/FileToString.ts`                          | Utility File      | `FileToString.ts`          | Placeholder file, not implemented.  |
| `src/lib/utils/renderingUtils.ts`                        | Utility Function  | `processMessageContent`    | Defined but not imported or called. |
| `src/lib/utils/renderingUtils.ts`                        | Utility Function  | `removeClassNames`         | Defined but not imported or called. |
| `src/lib/utils/renderingUtils.ts`                        | Utility Function  | `setupCodeCopy`            | Defined but not imported or called. |
| `src/lib/utils/SubmitLike.ts`                            | Utility Function  | `submitLike`               | Defined but not imported or called. |
| `src/components/genai/controls/ChangeModeButton.tsx`     | Component         | `ChangeModeButton`         | Defined but not imported or used.   |
| `src/components/genai/controls/CopyButton.tsx`           | Component         | `CopyButton`               | Defined but not imported or used.   |
| `src/components/genai/controls/TemperatureSlider.tsx`    | Component         | `TemperatureSlider`        | Defined but not imported or used.   |
| `src/components/genai/forms/DragOverForm.tsx`            | Component         | `DragOverForm`             | Defined but not imported or used.   |
| `src/components/genai/forms/InputBox.tsx`                | Component         | `InputBox`                 | Defined but not imported or used.   |
| `src/components/genai/modals/AccessDeniedModal.tsx`      | Component         | `AccessDeniedModal`        | Defined but not imported or used.   |
| `src/components/genai/modals/ChatErrorModal.tsx`         | Component         | `ChatErrorModal`           | Defined but not imported or used.   |
| `src/components/genai/modals/DesignatedHkbuStaffModal.tsx` | Component         | `DesignatedHkbuStaffModal` | Defined but not imported or used.   |
| `src/components/genai/modals/RequestQuoteSuccessModal.tsx` | Component         | `RequestQuoteSuccessModal` | Defined but not imported or used.   |
| `src/components/genai/model/ModelSelectDropDown.tsx`     | Component         | `ModelSelectDropDown`      | Defined but not imported or used.   |
| `src/components/genai/model/ModelSelectMenu.tsx`         | Component         | `ModelSelectMenu`          | Defined but not imported or used.   |

---

**Next Steps:**

1.  Review the items listed above.
2.  Confirm which items are safe to delete.
3.  Proceed with deletion (Step 5 of the plan).