#!/usr/bin/env node

/**
 * Simple Qwen Token Test Runner with File Logging
 */

import dotenv from 'dotenv';
import { ChatOpenAI } from '@langchain/openai';
import { HumanMessage } from '@langchain/core/messages';
import fs from 'fs';

// Load environment variables
dotenv.config({ path: '../apps/api/.env' });

// Setup logging
const logFile = 'qwen-test-results.log';
const logStream = fs.createWriteStream(logFile, { flags: 'w' });

function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}`;
  console.log(logMessage);
  logStream.write(logMessage + '\n');
}

// Test configuration
const qwenEndpoint = process.env.ALIBABA_MODELSTUDIO_ENDPOINT?.replace(/\/$/, '') + '/compatible-mode/v1';
const qwenApiKey = process.env.ALIBABA_MODELSTUDIO_API_KEY;

log('🧪 Starting Qwen Token Test');
log(`Endpoint: ${qwenEndpoint}`);
log(`API Key Available: ${qwenApiKey ? 'YES' : 'NO'}`);

if (!qwenApiKey || !qwenEndpoint || qwenEndpoint.includes('undefined')) {
  log('❌ Missing required environment variables');
  logStream.end();
  process.exit(1);
}

async function testQwenStreaming() {
  try {
    log('\n📡 Testing Qwen Streaming with Token Usage');
    
    const chatModel = new ChatOpenAI({
      temperature: 0.7,
      modelName: 'qwen-max',
      streaming: true,
      streamUsage: true,
      configuration: {
        apiKey: qwenApiKey,
        baseURL: qwenEndpoint,
      },
      modelKwargs: {
        stream_options: { include_usage: true },
      },
    });

    const message = new HumanMessage('Hello! Please respond briefly.');
    const stream = await chatModel.stream([message]);
    
    let chunkCount = 0;
    let tokensFound = false;
    let content = '';
    
    for await (const chunk of stream) {
      chunkCount++;
      log(`\n--- Chunk ${chunkCount} ---`);
      log(`Chunk keys: ${Object.keys(chunk).join(', ')}`);
      log(`Content: ${chunk.content || '[no content]'}`);
      
      if (chunk.content) {
        content += chunk.content;
      }
      
      // Check for token usage
      const usage = chunk?.usage_metadata ?? chunk?.response_metadata?.usage;
      if (usage) {
        log(`🎯 TOKENS FOUND: ${JSON.stringify(usage)}`);
        tokensFound = true;
      }
      
      if (chunk.response_metadata) {
        log(`Response metadata: ${JSON.stringify(chunk.response_metadata)}`);
      }
    }
    
    log(`\n✅ Streaming completed`);
    log(`Total chunks: ${chunkCount}`);
    log(`Content: "${content}"`);
    log(`Tokens found: ${tokensFound ? 'YES' : 'NO'}`);
    
  } catch (error) {
    log(`❌ Test failed: ${error.message}`);
    log(`Stack: ${error.stack}`);
  }
}

async function testDirectQwenAPI() {
  try {
    log('\n🔗 Testing Direct Qwen API');
    
    const response = await fetch(`${qwenEndpoint}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${qwenApiKey}`,
      },
      body: JSON.stringify({
        model: 'qwen-max',
        messages: [{ role: 'user', content: 'Hello! Please respond briefly.' }],
        temperature: 0.7,
        stream: true,
        stream_options: { include_usage: true },
      }),
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      log(`❌ API request failed: ${response.status} ${errorText}`);
      return;
    }
    
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let chunkCount = 0;
    let usageFound = false;
    let content = '';
    
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      
      const chunk = decoder.decode(value, { stream: true });
      const lines = chunk.split('\n').filter(line => line.trim());
      
      for (const line of lines) {
        if (!line.startsWith('data:')) continue;
        
        const data = line.slice(5).trim();
        if (data === '[DONE]') continue;
        
        try {
          const parsed = JSON.parse(data);
          chunkCount++;
          
          log(`\n--- Direct API Chunk ${chunkCount} ---`);
          log(`Chunk: ${JSON.stringify(parsed, null, 2)}`);
          
          const deltaContent = parsed.choices?.[0]?.delta?.content;
          if (deltaContent) {
            content += deltaContent;
          }
          
          if (parsed.usage) {
            log(`🎯 DIRECT API TOKENS FOUND: ${JSON.stringify(parsed.usage)}`);
            usageFound = true;
          }
          
        } catch (parseError) {
          log(`Parse error: ${parseError.message}`);
        }
      }
    }
    
    log(`\n✅ Direct API completed`);
    log(`Total chunks: ${chunkCount}`);
    log(`Content: "${content}"`);
    log(`Usage found: ${usageFound ? 'YES' : 'NO'}`);
    
  } catch (error) {
    log(`❌ Direct API test failed: ${error.message}`);
  }
}

// Run tests
async function runAllTests() {
  await testQwenStreaming();
  await testDirectQwenAPI();
  
  log('\n🏁 All tests completed');
  log(`Results saved to: ${logFile}`);
  logStream.end();
}

runAllTests().catch((error) => {
  log(`💥 Test suite failed: ${error.message}`);
  logStream.end();
  process.exit(1);
});