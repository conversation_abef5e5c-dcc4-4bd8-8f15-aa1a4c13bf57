'use client';
import { CSSProperties, Fragment } from 'react';
import { Dialog, Transition, Tab } from '@headlessui/react';
import { useSession } from 'next-auth/react';
import { Conversation } from '@/lib/store/chatSlice'; // Corrected import path
import CollapsibleContainer from '../ui/CollapsibleContainer'; // Import the actual component
import { useAppSelector } from '@/lib/store/hooks'; // Use path alias
import { Theme, selectTheme } from '@/lib/store/uiSlice'; // Use path alias

interface ConversationContainerProps {
  conversation: Conversation[];
  // Add currentTheme prop if needed inside ConversationContainer
}

const ConversationContainer: React.FC<ConversationContainerProps> = ({
  conversation,
}) => {
  return (
    <div className="border border-gray-300 my-2 p-4 rounded-lg">
      <ul className="lg:mx-auto lg:max-w-6xl flex grow flex-col w-full space-y-6 row overflow-hidden px-2">
        {conversation.map((convo, index) => (
          <li className="grow-0 relative" key={index}>
            <div
              className="absolute -bottom-[4px] w-10 h-7 z-[1] overflow-hidden"
              style={convo.role === 'user' ? { right: -8 } : { left: -8 }}
            >
              {/* Use Theme value from props or Redux */}
              <div
                className={`absolute w-20 h-20 rounded-full bottom-0 ${
                  convo.role === 'user'
                    ? 'left-0 bg-[#115e59]' // User message background
                    : 'right-0' // Assistant message background depends on theme
                }`}
                style={{
                  background:
                    convo.role !== 'user'
                      ? localStorage.theme === Theme.dark
                        ? '#595959'
                        : '#cfd7e1' // TODO: Replace localStorage with theme from Redux
                      : undefined,
                }}
              />
              <div
                className="absolute w-40 h-40 bg-[#ffffff] dark:bg-[#3B3B3B] rounded-full -bottom-[33px]"
                style={convo.role === 'user' ? { left: 23 } : { right: 25 }}
              />
            </div>
            <div
              className={`${
                convo.role === 'user'
                  ? 'place-self-end flex-row-reverse'
                  : 'place-self-start flex-row'
              } group flex items-center gap-6 space-y-2 w-full`}
            >
              <div
                className={`${
                  convo.role === 'user'
                    ? 'pt-4 bg-gradient-to-b from-emerald-700 to-teal-800 text-white'
                    : 'pt-4 bg-slate-100 dark:bg-[#595959]'
                } py-3 px-6 overflow-hidden max-w-10/12 rounded-[2rem] z-[2]`}
              >
                <pre
                  className="whitespace-pre-line m-0"
                  style={{ wordWrap: 'break-word' }}
                >
                  {convo.content}
                </pre>
              </div>
            </div>
          </li>
        ))}
      </ul>
    </div>
  );
};

// Define props for PromptWithTryNow
interface PromptWithTryNowProps {
  text: string;
  prompt?: string | null; // Allow null as per default value
  closeFn: (prompt?: string | null) => void; // Match type in PromptEngModalProps
}

const PromptWithTryNow: React.FC<PromptWithTryNowProps> = ({
  text,
  prompt = null,
  closeFn,
}) => {
  const lines = typeof text === 'string' ? text.split('<br>') : [];

  // Determine the argument for closeFn. If prompt exists, use it; otherwise, use text (which is a string).
  // If text itself could be considered nullish in some edge case, provide a fallback.
  // However, based on usage, `prompt ? prompt : text` should yield string | null.
  const promptArg = prompt ?? text; // Use nullish coalescing for clarity if prompt is null/undefined

  return (
    <div className="border border-gray-300 my-2 p-4 rounded-lg flex justify-between items-center">
      <div className="pl-1 font-light text-sm leading-normal tracking-wide">
        {lines.map((line, index) => (
          <Fragment key={index}>
            {line}
            <br />
          </Fragment>
        ))}
      </div>
      <button
        type="button"
        className="inline-flex mx-1 justify-center rounded-2xl bg-white dark:bg-[#3b3b3b] px-3 py-2 text-sm font-semibold text-gray-900 dark:text-white shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
        aria-label="Try now"
        onClick={() => closeFn(promptArg)} // Pass the determined argument
      >
        Try Now
      </button>
    </div>
  );
};

// Removed placeholder component definition
function classNames(...classes: (string | boolean | undefined)[]) {
  return classes.filter(Boolean).join(' ');
}

// Define props interface
interface PromptEngModalProps {
  show: boolean;
  onClose: () => void; // Passed from parent, likely dispatches closePromptEngModal
  closeFn: (prompt?: string | null) => void; // Function to close modal and possibly send prompt
  // sdPromptFn: (prompt: string) => void; // Removed unused prop
}

const PromptEngModal = ({
  show,
  onClose,
  closeFn /* Removed sdPromptFn */,
}: PromptEngModalProps) => {
  const { data: session } = useSession();
  const currentTheme = useAppSelector(selectTheme); // Get theme from Redux

  // Add null check for session
  const isStudent = session?.user?.type !== 'STAFF';

  const titleStyle: CSSProperties = {
    fontWeight: 'bold',
  };
  const hyperLinkStyle: CSSProperties = {
    // Use theme from Redux state
    color: currentTheme === Theme.dark ? '#4B8CFF' : '#0000EE',
  };
  return (
    <Transition.Root show={show}>
      {/* Use onClose for Dialog */}
      <Dialog as="div" className="relative z-30" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 dark:bg-[#4a4a4aa0] transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          {/* Use onClose for the backdrop click */}
          <div
            className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0"
            // onClick={onClose} // This closes the modal when clicking outside Dialog.Panel, might be too broad? Keep original behavior? Let's remove for now.
          >
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white dark:bg-[#3b3b3b] text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-3xl">
                <div className="bg-white dark:bg-[#3b3b3b] px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
                  <div className="sm:flex sm:items-start">
                    <div className="mt-3 text-left sm:ml-4 sm:mt-0 sm:text-left">
                      <Dialog.Title
                        as="h3"
                        className="text-lg font-semibold leading-6 text-gray-900 dark:text-white mb-4"
                      >
                        Prompt Engineering & Temperature
                      </Dialog.Title>

                      <Tab.Group>
                        <Tab.List className="flex space-x-1 rounded-xl bg-blue-900/20 p-1 dark:bg-[#595959]">
                          <Tab
                            className={({ selected }) =>
                              classNames(
                                'w-full rounded-lg py-2.5 text-sm font-bold leading-5',

                                selected
                                  ? 'bg-white dark:bg-[#3b3b3b] text-blue-700 dark:text-[#4f81ff] shadow'
                                  : 'text-white-100 hover:bg-white/[0.12] hover:text-white dark:text-white/70 dark:hover:text-white',
                              )
                            }
                          >
                            Prompt Engineering
                          </Tab>
                          <Tab
                            className={({ selected }) =>
                              classNames(
                                'w-full rounded-lg py-2.5 text-sm font-bold leading-5',

                                selected
                                  ? 'bg-white dark:bg-[#3b3b3b] text-blue-700 dark:text-[#4f81ff] shadow'
                                  : 'text-white-100 hover:bg-white/[0.12] hover:text-white dark:text-white/70 dark:hover:text-white',
                              )
                            }
                          >
                            Temperature
                          </Tab>
                        </Tab.List>
                        <Tab.Panels>
                          <Tab.Panel className="rounded-xl bg-white dark:bg-[#3b3b3b] dark:text-white p-1 text-sm">
                            <div>
                              <p style={titleStyle}>
                                <u>Introduction</u>
                              </p>
                              <p>
                                The concept of prompt engineering might sound
                                complicated when you first hear about it.
                                However, at its core, it&apos;s about crafting
                                questions or statements that guide AI in
                                providing the answers or outcomes you&apos;re
                                seeking.
                              </p>
                            </div>

                            <div className="mt-4">
                              <p style={titleStyle}>
                                <u>
                                  Here introduces some common prompting
                                  techniques:
                                </u>
                              </p>
                              <p>
                                The following techniques are applicable to any
                                language models.
                              </p>
                              <ol className="pl-3 list-decimal">
                                {isStudent ? (
                                  <div className="mb-4">
                                    <li style={titleStyle} className="pt-3">
                                      Draft an email
                                    </li>

                                    <p>
                                      Application: Create a professional email
                                      template for professors
                                    </p>

                                    <PromptWithTryNow
                                      text="I’m a student in your [Course Name] class. Please help me craft a professional email to Professor [Professor's Name] to ask for an extension on the upcoming [Assignment Name] due on [Date]. Explain that I am struggling with [Reason for needing an extension] and would appreciate the opportunity to submit the assignment by [Proposed New Due Date]."
                                      prompt="I’m a student in your [Course Name] class. Please help me craft a professional email to Professor [Professor's Name] to ask for an extension on the upcoming [Assignment Name] due on [Date]. Explain that I am struggling with [Reason for needing an extension] and would appreciate the opportunity to submit the assignment by [Proposed New Due Date]."
                                      closeFn={closeFn} // Use closeFn from props, not sdPromptFn directly
                                    />
                                  </div>
                                ) : (
                                  <div className="mb-4">
                                    <li style={titleStyle} className="pt-3">
                                      Draft an email
                                    </li>

                                    <p>
                                      Application: Informing students about an
                                      event cancellation{' '}
                                    </p>

                                    <PromptWithTryNow
                                      text="Generate an email to inform students in [Course Name] that the scheduled [Event Name] on [Date] has been cancelled due to [Reason for cancellation]. Provide alternative options for students to [Alternative action/information]."
                                      prompt="Generate an email to inform students in [Course Name] that the scheduled [Event Name] on [Date] has been cancelled due to [Reason for cancellation]. Provide alternative options for students to [Alternative action/information]."
                                      closeFn={closeFn} // Use closeFn from props
                                    />
                                  </div>
                                )}
                                <div className="mb-4">
                                  <li style={titleStyle} className="pt-3">
                                    Zero-Shot Chain of Thought (Detailed in{' '}
                                    <a
                                      style={hyperLinkStyle}
                                      href="https://arxiv.org/pdf/2211.01910.pdf"
                                      target="_blank"
                                    >
                                      this research
                                    </a>
                                    )
                                  </li>
                                  <p>
                                    Application: Enhances AI&apos;s reasoning
                                    ability{' '}
                                  </p>

                                  <PromptWithTryNow
                                    text='Including the phrase "Let’s think step by step" in prompts for reasoning tasks can significantly improve performance'
                                    prompt="Let’s think step by step, [Your Question]"
                                    closeFn={closeFn} // Use closeFn from props
                                  />

                                  <CollapsibleContainer title="Use Case: Simple Math Calculation">
                                    <ConversationContainer
                                      conversation={[
                                        {
                                          role: 'user',
                                          content:
                                            'What is the sum of 5,3,17,15,13,51 then multiply the result with 60? Just output the answer directly',
                                        },
                                        { role: 'assistant', content: '2280' },
                                        {
                                          role: 'user',
                                          content:
                                            'Think it again step by step',
                                        },
                                        {
                                          role: 'assistant',
                                          content:
                                            'I apologize for the error in my first response. Sum of the numbers is 104. Multiply the sum by 60 = 6240',
                                        },
                                      ]}
                                    />
                                  </CollapsibleContainer>
                                </div>
                                <div className="mb-4">
                                  <li style={titleStyle} className="pt-3">
                                    In-Context Learning (Detailed in{' '}
                                    <a
                                      style={hyperLinkStyle}
                                      href="https://arxiv.org/pdf/2005.14165.pdf"
                                      target="_blank"
                                    >
                                      this research
                                    </a>
                                    )
                                  </li>
                                  <p>
                                    Overview: Guides AI with example inputs and
                                    expected outputs
                                  </p>
                                  <p>
                                    Application: When information cannot be
                                    found in AI’s knowledge base
                                  </p>

                                  <PromptWithTryNow
                                    text="Framework:<br>[Introduction] A brief explanation of the task<br>
                              [Examples] A few examples showing the input followed by the expected output<br>
                               [Instructions] Clear instructions on what the model is expected to do
                               "
                                    prompt="Task: [Your Task Description]\nExamples:\n[A Few Input and Output Examples]\nInstructions:\n[A Clear Instruction, e.g. Label this sentence]"
                                    closeFn={closeFn} // Use closeFn from props
                                  />
                                  <CollapsibleContainer title="Use Case: Sentiment Analysis">
                                    <ConversationContainer
                                      conversation={[
                                        {
                                          role: 'user',
                                          content:
                                            "Task: Classify the sentiment of the text (Positive / Negative)\n\nExamples\nExample 1:\nText: I had an amazing day at the park with my friends.\nSentiment: Positive\n\nExample 2:\nText: The weather today is gloomy and it's making me feel sad.\nSentiment: Negative\n\nExpected output: You shall only return the result (Positive/Negative)\n\nText: The movie was a breathtaking rollercoaster of emotions, beautifully crafted.\nSentiment: ?",
                                        },
                                        {
                                          role: 'assistant',
                                          content: 'Positive',
                                        },
                                      ]}
                                    />
                                  </CollapsibleContainer>
                                </div>
                                <div className="mb-4">
                                  <li style={titleStyle} className="pt-3">
                                    Role Assignment Using the RTF Framework
                                    (Learn more{' '}
                                    <a
                                      style={hyperLinkStyle}
                                      href="https://www.thepromptwarrior.com/p/5-prompt-frameworks-level-prompts"
                                      target="_blank"
                                    >
                                      here
                                    </a>
                                    )
                                  </li>
                                  <p>
                                    Overview: Assign specific roles to the
                                    language model to tailor its responses
                                  </p>
                                  <PromptWithTryNow
                                    text="RTF Framework:<br> Act like a [the Role you want AI to take]. Give me a [Task] in [Format] format."
                                    prompt="Act like a [the Role you want AI to take]. Give me a [Task] in [Format] format."
                                    closeFn={closeFn} // Use closeFn from props
                                  />
                                  <CollapsibleContainer title="Use Case 1: Study Buddy">
                                    <p>
                                      To inquire about a specific topic and test
                                      your understanding, use the following
                                      prompt
                                    </p>

                                    <PromptWithTryNow
                                      text="You are an expert in [topic]. (Role) Teach me the [Any theorem/topic/rule name] and include a test in MC format (Task & Format) at the end, and let me know if my answers are correct after I respond, without providing the answers beforehand"
                                      prompt="You are an expert in [topic]. Teach me the [Any theorem/topic/rule name] and include a test in [MC format] at the end, and let me know if my answers are correct after I respond, without providing the answers beforehand"
                                      closeFn={closeFn} // Use closeFn from props
                                    />
                                  </CollapsibleContainer>

                                  <CollapsibleContainer title="Use Case 2: Text Summarization">
                                    <PromptWithTryNow
                                      text="You are an expert in [topic]. (Role) I've uploaded a series of articles and papers [list of file names]. Can you summarize the key findings in point form (Task & Format) and how they relate to my research topic [specify topic]? [Upload Document]"
                                      prompt="You are an expert in [topic]. I've uploaded a series of articles and papers [list of file names]. Can you summarize the key findings in [point form] and how they relate to my research topic [specify topic]? [**Upload Your Document**]"
                                      closeFn={closeFn} // Use closeFn from props
                                    />
                                  </CollapsibleContainer>
                                </div>

                                <div className="mb-4">
                                  <li style={titleStyle} className="pt-3">
                                    LEVER prompt (Learn more{' '}
                                    <a
                                      style={hyperLinkStyle}
                                      href="https://www.thepromptwarrior.com/p/5-prompt-frameworks-level-prompts"
                                      target="_blank"
                                    >
                                      here
                                    </a>
                                    )
                                  </li>
                                  <p>
                                    Overview: A way that allows you to adjust AI
                                    outputs in a more fine-tuned way
                                  </p>
                                  <p>Framework:</p>
                                  <ol className="pl-8 list-decimal">
                                    <li>
                                      You ask the AI to rate its output on a
                                      scale of 1-10. You also define the scale,
                                      e.g. 1 is very casual, 10 is very
                                      professional
                                    </li>
                                    <li>
                                      The AI will give you the answer (e.g.
                                      6/10)
                                    </li>
                                    <li>
                                      You then adjust it to your desired number
                                      (e.g. Turn it to a 8/10)
                                    </li>
                                  </ol>

                                  <CollapsibleContainer
                                    title="Use Case: Adjust the professionality of your
                                  email"
                                  >
                                    <PromptWithTryNow
                                      text="On a scale of 1-10, If 1 is a very casual sounding email a 10 is a very professional sound email. How would you rate this email? [Email content]"
                                      prompt="On a scale of 1-10, If 1 is a very casual sounding email a 10 is a very professional sound email. How would you rate this email?\n### Email content###\n[Your Email Content]"
                                      closeFn={closeFn} // Use closeFn from props
                                    />
                                    <ConversationContainer
                                      conversation={[
                                        {
                                          role: 'user',
                                          content:
                                            'On a scale of 1-10, If 1 is a very casual sounding email a 10 is a very professional sound email. How would you rate this email? \n### Email content###\n hey bro, I wanna take a leave tmr. bye. Ben',
                                        },
                                        {
                                          role: 'assistant',
                                          content:
                                            'I would rate that email as a 1',
                                        },
                                        {
                                          role: 'user',
                                          content: 'Turn it into 3',
                                        },
                                        {
                                          role: 'assistant',
                                          content:
                                            'Hey,\n I hope this message finds you well. I would like to kindly request a leave for tomorrow. Thank you.\n Best regards,\n Ben',
                                        },
                                      ]}
                                    />
                                  </CollapsibleContainer>
                                </div>
                              </ol>
                            </div>
                          </Tab.Panel>
                          <Tab.Panel className="rounded-xl bg-white dark:bg-[#3b3b3b] dark:text-white p-1 text-sm">
                            <div>
                              <p style={titleStyle}>
                                <u>Introduction</u>
                              </p>
                              <p>
                                Temperature (0-1) affects response creativity or
                                randomness: higher values increase diversity,
                                while lower values makes the output more
                                deterministic. You can learn more{' '}
                                <a
                                  style={hyperLinkStyle}
                                  href="https://community.openai.com/t/cheat-sheet-mastering-temperature-and-top-p-in-chatgpt-api/172683"
                                  target="_blank"
                                >
                                  here
                                </a>
                              </p>

                              <div className="mx-auto">
                                <p className="my-2" style={titleStyle}>
                                  Example Temperature Settings for Various Tasks
                                </p>
                                <div className="flex overflow-x-auto">
                                  <table className="table-auto border-collapse border border-gray-400">
                                    <thead>
                                      <tr>
                                        <th className="border border-gray-300 p-2">
                                          Task
                                        </th>
                                        <th className="border border-gray-300 p-2">
                                          Temperature Setting
                                        </th>
                                        <th className="border border-gray-300 p-2">
                                          Rationale
                                        </th>
                                      </tr>
                                    </thead>
                                    <tbody>
                                      <tr>
                                        <td className="border border-gray-300 p-2">
                                          Generating essay outlines
                                        </td>
                                        <td className="border border-gray-300 p-2">
                                          Low (e.g., 0.3)
                                        </td>
                                        <td className="border border-gray-300 p-2">
                                          A low temperature can help produce
                                          more structured and coherent outlines,
                                          focusing on commonly accepted formats
                                          and logical flow without too much
                                          deviation.
                                        </td>
                                      </tr>
                                      <tr>
                                        <td className="border border-gray-300 p-2">
                                          Brainstorming session for projects
                                        </td>
                                        <td className="border border-gray-300 p-2">
                                          Medium (e.g., 0.5-0.6)
                                        </td>
                                        <td className="border border-gray-300 p-2">
                                          A medium temperature strikes a balance
                                          between creativity and relevance,
                                          generating a variety of ideas that are
                                          both innovative and applicable to the
                                          project at hand.
                                        </td>
                                      </tr>
                                      <tr>
                                        <td className="border border-gray-300 p-2">
                                          Creating discussion questions for
                                          literature classes
                                        </td>
                                        <td className="border border-gray-300 p-2">
                                          Medium to High (e.g., 0.6-0.8)
                                        </td>
                                        <td className="border border-gray-300 p-2">
                                          Encouraging diverse perspectives and
                                          interpretations in literature
                                          discussions benefits from a bit more
                                          creativity and openness in the
                                          questions generated
                                        </td>
                                      </tr>
                                    </tbody>
                                  </table>
                                </div>
                              </div>
                            </div>
                          </Tab.Panel>
                        </Tab.Panels>
                      </Tab.Group>
                      <div className="my-4 text-sm text-gray-900"></div>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 dark:bg-[#4a4a4a] px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                  {/* Use onClose for the Close button */}
                  <button
                    type="button"
                    className="mt-3 inline-flex w-full justify-center rounded-2xl bg-white dark:bg-[#3b3b3b] px-3 py-2 text-sm font-semibold text-gray-900 dark:text-white shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 dark:hover:bg-[#4a4a4a] sm:mt-0 sm:w-auto"
                    onClick={onClose}
                  >
                    Close
                  </button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
};

export default PromptEngModal;
