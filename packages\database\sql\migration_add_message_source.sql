-- CreateTable
CREATE TABLE [dbo].[message_source] (
    [message_source_id] INT NOT NULL IDENTITY(1,1),
    [message_id] INT NOT NULL,
    [title_encrypted] VARBINARY(max),
    [link_encrypted] VARBINARY(max),
    [snippet_encrypted] VARBINARY(max),
    [create_dt] DATETIME NOT NULL CONSTRAINT [message_source_create_dt_df] DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT [message_source_pkey] PRIMARY KEY CLUSTERED ([message_source_id])
);

-- AddForeignKey
ALTER TABLE [dbo].[message_source] ADD CONSTRAINT [message_source_message_id_fkey] FOREIGN KEY ([message_id]) REFERENCES [dbo].[message]([message_id]) ON DELETE NO ACTION ON UPDATE CASCADE;


-- CreateIndex
CREATE NONCLUSTERED INDEX [message_source_message_id_idx] ON [dbo].[message_source]([message_id]);