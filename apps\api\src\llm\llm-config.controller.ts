import {
  Controller,
  Get,
  Logger,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { LlmConfigService } from './llm-config.service';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger'; // For Swagger documentation

@ApiTags('LLM Configuration (REST API)') // Tag for Swagger UI
@Controller('rest/llm') // Route prefix, e.g., /api/rest/llm
export class LlmConfigController {
  private readonly logger = new Logger(LlmConfigController.name);

  constructor(private readonly llmConfigService: LlmConfigService) {}

  @Get('models-versions') // Full path: e.g. /api/llm/models-versions
  @ApiOperation({ summary: 'Get available LLM models and their API versions' })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved available models and versions.',
  })
  @ApiResponse({ status: 500, description: 'Internal server error.' })
  async getModelsAndVersions() {
    this.logger.log('Received request for /models-versions');
    try {
      const models =
        await this.llmConfigService.getAvailableModelsAndVersions();
      return models;
    } catch (error) {
      let errorMessage =
        'An unknown error occurred while fetching models and versions.';
      let errorStack: string | undefined = undefined; // Corrected type
      if (error instanceof Error) {
        errorMessage = error.message;
        errorStack = error.stack;
      }
      this.logger.error(
        `Error fetching models and versions: ${errorMessage}`,
        errorStack,
      );
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        'Failed to retrieve LLM models and versions.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
