'use client';
import { useRef } from 'react';
import { Provider } from 'react-redux';
import { makeStore, AppStore } from './store';
// Import reducers or initial state setup if needed for the first render
// import { initializeCounter } from './features/counter/counterSlice'

export default function StoreProvider({
  // count, // Pass initial state props if needed
  children,
}: {
  // count: number
  children: React.ReactNode;
}) {
  const storeRef = useRef<AppStore | null>(null);
  if (!storeRef.current) {
    // Create the store instance the first time this renders
    storeRef.current = makeStore();
    // Initialize store state if needed
    // storeRef.current.dispatch(initializeCounter(count))
  }

  return <Provider store={storeRef.current}>{children}</Provider>;
}
