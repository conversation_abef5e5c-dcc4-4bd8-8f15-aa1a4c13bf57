#!/usr/bin/env node

/**
 * Test Script: DeepSeek Azure AI Service Token Usage Verification
 * 
 * This script tests whether DeepSeek's Azure AI Service API properly provides
 * token usage information in streaming responses.
 * 
 * DeepSeek uses Azure AI Service endpoints, not OpenAI-compatible format.
 */

import dotenv from 'dotenv';

// Load environment variables from parent directory
dotenv.config({ path: '../apps/api/.env' });

// Test configuration for DeepSeek
const TEST_CONFIG = {
  // DeepSeek Azure AI Service endpoint
  deepseekEndpoint: process.env.AZURE_AI_SERVICE_ENDPOINT,
  deepseekApiKey: process.env.AZURE_AI_SERVICE_KEY,
  apiVersion: '2024-12-01-preview', // Common API version
  modelName: 'deepseek-chat', // Common DeepSeek model
  deploymentName: 'deepseek', // Common deployment name
  instanceName: 'eastus', // Instance name
  testMessage: 'Hello! Please respond with a short greeting. This is a test message to verify DeepSeek token usage extraction.',
  temperature: 0.7,
};

console.log('🧪 DeepSeek Azure AI Service Token Usage Test');
console.log('============================================');
console.log(`Endpoint: ${TEST_CONFIG.deepseekEndpoint}`);
console.log(`Model: ${TEST_CONFIG.modelName}`);
console.log(`API Version: ${TEST_CONFIG.apiVersion}`);
console.log('');

// Validate configuration
if (!TEST_CONFIG.deepseekApiKey) {
  console.error('❌ AZURE_AI_SERVICE_KEY not found in environment variables');
  process.exit(1);
}

if (!TEST_CONFIG.deepseekEndpoint) {
  console.error('❌ AZURE_AI_SERVICE_ENDPOINT not found in environment variables');
  process.exit(1);
}

/**
 * Test 1: Non-Streaming DeepSeek Request
 * This should provide token usage as baseline comparison
 */
async function testNonStreaming() {
  console.log('📝 Test 1: Non-Streaming DeepSeek Request');
  console.log('-----------------------------------------');
  
  try {
    // Ensure endpoint ends with proper API path
    let endpoint = TEST_CONFIG.deepseekEndpoint;
    if (!endpoint.endsWith('/chat/completions')) {
      endpoint = endpoint.replace(/\/+$/, '') + '/chat/completions';
    }
    endpoint = `${endpoint}?api-version=${TEST_CONFIG.apiVersion}`;

    const payload = {
      messages: [
        { role: 'user', content: TEST_CONFIG.testMessage }
      ],
      temperature: TEST_CONFIG.temperature,
      stream: false, // Non-streaming
      model: `${TEST_CONFIG.deploymentName}-${TEST_CONFIG.instanceName.toLowerCase()}`,
    };

    console.log('Endpoint:', endpoint);
    console.log('Payload:', JSON.stringify(payload, null, 2));
    
    const startTime = Date.now();
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'api-key': TEST_CONFIG.deepseekApiKey,
      },
      body: JSON.stringify(payload),
    });
    const endTime = Date.now();
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`DeepSeek API request failed: ${response.status} ${response.statusText}\n${errorText}`);
    }

    const responseData = await response.json();
    
    console.log(`\n✅ Response received in ${endTime - startTime}ms`);
    console.log(`Content: "${responseData.choices?.[0]?.message?.content || 'No content'}"`);
    console.log('\n🔍 Token Usage Analysis:');
    console.log('Response object keys:', Object.keys(responseData));
    console.log('Usage data:', responseData.usage);
    
    if (responseData.usage) {
      console.log('✅ Token usage found in non-streaming response!');
      console.log('Prompt tokens:', responseData.usage.prompt_tokens);
      console.log('Completion tokens:', responseData.usage.completion_tokens);
      console.log('Total tokens:', responseData.usage.total_tokens);
    } else {
      console.log('❌ No token usage found in non-streaming response');
    }
    
    return responseData;
  } catch (error) {
    console.error('❌ DeepSeek non-streaming test failed:', error);
    throw error;
  }
}

/**
 * Test 2: Streaming DeepSeek Request with Token Usage
 * This tests DeepSeek's streaming token usage provision
 */
async function testStreaming() {
  console.log('\n📡 Test 2: Streaming DeepSeek Request with Token Usage');
  console.log('-----------------------------------------------------');
  
  try {
    // Ensure endpoint ends with proper API path
    let endpoint = TEST_CONFIG.deepseekEndpoint;
    if (!endpoint.endsWith('/chat/completions')) {
      endpoint = endpoint.replace(/\/+$/, '') + '/chat/completions';
    }
    endpoint = `${endpoint}?api-version=${TEST_CONFIG.apiVersion}`;

    const payload = {
      messages: [
        { role: 'user', content: TEST_CONFIG.testMessage }
      ],
      temperature: TEST_CONFIG.temperature,
      stream: true, // Streaming
      stream_options: { include_usage: true }, // Request token usage
      model: `${TEST_CONFIG.deploymentName}-${TEST_CONFIG.instanceName.toLowerCase()}`,
    };

    console.log('Endpoint:', endpoint);
    console.log('Payload:', JSON.stringify(payload, null, 2));
    
    const startTime = Date.now();
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'api-key': TEST_CONFIG.deepseekApiKey,
      },
      body: JSON.stringify(payload),
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`DeepSeek streaming request failed: ${response.status} ${response.statusText}\n${errorText}`);
    }
    
    console.log('\n🔄 Processing DeepSeek stream chunks...');
    
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let accumulatedContent = '';
    let chunkCount = 0;
    let usageFound = false;
    let finalUsage = null;
    
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      
      const chunk = decoder.decode(value, { stream: true });
      const lines = chunk.split('\n').filter(line => line.trim());
      
      for (const line of lines) {
        if (!line.startsWith('data:')) continue;
        
        const data = line.slice(5).trim();
        if (data === '[DONE]') continue;
        
        try {
          const parsed = JSON.parse(data);
          chunkCount++;
          
          console.log(`\n--- DeepSeek Stream Chunk ${chunkCount} ---`);
          console.log('Chunk keys:', Object.keys(parsed));
          
          // Check for content
          const content = parsed.choices?.[0]?.delta?.content;
          if (content) {
            accumulatedContent += content;
            console.log('Content:', content);
          }
          
          // Check for usage (appears in final chunk before or with [DONE])
          if (parsed.usage) {
            console.log('🎯 Token usage found in DeepSeek stream!');
            console.log('Usage:', JSON.stringify(parsed.usage, null, 2));
            usageFound = true;
            finalUsage = parsed.usage;
          }
          
          // Log finish reason
          const finishReason = parsed.choices?.[0]?.finish_reason;
          if (finishReason) {
            console.log('Finish reason:', finishReason);
          }
          
        } catch (parseError) {
          console.log('Parse error for line:', data);
        }
      }
    }
    
    const endTime = Date.now();
    
    console.log('\n✅ DeepSeek streaming completed');
    console.log(`Duration: ${endTime - startTime}ms`);
    console.log(`Total chunks: ${chunkCount}`);
    console.log(`Final content: "${accumulatedContent}"`);
    console.log(`Usage found: ${usageFound ? '✅ YES' : '❌ NO'}`);
    
    if (finalUsage) {
      console.log('\n🏆 Final DeepSeek Token Usage:');
      console.log(JSON.stringify(finalUsage, null, 2));
    } else {
      console.log('\n⚠️  No token usage metadata found in DeepSeek stream');
    }
    
    return { accumulatedContent, usageFound, finalUsage };
  } catch (error) {
    console.error('❌ DeepSeek streaming test failed:', error);
    throw error;
  }
}

/**
 * Test 3: LangChain ChatOpenAI with DeepSeek (if possible)
 * This tests if we can use LangChain with DeepSeek's OpenAI-compatible endpoint
 */
async function testLangChainDeepSeek() {
  console.log('\n🔗 Test 3: LangChain ChatOpenAI with DeepSeek (if supported)');
  console.log('-------------------------------------------------------------');
  
  try {
    // Import LangChain dynamically
    const { ChatOpenAI } = await import('@langchain/openai');
    const { HumanMessage } = await import('@langchain/core/messages');
    
    // Try to use DeepSeek with OpenAI-compatible endpoint (if they have one)
    // Note: This might not work if DeepSeek doesn't provide OpenAI-compatible endpoints
    const baseURL = TEST_CONFIG.deepseekEndpoint.replace('/chat/completions', '/v1');
    
    const chatModel = new ChatOpenAI({
      temperature: TEST_CONFIG.temperature,
      modelName: TEST_CONFIG.modelName,
      streaming: true,
      streamUsage: true,
      configuration: {
        apiKey: TEST_CONFIG.deepseekApiKey,
        baseURL: baseURL,
      },
      modelKwargs: {
        stream_options: { include_usage: true },
      },
    });

    const message = new HumanMessage(TEST_CONFIG.testMessage);
    console.log(`Input: "${TEST_CONFIG.testMessage}"`);
    console.log(`Base URL: ${baseURL}`);
    
    let accumulatedContent = '';
    let chunkCount = 0;
    let tokensFound = false;
    let finalUsageMetadata = null;
    
    const startTime = Date.now();
    const stream = await chatModel.stream([message]);
    
    console.log('\n🔄 Processing LangChain DeepSeek stream...');
    
    for await (const chunk of stream) {
      chunkCount++;
      
      console.log(`\n--- LangChain DeepSeek Chunk ${chunkCount} ---`);
      console.log('Chunk keys:', Object.keys(chunk));
      console.log('Content:', chunk.content || '[no content]');
      
      // Check for token usage
      const usageMetadata = 
        chunk?.usage_metadata ?? 
        chunk?.response_metadata?.tokenUsage ??
        chunk?.response_metadata?.usage_metadata ??
        chunk?.response_metadata?.usage ?? 
        chunk?.usage;
      
      if (usageMetadata) {
        console.log('🎯 Token usage found in LangChain DeepSeek chunk!');
        console.log('Usage metadata:', JSON.stringify(usageMetadata, null, 2));
        tokensFound = true;
        finalUsageMetadata = usageMetadata;
      }
      
      if (chunk.content) {
        accumulatedContent += chunk.content;
      }
    }
    
    const endTime = Date.now();
    
    console.log('\n✅ LangChain DeepSeek streaming completed');
    console.log(`Duration: ${endTime - startTime}ms`);
    console.log(`Total chunks: ${chunkCount}`);
    console.log(`Final content: "${accumulatedContent}"`);
    console.log(`Tokens found: ${tokensFound ? '✅ YES' : '❌ NO'}`);
    
    return { accumulatedContent, tokensFound, finalUsageMetadata };
  } catch (error) {
    console.log('⚠️  LangChain DeepSeek test not supported or failed:', error.message);
    return { supported: false, error: error.message };
  }
}

/**
 * Main test execution
 */
async function runTests() {
  try {
    // Test 1: Non-streaming baseline
    const nonStreamingResult = await testNonStreaming();
    
    // Test 2: Direct streaming API
    const streamingResult = await testStreaming();
    
    // Test 3: LangChain attempt (may not be supported)
    const langchainResult = await testLangChainDeepSeek();
    
    // Summary analysis
    console.log('\n📊 DEEPSEEK TEST RESULTS SUMMARY');
    console.log('================================');
    
    console.log('1. Non-streaming (Direct API):');
    console.log(`   - Content received: ${!!nonStreamingResult.choices?.[0]?.message?.content}`);
    console.log(`   - Token usage available: ${!!nonStreamingResult.usage}`);
    
    console.log('2. Streaming (Direct API):');
    console.log(`   - Content received: ${!!streamingResult.accumulatedContent}`);
    console.log(`   - Usage found: ${streamingResult.usageFound}`);
    
    if (langchainResult.supported !== false) {
      console.log('3. LangChain (if supported):');
      console.log(`   - Content received: ${!!langchainResult.accumulatedContent}`);
      console.log(`   - Tokens found: ${langchainResult.tokensFound}`);
    } else {
      console.log('3. LangChain: Not supported or failed');
    }
    
    // Key findings
    if (nonStreamingResult.usage && !streamingResult.usageFound) {
      console.log('\n🔍 KEY FINDING:');
      console.log('   ⚠️  DeepSeek API provides token usage in non-streaming but NOT in streaming mode');
      console.log('   📋 The stream_options parameter may not be supported by DeepSeek');
    } else if (streamingResult.usageFound) {
      console.log('\n🔍 KEY FINDING:');
      console.log('   ✅ DeepSeek API provides token usage in streaming mode');
      console.log('   📋 Token extraction should work with proper parsing');
    } else {
      console.log('\n🔍 KEY FINDING:');
      console.log('   ❌ DeepSeek API does not provide token usage in either mode');
      console.log('   📋 Token tracking will always be zero for DeepSeek models');
    }
    
  } catch (error) {
    console.error('\n💥 DeepSeek test execution failed:', error);
    process.exit(1);
  }
}

// Execute tests
runTests().then(() => {
  console.log('\n🏁 All DeepSeek tests completed successfully');
}).catch((error) => {
  console.error('\n💥 DeepSeek test suite failed:', error);
  process.exit(1);
});