#!/usr/bin/env python3
"""
Basic test script for embedding models via the HKBU GenAI Platform Embeddings API.
Tests all embedding models defined in models.json and validates the API responses.
"""

import json
import os
import sys
import time
import argparse
from datetime import datetime
from typing import Dict, Any, List, Optional
import requests
from dotenv import load_dotenv


def load_config() -> Dict[str, str]:
    """Load configuration from .env file."""
    load_dotenv()
    
    api_key = os.getenv("API_KEY")
    base_url = os.getenv("BASE_URL")
    
    if not api_key:
        raise ValueError("API_KEY not found in .env file")
    if not base_url:
        raise ValueError("BASE_URL not found in .env file")
    
    return {
        "api_key": api_key,
        "base_url": base_url
    }


def load_models_config() -> Dict[str, Any]:
    """Load models configuration from models.json."""
    config_path = os.path.join(os.path.dirname(__file__), "../../config/models.json")
    if not os.path.exists(config_path):
        print("⚠️  models.json not found, using default configuration")
        return {"models": [], "default_parameters": {}}
    
    with open(config_path, 'r') as f:
        return json.load(f)


def get_embedding_models(models_config: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Filter and return only embedding models."""
    all_models = models_config.get("models", [])
    embedding_models = [m for m in all_models if m.get("model_type") == "embedding"]
    return embedding_models


def test_single_embedding(config: Dict[str, str], model_info: Dict[str, Any], 
                         input_text: str) -> Dict[str, Any]:
    """Test a single embedding model with given input text."""
    
    model_name = model_info["name"]
    deployment_name = model_info["deployment_name"]
    api_version = model_info.get("api_version", "2024-02-01")
    timeout = model_info.get("timeout", 30)
    
    print(f"    Testing {model_name} (deployment: {deployment_name}, api_version: {api_version})")
    
    # Construct the correct endpoint URL
    url = f"{config['base_url']}/rest/deployments/{deployment_name}/embeddings"
    
    # Request payload
    payload = {
        "input": input_text,
        "encoding_format": "float"
    }
    
    # Add API version as query parameter
    params = {
        "api-version": api_version
    }
    
    headers = {
        "Content-Type": "application/json",
        "api-key": config["api_key"]
    }
    
    start_time = time.time()
    
    try:
        response = requests.post(url, json=payload, headers=headers, params=params, timeout=timeout)
        end_time = time.time()
        response_time = end_time - start_time
        
        if response.status_code == 200:
            response_data = response.json()
            
            # Validate response structure
            validation_errors = []
            
            if response_data.get("object") != "list":
                validation_errors.append("Missing or incorrect 'object' field")
            
            if not response_data.get("data"):
                validation_errors.append("Missing 'data' field")
            elif not isinstance(response_data["data"], list):
                validation_errors.append("'data' field is not a list")
            elif len(response_data["data"]) == 0:
                validation_errors.append("'data' field is empty")
            else:
                # Check first embedding
                first_embedding = response_data["data"][0]
                if first_embedding.get("object") != "embedding":
                    validation_errors.append("Missing or incorrect 'object' field in embedding")
                if not isinstance(first_embedding.get("embedding"), list):
                    validation_errors.append("'embedding' field is not a list")
                elif len(first_embedding["embedding"]) == 0:
                    validation_errors.append("'embedding' vector is empty")
            
            if not response_data.get("usage"):
                validation_errors.append("Missing 'usage' field")
            elif not isinstance(response_data["usage"].get("prompt_tokens"), int):
                validation_errors.append("Missing or invalid 'prompt_tokens' in usage")
            
            # Calculate embedding dimensions
            embedding_dims = len(response_data["data"][0]["embedding"]) if response_data.get("data") and len(response_data["data"]) > 0 else 0
            
            return {
                "model": model_name,
                "deployment_name": deployment_name,
                "api_version": api_version,
                "success": len(validation_errors) == 0,
                "status_code": response.status_code,
                "response_time": response_time,
                "embedding_dimensions": embedding_dims,
                "prompt_tokens": response_data.get("usage", {}).get("prompt_tokens", 0),
                "total_tokens": response_data.get("usage", {}).get("total_tokens", 0),
                "validation_errors": validation_errors,
                "error": None
            }
        else:
            error_msg = f"HTTP {response.status_code}"
            try:
                error_data = response.json()
                if "error" in error_data:
                    error_msg = error_data["error"].get("message", error_msg)
            except:
                pass
            
            return {
                "model": model_name,
                "deployment_name": deployment_name,
                "api_version": api_version,
                "success": False,
                "status_code": response.status_code,
                "response_time": response_time,
                "embedding_dimensions": 0,
                "prompt_tokens": 0,
                "total_tokens": 0,
                "validation_errors": [],
                "error": error_msg
            }
            
    except requests.exceptions.Timeout:
        return {
            "model": model_name,
            "deployment_name": deployment_name,
            "api_version": api_version,
            "success": False,
            "status_code": 0,
            "response_time": timeout,
            "embedding_dimensions": 0,
            "prompt_tokens": 0,
            "total_tokens": 0,
            "validation_errors": [],
            "error": "Request timeout"
        }
    except Exception as e:
        return {
            "model": model_name,
            "deployment_name": deployment_name,
            "api_version": api_version,
            "success": False,
            "status_code": 0,
            "response_time": time.time() - start_time,
            "embedding_dimensions": 0,
            "prompt_tokens": 0,
            "total_tokens": 0,
            "validation_errors": [],
            "error": str(e)
        }


def test_array_embedding(config: Dict[str, str], model_info: Dict[str, Any], 
                        input_texts: List[str]) -> Dict[str, Any]:
    """Test embedding model with an array of input texts."""
    
    model_name = model_info["name"]
    deployment_name = model_info["deployment_name"]
    api_version = model_info.get("api_version", "2024-02-01")
    timeout = model_info.get("timeout", 30)
    
    print(f"    Testing {model_name} with array input ({len(input_texts)} texts)")
    
    # Construct the correct endpoint URL
    url = f"{config['base_url']}/rest/deployments/{deployment_name}/embeddings"
    
    # Request payload
    payload = {
        "input": input_texts,
        "encoding_format": "float"
    }
    
    # Add API version as query parameter
    params = {
        "api-version": api_version
    }
    
    headers = {
        "Content-Type": "application/json",
        "api-key": config["api_key"]
    }
    
    start_time = time.time()
    
    try:
        response = requests.post(url, json=payload, headers=headers, params=params, timeout=timeout)
        end_time = time.time()
        response_time = end_time - start_time
        
        if response.status_code == 200:
            response_data = response.json()
            
            # Validate response structure for array input
            validation_errors = []
            
            if not response_data.get("data"):
                validation_errors.append("Missing 'data' field")
            elif len(response_data["data"]) != len(input_texts):
                validation_errors.append(f"Expected {len(input_texts)} embeddings, got {len(response_data['data'])}")
            
            return {
                "model": model_name,
                "test_type": "array_input",
                "success": len(validation_errors) == 0,
                "status_code": response.status_code,
                "response_time": response_time,
                "input_count": len(input_texts),
                "output_count": len(response_data.get("data", [])),
                "validation_errors": validation_errors,
                "error": None
            }
        else:
            error_msg = f"HTTP {response.status_code}"
            try:
                error_data = response.json()
                if "error" in error_data:
                    error_msg = error_data["error"].get("message", error_msg)
            except:
                pass
            
            return {
                "model": model_name,
                "test_type": "array_input",
                "success": False,
                "status_code": response.status_code,
                "response_time": response_time,
                "input_count": len(input_texts),
                "output_count": 0,
                "validation_errors": [],
                "error": error_msg
            }
            
    except Exception as e:
        return {
            "model": model_name,
            "test_type": "array_input",
            "success": False,
            "status_code": 0,
            "response_time": time.time() - start_time,
            "input_count": len(input_texts),
            "output_count": 0,
            "validation_errors": [],
            "error": str(e)
        }


def print_results_summary(results: List[Dict[str, Any]]):
    """Print a summary of test results."""
    
    total_tests = len(results)
    successful_tests = len([r for r in results if r["success"]])
    failed_tests = total_tests - successful_tests
    
    print(f"\n" + "="*80)
    print(f"📊 EMBEDDING MODELS TEST SUMMARY")
    print(f"="*80)
    print(f"📈 Total Tests: {total_tests}")
    print(f"✅ Successful: {successful_tests}")
    print(f"❌ Failed: {failed_tests}")
    print(f"📊 Success Rate: {(successful_tests/total_tests*100):.1f}%" if total_tests > 0 else "📊 Success Rate: 0%")
    
    if successful_tests > 0:
        avg_response_time = sum([r["response_time"] for r in results if r["success"]]) / successful_tests
        print(f"⏱️ Average Response Time: {avg_response_time:.2f}s")
        
        # Group by model for embedding dimensions
        embedding_dims = {}
        for result in results:
            if result["success"] and "embedding_dimensions" in result and result["embedding_dimensions"] > 0:
                model = result["model"]
                dims = result["embedding_dimensions"]
                if model not in embedding_dims:
                    embedding_dims[model] = dims
        
        if embedding_dims:
            print(f"\n🔢 Embedding Dimensions:")
            for model, dims in embedding_dims.items():
                print(f"   {model}: {dims} dimensions")
    
    print(f"\n📋 DETAILED RESULTS:")
    print(f"="*80)
    
    for result in results:
        status = "✅" if result["success"] else "❌"
        model_info = f"{result['model']}"
        if "deployment_name" in result:
            model_info += f" ({result['deployment_name']})"
        if "api_version" in result:
            model_info += f" [API: {result['api_version']}]"
        
        test_type = result.get("test_type", "single_text")
        
        print(f"{status} {model_info} - {test_type}")
        
        if result["success"]:
            print(f"    ⏱️ Response Time: {result['response_time']:.2f}s")
            if "embedding_dimensions" in result and result["embedding_dimensions"] > 0:
                print(f"    🔢 Dimensions: {result['embedding_dimensions']}")
            if "prompt_tokens" in result and result["prompt_tokens"] > 0:
                print(f"    🎫 Tokens: {result['prompt_tokens']} prompt, {result.get('total_tokens', 0)} total")
            if "input_count" in result:
                print(f"    📝 Input/Output: {result['input_count']}/{result.get('output_count', 0)} texts")
        else:
            print(f"    ❌ Error: {result.get('error', 'Unknown error')}")
            if result.get('validation_errors'):
                for error in result['validation_errors']:
                    print(f"    ⚠️ Validation: {error}")
        print()


def main():
    parser = argparse.ArgumentParser(description="Test embedding models via HKBU GenAI Platform API")
    parser.add_argument("--models", type=str, help="Comma-separated list of specific models to test")
    parser.add_argument("--text", type=str, default="Hello world, this is a test for embeddings.", 
                       help="Text to embed (default: 'Hello world, this is a test for embeddings.')")
    parser.add_argument("--array-test", action="store_true", 
                       help="Also test with array input")
    parser.add_argument("--list", action="store_true", 
                       help="List all available embedding models and exit")
    
    args = parser.parse_args()
    
    try:
        # Load configurations
        config = load_config()
        models_config = load_models_config()
        
        # Get embedding models
        embedding_models = get_embedding_models(models_config)
        
        if args.list:
            print("Available embedding models:")
            for model in embedding_models:
                print(f"  - {model['name']} ({model['deployment_name']}) [API: {model.get('api_version', 'N/A')}]")
            return
        
        if not embedding_models:
            print("❌ No embedding models found in configuration!")
            sys.exit(1)
        
        # Filter models if specific models requested
        if args.models:
            requested_models = [m.strip() for m in args.models.split(",")]
            embedding_models = [m for m in embedding_models if m["name"] in requested_models]
            
            if not embedding_models:
                print(f"❌ No matching embedding models found for: {args.models}")
                sys.exit(1)
        
        print("🚀 HKBU GenAI Platform - Embedding Models Testing")
        print("=" * 80)
        print(f"📍 API Endpoint: {config['base_url']}")
        print(f"🔑 API Key: {config['api_key'][:8]}...")
        print(f"🎯 Models to Test: {len(embedding_models)}")
        print(f"📝 Test Text: '{args.text}'")
        print(f"📚 Array Test: {'Enabled' if args.array_test else 'Disabled'}")
        
        all_results = []
        
        print(f"\n🏁 Starting tests at {datetime.now().strftime('%H:%M:%S')}...")
        
        # Test each embedding model
        for i, model_info in enumerate(embedding_models, 1):
            print(f"\n[{i}/{len(embedding_models)}] Testing {model_info['name']}...")
            
            # Test single text
            result = test_single_embedding(config, model_info, args.text)
            all_results.append(result)
            
            # Test array input if requested
            if args.array_test:
                array_texts = [
                    "Hello world",
                    "This is a test",
                    "Embedding models are useful"
                ]
                array_result = test_array_embedding(config, model_info, array_texts)
                all_results.append(array_result)
        
        # Print summary
        print_results_summary(all_results)
        
        # Exit with appropriate code
        failed_count = len([r for r in all_results if not r["success"]])
        sys.exit(0 if failed_count == 0 else 1)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()