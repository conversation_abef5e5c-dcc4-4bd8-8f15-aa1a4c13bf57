import { Controller, Get, UseGuards } from '@nestjs/common';
import { TasksService } from './tasks.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { GeneralRateLimitGuard } from '../../common/guards/general-rate-limit.guard';

@UseGuards(JwtAuthGuard, GeneralRateLimitGuard)
@Controller('general/tasks')
export class TasksController {
  constructor(private readonly tasksService: TasksService) {}

  @Get()
  findAll() {
    return this.tasksService.findAll();
  }
}
