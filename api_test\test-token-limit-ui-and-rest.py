#!/usr/bin/env python3
"""
Test script to verify token limit blocking works for both Web UI and REST API endpoints.
This script tests the rate limiting functionality after the HTTP status code fix (406 -> 429).
"""

import requests
import json
import sys
import os
from typing import Dict, Any, Optional

class TokenLimitTester:
    def __init__(self, 
                 base_url: str = "http://localhost:3003",
                 api_key: Optional[str] = None,
                 jwt_token: Optional[str] = None):
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.jwt_token = jwt_token
        
    def test_web_ui_endpoint(self, model: str = "gpt-4o-mini") -> Dict[str, Any]:
        """Test the Web UI chat completion endpoint."""
        url = f"{self.base_url}/api/v0/general/chat/completions"
        
        headers = {
            "Content-Type": "application/json"
        }
        
        if self.jwt_token:
            headers["Authorization"] = f"Bearer {self.jwt_token}"
        
        payload = {
            "prompt": "Hello, this is a test message to check token limits.",
            "model": model,
            "temperature": 0.7,
            "stream": False,
            "useGoogle": False
        }
        
        try:
            response = requests.post(url, headers=headers, json=payload, timeout=30)
            return {
                "endpoint": "Web UI",
                "url": url,
                "status_code": response.status_code,
                "success": response.ok,
                "response_data": response.text[:500] if response.text else None,
                "headers": dict(response.headers),
                "error": None
            }
        except Exception as e:
            return {
                "endpoint": "Web UI",
                "url": url,
                "status_code": None,
                "success": False,
                "response_data": None,
                "headers": None,
                "error": str(e)
            }
    
    def test_rest_api_endpoint(self, model_deployment: str = "gpt-4o-mini") -> Dict[str, Any]:
        """Test the REST API chat completion endpoint."""
        url = f"{self.base_url}/api/v0/rest/deployments/{model_deployment}/chat/completions"
        
        headers = {
            "Content-Type": "application/json"
        }
        
        if self.api_key:
            headers["api-key"] = self.api_key
        
        # OpenAI-compatible payload format
        payload = {
            "messages": [
                {
                    "role": "user",
                    "content": "Hello, this is a test message to check token limits."
                }
            ],
            "temperature": 0.7,
            "stream": False
        }
        
        try:
            response = requests.post(url, headers=headers, json=payload, timeout=30)
            return {
                "endpoint": "REST API",
                "url": url,
                "status_code": response.status_code,
                "success": response.ok,
                "response_data": response.text[:500] if response.text else None,
                "headers": dict(response.headers),
                "error": None
            }
        except Exception as e:
            return {
                "endpoint": "REST API",
                "url": url,
                "status_code": None,
                "success": False,
                "response_data": None,
                "headers": None,
                "error": str(e)
            }
    
    def print_result(self, result: Dict[str, Any]):
        """Print test result in a formatted way."""
        print(f"\n=== {result['endpoint']} Test Result ===")
        print(f"URL: {result['url']}")
        print(f"Status Code: {result['status_code']}")
        print(f"Success: {result['success']}")
        
        if result['error']:
            print(f"Error: {result['error']}")
        
        if result['status_code']:
            # Check for expected rate limit status codes
            if result['status_code'] == 429:
                print("EXPECTED: HTTP 429 (Too Many Requests) - Token limit correctly enforced")
            elif result['status_code'] == 406:
                print("OUTDATED: HTTP 406 (Not Acceptable) - Should be 429 after fix")
            elif result['status_code'] == 401:
                print("AUTH: Authentication required")
            elif result['status_code'] == 403:
                print("FORBIDDEN: Check permissions")
            elif result['status_code'] == 200:
                print("SUCCESS: Request completed successfully")
            else:
                print(f"OTHER: Status {result['status_code']}")
        
        if result['response_data']:
            print(f"Response Preview: {result['response_data']}")
        
        print("-" * 50)

def main():
    print("Token Limit Testing Script")
    print("This script tests both Web UI and REST API endpoints for token limit enforcement.")
    print("=" * 70)
    
    # Configuration
    base_url = os.getenv("API_BASE_URL", "http://localhost:3003")
    api_key = os.getenv("API_KEY")  # For REST API
    jwt_token = os.getenv("JWT_TOKEN")  # For Web UI
    
    print(f"Base URL: {base_url}")
    print(f"API Key provided: {'Yes' if api_key else 'No'}")
    print(f"JWT Token provided: {'Yes' if jwt_token else 'No'}")
    
    tester = TokenLimitTester(base_url, api_key, jwt_token)
    
    # Test models - you can modify these based on your available models
    test_models = ["gpt-4o-mini", "gpt-4o", "claude-3-5-sonnet-20241022"]
    
    print(f"\nTesting {len(test_models)} models: {', '.join(test_models)}")
    
    for model in test_models:
        print(f"\nTesting model: {model}")
        
        # Test Web UI endpoint
        if jwt_token:
            web_result = tester.test_web_ui_endpoint(model)
            tester.print_result(web_result)
        else:
            print("\n=== Web UI Test Result ===")
            print("WARNING: Skipped: No JWT token provided")
            print("Set JWT_TOKEN environment variable to test Web UI endpoint")
            print("-" * 50)
        
        # Test REST API endpoint
        if api_key:
            rest_result = tester.test_rest_api_endpoint(model)
            tester.print_result(rest_result)
        else:
            print("\n=== REST API Test Result ===")
            print("WARNING: Skipped: No API key provided")
            print("Set API_KEY environment variable to test REST API endpoint")
            print("-" * 50)
    
    print("\nSummary:")
    print("- HTTP 429: Token limit correctly enforced (Expected)")
    print("- HTTP 406: Old status code, should be 429 after fix")
    print("- HTTP 200: Request successful (user hasn't hit limits)")
    print("- HTTP 401/403: Authentication/authorization issues")
    
    print("\nTo get tokens:")
    print("- JWT Token: Login to web UI and extract from browser developer tools")
    print("- API Key: Generate from the web UI settings page")

if __name__ == "__main__":
    main()