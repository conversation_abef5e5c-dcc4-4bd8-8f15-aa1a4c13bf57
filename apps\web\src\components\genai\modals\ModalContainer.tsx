'use client';

import React from 'react';
import {
  Modal,
  SwipeableDrawer,
  useTheme,
  useMediaQuery,
  Box,
  Typography,
  Button,
  RadioGroup,
  FormControlLabel,
  Radio,
} from '@mui/material';
import DialogActions from '@mui/material/DialogActions';
import { useAppDispatch, useAppSelector } from '@/lib/store/hooks';
import { setTheme, selectTheme, ThemeMode } from '@/lib/store/themeSlice';

const style = {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: '80%',
  maxWidth: '600px', // Reduced max width for desktop
  height: '80vh',
  bgcolor: 'background.paper',
  boxShadow: 24,
  p: 0,
  display: 'flex',
  flexDirection: 'column',
  borderRadius: '16px', // Apply border-radius to the modal itself
  overflow: 'hidden', // Ensure content respects the border-radius
};

interface ModalContainerProps {
  open: boolean;
  handleClose: () => void;
  children?: React.ReactNode;
}

const ModalContainer: React.FC<ModalContainerProps> = ({
  open,
  handleClose,
  children,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  if (isMobile) {
    return (
      <SwipeableDrawer
        anchor="bottom"
        open={open}
        onClose={handleClose}
        onOpen={() => {}} // Required prop for SwipeableDrawer
        PaperProps={{
          sx: {
            borderTopLeftRadius: 16, // Apply border-radius to top-left corner
            borderTopRightRadius: 16, // Apply border-radius to top-right corner
            backdropFilter: 'blur(10px)', // Add blur effect
            backgroundColor: 'background.paper', // Ensure solid background
          },
        }}
        disableSwipeToOpen={false}
        ModalProps={{
          keepMounted: true,
        }}
      >
        <Box
          onClick={handleClose} // Add onClick to close the drawer
          sx={{
            display: 'flex',
            justifyContent: 'center',
            pt: 1, // Padding top for the handle
            pb: 0.5, // Padding bottom for the handle
            cursor: 'pointer', // Indicate it's clickable
          }}
        >
          <Box
            sx={{
              width: 40,
              height: 4,
              borderRadius: 2,
              bgcolor: 'text.secondary', // Color of the handle
            }}
          />
        </Box>
        <Box sx={{ maxHeight: '80svh', overflowY: 'auto' }}>{children}</Box>
      </SwipeableDrawer>
    );
  }

  return (
    <Modal
      open={open}
      onClose={handleClose}
      disableRestoreFocus // Add this prop
      BackdropProps={{
        sx: {
          backdropFilter: 'blur(10px)',
        },
      }}
    >
      <Box sx={{ maxHeight: '80svh', overflowY: 'auto' }}>{children}</Box>
    </Modal>
  );
};

export default ModalContainer;
