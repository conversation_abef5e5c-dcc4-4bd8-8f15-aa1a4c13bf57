-- =============================================
-- Author:      <PERSON>
-- Create date: 2025-04-16
-- Description: Retrieves conversation UUID, creation date, and decrypted title for a specific user (ssoid).
--              (Modified: Selects title directly from conversation table and decrypts it)
-- =============================================
CREATE PROCEDURE [dbo].[sp_cvst_GetConversationHistoryForUser]
    @ssoid VARCHAR(30),
    @encryption_key_name NVARCHAR(128),  -- Input parameter for key name
    @decryption_cert_name NVARCHAR(128) -- Input parameter for cert name
AS
BEGIN
    SET NOCOUNT ON;

    -- Check if the symmetric key exists using the input parameter
    IF NOT EXISTS (SELECT * FROM sys.symmetric_keys WHERE name = @encryption_key_name)
    BEGIN
        RAISERROR('Symmetric key specified by @encryption_key_name (''%s'') not found.', 16, 1, @encryption_key_name);
        RETURN;
    END

    -- Check if the certificate used for the key exists using the input parameter
    IF NOT EXISTS (SELECT * FROM sys.certificates WHERE name = @decryption_cert_name)
    BEGIN
        RAISERROR('Certificate specified by @decryption_cert_name (''%s'') for symmetric key not found.', 16, 1, @decryption_cert_name);
        RETURN;
    END

    -- Removed temporary table creation and population logic.

    BEGIN TRY
        -- Open the symmetric key
        DECLARE @OpenKeySQL NVARCHAR(MAX) = N'OPEN SYMMETRIC KEY ' + QUOTENAME(@encryption_key_name) + N' DECRYPTION BY CERTIFICATE ' + QUOTENAME(@decryption_cert_name) + N';';
        EXEC sp_executesql @OpenKeySQL;

        -- Select required fields directly from the conversation table, decrypting the title
        SELECT
            c.conversation_uuid,
            c.create_dt,
            -- Decrypt and convert the title, handling NULLs and potential conversion errors
            COALESCE(
                CONVERT(NVARCHAR(MAX), DecryptByKey(c.conversation_title)), -- Decrypt VARBINARY -> NVARCHAR
                N'Decryption/Conversion Failed'
            ) AS conversation_title -- Removed c.model_name
        FROM
            dbo.conversation c
        WHERE
            c.ssoid = @ssoid
        ORDER BY
            c.updated_dt DESC,  -- Order by updated date first
            c.create_dt DESC; -- Order final results

        -- Close the symmetric key
        DECLARE @CloseKeySQL NVARCHAR(MAX) = N'CLOSE SYMMETRIC KEY ' + QUOTENAME(@encryption_key_name) + N';';
        EXEC sp_executesql @CloseKeySQL;

    END TRY
    BEGIN CATCH
        -- Ensure key is closed if an error occurred before closing
        DECLARE @CheckOpenKeySQL NVARCHAR(MAX) = N'IF EXISTS (SELECT * FROM sys.openkeys WHERE key_name = ''' + REPLACE(@encryption_key_name, '''', '''''') + N''') CLOSE SYMMETRIC KEY ' + QUOTENAME(@encryption_key_name) + N';';
        EXEC sp_executesql @CheckOpenKeySQL;

        -- Re-throw the error
        ;THROW;
    END CATCH

    -- Removed temp table cleanup as it's no longer created.

END
GO