import { SecretClient } from '@azure/keyvault-secrets';
import { DefaultAzureCredential } from '@azure/identity';

let cachedSecrets: { [key: string]: string } | null = null;

export async function loadKeyVaultSecrets() {
  if (cachedSecrets) {
    return cachedSecrets;
  }

  const keyVaultName = process.env.KEY_VAULT_NAME;
  const isProductionOrUat = ['production', 'uat'].includes(process.env.NODE_ENV || '');

  if (!isProductionOrUat || !keyVaultName) {
    return {};
  }

  const credential = new DefaultAzureCredential();
  const url = `https://${keyVaultName}.vault.azure.net`;
  const client = new SecretClient(url, credential);

  const secrets: { [key: string]: string } = {};
  const secretNameToFetch = 'DATABASE_URL';
  const secretNameInKeyVault = secretNameToFetch.replaceAll('_', '-');

  try {
    const secret = await client.getSecret(secretNameInKeyVault);
    if (secret.value) {
      secrets[secretNameToFetch] = secret.value;
    }
  } catch (error) {
    console.error(`Failed to retrieve secret: ${secretNameToFetch}`, error);
  }

  cachedSecrets = secrets;
  return secrets;
}