#!/usr/bin/env python3
"""
Python script to test a single model via the HKBU GenAI Platform Chat Completion API.
Supports specifying the model via command-line argument.
"""

import json
import os
import sys
import argparse
import time
import requests
from dotenv import load_dotenv
from typing import Dict, Any, Optional


def load_config() -> Dict[str, str]:
    """Load configuration from .env file."""
    load_dotenv()
    
    api_key = os.getenv("API_KEY")
    base_url = os.getenv("BASE_URL")
    
    if not api_key:
        raise ValueError("API_KEY not found in .env file")
    if not base_url:
        raise ValueError("BASE_URL not found in .env file")
    
    return {
        "api_key": api_key,
        "base_url": base_url
    }


def load_models_config() -> Dict[str, Any]:
    """Load models configuration from models.json."""
    config_path = os.path.join(os.path.dirname(__file__), "../../config/models.json")
    if not os.path.exists(config_path):
        print("⚠️  models.json not found, using default configuration")
        return {
            "models": [],
            "default_parameters": {
                "temperature": 0.7,
                "stream": False,
                "api_version": "2024-02-01"
            }
        }
    
    with open(config_path, 'r') as f:
        return json.load(f)


def test_chat_completion(config: Dict[str, str], model_name: str, 
                        prompt: Optional[str] = None, 
                        timeout: int = 30) -> Dict[str, Any]:
    """Test the chat completion endpoint with specified model."""
    
    # Load model configuration
    models_config = load_models_config()
    default_params = models_config.get("default_parameters", {})
    
    # Get model-specific configuration and deployment name
    model_info = None
    deployment_name = model_name  # fallback
    
    for model in models_config.get("models", []):
        if model["name"] == model_name:
            model_info = model
            deployment_name = model["deployment_name"]
            break
    
    # Construct the URL using model name instead of deployment name
    # This is a temporary fix for models where deployment_name != model_name
    url = f"{config['base_url']}/rest/deployments/{model_name}/chat/completions"
    
    # Query parameters - use model-specific api_version if available
    api_version = default_params.get("api_version", "2024-02-01")
    if model_info and "api_version" in model_info:
        api_version = model_info["api_version"]
    
    params = {
        "api-version": api_version
    }
    
    # Headers
    headers = {
        "Content-Type": "application/json",
        "api-key": config["api_key"]
    }
    
    # Default prompt if none provided
    if not prompt:
        prompt = "Hello, how are you? Please respond in one sentence."
    
    # Build messages based on model support for system messages
    supports_system_messages = True
    if model_info:
        supports_system_messages = model_info.get("supports_system_messages", True)
    
    messages = []
    if supports_system_messages:
        messages.append({
            "role": "system",
            "content": "You are a helpful assistant."
        })
    messages.append({
        "role": "user",
        "content": prompt
    })
    
    # Build payload based on model support for temperature
    supports_temperature = True
    if model_info:
        supports_temperature = model_info.get("supports_temperature", True)
    
    # Request payload
    payload = {
        "messages": messages,
        "stream": default_params.get("stream", False)
    }
    
    # Only add temperature if model supports it
    if supports_temperature:
        payload["temperature"] = default_params.get("temperature", 0.7)
    
    print(f"🚀 Testing HKBU GenAI Platform - Model: {model_name}")
    if deployment_name != model_name:
        print(f"📦 Deployment: {deployment_name}")
    print(f"📍 URL: {url}")
    print(f"🔑 API Key: {config['api_key'][:8]}...")
    print(f"💬 Prompt: {prompt}")
    print(f"⚙️  API Version: {api_version}")
    if not supports_system_messages:
        print(f"ℹ️  System messages disabled for {model_name}")
    if not supports_temperature:
        print(f"ℹ️  Temperature parameter disabled for {model_name}")
    print("-" * 60)
    
    start_time = time.time()
    
    try:
        # Make the request
        response = requests.post(
            url,
            params=params,
            headers=headers,
            json=payload,
            timeout=timeout
        )
        
        elapsed_time = time.time() - start_time
        
        # Check response status
        print(f"📊 Status Code: {response.status_code}")
        print(f"⏱️  Response Time: {elapsed_time:.2f}s")
        
        if response.status_code == 200:
            print("✅ Request successful!")
            
            # Parse response
            response_data = response.json()
            
            # Validate response structure
            required_fields = ["id", "object", "created", "model", "choices", "usage"]
            missing_fields = [field for field in required_fields if field not in response_data]
            
            if missing_fields:
                print(f"⚠️  Missing fields in response: {missing_fields}")
            else:
                print("✅ Response structure is valid (OpenAI-compatible format)!")
            
            # Extract and display the assistant's response
            if "choices" in response_data and len(response_data["choices"]) > 0:
                assistant_message = response_data["choices"][0]["message"]["content"]
                print(f"\n🤖 Assistant Response:\n{assistant_message}")
            
            # Display response details
            print("\n📋 Response Details:")
            print(f"   ID: {response_data.get('id', 'N/A')}")
            print(f"   Model: {response_data.get('model', 'N/A')}")
            print(f"   Object Type: {response_data.get('object', 'N/A')}")
            print(f"   Created: {response_data.get('created', 'N/A')}")
            print(f"   Finish Reason: {response_data.get('choices', [{}])[0].get('finish_reason', 'N/A')}")
            
            # Display usage info
            usage = response_data.get("usage", {})
            print(f"\n📊 Token Usage:")
            print(f"   Prompt Tokens: {usage.get('prompt_tokens', 'N/A')}")
            print(f"   Completion Tokens: {usage.get('completion_tokens', 'N/A')}")
            print(f"   Total Tokens: {usage.get('total_tokens', 'N/A')}")
            
            # Check for conversation_uuid (should not be present)
            if "conversation_uuid" in response_data:
                print(f"\n⚠️  WARNING: Response contains conversation_uuid: {response_data['conversation_uuid']}")
                print("   This suggests conversations are being created (unexpected for REST API)!")
            
            return {
                "success": True,
                "model": model_name,
                "status_code": response.status_code,
                "response_time": elapsed_time,
                "tokens": usage.get('total_tokens', 0),
                "response": assistant_message if "choices" in response_data else None
            }
            
        else:
            print(f"❌ Request failed with status code: {response.status_code}")
            print(f"Response: {response.text}")
            
            return {
                "success": False,
                "model": model_name,
                "status_code": response.status_code,
                "response_time": elapsed_time,
                "error": response.text
            }
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Unable to connect to the API server.")
        print(f"   Make sure the server is running on {config['base_url']}")
        return {
            "success": False,
            "model": model_name,
            "status_code": 0,
            "response_time": time.time() - start_time,
            "error": "Connection Error"
        }
        
    except requests.exceptions.Timeout:
        print(f"❌ Timeout Error: Request took longer than {timeout}s")
        return {
            "success": False,
            "model": model_name,
            "status_code": 0,
            "response_time": timeout,
            "error": "Timeout"
        }
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Request Error: {e}")
        return {
            "success": False,
            "model": model_name,
            "status_code": 0,
            "response_time": time.time() - start_time,
            "error": str(e)
        }
        
    except json.JSONDecodeError:
        print("❌ JSON Decode Error: Invalid response format.")
        print(f"Raw response: {response.text}")
        return {
            "success": False,
            "model": model_name,
            "status_code": response.status_code if 'response' in locals() else 0,
            "response_time": time.time() - start_time,
            "error": "Invalid JSON response"
        }
        
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")
        return {
            "success": False,
            "model": model_name,
            "status_code": 0,
            "response_time": time.time() - start_time,
            "error": str(e)
        }


def main():
    """Main function to run the test."""
    parser = argparse.ArgumentParser(
        description="Test a single model via HKBU GenAI Platform API"
    )
    parser.add_argument(
        "model",
        help="Model deployment name (e.g., gpt-4.1, gemini-2.5-pro)"
    )
    parser.add_argument(
        "-p", "--prompt",
        help="Custom prompt to send to the model",
        default=None
    )
    parser.add_argument(
        "-t", "--timeout",
        help="Request timeout in seconds",
        type=int,
        default=30
    )
    parser.add_argument(
        "--list-models",
        action="store_true",
        help="List all available models from models.json"
    )
    
    args = parser.parse_args()
    
    # Load models configuration
    models_config = load_models_config()
    
    # List models if requested
    if args.list_models:
        print("📋 Available Models:")
        print("-" * 60)
        for model in models_config.get("models", []):
            deployment_info = f" (deployment: {model['deployment_name']})" if model['deployment_name'] != model['name'] else ""
            print(f"  • {model['name']:20} - {model['description']}{deployment_info}")
        print("-" * 60)
        return
    
    try:
        config = load_config()
        
        # Get timeout from model configuration if available
        timeout = args.timeout
        for model in models_config.get("models", []):
            if model["name"] == args.model:
                # Use configured timeout if not specified via command line
                if args.timeout == 30:  # Default value
                    timeout = model.get("timeout", 30)
                    print(f"ℹ️  Using configured timeout: {timeout}s")
                break
        
        # Run the test
        result = test_chat_completion(config, args.model, args.prompt, timeout)
        
        # Exit with appropriate code
        sys.exit(0 if result["success"] else 1)
        
    except Exception as e:
        print(f"❌ Configuration Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()