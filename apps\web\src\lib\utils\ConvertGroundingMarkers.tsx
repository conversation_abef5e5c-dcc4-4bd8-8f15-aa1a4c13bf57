// Define interface for individual marker objects within the markers array
interface Marker {
  url: string;
  number: number | string; // Allow number or string based on usage
}

export const ConvertGroundingMarkers = (text: string): string => {
  const markerMatch = text.match(/__GROUNDING_MARKERS__(.+?)__END_MARKERS__/);
  if (!markerMatch) return text;

  try {
    // Extract and remove the marker section
    const baseText = text.replace(
      /__GROUNDING_MARKERS__(.+?)__END_MARKERS__/,
      '',
    );
    const markersData = JSON.parse(markerMatch[1]);

    // First, find all code blocks and their positions
    const codeBlocks: { start: number; end: number }[] = [];
    const codeBlockRegex = /```[\s\S]*?```/g;
    let match;

    while ((match = codeBlockRegex.exec(baseText)) !== null) {
      codeBlocks.push({
        start: match.index,
        end: match.index + match[0].length,
      });
    }

    // Process markers, but skip if they're inside code blocks
    let result = baseText;
    for (const marker of markersData) {
      const position = result.indexOf(marker.text);
      if (position === -1) continue;

      const endPosition = position + marker.text.length;

      // Check if this marker position is inside any code block
      const isInCodeBlock = codeBlocks.some(
        (block) => position >= block.start && endPosition <= block.end,
      );

      if (!isInCodeBlock) {
        const markerStr =
          ' ' +
          marker.markers
            .map((m: Marker) => `[[MARKER_LINK:${m.url}:${m.number}]]`) // Added Marker type
            .join(' ');
        result =
          result.slice(0, endPosition) + markerStr + result.slice(endPosition);

        // Update code block positions after inserting markers
        codeBlocks.forEach((block) => {
          if (block.start > endPosition) {
            block.start += markerStr.length;
            block.end += markerStr.length;
          }
        });
      }
    }

    return result;
  } catch (e) {
    console.error('Error processing grounding markers:', e);
    return text;
  }
};

export default ConvertGroundingMarkers;
