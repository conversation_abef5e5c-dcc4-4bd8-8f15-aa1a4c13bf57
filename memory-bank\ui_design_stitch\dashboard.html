<!DOCTYPE html>
<html lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>AI Model Showcase</title>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&amp;display=swap" rel="stylesheet"/>
<style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8fafc;
        }
        .sidebar-icon {
            font-size: 28px;
            color: #64748b;
        }
        .sidebar-icon:hover {
            color: #0ea5e9;
        }
        .active-sidebar-icon {
            color: #0ea5e9;
        }
        .model-card {
            background-color: #f1f5f9;
            border-radius: 0.75rem;
            padding: 1.5rem;
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }
        .model-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        .model-card-active {
            background-color: #0284c7;
            color: white;
        }
        .model-card-active .model-description {
            color: #e0f2fe;
        }
        .model-card-active .model-icon-bg {
            background-color: white;
        }
        .model-card-active .model-icon {
            color: #0284c7;
        }
        .model-icon-bg {
            width: 48px;
            height: 48px;
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .model-icon {
            font-size: 24px;
        }
        .scroll-arrow {
            background-color: #e2e8f0;
            border-radius: 9999px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #64748b;
            cursor: pointer;
        }
        .scroll-arrow:hover {
            background-color: #cbd5e1;
        }
        .category-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 1rem;
        }
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        .see-all-link {
            color: #0ea5e9;
            font-weight: 500;
            text-decoration: none;
        }
        .see-all-link:hover {
            text-decoration: underline;
        }
        .query-input-container {
            background-color: white;
            border-radius: 0.75rem;
            padding: 1rem 1.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        .query-input {
            border: none;
            outline: none;
            flex-grow: 1;
            font-size: 1rem;
            color: #475569;
        }
        .query-icon {
            color: #64748b;
            font-size: 24px;
        }
        .filter-button {
            padding: 0.5rem 1rem;
            border-radius: 9999px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;
        }
        .filter-button-active {
            background-color: #e0f2fe;
            color: #0ea5e9;
        }
        .filter-button-inactive {
            background-color: #f1f5f9;
            color: #475569;
        }
        .filter-button-inactive:hover {
            background-color: #e2e8f0;
        }
        .no-scrollbar::-webkit-scrollbar {
            display: none;
        }
        .no-scrollbar {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
    </style>
</head>
<body class="flex h-screen"> {}
    <aside class="w-20 bg-white flex flex-col items-center py-8 justify-between shadow-md">
<div>
<div class="p-3 mb-6">
<span class="material-icons sidebar-icon active-sidebar-icon">auto_awesome_mosaic</span>
</div>
<div class="p-3">
<span class="material-icons sidebar-icon">arrow_forward_ios</span>
</div>
</div>
<div class="p-3">
<span class="material-icons sidebar-icon">settings</span>
</div>
</aside>
<main class="flex-1 p-8 overflow-y-auto">
<header class="text-center mb-10">
<h1 class="text-3xl font-semibold text-slate-800 mb-6">What do you want to know?</h1>
<div class="flex justify-center items-center space-x-3 mb-8">
<button class="filter-button filter-button-active">
<span class="material-icons text-lg">psychology</span>
<span>GPT-4.O</span>
</button>
<button class="filter-button filter-button-inactive">
<span class="material-icons text-lg">model_training</span>
<span>DeepSeek-R1</span>
</button>
<button class="p-2 rounded-full hover:bg-slate-200 transition">
<span class="material-icons query-icon">search</span>
</button>
</div>
<div class="max-w-3xl mx-auto query-input-container flex items-center space-x-4">
<span class="material-icons query-icon text-green-500">data_object</span>
<input class="query-input" placeholder="Type your query" type="text"/>
<span class="material-icons query-icon text-blue-500">mic</span>
<div class="w-6 h-6 bg-orange-400 text-white text-sm rounded-full flex items-center justify-center font-semibold">A</div>
</div>
</header>
<section class="mb-12">
<div class="section-header">
<h2 class="category-title">General Model</h2>
<a class="see-all-link" href="#">See all</a>
</div>
<div class="relative">
<div class="flex space-x-6 overflow-x-auto pb-4 no-scrollbar">
<div class="model-card model-card-active flex-shrink-0 w-72">
<div class="flex items-center mb-3">
<div class="model-icon-bg mr-3">
<span class="material-icons model-icon">hub</span>
</div>
<h3 class="text-xl font-semibold">GPT-4o</h3>
</div>
<p class="text-sm model-description">Korem ipsum dolor sit amet, consectetur adipiscing elit. Nunc vulputate libero et velit interdum, ac aliquet odio mattis...</p>
</div>
<div class="model-card flex-shrink-0 w-72">
<div class="flex items-center mb-3">
<div class="model-icon-bg bg-purple-200 mr-3">
<span class="material-icons model-icon text-purple-600">psychology</span>
</div>
<h3 class="text-xl font-semibold text-slate-700">DeepSeek-V3</h3>
</div>
<p class="text-sm text-slate-500 model-description">Korem ipsum dolor sit amet, consectetur adipiscing elit. Nunc vulputate libero et velit interdum, ac aliquet odio mattis. Class what...</p>
</div>
<div class="model-card flex-shrink-0 w-72">
<div class="flex items-center mb-3">
<div class="model-icon-bg bg-indigo-200 mr-3">
<span class="material-icons model-icon text-indigo-600">model_training</span>
</div>
<h3 class="text-xl font-semibold text-slate-700">Gemini-1.5-Pro</h3>
</div>
<p class="text-sm text-slate-500 model-description">Korem ipsum dolor sit amet, consectetur adipiscing elit. Nunc vulputate libero et velit interdum, ac aliquet odio mattis. Class...</p>
</div>
<div class="model-card flex-shrink-0 w-72">
<div class="flex items-center mb-3">
<div class="model-icon-bg bg-sky-200 mr-3">
<span class="material-icons model-icon text-sky-600">auto_awesome</span>
</div>
<h3 class="text-xl font-semibold text-slate-700">Gemini-2.0-Flash</h3>
</div>
<p class="text-sm text-slate-500 model-description">Korem ipsum dolor sit amet, consectetur adipiscing elit. Nunc vulputate libero et velit interdum, ac aliquet odio mattis. Class...</p>
</div>
</div>
<div class="scroll-arrow absolute top-1/2 -right-4 transform -translate-y-1/2 hidden md:flex">
<span class="material-icons">chevron_right</span>
</div>
</div>
</section>
<section class="mb-12">
<div class="section-header">
<h2 class="category-title">Thinking Model</h2>
<a class="see-all-link" href="#">See all</a>
</div>
<div class="relative">
<div class="flex space-x-6 overflow-x-auto pb-4 no-scrollbar">
<div class="model-card flex-shrink-0 w-72">
<div class="flex items-center mb-3">
<div class="model-icon-bg bg-purple-200 mr-3">
<span class="material-icons model-icon text-purple-600">flare</span>
</div>
<h3 class="text-xl font-semibold text-slate-700">Gemini-2.5-Pro</h3>
</div>
<p class="text-sm text-slate-500 model-description">Korem ipsum dolor sit amet, consectetur adipiscing elit. Nunc vulputate libero et velit interdum, ac aliquet odio mattis. Class aptent taciti sociosqu ad litora torquent per conubia nostra...</p>
</div>
<div class="model-card flex-shrink-0 w-72">
<div class="flex items-center mb-3">
<div class="model-icon-bg bg-green-200 mr-3">
<span class="material-icons model-icon text-green-600">memory</span>
</div>
<h3 class="text-xl font-semibold text-slate-700">o1</h3>
</div>
<p class="text-sm text-slate-500 model-description">Korem ipsum dolor sit amet, consectetur adipiscing elit. Nunc vulputate libero et velit interdum, ac aliquet odio mattis whi...</p>
</div>
<div class="model-card flex-shrink-0 w-72">
<div class="flex items-center mb-3">
<div class="model-icon-bg bg-blue-200 mr-3">
<span class="material-icons model-icon text-blue-600">insights</span>
</div>
<h3 class="text-xl font-semibold text-slate-700">DeepSeek-R1</h3>
</div>
<p class="text-sm text-slate-500 model-description">Korem ipsum dolor sit amet, consectetur adipiscing elit. Nunc vulputate libero et velit interdum, ac aliquet odio mattis. Class...</p>
</div>
<div class="model-card flex-shrink-0 w-72">
<div class="flex items-center mb-3">
<div class="model-icon-bg bg-teal-200 mr-3">
<span class="material-icons model-icon text-teal-600">hub</span>
</div>
<h3 class="text-xl font-semibold text-slate-700">o3-mini</h3>
</div>
<p class="text-sm text-slate-500 model-description">Korem ipsum dolor sit amet, consectetur adipiscing elit. Nunc vulputate libero et velit interdum, ac aliquet odio mattis wh...</p>
</div>
</div>
<div class="scroll-arrow absolute top-1/2 -right-4 transform -translate-y-1/2 hidden md:flex">
<span class="material-icons">chevron_right</span>
</div>
</div>
</section>
<section>
<h2 class="category-title">Image Generation</h2>
<div class="flex space-x-6 overflow-x-auto pb-4 no-scrollbar">
<div class="model-card flex-shrink-0 w-72">
<div class="flex items-center mb-3">
<div class="model-icon-bg bg-red-200 mr-3 p-2">
<img alt="Adobe Firefly logo" class="w-full h-full object-contain" src="https://lh3.googleusercontent.com/aida-public/AB6AXuAwmPJVDf08tjmKs7g-31xqtd8ypQMtEbTrImS3X-geVFemfrTYjFJouFXlaLT7KVUrXpqBvpV00CN4yn7gYJWREjrLcyV_N1IwixglBKgR8nNfO46jKkrbOXNmN1GMK_jFEk79Tb3BbpPaTV29Qv775gy4797DOY30Up150doJF_iFgDBkm9QeZic6U9io6BJUa6FrIvJtOUd7cDlb7dT7mxdqSt1wyQ5yJ8YvFvG7U6XZobHdV0ajXJjj9O8AR04th_2J-b1bJEGr"/>
</div>
<h3 class="text-xl font-semibold text-slate-700">Adobe Firefly</h3>
</div>
<p class="text-sm text-slate-500 model-description">Korem ipsum dolor sit amet, consectetur adipiscing elit. Nunc vulputate libero et velit interdum, ac aliquet odio mattis. Class aptent taciti sociosqu ad litora torquent per conubia nostra...</p>
</div>
<div class="model-card flex-shrink-0 w-72">
<div class="flex items-center mb-3">
<div class="model-icon-bg bg-gray-800 mr-3 p-2">
<img alt="Adobe Express logo" class="w-full h-full object-contain" src="https://lh3.googleusercontent.com/aida-public/AB6AXuCijjwjaN3NNzUc8GqPd1DA8qHtDnuD6jBaUIAjPe8ctRFc0ayHslJEDwFavo_9U02P89Czwy27lCI6bBP6QxWPnMDywDOSk2ErrA4-B1lpl0odEy36eB6c3UVJIcUYD9i8aw9j4MbieAHstVOEUL7dzz1c-m2AphOKl43ii6VV20wPR0wlFjxxfIpminz04tFI37SqmSssL0u2u2HkrIqm8vESjLwwFPmjcA1pKXqBBjwJZj5rEZlfQc3L3qMePsfyMxXyKPejnMnq"/>
</div>
<h3 class="text-xl font-semibold text-slate-700">Adobe Express</h3>
</div>
<p class="text-sm text-slate-500 model-description">Korem ipsum dolor sit amet, consectetur adipiscing elit. Nunc vulputate libero et velit interdum, ac aliquet odio mattis. Class aptent taciti sociosqu ad litora torquent per conubia nostra...</p>
</div>
</div>
</section>
</main>

</body></html>