import { ConversationHistoryItem } from '../store/apiSlice';

export function groupHistoryByDate(
  items?: ConversationHistoryItem[],
): Record<string, ConversationHistoryItem[]> {
  return (
    items?.reduce(
      (acc, item) => {
        // Extract date part (YYYY-MM-DD) from ISO string
        const date = item.updated_at.split('T')[0];

        // Initialize array for this date if it doesn't exist
        acc[date] = acc[date] || [];

        // Add item to the date's array
        acc[date].push(item);

        return acc;
      },
      {} as Record<string, ConversationHistoryItem[]>,
    ) ?? {}
  );
}
