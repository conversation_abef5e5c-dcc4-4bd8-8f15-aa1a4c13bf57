import <PERSON>z<PERSON><PERSON>, { Data } from 'pizzip';
import { read, utils } from 'xlsx';
import { parse } from 'pptxtojson';
import { humanFileSize } from './HumanFileSize';

const fileSizeLimitString = process.env.NEXT_PUBLIC_UPLOAD_FILE_SIZE_LIMIT;
const fileSizeLimit = fileSizeLimitString
  ? parseInt(fileSizeLimitString, 10)
  : 10000000; // Default 10MB

export const sizeExceedsMessage = `The total size of the files exceeds the maximum allowed limit(${humanFileSize(
  fileSizeLimit,
  true,
  0,
)}). Please delete some files and try again.`;

const fileToString = (
  file: File,
  deployment_name: string,
  onComplete: (_: { name: string; data?: string }) => void,
  onError?: (_?: string) => void,
) => {
  if (file.size > fileSizeLimit) {
    onError?.(sizeExceedsMessage); // Check if onError exists
    return;
  }

  try {
    const reader = new FileReader();

    reader.onload = async (e) => {
      try {
        switch (file.type) {
          case 'text/csv':
          case 'text/plain':
            const text = e.target?.result as string;
            onComplete({ name: file.name, data: text });
            break;
          case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': //xlsx
          case 'application/vnd.ms-excel': //xls or xlt or xla
          case 'application/vnd.ms-excel.sheet.macroEnabled.12': //xlsm
          case 'application/vnd.ms-excel.sheet.binary.macroEnabled.12': //xlsb
          case 'application/vnd.openxmlformats-officedocument.spreadsheetml.template': //xltx
          case 'application/vnd.ms-excel.template.macroEnabled.12': //xltm
          case 'application/vnd.ms-excel.addin.macroEnabled.12': //xlam
            onComplete({
              name: file.name,
              data: formTableFromXlsx(e.target?.result as ArrayBuffer).join(
                // Add null check for e.target
                '\n \n',
              ),
            });
            break;
          case 'application/msword':
          case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
            const content = e.target?.result; // Add null check for e.target
            // Add type check for content before passing to getParagraphs
            const paragraphs =
              typeof content === 'string' || content instanceof ArrayBuffer
                ? getParagraphs(content)
                : [];
            onComplete({
              name: file.name,
              data: paragraphs.join(' '),
            });
            break;
          case 'application/vnd.openxmlformats-officedocument.presentationml.presentation':
            onComplete({
              name: file.name,
              data: await handlePptx(e.target?.result), // Add null check for e.target
            });
            break;
          default:
            throw null;
        }
      } catch (_e) {
        // Prefixed unused 'e'
        if (onError !== undefined) onError();
      }
    };

    //console.log("file.type", file.type);

    switch (file.type) {
      case 'image/bmp':
      case 'image/png':
      case 'image/jpeg':
        if (
          deployment_name.includes('vision') ||
          deployment_name.includes('claude') ||
          deployment_name.includes('gemini-1.5') ||
          deployment_name.includes('4o')
        ) {
          // directly send to vision to process
          convertImageToBase64(file, deployment_name, onComplete, onError);
          break;
        } else {
          ocr(file, onComplete, onError);
          break;
        }
      case 'image/tiff':
      case 'application/pdf':
        ocr(file, onComplete, onError);
        break;
      case 'text/csv':
      case 'text/plain':
        reader.readAsText(file, 'utf-8');
        break;
      case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': //xlsx
      case 'application/vnd.ms-excel': //xls or xlt or xla
      case 'application/vnd.ms-excel.sheet.macroEnabled.12': //xlsm
      case 'application/vnd.ms-excel.sheet.binary.macroEnabled.12': //xlsb
      case 'application/vnd.openxmlformats-officedocument.spreadsheetml.template': //xltx
      case 'application/vnd.ms-excel.template.macroEnabled.12': //xltm
      case 'application/vnd.ms-excel.addin.macroEnabled.12': //xlam
        reader.readAsArrayBuffer(file);
        break;
      case 'application/msword':
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        reader.readAsBinaryString(file);
        break;
      case 'application/vnd.openxmlformats-officedocument.presentationml.presentation':
        reader.readAsArrayBuffer(file);
        break;
      default:
        throw null;
    }
  } catch (_e) {
    // Prefixed unused 'e'
    if (onError !== undefined) onError();
  }
};

const str2xml = (str: string) => {
  if (str.charCodeAt(0) === 65279) {
    str = str.substr(1);
  }
  return new DOMParser().parseFromString(str, 'text/xml');
};

const getParagraphs = (content: Data) => {
  const zip = new PizZip(content);
  const xml = str2xml(zip.files['word/document.xml'].asText());
  const body: string[] = []; // Explicitly type body as string[]

  const bodyXml = xml.getElementsByTagName('w:body')[0];

  for (let i = 0, len = bodyXml.children.length; i < len; i++) {
    const item = bodyXml.children.item(i);
    let fullText = '';
    if (!item) continue; // Add null check for item
    switch (item.tagName) {
      case 'w:tbl':
        fullText += '\n';
        const rowsXml = item.getElementsByTagName('w:tr');
        for (let j = 0, len2 = rowsXml.length; j < len2; j++) {
          fullText += '|';
          const colsXml = rowsXml[j].getElementsByTagName('w:tc');
          for (let k = 0, len3 = colsXml.length; k < len3; k++) {
            const textsXml = colsXml[k].getElementsByTagName('w:t');
            if (textsXml.length === 0) {
              fullText += '(empty)';
            } else {
              for (let l = 0, len4 = textsXml.length; l < len4; l++) {
                const textXml = textsXml[l];
                if (textXml.childNodes) {
                  fullText += textXml.childNodes[0].nodeValue + '<br/>';
                }
              }
            }
            fullText += '|';
          }
          fullText += '\n';
        }
        fullText += '<br/>';
        break;
      default:
        const textsXml = item.getElementsByTagName('w:t');
        for (let j = 0, len2 = textsXml.length; j < len2; j++) {
          const textXml = textsXml[j];
          if (textXml.childNodes) {
            fullText += textXml.childNodes[0].nodeValue + '<br/>';
          }
        }
    }

    if (fullText) {
      body.push(fullText);
    }
  }

  return body;
};

const formTableFromXlsx = (data: ArrayBuffer) => {
  const wb = read(data);
  return wb.SheetNames.map((sheetName) => {
    const ws = wb.Sheets[sheetName];
    const data: string[][] = utils.sheet_to_json(ws, {
      header: 1,
      defval: '&#32',
    });
    const stringTable = data.map((row) => `|${row.join('|')}|`).join('\n');
    return data.length > 0 ? `sheet: ${sheetName}\n\n${stringTable}` : '';
  });
};

const ocr = async (
  file: File,
  onComplete: (_: { name: string; media_type?: string; data?: string }) => void,
  onError?: (_?: string) => void,
) => {
  try {
    const formData = new FormData();
    formData.append('file', file);
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_BASE_PATH}/api/general/ocr`,
      {
        method: 'POST',
        body: formData,
      },
    );
    const { result } = await response.json();
    onComplete({
      name: file.name,
      data: result,
    });
  } catch (_error) {
    // Prefixed unused 'error'
    onError?.(''); // Check if onError exists
  }
};

const convertImageToBase64 = (
  file: File,
  deploymentName: string,
  onComplete: {
    (_: { name: string; data?: string }): void;
    (arg0: {
      name: string;
      data: string;
      media_type: string;
      width: number;
      height: number;
    }): void;
  },
  onError: ((_?: string) => void) | undefined,
) => {
  const reader = new FileReader();

  reader.onloadend = () => {
    let base64String = reader.result as string;
    const image = new Image();

    image.onload = () => {
      const resizeFactor = getResizingScaleFactor(
        image.width,
        image.height,
        deploymentName,
      );

      const targetWidth = image.width * resizeFactor;
      const targetHeight = image.height * resizeFactor;
      const fileType = file.type === 'image/bmp' ? 'image/jpeg' : file.type;

      if (resizeFactor != 1 || file.type === 'image/bmp') {
        //console.log("resizeFactor", resizeFactor);
        const canvas = document.createElement('canvas');
        canvas.width = targetWidth;
        canvas.height = targetHeight;

        const ctx = canvas.getContext('2d');
        if (ctx) {
          // Add null check for ctx
          ctx.drawImage(image, 0, 0, targetWidth, targetHeight);
        } else {
          console.error('Failed to get canvas context for image resizing.');
          onError?.('Failed to process image.'); // Notify error if context fails
          return; // Exit if context is null
        }

        // Convert canvas content to JPEG Base64 string
        base64String = canvas.toDataURL(fileType);
      }
      onComplete({
        name: file.name,
        data: base64String.replace(/^data:image\/\w+;base64,/, ''),
        media_type: fileType,
        width: targetWidth,
        height: targetHeight,
      });
    };

    image.onerror = () => {
      onError?.('Failed to load image for dimensions.');
    };

    // Set the src of the Image object to the Base64-encoded string
    image.src = base64String;
  };

  reader.onerror = () => {
    onError?.('Failed to convert image to base64.');
  };

  // Initiate the FileReader operation
  reader.readAsDataURL(file);
};
const getResizingScaleFactor = (
  width: number,
  height: number,
  _deployment_name: string,
) => {
  // Added types and prefixed unused deployment_name
  let scaleFactor = 1;
  const maxDimension = 512;

  if (Math.max(width, height) > maxDimension) {
    scaleFactor = maxDimension / Math.max(width, height);
  }
  // if (deployment_name.includes("claude")) {
  //   const maxDimension = 512;

  //   if (Math.max(width, height) > maxDimension) {
  //     scaleFactor = maxDimension / Math.max(width, height);
  //   }
  // } else if (deployment_name.includes("gpt-4-vision")) {
  //   const maxLongSide = 2000;
  //   const maxShortSide = 768;

  //   if (width > height) {
  //     if (width > maxLongSide) {
  //       scaleFactor = maxLongSide / width;
  //     }
  //     if (height * scaleFactor > maxShortSide) {
  //       scaleFactor = maxShortSide / height;
  //     }
  //   } else {
  //     if (height > maxLongSide) {
  //       scaleFactor = maxLongSide / height;
  //     }
  //     if (width * scaleFactor > maxShortSide) {
  //       scaleFactor = maxShortSide / width;
  //     }
  //   }
  // }
  return scaleFactor;
};

const handlePptx = async (data: unknown) => {
  // Changed any to unknown
  // Add type check for data before passing to parse
  if (!(data instanceof ArrayBuffer)) {
    console.error('handlePptx received invalid data type:', typeof data);
    return 'Error: Invalid PPTX data format.'; // Return error string
  }
  const pptJson = await parse(data);
  const stringhtml = pptJson.slides
    .map((slide) =>
      slide.elements
        .filter((ele) => ele.type === 'text')
        .map(
          (ele) =>
            // Removed @ts-ignore - If pptxtojson types are insufficient, consider adding a local type definition
            ele.content,
        )
        .join(),
    )
    .join();

  const parser = new DOMParser(); // Changed let to const
  const doc = parser.parseFromString(stringhtml, 'text/html');
  const filteredElements = doc.querySelectorAll(
    'title:not(:has(*)), h1:not(:has(*)), h2:not(:has(*)), h3:not(:has(*)), span:not(:has(*)), p:not(:has(*)), div:not(:has(*))',
  );
  filteredElements.forEach((element) => {
    Array.from(element.attributes).forEach((attr) => {
      element.removeAttribute(attr.name);
    });
  });
  let filteredHtmlString = '';
  filteredElements.forEach((element) => {
    filteredHtmlString += element.innerHTML + ' ';
  });

  return `content of a .pptx file:\n${filteredHtmlString} `;
};

export default fileToString;
