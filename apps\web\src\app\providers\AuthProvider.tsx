'use client'; // This directive is required for SessionProvider

import { SessionProvider, useSession } from 'next-auth/react';
import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { setAuthSession, clearAuthSession } from '@/lib/store/authSlice';
import { AppDispatch } from '@/lib/store/store'; // Assuming AppDispatch is exported from store

// Define a new component to bridge session data to Redux
const SessionToReduxBridge: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const { status } = useSession();
  const dispatch = useDispatch<AppDispatch>();

  useEffect(() => {
    if (status === 'authenticated') {
      dispatch(setAuthSession({ isAuthenticated: true }));
    } else if (status === 'unauthenticated') {
      dispatch(clearAuthSession());
    }
    // No action needed for 'loading' status
  }, [status, dispatch]);

  return <>{children}</>; // Render children directly
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export default function AuthProvider({ children }: AuthProviderProps) {
  return (
    <SessionProvider basePath={`${process.env.NEXT_PUBLIC_BASE_PATH}/api/auth`}>
      <SessionToReduxBridge>{children}</SessionToReduxBridge>
    </SessionProvider>
  );
}
