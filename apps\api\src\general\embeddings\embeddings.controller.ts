import {
  Controller,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  Req,
  Logger,
  HttpException,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { ApiKeyAuthGuard } from '../../auth/guards/api-key-auth.guard'; // Adjust path
import { ModelRateLimitGuard } from '../../common/guards/model-rate-limit.guard';
import { GeneralRateLimitGuard } from '../../common/guards/general-rate-limit.guard';
import { EmbeddingsService } from './embeddings.service'; // Uncomment and import
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiHeader,
  ApiBody,
  ApiProperty,
  ApiPropertyOptional,
} from '@nestjs/swagger'; // Added ApiPropertyOptional
import { ApiUserPayload } from '../../auth/api-key.strategy'; // Adjust path
import {
  IsNotEmpty,
  IsOptional,
  IsString,
  <PERSON>In,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
} from 'class-validator';

// DTO for creating an embeddings request
// Based on OpenAI's spec: input (string or array of strings) and model (string)
export class CreateEmbeddingsDto {
  @ApiProperty({
    description:
      'Input text to embed, encoded as a string or array of strings. The array can contain up to 2048 strings.',
    oneOf: [
      { type: 'string', example: 'The food was delicious and the waiter...' },
      {
        type: 'array',
        items: { type: 'string' },
        example: ['The food was delicious', 'and the waiter...'],
      },
    ],
  })
  @IsNotEmpty()
  input!: string | string[]; // Added '!' for definite assignment assertion, assuming it's always provided

  @ApiPropertyOptional({
    // Changed to ApiPropertyOptional as model can come from path
    description:
      'ID of the model to use. This typically comes from the path parameter but can be overridden here.',
    example: 'text-embedding-ada-002',
    required: false,
  })
  @IsOptional()
  @IsString()
  model?: string;

  @ApiPropertyOptional({
    description:
      'The format to return the embeddings in. Can be either float or base64.',
    enum: ['float', 'base64'],
    required: false,
  })
  @IsOptional()
  @IsIn(['float', 'base64'])
  encoding_format?: 'float' | 'base64';

  @ApiPropertyOptional({
    description:
      'The number of dimensions the resulting output embeddings should have. Only supported in `text-embedding-3` and later models.',
    type: Number,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  dimensions?: number;

  // user field is not typically part of embeddings request body for OpenAI but good for logging/internal use
  // @ApiPropertyOptional({ description: 'A unique identifier representing your end-user, which can help OpenAI to monitor and detect abuse.', example: 'user-12345', required: false })
  // user?: string;
}

interface AuthenticatedRequestWithUser extends Request {
  // Express Request
  user: ApiUserPayload;
}

@ApiTags('LLM - Embeddings (REST API)')
@Controller('rest/deployments/:modelDeploymentName/embeddings')
@UseGuards(ApiKeyAuthGuard, ModelRateLimitGuard, GeneralRateLimitGuard)
@ApiHeader({
  name: 'api-key',
  description: 'Your API Key for authentication.',
  required: true,
})
export class EmbeddingsController {
  private readonly logger = new Logger(EmbeddingsController.name);

  constructor(private readonly embeddingsService: EmbeddingsService) {} // Uncomment constructor

  @Post()
  @HttpCode(HttpStatus.OK) // OpenAI uses 200 for embeddings
  @ApiOperation({
    summary: 'Creates an embedding vector representing the input text.',
  })
  @ApiParam({
    name: 'modelDeploymentName',
    description:
      'The deployment name of the embeddings model (e.g., text-embedding-ada-002)',
    required: true,
  })
  @ApiBody({ type: CreateEmbeddingsDto })
  @ApiResponse({ status: 200, description: 'Embeddings created successfully.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 500, description: 'Internal server error.' })
  async createEmbeddings(
    @Query('api-version') apiVersion: string,
    @Param('modelDeploymentName') modelDeploymentName: string,
    @Body() createEmbeddingsDto: CreateEmbeddingsDto,
    @Req() req: AuthenticatedRequestWithUser,
  ) {
    this.logger.log(
      `Embeddings request for model [${modelDeploymentName}], API version [${apiVersion}] by user [${req.user.ssoid}]`,
    );

    // Ensure the model from path is used if not provided in body, or if body's model should be overridden
    const effectiveDto: CreateEmbeddingsDto = {
      ...createEmbeddingsDto,
      model: modelDeploymentName, // Prioritize model from path
    };

    try {
      const result = await this.embeddingsService.create(
        req.user,
        apiVersion,
        modelDeploymentName,
        effectiveDto,
      );
      return result;
      // this.logger.warn('EmbeddingsService.create() not implemented yet. Returning placeholder.');
      // return {
      //     object: "list",
      //     data: [
      //         { object: "embedding", index: 0, embedding: [0.0023064255, -0.009327292, 0.0028842222] } // Example data
      //     ],
      //     model: modelDeploymentName,
      //     usage: { prompt_tokens: 8, total_tokens: 8 }
      // };
    } catch (error) {
      let errorMessage =
        'An unknown error occurred while processing the embeddings request.';
      let errorStack: string | undefined = undefined; // Explicitly type errorStack
      if (error instanceof Error) {
        errorMessage = error.message;
        errorStack = error.stack;
      }
      this.logger.error(
        `Error in createEmbeddings for ${modelDeploymentName} by ${req.user.ssoid}: ${errorMessage}`,
        errorStack,
      );
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        'Failed to process embeddings request.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
