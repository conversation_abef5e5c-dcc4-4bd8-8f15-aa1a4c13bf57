import { useState, useEffect } from 'react';

export interface FrontendConfig {
  // Add any non-sensitive config values that the frontend needs here
  // For example:
  // featureFlags: {
  //   newChat: boolean;
  // };
  placeholder?: string;
}

export function useConfig() {
  const [config] = useState<FrontendConfig>({});
  const [error] = useState<Error | null>(null);

  return { config, error };
}