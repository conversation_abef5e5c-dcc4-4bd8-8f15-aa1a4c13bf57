'use client';

import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import MinimalSwaggerUI from './MinimalSwaggerUI';
import { Box, CircularProgress, Alert, Typography } from '@mui/material';

interface ClientSwaggerFetcherProps {
  provider: string;
  config: any;
}

export default function ClientSwaggerFetcher({
  provider,
  config,
}: ClientSwaggerFetcherProps) {
  const { data: session, status } = useSession();
  const [spec, setSpec] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchSwaggerSpec = async () => {
      try {
        setLoading(true);
        setError(null);

        const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || '/api/v0';
        const response = await fetch(
          `${apiBaseUrl}/general/rest/docs/${provider}-swagger.json`,
          {
            headers: {
              Authorization: `Bearer ${session?.accessToken}`,
            },
          },
        );

        if (!response.ok) {
          throw new Error(
            `Failed to fetch swagger spec: ${response.status} ${response.statusText}`,
          );
        }

        const data = await response.json();
        setSpec(data);
      } catch (err) {
        console.error('Error fetching swagger spec:', err);
        setError(
          err instanceof Error ? err.message : 'An unknown error occurred',
        );
      } finally {
        setLoading(false);
      }
    };

    if (status === 'authenticated' && session?.accessToken) {
      fetchSwaggerSpec();
    } else if (status === 'unauthenticated') {
      setLoading(false);
    }
  }, [session, status, provider]);

  if (status === 'loading' || loading) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '400px',
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  if (!session) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          <Typography>Access Denied</Typography>
          <Typography variant="body2">
            You must be signed in to view this page.
          </Typography>
        </Alert>
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          <Typography>Error loading API documentation</Typography>
          <Typography variant="body2">{error}</Typography>
        </Alert>
      </Box>
    );
  }

  if (!spec) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="warning">
          No specification available for {provider}.
        </Alert>
      </Box>
    );
  }

  return <MinimalSwaggerUI spec={spec} />;
}
