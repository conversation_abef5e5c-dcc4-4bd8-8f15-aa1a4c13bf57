-- =============================================
-- Author:      ken-chow / Roo
-- Create date: 2025-04-17
-- Description: Retrieves and decrypts messages and their associated sources for a specific conversation UUID.
--              Handles prompts stored as multiple individually encrypted text chunks in dbo.prompt,
--              decrypting each chunk and concatenating them in order.
--              Also includes fallback for old structure (single prompt in dbo.message.last_prompt).
--              Sources are decrypted and returned as a JSON array string.
-- History
-- When			Who			Remarks
-- 2025-04-17   ken-chow    Initial version with STRING_AGG for potential chunking.
-- 2025-04-25   Roo         Removed STRING_AGG and CTE; simplified to join single prompt row (prompt_order = 1). (Reverted below)
-- 2025-04-25   Roo         Reinstated CTE and STRING_AGG to handle individually encrypted text chunks.
-- =============================================
ALTER PROCEDURE [dbo].[sp_cvst_GetDecryptedMessagesByConversationUUID]
    @conversation_uuid UNIQUEIDENTIFIER,
    @encryption_key_name NVARCHAR(128),  -- Input parameter for key name
    @decryption_cert_name NVARCHAR(128) -- Input parameter for cert name
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @conversation_id INT;

    -- Check if the symmetric key exists
    IF NOT EXISTS (SELECT * FROM sys.symmetric_keys WHERE name = @encryption_key_name)
    BEGIN
        RAISERROR('Symmetric key specified by @encryption_key_name (''%s'') not found.', 16, 1, @encryption_key_name);
        RETURN;
    END

    -- Check if the certificate used for the key exists
    IF NOT EXISTS (SELECT * FROM sys.certificates WHERE name = @decryption_cert_name)
    BEGIN
        RAISERROR('Certificate specified by @decryption_cert_name (''%s'') for symmetric key not found.', 16, 1, @decryption_cert_name);
        RETURN;
    END

    -- Find the conversation_id for the given UUID
    SELECT @conversation_id = conversation_id
    FROM dbo.conversation
    WHERE conversation_uuid = @conversation_uuid AND delete_dt IS NULL;

    -- If conversation_id is not found, return empty result with correct schema
    IF @conversation_id IS NULL
    BEGIN
        SELECT
            CAST(NULL AS UNIQUEIDENTIFIER) AS message_uuid,
            CAST(NULL AS VARCHAR(20)) AS sender,
            CAST(NULL AS DATETIME) AS create_dt,
            CAST(NULL AS VARCHAR(100)) AS model_name, -- Match potential size
            CAST(NULL AS VARCHAR(1)) AS reaction,
            CAST(NULL AS INT) AS token_spent,
            CAST(NULL AS NVARCHAR(MAX)) AS content,
            CAST(NULL AS NVARCHAR(MAX)) AS sources_json, -- Added for sources
            CAST(NULL AS BIT) AS is_deleted, -- Added for soft delete tracking
            CAST(NULL AS BIT) AS used_mention -- Added for @mention tracking
        WHERE 1 = 0; -- Ensures schema is returned but no rows
        RETURN;
    END

    BEGIN TRY
        -- Open the symmetric key
        DECLARE @OpenKeySQL NVARCHAR(MAX) = N'OPEN SYMMETRIC KEY ' + QUOTENAME(@encryption_key_name) + N' DECRYPTION BY CERTIFICATE ' + QUOTENAME(@decryption_cert_name) + N';';
        EXEC sp_executesql @OpenKeySQL;

        -- Use CTE to aggregate individually decrypted prompt chunks
        ;WITH AggregatedPrompts AS (
            SELECT
                m.message_id,
                m.message_uuid,
                m.sender,
                m.create_dt,
                m.model_name,
                m.reaction,
                m.token_spent,
                m.last_prompt, -- Include last_prompt for fallback
                m.is_deleted, -- Include is_deleted field
                m.used_mention, -- Include used_mention field
                -- Aggregate decrypted prompt chunks
                STRING_AGG(
                     -- Decrypt each chunk and convert to NVARCHAR(MAX)
                    CASE
                        WHEN p.prompt_data IS NULL THEN NULL -- Should be handled by LEFT JOIN, but explicit check is safe
                        ELSE COALESCE(CONVERT(NVARCHAR(MAX), DecryptByKey(p.prompt_data)), N'[Decryption Error]')
                    END,
                    N'' -- Concatenate parts directly
                ) WITHIN GROUP (ORDER BY p.prompt_order ASC) AS aggregated_content
            FROM
                dbo.message m
            LEFT JOIN -- Use LEFT JOIN to include messages without prompts or for fallback
                dbo.prompt p ON m.message_id = p.message_id
            WHERE
                m.conversation_id = @conversation_id
                AND COALESCE(m.is_deleted, 0) = 0  -- Filter out soft-deleted messages
            GROUP BY -- Group by all message fields to aggregate prompts per message
                m.message_id, m.message_uuid, m.sender, m.create_dt, m.model_name,
                m.reaction, m.token_spent, m.last_prompt, m.is_deleted, m.used_mention
        )
        -- Select final results, choosing between aggregated prompts or last_prompt
        SELECT
            ap.message_uuid,
            ap.sender,
            ap.create_dt,
            ap.model_name,
            ap.reaction,
            ap.token_spent,
            COALESCE(
                ap.aggregated_content, -- Prioritize aggregated content from dbo.prompt (new structure)
                -- Fallback to decrypting last_prompt if aggregated_content is NULL (old structure)
                CASE
                    WHEN ap.last_prompt IS NULL THEN N'' -- Handle NULL last_prompt
                    ELSE COALESCE(CONVERT(NVARCHAR(MAX), DecryptByKey(ap.last_prompt)), N'[Decryption Error]')
                END,
                N'' -- Final fallback if both sources are NULL or fail decryption
            ) AS content,
            -- Add subquery to fetch, decrypt, and aggregate sources as JSON
            COALESCE(
                (
                    SELECT
                        COALESCE(CONVERT(NVARCHAR(MAX), DecryptByKey(ms.title_encrypted)), N'[Decryption Error]') AS title,
                        COALESCE(CONVERT(NVARCHAR(MAX), DecryptByKey(ms.link_encrypted)), N'[Decryption Error]') AS link,
                        COALESCE(CONVERT(NVARCHAR(MAX), DecryptByKey(ms.snippet_encrypted)), N'[Decryption Error]') AS snippet
                    FROM dbo.message_source ms
                    WHERE ms.message_id = ap.message_id -- Correlate with the outer message
                    ORDER BY ms.message_source_id ASC -- Maintain source order if needed
                    FOR JSON PATH
                ),
                N'[]' -- Default to empty JSON array if no sources exist
            ) AS sources_json,
            ap.is_deleted, -- Include is_deleted field in results
            ap.used_mention -- Include used_mention field in results
        FROM
            AggregatedPrompts ap
        ORDER BY
            ap.create_dt ASC; -- Order messages chronologically

        -- Close the symmetric key
        DECLARE @CloseKeySQL NVARCHAR(MAX) = N'CLOSE SYMMETRIC KEY ' + QUOTENAME(@encryption_key_name) + N';';
        EXEC sp_executesql @CloseKeySQL;

    END TRY
    BEGIN CATCH
        -- Ensure key is closed if an error occurred before closing
        DECLARE @CheckOpenKeySQL NVARCHAR(MAX) = N'IF EXISTS (SELECT * FROM sys.openkeys WHERE key_name = ''' + REPLACE(@encryption_key_name, '''', '''''') + N''') CLOSE SYMMETRIC KEY ' + QUOTENAME(@encryption_key_name) + N';';
        EXEC sp_executesql @CheckOpenKeySQL;

        -- Re-throw the error
        ;THROW;
    END CATCH

END
GO