'use client';

import React from 'react';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Link from '@mui/material/Link'; // Using MUI Link

const PrivacyPolicySection: React.FC = () => {
  const privacyPolicyUrl =
    'https://bupdpo.hkbu.edu.hk/policies-and-procedures/pps-pics/';
  const hyperLinkStyle = { color: 'primary.main' };

  return (
    <Box className="text-sm" sx={{ color: 'text.primary' }}>
      <Typography variant="body2" paragraph>
        The University is committed to protecting your privacy and will only use
        your personal data in accordance with the University’s Privacy Policy
        Statement and Personal Information Collection Statement, and the
        relevant laws.
      </Typography>
      <Typography variant="body2" paragraph>
        For detailed information, please refer to the official{' '}
        <Link
          sx={hyperLinkStyle}
          href={privacyPolicyUrl}
          target="_blank"
          rel="noopener noreferrer"
        >
          HKBU Privacy Policy Statement and Personal Information Collection
          Statement
        </Link>
        .
      </Typography>
    </Box>
  );
};

export default PrivacyPolicySection;
