{"description": "Configuration templates for stress testing scenarios", "version": "1.0.0", "stress_scenarios": {"rapid_fire": {"name": "Rapid Fire Testing", "description": "High-frequency requests with minimal intervals", "config": {"num_requests": 20, "interval": 0.2, "timeout": 10, "max_retries": 2}, "prompts": ["Quick: What is 1+1?", "Fast: Name a color.", "Brief: Capital of USA?", "Short: What is H2O?", "Simple: 10/2 = ?"], "success_criteria": {"min_success_rate": 0.8, "max_avg_response_time": 5.0, "max_timeouts": 3}}, "sustained_heavy_load": {"name": "Sustained Heavy Load", "description": "Consistent high load over extended period", "config": {"num_requests": 30, "interval": 0.5, "timeout": 30, "max_retries": 1}, "prompts": ["Explain the concept of artificial intelligence in detail.", "Describe the process of climate change and its effects.", "Write a comprehensive guide to healthy eating habits.", "Analyze the benefits and drawbacks of renewable energy.", "Discuss the impact of social media on modern society."], "success_criteria": {"min_success_rate": 0.9, "max_avg_response_time": 20.0, "max_timeouts": 2}}, "peak_burst": {"name": "Peak Burst Testing", "description": "Short bursts of very high activity", "config": {"num_requests": 15, "interval": 0.1, "timeout": 8, "max_retries": 1}, "prompts": ["Quick math: 7 * 8 = ?", "One word: Opposite of hot?", "Fast: How many days in a week?", "Simple: What comes after 9?", "Brief: Largest planet?"], "success_criteria": {"min_success_rate": 0.75, "max_avg_response_time": 4.0, "allow_rate_limiting": true}}, "endurance_test": {"name": "Endurance Testing", "description": "Long-running test with consistent moderate load", "config": {"num_requests": 50, "interval": 2.0, "timeout": 45, "max_retries": 3}, "prompts": ["Provide a detailed explanation of photosynthesis including chemical equations.", "Write a comprehensive analysis of World War II's major turning points.", "Explain quantum mechanics and its practical applications in technology.", "Describe the entire process of software development from planning to deployment.", "Analyze the economic factors that led to the 2008 financial crisis."], "success_criteria": {"min_success_rate": 0.95, "max_avg_response_time": 30.0, "response_consistency": true}}, "variable_load": {"name": "Variable Load <PERSON>", "description": "Alternating between low and high load periods", "phases": [{"name": "warm_up", "num_requests": 5, "interval": 3.0, "prompts": ["What is the weather like today?"]}, {"name": "ramp_up", "num_requests": 10, "interval": 1.0, "prompts": ["Explain machine learning basics."]}, {"name": "peak_load", "num_requests": 20, "interval": 0.3, "prompts": ["Quick: What is 5+5?", "Fast: Name an animal."]}, {"name": "cool_down", "num_requests": 5, "interval": 4.0, "prompts": ["Describe a sunset."]}], "success_criteria": {"phase_success_rates": {"warm_up": 1.0, "ramp_up": 0.95, "peak_load": 0.8, "cool_down": 1.0}}}, "memory_pressure": {"name": "Memory Pressure Test", "description": "Large payloads to test memory handling", "config": {"num_requests": 10, "interval": 5.0, "timeout": 60, "max_retries": 2}, "prompts": ["Write a very detailed 1000-word essay about the history of computing, covering everything from early mechanical calculators to modern artificial intelligence, including all major milestones, key inventors, technological breakthroughs, and societal impacts.", "Provide an exhaustive analysis of climate change, including detailed explanations of greenhouse gases, global temperature trends, ice sheet melting, sea level rise, extreme weather patterns, ecosystem disruptions, economic impacts, mitigation strategies, and adaptation measures.", "Create a comprehensive programming tutorial covering basic concepts, data types, control structures, functions, object-oriented programming, error handling, best practices, debugging techniques, and provide multiple code examples in different languages."], "success_criteria": {"min_success_rate": 0.9, "max_avg_response_time": 45.0, "check_response_completeness": true}}, "timeout_stress": {"name": "Timeout Stress Test", "description": "Test behavior under timeout pressure", "config": {"num_requests": 15, "interval": 1.0, "timeout": 3, "max_retries": 0}, "prompts": ["Write a complete novel about space exploration with detailed character development, plot twists, scientific accuracy, and compelling narrative arc spanning multiple chapters.", "Provide a comprehensive analysis of every aspect of quantum physics including mathematical proofs, historical development, practical applications, and philosophical implications.", "Create a detailed business plan for a tech startup including market analysis, financial projections, competitive landscape, marketing strategy, and operational procedures."], "success_criteria": {"expect_timeouts": true, "min_attempts": 15, "timeout_handling": "graceful"}}}, "load_patterns": {"linear_ramp": {"description": "Gradually increasing load", "pattern": "linear", "start_interval": 3.0, "end_interval": 0.5, "duration_minutes": 5}, "exponential_growth": {"description": "Exponentially increasing request rate", "pattern": "exponential", "base_interval": 2.0, "growth_factor": 0.8, "max_requests": 25}, "sine_wave": {"description": "Oscillating load pattern", "pattern": "sine", "min_interval": 0.5, "max_interval": 3.0, "period_minutes": 3, "cycles": 2}}, "failure_scenarios": {"network_simulation": {"name": "Network Issues Simulation", "description": "Simulate network problems", "scenarios": [{"type": "intermittent_failures", "failure_rate": 0.1, "description": "Random 10% failure rate"}, {"type": "timeout_bursts", "burst_duration": 30, "burst_interval": 120, "description": "30s timeout bursts every 2 minutes"}]}, "rate_limiting": {"name": "Rate Limiting Tests", "description": "Test rate limiting behavior", "test_rates": [{"requests_per_minute": 30, "expected": "success"}, {"requests_per_minute": 60, "expected": "success"}, {"requests_per_minute": 120, "expected": "rate_limited"}, {"requests_per_minute": 300, "expected": "rate_limited"}]}}, "monitoring": {"metrics_to_track": ["request_rate", "response_time_distribution", "error_rate", "timeout_rate", "throughput", "concurrent_connections", "memory_usage", "cpu_utilization"], "alerting_thresholds": {"error_rate": 0.05, "avg_response_time": 30.0, "timeout_rate": 0.02}}, "recovery_testing": {"graceful_degradation": {"description": "Test system behavior under extreme load", "scenarios": ["overload_detection", "circuit_breaker_activation", "load_shedding", "recovery_after_overload"]}}}