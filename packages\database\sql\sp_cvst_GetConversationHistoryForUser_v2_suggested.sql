-- =============================================
-- Author:      <PERSON>
-- Create date: 2025-04-16
-- Description: Retrieves paginated conversation UUID, creation date, decrypted title, and model name for a specific user (ssoid).
--              (Modified: Selects title directly from conversation table and decrypts it, adds pagination, re-adds model_name, and total count)
-- Version: 2.0
-- =============================================
ALTER PROCEDURE [dbo].[sp_cvst_GetConversationHistoryForUser]
    @ssoid VARCHAR(30),
    @encryption_key_name NVARCHAR(128),  -- Input parameter for key name
    @decryption_cert_name NVARCHAR(128), -- Input parameter for cert name
    @PageNumber INT = 1,
    @PageSize INT = 10,
    @TotalRecords INT OUTPUT -- Although service now does its own count, SP calculates it.
AS
BEGIN
    SET NOCOUNT ON;

    -- Check if the symmetric key exists using the input parameter
    IF NOT EXISTS (SELECT * FROM sys.symmetric_keys WHERE name = @encryption_key_name)
    BEGIN
        RAISERROR('Symmetric key specified by @encryption_key_name (''%s'') not found.', 16, 1, @encryption_key_name);
        RETURN;
    END

    -- Check if the certificate used for the key exists using the input parameter
    IF NOT EXISTS (SELECT * FROM sys.certificates WHERE name = @decryption_cert_name)
    BEGIN
        RAISERROR('Certificate specified by @decryption_cert_name (''%s'') for symmetric key not found.', 16, 1, @decryption_cert_name);
        RETURN;
    END

    BEGIN TRY
        -- Calculate total records for the user
        SELECT @TotalRecords = COUNT(*)
        FROM dbo.conversation c
        WHERE c.ssoid = @ssoid AND c.delete_dt IS NULL;

        -- Open the symmetric key
        DECLARE @OpenKeySQL NVARCHAR(MAX) = N'OPEN SYMMETRIC KEY ' + QUOTENAME(@encryption_key_name) + N' DECRYPTION BY CERTIFICATE ' + QUOTENAME(@decryption_cert_name) + N';';
        EXEC sp_executesql @OpenKeySQL;

        -- Select required fields directly from the conversation table, decrypting the title
        SELECT
            c.conversation_uuid,
            c.create_dt, -- This will be used as 'updated_at' for now
            c.model_name, -- Re-added model_name
            CASE
                WHEN c.conversation_title IS NULL THEN N'Source Title is NULL'
                ELSE
                    COALESCE(
                        CONVERT(NVARCHAR(MAX), DecryptByKey(c.conversation_title)),
                        N'Decryption/Conversion Failed'
                    )
            END AS conversation_title
        FROM
            dbo.conversation c
        WHERE
            c.ssoid = @ssoid AND c.delete_dt IS NULL
        ORDER BY
            COALESCE(c.update_dt, c.create_dt) DESC,  -- Order by updated date, fallback to create date for shared conversations
            c.create_dt DESC -- Order final results
        OFFSET (@PageNumber - 1) * @PageSize ROWS
        FETCH NEXT @PageSize ROWS ONLY;

        -- Close the symmetric key
        DECLARE @CloseKeySQL NVARCHAR(MAX) = N'CLOSE SYMMETRIC KEY ' + QUOTENAME(@encryption_key_name) + N';';
        EXEC sp_executesql @CloseKeySQL;

    END TRY
    BEGIN CATCH
        -- Ensure key is closed if an error occurred before closing
        DECLARE @CheckOpenKeySQL NVARCHAR(MAX) = N'IF EXISTS (SELECT * FROM sys.openkeys WHERE key_name = ''' + REPLACE(@encryption_key_name, '''', '''''') + N''') CLOSE SYMMETRIC KEY ' + QUOTENAME(@encryption_key_name) + N';';
        EXEC sp_executesql @CheckOpenKeySQL;

        -- Re-throw the error
        ;THROW;
    END CATCH

END
GO