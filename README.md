# HKBU GenAI Platform - Project Overview

## 1. Project Goal

The HKBU GenAI Platform aims to provide a comprehensive and robust platform for leveraging various Generative AI models and services. It includes a user-facing web application for chat interactions, a backend API for managing LLM operations, and a set of administrative and user-centric features for an integrated experience. The platform supports multiple LLM providers and focuses on providing both interactive chat functionalities and a consumable API service for external applications.

## 2. Technology Stack

The project is a monorepo (`hkbu-genai-platform`) containing a web application and an API service.

### 2.1. Frontend (Web Application - `apps/web`)

*   **Framework**: Next.js 15.2.4 (with React 19, App Router)
*   **Language**: TypeScript
*   **UI Libraries & Styling**:
    *   Material-UI (MUI)
    *   Emotion (for styling with MUI)
    *   Headless UI
    *   Heroicons
    *   Tailwind CSS (for utility-first CSS)
*   **State Management**: Redux Toolkit
*   **Authentication**: NextAuth.js v4.24.11 (with BUAM OIDC provider)
*   **Data Fetching/Caching**: SWR, Axios
*   **Markdown & Content Rendering**: `marked`, `react-markdown`, `highlight.js` (for code syntax highlighting), `katex` (for LaTeX rendering)
*   **File Handling**: `pizzip` (DOCX), `xlsx` (Excel), `pptxtojson` (PowerPoint)
*   **Charting**: Chart.js
*   **Utilities**: Lodash, Moment.js, crypto-js

### 2.2. Backend (API Service - `apps/api`)

*   **Framework**: NestJS 11
*   **Language**: TypeScript
*   **Server Platforms**: Express, Fastify (supports both)
*   **Database/ORM**: Prisma (with a shared `@hkbu-genai-platform/database` package)
*   **Authentication**: Passport.js (JWT strategy, custom strategies)
*   **API Documentation**: Swagger (OpenAPI)
*   **LLM Integrations**:
    *   Langchain (core, community, OpenAI, Google GenAI, Google VertexAI, Anthropic)
    *   Direct SDKs: Azure OpenAI, Google Vertex AI, Anthropic AI SDK
*   **Cloud Services**:
    *   Azure Cognitive Services (Computer Vision for OCR)
    *   Google Cloud Speech
*   **Validation**: `class-validator`, `class-transformer`
*   **Other Key Libraries**: Nodemailer (for email), UUID, Rate Limiting (`@nestjs/throttler`)

### 2.3. Database

*   Microsoft SQL Server (implied by connection string and stored procedure names like `sp_...`)
*   Prisma as the ORM.
*   Utilizes various stored procedures for data access and business logic (e.g., `sp_model_GetModelList_v6`, `sp_acl_GetApiUserByUsername`).

## 3. Core Features & Functionality

### 3.1. User Authentication and Authorization
*   **Frontend Authentication**: User login via BUAM OIDC provider, managed by NextAuth.js.
*   **Session Management**: JWT-based sessions.
*   **Route Protection**: Frontend routes protected, redirecting unauthenticated users to a sign-in page.
*   **API Authentication (for external API service)**: API key-based authentication.
*   **API Authorization**: Granular access control for API usage based on user roles/department (e.g., 'ITO', 'SCI', 'SCID', whitelisted API users), replicating logic from `check_enable_restful_api.ts`.

### 3.2. LLM Chat Interface
*   Interactive chat interface for various LLM models.
*   Support for multiple LLM providers (Azure OpenAI, Google Vertex AI, Anthropic, etc.).
*   (Details on specific chat features like file upload, model parameter adjustments would be in other plans like `file_upload_plan.md`, `model-parameters-plan.md`).

### 3.3. Chat History
*   **Enhanced Sidebar Display**: Shows recent 10 chats, grouped by date (DD/MM/YYYY).
*   **Paginated History Page (`/chat/history`)**: "View All" button leads to a page displaying all user chat history with pagination.
*   **Display Details**: Each history item shows title, LLM model icon, and date.
*   **Backend Support**: API endpoint for fetching chat history with pagination and sorting.

### 3.4. API Service for External Use (API Revamp)
*   **API Key Management**:
    *   Frontend UI for users to generate and view their API keys.
    *   Backend endpoint to generate, store, and associate API keys with users.
*   **LLM Model & API Version Endpoint**:
    *   API endpoint (`/llm/config`) to list available LLM models and their API versions (filtered for active models).
*   **Core LLM Endpoints**:
    *   Standardized API paths like `/{apiVersion}/deployments/{modelDeploymentName}/chat/completions`, embeddings, etc.
    *   Handles routing to appropriate LLM services based on model and version.
*   **Swagger Documentation**:
    *   Dynamically generated Swagger (OpenAPI) specifications per API version.
    *   Accessible via a frontend UI (`/settings/api-service/docs/{apiVersion}`).

### 3.5. Unified Settings Page
*   Centralized `/settings` page accessible from the sidebar.
*   Displays user account information and a "Sign out" button.
*   Expandable sections for:
    *   FAQ
    *   API (linking to API key management and Swagger docs)
    *   Prompt Engineering & Temperature settings
    *   Terms & Conditions
    *   Privacy Policy
    *   Feedback submission
    *   Request Quote
    *   Contact Us
    *   LLM Health Check

## 4. Planned or In-Progress Enhancements (from `memory-bank/` and other plans)

The project also includes plans or ongoing work for features such as:
*   Chat Payload Refactoring (`chat_payload_refactor_plan.md`)
*   File Upload capabilities (`file_upload_plan.md`)
*   Google Search Integration (`google_search_integration_plan.md`)
*   Homepage Rework (`HOMEPAGE_REWORK_PLAN.md`)
*   Database Housekeeping/Migration (`housekeeping-plan.md`, `migration-plan.md`)
*   Model Parameter Adjustments UI (`model-parameters-plan.md`)
*   Multi-LLM strategy refinements (`multi-llm-plan.md`)
*   Speech-to-Text (GCP, interim results) (`speech_interim_results_plan.md`, `speech_to_text_gcp_plan.md`)
*   UI Redesign efforts (`ui-redesign-plan.md`, `ui_design_stitch/`)

This overview provides a snapshot of the HKBU GenAI Platform's architecture, technology choices, and key features, both implemented and planned.
---

## 5. How to Start the Project

### 5.1. Prerequisites
*   Node.js (latest LTS version recommended)
*   pnpm (version 10.7.1 or as specified in the root `package.json`)

### 5.2. Initial Setup
1.  **Clone the Repository**:
    ```bash
    git clone <repository_url>
    cd ito-hkbuchatgpt
    ```
2.  **Install Dependencies**:
    From the monorepo root directory (`ito-hkbuchatgpt`), run:
    ```bash
    pnpm install
    ```
    This will install dependencies for all packages and apps in the monorepo.

### 5.3. Environment Variable Setup

You will need to create `.env` files for both the `web` and `api` applications. Since no `.env.example` files are present, create them manually:

1.  **Web Application (`hkbu-genai-platform/apps/web/.env`)**:
    Create this file and add the following key variables (refer to existing deployed environments or team members for actual values):
    ```env
    # NextAuth.js Configuration
    NEXTAUTH_URL=https://localhost:3001/chatdemo # Or http://localhost:3000/chatdemo if not using SSL proxy
    NEXT_PUBLIC_BASE_PATH=/chatdemo
    BUAM_NEXTAUTH_JWT_SECRET=your_strong_jwt_secret_for_nextauth
    NODE_TLS_REJECT_UNAUTHORIZED=0 # Set to 1 in production, 0 for local dev with self-signed certs if using SSL proxy

    # BUAM OIDC Provider Details
    BUAM_OAUTH=https://issuat.hkbu.edu.hk/buam/Auth?client_id=YOUR_CLIENT_ID&redirect_uri=https%3A%2F%2Flocalhost%3A3001%2Fchatdemo%2Fapi%2Fauth%2Fcallback%2Fbuam&response_type=code&scope=get_user_portfolio
    BUAM_OAUTH_TOKEN=https://issuat.hkbu.edu.hk/BUAM-REST/oauth2/token
    BUAM_OAUTH_USERINFO=https://issuat.hkbu.edu.hk/BUAM-REST/oauth2/get_user_portfolio
    BUAM_OAUTH_CLIENTID=YOUR_BUAM_CLIENT_ID
    BUAM_OAUTH_CLIENTSECRET=YOUR_BUAM_CLIENT_SECRET
    BUAM_NEXTAUTH_SESSION_EXPIRY=7 # In days

    # API Backend URL (for frontend to call the NestJS API)
    NEXT_PUBLIC_API_BASE_URL=http://localhost:3003/api/v0 # Assuming API runs on port 3003

    # Other variables as needed (e.g., encryption keys, feature flags)
    # HTTPS_AGENT=http://your_proxy_url:port # If behind a corporate proxy
    ```

2.  **API Application (`hkbu-genai-platform/apps/api/.env`)**:
    Create this file and add the following key variables:
    ```env
    # Database
    DATABASE_URL="sqlserver://user:password@server:port;database=db_name;encrypt=true;trustServerCertificate=true;"

    # JWT Secret (must match BUAM_NEXTAUTH_JWT_SECRET in apps/web/.env)
    BUAM_NEXTAUTH_JWT_SECRET=your_strong_jwt_secret_for_nextauth

    # LLM Provider API Keys (Azure, Google, Anthropic, etc.)
    # Example:
    # AZURE_OPENAI_API_KEY_SWC=your_azure_openai_key
    # GOOGLE_API_KEY=your_google_api_key
    # ANTHROPIC_API_KEY=your_anthropic_key

    # Azure OCR
    # AZURE_OCR_KEY=your_ocr_key
    # AZURE_OCR_ENDPOINT=your_ocr_endpoint

    # Google Cloud Credentials (for Speech-to-Text, Vertex AI)
    # GOOGLE_APPLICATION_CREDENTIALS=/path/to/your/gcp-service-account-key.json
    # GOOGLE_VERTEXAI_PROJECT_ID=your_gcp_project_id
    # GOOGLE_VERTEXAI_REGION=your_gcp_region

    # Other necessary variables (e.g., mailer config, specific LLM endpoints)
    PORT=3003 # Port for the API service
    CORS_ORIGIN=http://localhost:3000 # Or https://localhost:3001 if using SSL proxy for web
    ```

### 5.4. Running the Development Servers

You have two main options:

**Option A: Recommended for full setup (Web app with SSL, separate API)**

1.  **Start the API Service**:
    Open a terminal in the monorepo root (`ito-hkbuchatgpt`) and run:
    ```bash
    pnpm --filter=@hkbu-genai-platform/api dev
    ```
    This will start the NestJS API service (typically on port 3003, as configured in its `.env`).

2.  **Start the Web Application with SSL Proxy**:
    Open another terminal, navigate to the web app directory:
    ```bash
    cd hkbu-genai-platform/apps/web
    ```
    Then run the `dev-ssl` script:
    ```bash
    pnpm run dev-ssl
    ```
    This starts the Next.js app on `http://localhost:3000` and `local-ssl-proxy` makes it available via `https://localhost:3001`. Ensure `NEXTAUTH_URL` in `apps/web/.env` is `https://localhost:3001/chatdemo`.

**Option B: Simpler setup (Web app on HTTP, separate API)**

1.  **Start Both API and Web App in Parallel**:
    Open a terminal in the monorepo root (`ito-hkbuchatgpt`) and run:
    ```bash
    pnpm dev
    ```
    This uses TurboRepo to start the `dev` script for both `apps/api` (NestJS) and `apps/web` (Next.js).
    *   The API service will start (typically on port 3003).
    *   The Web application will start on `http://localhost:3000`.

2.  **Environment Adjustment for Option B**:
    If using this option, ensure `NEXTAUTH_URL` in `hkbu-genai-platform/apps/web/.env` is set to `http://localhost:3000/chatdemo` and `NODE_TLS_REJECT_UNAUTHORIZED` is commented out or set to `1`. Also, `CORS_ORIGIN` in `hkbu-genai-platform/apps/api/.env` should allow `http://localhost:3000`.

### 5.5. Accessing the Applications
*   **Web Application**:
    *   If using Option A (SSL): `https://localhost:3001/chatdemo`
    *   If using Option B (HTTP): `http://localhost:3000/chatdemo`
*   **API Service**: Typically on `http://localhost:3003` (or as configured). Swagger docs might be available at a path like `http://localhost:3003/api-docs`.

Remember to restart servers after changing `.env` files or `next.config.ts`.