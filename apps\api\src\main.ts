import { NestFactory, HttpAdapterHost } from '@nestjs/core'; // Import HttpAdapterHost
import { ConfigService } from '@nestjs/config'; // Import ConfigService
import { ValidationPipe } from '@nestjs/common'; // Import ValidationPipe
import {
  FastifyAdapter,
  NestFastifyApplication,
} from '@nestjs/platform-fastify';
import { AppModule } from './app.module';
import { PrismaService } from './prisma/prisma.service'; // Import PrismaService
import fastifyCors from '@fastify/cors'; // Import the fastify-cors plugin
import fastifyMultipart from '@fastify/multipart'; // Import fastify-multipart
import { keyVaultLoader } from './config/key-vault.config';

async function bootstrap() {
  // Load Key Vault secrets before bootstrapping the app
  const loadedConfig = await keyVaultLoader();
  Object.assign(process.env, loadedConfig);

  // Increase body limit for Fastify adapter (e.g., 50MB)
  const adapter = new FastifyAdapter({
    bodyLimit: 50 * 1024 * 1024, // 50MB limit
  });
  const app = await NestFactory.create<NestFastifyApplication>(
    AppModule,
    adapter, // Use the configured adapter
  );

  // Enable Prisma shutdown hooks
  const prismaService = app.get(PrismaService);
  await prismaService.enableShutdownHooks(app);

  // Register fastify-multipart BEFORE global pipes or interceptors that need the parsed body
  await app.register(fastifyMultipart);
  console.log(`fastify-multipart plugin registered.`);

  // Apply global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true, // Strip properties that do not have any decorators
      transform: true, // Automatically transform payloads to be objects typed according to their DTO classes
      // forbidNonWhitelisted: true, // Optionally throw error if non-whitelisted properties are present
    }),
  );

  // Set global prefix for all routes (e.g., /api/v0)
  const configService = app.get(ConfigService);
  const basePath = configService.get<string>('BASE_PATH');
  const fullPath = basePath ? `${basePath}/api/v0` : 'api/v0'; // Append /api/v0
  app.setGlobalPrefix(fullPath);
  console.log(`Global prefix set to: ${fullPath}`);

  // Enable CORS for the frontend origin
  // Register fastify-cors plugin directly
  await app.register(fastifyCors, {
    origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'], // Ensure OPTIONS is included
    credentials: true,
  });

  console.log(
    `fastify-cors plugin registered for origin: ${process.env.CORS_ORIGIN || 'http://localhost:3000'}`,
  );

  // The NestJS app.enableCors() call is removed as fastify-cors handles it now

  const port = process.env.PORT || 3000;
  await app.listen(port, '0.0.0.0');
  console.log(`Application is running on : ${await app.getUrl()}`);
}
bootstrap();
