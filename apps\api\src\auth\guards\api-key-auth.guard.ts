import {
  Injectable,
  ExecutionContext,
  UnauthorizedException,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { AuthService } from '../auth.service'; // Adjust path as necessary
import { ApiUserPayload } from '../api-key.strategy'; // Adjust path as necessary

@Injectable()
export class ApiKeyAuthGuard extends AuthGuard('api-key') {
  // 'api-key' matches the strategy name
  private readonly logger = new Logger(ApiKeyAuthGuard.name);

  constructor(private readonly authService: AuthService) {
    super();
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    this.logger.debug('ApiKeyAuthGuard canActivate called.');
    // Perform authentication using the 'api-key' strategy
    const isAuthenticated = await super.canActivate(context);
    if (!isAuthenticated) {
      this.logger.warn('Authentication failed by Passport strategy.');
      throw new UnauthorizedException(
        'Authentication failed: Invalid or missing API key.',
      );
    }

    this.logger.debug(
      'Authentication successful, proceeding to authorization.',
    );
    const request = context.switchToHttp().getRequest();
    const user = request.user as ApiUserPayload; // User should be populated by Passport strategy

    if (!user) {
      this.logger.error(
        'User object not found on request after successful authentication.',
      );
      // This case should ideally not happen if Passport strategy works correctly
      throw new UnauthorizedException(
        'User authentication data not found after validation.',
      );
    }

    this.logger.debug(`Authorizing user: ${user.ssoid}`);
    const isAuthorized =
      await this.authService.isUserAuthorizedForApiService(user);

    if (!isAuthorized) {
      this.logger.warn(
        `User ${user.ssoid} is not authorized for this API service.`,
      );
      throw new ForbiddenException(
        'User is not authorized to access this service.',
      );
    }

    this.logger.log(`User ${user.ssoid} authorized for API service.`);
    return true;
  }

  // Optional: Handle request to customize error messages or behavior
  handleRequest(err, user, info, context, status) {
    if (err || !user) {
      this.logger.warn(
        `handleRequest: Authentication error or no user. Error: ${err}, Info: ${info}, Status: ${status}`,
      );
      if (
        err instanceof ForbiddenException ||
        err instanceof UnauthorizedException
      )
        throw err;
      throw (
        err || new UnauthorizedException('Authentication failed via API key.')
      );
    }
    return user; // Important: return user to be attached to request
  }
}
