import { Module, Global } from '@nestjs/common';
import { RateLimitService } from './services/rate-limit.service';
import { ModelMappingService } from './services/model-mapping.service';
import { TokenUsageService } from './services/token-usage.service';
import { ModelRateLimitGuard } from './guards/model-rate-limit.guard';
import { PromptSpeechRateLimitGuard } from './guards/prompt-speech-rate-limit.guard';
import { GeneralRateLimitGuard } from './guards/general-rate-limit.guard';
import { PrismaModule } from '../prisma/prisma.module';

@Global()
@Module({
  imports: [PrismaModule],
  providers: [
    RateLimitService,
    ModelMappingService,
    TokenUsageService,
    ModelRateLimitGuard,
    PromptSpeechRateLimitGuard,
    GeneralRateLimitGuard,
  ],
  exports: [
    RateLimitService,
    ModelMappingService,
    TokenUsageService,
    ModelRateLimitGuard,
    PromptSpeechRateLimitGuard,
    GeneralRateLimitGuard,
  ],
})
export class CommonModule {}
