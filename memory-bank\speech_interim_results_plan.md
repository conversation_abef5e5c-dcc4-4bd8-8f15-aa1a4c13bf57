# Plan: Enable Interim Results for Browser Speech Recognition

This plan outlines the modifications needed in `hkbu-genai-platform/apps/web/src/components/genai/chat/ChatInputArea.tsx` to use the `interimResults` feature of the browser's `SpeechRecognition` API, providing live feedback as the user speaks.

## Steps:

1.  **Enable Interim Results:**
    *   Modify the `SpeechRecognition` instance configuration.
    *   **File:** `hkbu-genai-platform/apps/web/src/components/genai/chat/ChatInputArea.tsx`
    *   **Change:** Update line 66 from `recognitionInstance.interimResults = false;` to `recognitionInstance.interimResults = true;`.

2.  **Update Result Handling (`onresult` event handler):**
    *   Modify the `onresult` handler (lines 70-87) to process multiple event firings with both interim and final results.
    *   **Logic:**
        *   Iterate through the `event.results` array.
        *   Check the `isFinal` property of each result.
        *   Build the transcript by concatenating final results and appending the latest interim result.
        *   Update the `messageInputRef.current.value` with the latest transcript.
        *   Continue dispatching the `input` event (lines 80-81).
        *   Remove `setIsRecording(false);` (line 86) from this handler.

3.  **Visual Feedback (Optional):**
    *   Consider adding subtle visual feedback (e.g., greyed-out text) for interim results. (Can be implemented later).

## Sequence Diagram:

```mermaid
sequenceDiagram
    participant User
    participant ChatInputArea (Frontend)
    participant SpeechRecognition API (Browser)

    User->>+ChatInputArea: Clicks Mic Button (toggleRecording)
    ChatInputArea->>+SpeechRecognition API: start()
    Note over ChatInputArea: isRecording = true
    SpeechRecognition API-->>-ChatInputArea: onstart event (optional handling)
    User->>SpeechRecognition API: Speaks "Hello world"
    SpeechRecognition API-->>ChatInputArea: onresult event (interim: "Hello")
    ChatInputArea->>ChatInputArea: Update input field ("Hello")
    SpeechRecognition API-->>ChatInputArea: onresult event (interim: "Hello world")
    ChatInputArea->>ChatInputArea: Update input field ("Hello world")
    User->>ChatInputArea: Clicks Mic Button (toggleRecording)
    ChatInputArea->>+SpeechRecognition API: stop()
    SpeechRecognition API-->>-ChatInputArea: onresult event (final: "Hello world")
    ChatInputArea->>ChatInputArea: Update input field ("Hello world")
    SpeechRecognition API-->>ChatInputArea: onend event
    Note over ChatInputArea: isRecording = false
    ChatInputArea-->>-User: Mic button returns to normal state